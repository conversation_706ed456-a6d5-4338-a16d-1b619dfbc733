﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <EffecterDef>
    <defName>NociosphereDeparting</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>PlainFlash</fleckDef>
        <burstCount>1</burstCount>
        <chancePerTick>1</chancePerTick>
        <chancePeriodTicks>15</chancePeriodTicks>
        <scale>10</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>PlainFlash</fleckDef>
        <initialDelayTicks>660</initialDelayTicks> <!--11 seconds -->
        <burstCount>1</burstCount>
        <chancePerTick>.1</chancePerTick>
        <chancePeriodTicks>3</chancePeriodTicks>
        <scale>6</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>2</positionRadius>
        <fleckDef>EnergyCrackle</fleckDef>
        <chancePerTick>.05</chancePerTick>
        <chancePeriodTicks>15</chancePeriodTicks>
        <burstCount>1~4</burstCount>
        <scale>.15~.25</scale>
        <spawnLocType>OnSource</spawnLocType>
        <soundDef>Pawn_Nociosphere_Arc</soundDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>NociosphereDepartingRing</fleckDef>
        <burstCount>1~1</burstCount>
        <scale>2</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef> 
    <defName>NociosphereDepartComplete</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <burstCount>1</burstCount>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>NociosphereDepartCompleteDistortion</fleckDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>.2</scale>
        <speed>0</speed>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <scale>.5~1</scale>
        <fleckDef>HoraxianField</fleckDef>
        <burstCount>40</burstCount>
        <angle>0</angle>
        <rotateTowardsTargetCenter>true</rotateTowardsTargetCenter>
        <positionRadius>2</positionRadius>
        <positionRadiusMin>2</positionRadiusMin>
        <rotationRate>-5~5</rotationRate>
        <speed>0</speed>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <scale>3~14</scale>
        <fleckDef>HoraxianSparks</fleckDef>
        <burstCount>8~8</burstCount>
        <angle>0</angle>
        <positionRadius>1</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <rotationRate>0~0</rotationRate>
        <speed>0</speed>
      </li>
    </children>
  </EffecterDef>



  <EffecterDef>
    <defName>MeatExplosionTiny</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>MeatExplosionSmall</soundDef>
        <spawnLocType>OnTarget</spawnLocType>
      </li>

      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>MeatExplosionSplatter</fleckDef>
        <burstCount>2</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>.75</positionRadius>
        <speed>0.6~1.1</speed>
        <scale>.5~.75</scale>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>

      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MeatExplosionMist</moteDef>
        <burstCount>1~2</burstCount>
        <scale>.4~.7</scale>
        <rotation>-150~150</rotation>
        <positionRadius>.75</positionRadius>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MeatExplosionFlyingSplatter</moteDef>
        <burstCount>1</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>0.6</positionRadius>
        <scale>.5~.75</scale>
        <speed>0.8~1.2</speed>
        <spawnLocType>OnSource</spawnLocType>
      </li>

    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>MeatExplosionSmall</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>MeatExplosionSmall</soundDef>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>MeatExplosionSplatter</fleckDef>
        <burstCount>2</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>1.3</positionRadius>
        <speed>0.6~1.1</speed>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MeatExplosionMist</moteDef>
        <burstCount>4</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>1.3</positionRadius>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MeatExplosionFlyingSplatter</moteDef>
        <burstCount>1</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>0.6</positionRadius>
        <speed>0.8~1.2</speed>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MeatExplosionFlyingPiece</moteDef>
        <burstCount>3</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>0.5</positionRadius>
        <speed>2~3</speed>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>MeatExplosion</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>MeatExplosionNormal</soundDef>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>MeatExplosionSplatter</fleckDef>
        <burstCount>5</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>1.3</positionRadius>
        <speed>0.6~1.1</speed>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MeatExplosionMist</moteDef>
        <burstCount>12</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>1.3</positionRadius>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MeatExplosionFlyingSplatter</moteDef>
        <burstCount>3</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>0.6</positionRadius>
        <speed>0.8~1.2</speed>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MeatExplosionFlyingPiece</moteDef>
        <burstCount>5</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>0.5</positionRadius>
        <speed>2~3</speed>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>
  
  <EffecterDef>
    <defName>MeatExplosionLarge</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>MeatExplosionLarge</soundDef>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>MeatExplosionSplatter</fleckDef>
        <burstCount>8</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>1.5</positionRadius>
        <speed>0.6~1.3</speed>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MeatExplosionMist</moteDef>
        <burstCount>14</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>1.6</positionRadius>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MeatExplosionFlyingSplatter</moteDef>
        <burstCount>6</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>0.75</positionRadius>
        <speed>1~1.4</speed>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MeatExplosionFlyingPiece</moteDef>
        <burstCount>8</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>0.6</positionRadius>
        <speed>2.5~3.5</speed>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>MeatExplosionExtraLarge</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>MeatExplosionLarge</soundDef>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>MeatExplosionSplatter</fleckDef>
        <burstCount>8</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>1.5</positionRadius>
        <speed>0.6~1.3</speed>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MeatExplosionMist</moteDef>
        <burstCount>14~17</burstCount>
        <rotation>-150~150</rotation>
        <scale>1~1.4</scale>
        <positionRadius>1.9</positionRadius>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MeatExplosionFlyingSplatter</moteDef>
        <burstCount>6~8</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>0.75</positionRadius>
        <speed>1~1.4</speed>
        <scale>1~1.4</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MeatExplosionFlyingPiece</moteDef>
        <burstCount>8~10</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>0.6</positionRadius>
        <speed>2.5~3.5</speed>
        <scale>1~1.4</scale>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>FleshmassDestroyed</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>Fleshmass_Destroyed</soundDef>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>MeatExplosionSplatter</fleckDef>
        <burstCount>2</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>1.3</positionRadius>
        <speed>0.6~1.1</speed>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MeatExplosionMist</moteDef>
        <burstCount>4</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>1.3</positionRadius>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MeatExplosionFlyingSplatter</moteDef>
        <burstCount>1</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>0.6</positionRadius>
        <speed>0.8~1.2</speed>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MeatExplosionFlyingPiece</moteDef>
        <burstCount>3</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>0.5</positionRadius>
        <speed>2~3</speed>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>FleshmassHeartDestroyed</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_CameraShakeSingle</subEffecterClass>
        <cameraShake>1</cameraShake>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>FleshmassHeart_Destroyed</soundDef>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>MeatExplosionSplatter</fleckDef>
        <burstCount>2</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>1.3</positionRadius>
        <speed>0.6~1.1</speed>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MeatExplosionMist</moteDef>
        <burstCount>4</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>1.3</positionRadius>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MeatExplosionFlyingSplatter</moteDef>
        <burstCount>1</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>0.6</positionRadius>
        <speed>0.8~1.2</speed>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MeatExplosionFlyingPiece</moteDef>
        <burstCount>3</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>0.5</positionRadius>
        <speed>2~3</speed>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>TachycardiacArrest</defName>
    <maintainTicks>720</maintainTicks>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>MeatExplosionSplatter</fleckDef>
        <burstCount>2</burstCount>
        <ticksBetweenMotes>5</ticksBetweenMotes>
        <rotation>-150~150</rotation>
        <positionRadius>1.3</positionRadius>
        <speed>0.6~1.1</speed>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <moteDef>Mote_MeatExplosionMist</moteDef>
        <burstCount>1</burstCount>
        <ticksBetweenMotes>5</ticksBetweenMotes>
        <rotation>-150~150</rotation>
        <positionRadius>1.3</positionRadius>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <moteDef>Mote_MeatExplosionFlyingSplatter</moteDef>
        <burstCount>1</burstCount>
        <ticksBetweenMotes>2</ticksBetweenMotes>
        <rotation>-150~150</rotation>
        <positionRadius>1</positionRadius>
        <speed>0.8~1.2</speed>
        <scale>2~5</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <moteDef>Mote_MeatExplosionFlyingPieceOverloaded</moteDef>
        <burstCount>2</burstCount>
        <ticksBetweenMotes>30</ticksBetweenMotes>
        <rotation>-150~150</rotation>
        <positionRadius>1.3</positionRadius>
        <speed>5~20</speed>
        <scale>2~2.5</scale>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <moteDef>Mote_MeatExplosionFlyingPieceOverloaded</moteDef>
        <initialDelayTicks>710</initialDelayTicks>
        <burstCount>40</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>2</positionRadius>
        <speed>5~20</speed>
        <scale>2~2.5</scale>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <burstCount>1</burstCount>
        <initialDelayTicks>550</initialDelayTicks>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>TachycardiacArrestRiserRing</fleckDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>3</scale>
        <speed>0~0</speed>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <moteDef>TachycardiacArrestExplosion</moteDef>
        <burstCount>1</burstCount>
        <initialDelayTicks>710</initialDelayTicks>
        <rotation>0</rotation>
        <positionRadius>0</positionRadius>
        <speed>0</speed>
        <scale>1</scale>
        <fleckUsesAngleForVelocity>false</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>ShamblerRaise</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass> <!-- Initial burst -->
        <fleckDef>ShamblerRaiseSplatter</fleckDef>
        <burstCount>2~5</burstCount> 
        <rotation>-150~150</rotation>
        <positionRadius>0.3</positionRadius>
        <speed>0.6~1.1</speed>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>ShamblerRaiseSplatter</fleckDef>
        <chancePerTick>0.1</chancePerTick>
        <rotation>-150~150</rotation>
        <positionRadius>0.3</positionRadius>
        <speed>0.6~1.1</speed>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>
     
  <EffecterDef>
    <defName>ShamblerRaise_Sketch</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>  <!-- Initial burst -->
        <fleckDef>ShamblerRaiseSplatter</fleckDef>
        <burstCount>2~5</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>0.3</positionRadius>
        <speed>0.6~1.1</speed>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>ShamblerRaiseSplatter</fleckDef>
        <chancePerTick>0.01</chancePerTick>
        <rotation>-150~150</rotation>
        <positionRadius>0.3</positionRadius>
        <scale>1~2</scale>
        <speed>0.6~1.1</speed>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
     
    </children>
  </EffecterDef>
  
  <EffecterDef>
    <defName>MetalhorrorEmerging</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>CocoonDestroyed</soundDef> <!-- Placeholder -->
        <spawnLocType>OnTarget</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>MeatExplosionSplatter</fleckDef>
        <burstCount>5</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>1.3</positionRadius>
        <speed>0.6~1.1</speed>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MeatExplosionMist</moteDef>
        <burstCount>12</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>1.3</positionRadius>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MeatExplosionFlyingSplatter</moteDef>
        <burstCount>3</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>0.6</positionRadius>
        <speed>0.8~1.2</speed>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MeatExplosionFlyingPiece</moteDef>
        <burstCount>5</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>0.5</positionRadius>
        <speed>2~3</speed>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>ImpactDustCloud</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>DustPuffLong</fleckDef>
        <burstCount>15</burstCount>
        <positionRadius>2</positionRadius>
        <scale>10</scale>
        <rotationRate>-5~5</rotationRate>
        <angle>0~360</angle>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <speed>0.5~3.0</speed>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>ImpactSmallDustCloud</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>DustPuff</fleckDef>
        <burstCount>15</burstCount>
        <positionRadius>2</positionRadius>
        <scale>1</scale>
        <rotationRate>-5~5</rotationRate>
        <angle>0~360</angle>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <speed>0.5~1.0</speed>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>UndercaveCeilingDebris</defName>
    <maintainTicks>130</maintainTicks>
    <randomWeight>1</randomWeight>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>SheetingDust</fleckDef>
        <burstCount>2~3</burstCount>
        <positionRadius>.2</positionRadius>
        <scale>20</scale>
        <rotationRate>0</rotationRate>
        <angle>180</angle>
        <rotation>0</rotation>
        <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
        <speed>0</speed> <!-- moved via height curve in fleck -->
      </li>
      
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <fleckDef>DustImpact</fleckDef>
        <initialDelayTicks>69</initialDelayTicks>
        <burstCount>6~10</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <positionRadius>2</positionRadius>
        <scale>3~5</scale>
        <rotationRate>-20~20</rotationRate>
        <angle>0~360</angle>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <speed>1~2</speed>
      </li>
      
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <fleckDef>BouncingDebris</fleckDef>
        <initialDelayTicks>69</initialDelayTicks>
        <burstCount>3~5</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <positionRadius>2</positionRadius>
        <scale>.5~1</scale>
        <rotationRate>-200~200</rotationRate>
        <angle>0~360</angle>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <speed>1~3</speed>
      </li>


    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>HoraxianSpellLight_Warmup</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <scale>1.5~1.5</scale>
        <fleckDef>HoraxianHugeSpellLightWarmup</fleckDef>
        <burstCount>1~1</burstCount>
        <rotationRate>0~0</rotationRate>
        <speed>0~0</speed>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>HoraxianSpellDark_Warmup</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <scale>1.5~1.5</scale>
        <fleckDef>HoraxianHugeSpellDarkWarmup</fleckDef>
        <burstCount>1~1</burstCount>
        <rotationRate>0~0</rotationRate>
        <speed>0~0</speed>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
    </children>
  </EffecterDef>
  
  <EffecterDef>
    <defName>AgonyPulseExplosion</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <scale>1</scale>
        <fleckDef>HoraxianBurstDistortion</fleckDef>
        <burstCount>1~1</burstCount>
        <rotationRate>0~0</rotationRate>
        <speed>0~0</speed>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <scale>.5~1</scale>
        <fleckDef>HoraxianField</fleckDef>
        <burstCount>20~20</burstCount>
        <angle>0</angle>
        <positionRadius>2</positionRadius>
        <positionRadiusMin>2</positionRadiusMin>
        <rotationRate>0~0</rotationRate>
        <speed>0</speed>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>HoraxianClouds</fleckDef>
        <burstCount>30</burstCount>
        <positionRadius>5</positionRadius>
        <scale>2~4</scale>
        <rotationRate>-5~5</rotationRate>
        <angle>0~360</angle>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <speed>1.0~1.0</speed>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <scale>3~14</scale>
        <fleckDef>HoraxianSparks</fleckDef>
        <burstCount>8~8</burstCount>
        <angle>0</angle>
        <positionRadius>1</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <rotationRate>0~0</rotationRate>
        <speed>0</speed>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>HoraxianWhiteRing</fleckDef>
        <burstCount>1</burstCount>
        <scale>.1</scale>
        <spawnLocType>OnSource</spawnLocType>
        <absoluteAngle>true</absoluteAngle>
        <rotation>0~0</rotation>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>ObeliskSpark</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <spawnLocType>OnSource</spawnLocType>
        <soundDef>Explosion_EMP</soundDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_SparkSimple</moteDef> <!-- placeholder -->
        <positionRadius>2</positionRadius>
        <burstCount>1~2</burstCount>
        <scale>1</scale>
        <rotation>-150~-150</rotation>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>HoraxianAbilityCasting</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <fleckDef>PlainFlash</fleckDef>
        <initialDelayTicks>14</initialDelayTicks>
        <burstCount>1~1</burstCount>
        <scale>1.3~1.3</scale>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <positionRadius>1</positionRadius>
        <moteDef>Mote_SparkSimple</moteDef> <!-- placeholder -->
        <rotation>-150~-150</rotation>
        <burstCount>1~4</burstCount>
        <scale>0.25</scale>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
    </children>
  </EffecterDef>
  
  <EffecterDef>
    <defName>GrayBoxOpened</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>GrayBoxOpening</soundDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>DustPuff</fleckDef>
        <spawnLocType>OnSource</spawnLocType>
        <rotationRate>-10~10</rotationRate>
        <speed>0.1~0.2</speed>
        <scale>0.8~2</scale>
        <positionRadius>0.75</positionRadius>
        <burstCount>10~20</burstCount>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>AbductWarmup</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <scale>.5</scale>
        <chancePerTick>1</chancePerTick>
        <chancePeriodTicks>30</chancePeriodTicks>
        <moteDef>Mote_AbductWarmupDistortionRing</moteDef>
        <burstCount>1~1</burstCount>
        <rotationRate>0~0</rotationRate>
        <speed>0~0</speed>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <positionRadius>1</positionRadius>
        <fleckDef>AbductWarmupSpark</fleckDef>
        <rotation>-150~-150</rotation>
        <burstCount>1~4</burstCount>
        <scale>0.25~.4</scale>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>AbductWarmup_Pawn</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <scale>.5</scale>
        <chancePerTick>1</chancePerTick>
        <chancePeriodTicks>30</chancePeriodTicks>
        <moteDef>Mote_AbductWarmupDistortionRing</moteDef>
        <burstCount>1~1</burstCount>
        <rotationRate>0~0</rotationRate>
        <speed>0~0</speed>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <positionRadius>1</positionRadius>
        <fleckDef>AbductWarmupSpark</fleckDef>
        <rotation>-150~-150</rotation>
        <burstCount>1~4</burstCount>
        <scale>0.25~.4</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>AbductWarmup_Monolith</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_Sustainer</subEffecterClass>
        <soundDef>VoidMonolith_GleamingActivating</soundDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <scale>2</scale>
        <chancePerTick>1</chancePerTick>
        <chancePeriodTicks>30</chancePeriodTicks>
        <moteDef>Mote_AbductWarmupDistortionRingBuilding</moteDef>
        <burstCount>1~1</burstCount>
        <rotationRate>0~0</rotationRate>
        <speed>0~0</speed>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <fleckDef>AbductWarmupSpark</fleckDef>
        <rotation>-150~-150</rotation>
        <angle>0~360</angle>
        <absoluteAngle>true</absoluteAngle>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <rotateTowardsTargetCenter>true</rotateTowardsTargetCenter>
        <burstCount>6~12</burstCount>
        <speed>-1~0</speed>
        <positionRadius>5</positionRadius>
        <positionRadiusMin>3</positionRadiusMin>
        <scale>0.25~.4</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>
  
  <EffecterDef>
    <defName>ObeliskActionWarmup</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>ObeliskActionDarkHighlightRing</fleckDef>
        <chancePerTick>1</chancePerTick>
        <chancePeriodTicks>20</chancePeriodTicks>
        <burstCount>1~1</burstCount>
        <scale>3</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>EnergyCrackle</fleckDef>
        <chancePerTick>.05</chancePerTick>
        <chancePeriodTicks>15</chancePeriodTicks>
        <positionRadius>1</positionRadius>
        <burstCount>1~4</burstCount>
        <scale>.15~.23</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>PsychicRitual_Sustained</defName>
    <children>
      
      <li>
        <subEffecterClass>SubEffecter_CameraShakeChance</subEffecterClass>
        <scale>.03~.03</scale>
        <chancePeriodTicks>5</chancePeriodTicks>
        <chancePerTick>1</chancePerTick>
        <distanceAttenuationScale>1</distanceAttenuationScale>
        <distanceAttenuationMax>10</distanceAttenuationMax>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>PulsingDistortionRing</fleckDef>
        <spawnLocType>OnTarget</spawnLocType>
        <burstCount>1</burstCount>
        <chancePerTick>1</chancePerTick>
        <chancePeriodTicks>96</chancePeriodTicks>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>.3</scale>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <speed>0</speed>
        <attachToSpawnThing>true</attachToSpawnThing>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <chancePerTick>.3</chancePerTick>
        <chancePeriodTicks>6</chancePeriodTicks>
        <fleckDef>HoraxianFieldQuickRingCurved</fleckDef>
        <spawnLocType>OnTarget</spawnLocType>
        <burstCount>2~2</burstCount>
        <positionRadius>1.75</positionRadius>
        <positionRadiusMin>1</positionRadiusMin>
        <scale>.45~.55</scale>
        <rotation>50~120</rotation>
        <rotationRate>-5~5</rotationRate>
        <angle>0~0</angle>
        <speed>-.2~.35</speed>
        <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
        <rotateTowardsTargetCenter>true</rotateTowardsTargetCenter>
        <attachToSpawnThing>true</attachToSpawnThing>
      </li>
      <!-- attracting bits -->
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <chancePerTick>.2</chancePerTick>
        <chancePeriodTicks>20</chancePeriodTicks>
        <scale>.05</scale>
        <fleckDef>HoraxianFerroBlob</fleckDef>
        <positionRadius>4.2</positionRadius>
        <positionRadiusMin>4.2</positionRadiusMin>
        <burstCount>1~2</burstCount>
        <rotationRate>0~0</rotationRate>
        <speed>-1.6</speed>
        <angle>0</angle>
        <spawnLocType>OnSource</spawnLocType>
        <absoluteAngle>false</absoluteAngle>
        <rotateTowardsTargetCenter>true</rotateTowardsTargetCenter>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <chancePerTick>1</chancePerTick>
        <chancePeriodTicks>120</chancePeriodTicks>
        <avoidLastPositionRadius>1</avoidLastPositionRadius>
        <fleckDef>GroundCrack</fleckDef>
        <initialDelayTicks>0</initialDelayTicks>
        <burstCount>1</burstCount>
        <color>(1, 1, 1, .75)</color>
        <spawnLocType>OnSource</spawnLocType>
        <positionRadius>5</positionRadius>
        <scale>.75~2</scale>
        <rotation>0~360</rotation>
        <rotationRate>0</rotationRate>
        <absoluteAngle>True</absoluteAngle>
        <positionOffset>(-.5, 0, -.5)</positionOffset>
        <angle>0</angle>
        <fleckUsesAngleForVelocity>False</fleckUsesAngleForVelocity>
        <speed>0</speed>
        <makeMoteOnSubtrigger>true</makeMoteOnSubtrigger>
      </li>
      
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>PsychicRitual_CompleteSoon</defName>
    <maintainTicks>60</maintainTicks>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <burstCount>1</burstCount>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>PsychicRitualRiserRing</fleckDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>2</scale>
        <speed>0</speed>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
    </children>
  </EffecterDef>
  
  
  <EffecterDef>
    <defName>PsychicRitual_Complete</defName>
    <maintainTicks>120</maintainTicks>
    <children>
      
      <li>
        <subEffecterClass>SubEffecter_CameraShakeChance</subEffecterClass>
        <initialDelayTicks>0</initialDelayTicks>
        <positionOffset>(0, 0, 0)</positionOffset>
        <chancePerTick>1</chancePerTick>
        <cameraShake>.5</cameraShake>
        <lifespanMaxTicks>1</lifespanMaxTicks>
        <distanceAttenuationScale>.5</distanceAttenuationScale>
      </li>
      
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>ExpandingDistortionRing</fleckDef>
        <spawnLocType>OnTarget</spawnLocType>
        <burstCount>1</burstCount>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>1</scale>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <speed>0</speed>
        <attachToSpawnThing>true</attachToSpawnThing>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>PsychicRitual_Complete</fleckDef>
        <spawnLocType>OnTarget</spawnLocType>
        <burstCount>25</burstCount>
        <positionRadius>1</positionRadius>
        <positionRadiusMin>1</positionRadiusMin>
        <scale>.4~.5</scale>
        <rotation>50~120</rotation>
        <rotationRate>-5~5</rotationRate>
        <angle>0~0</angle>
        <speed>13~20</speed>
        <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
        <rotateTowardsTargetCenter>true</rotateTowardsTargetCenter>
        <attachToSpawnThing>true</attachToSpawnThing>
      </li>
      
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>PsychicRitual_Complete</soundDef>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>VoidTerror_Target</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_CameraShakeSingle</subEffecterClass>
        <initialDelayTicks>0</initialDelayTicks>
        <positionOffset>(0, 0, 0)</positionOffset>
        <cameraShake>.2</cameraShake>
        <lifespanMaxTicks>1</lifespanMaxTicks>
        <distanceAttenuationScale>1</distanceAttenuationScale>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>PsychicRitual_Complete</fleckDef>
        <spawnLocType>OnTarget</spawnLocType>
        <burstCount>10</burstCount>
        <positionRadius>1</positionRadius>
        <positionRadiusMin>1</positionRadiusMin>
        <scale>.1~.25</scale>
        <rotation>50~120</rotation>
        <rotationRate>-5~5</rotationRate>
        <angle>0~0</angle>
        <speed>1~8</speed>
        <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
        <rotateTowardsTargetCenter>true</rotateTowardsTargetCenter>
        <attachToSpawnThing>true</attachToSpawnThing>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef> <!-- Played upon initial investigation -->
    <defName>MonolithStage1</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <burstCount>1</burstCount>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>DarkHighlightRing</fleckDef>
        <positionRadius>1</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>1~1</scale>
        <speed>0~.3</speed>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <burstCount>1</burstCount>
        <chancePerTick>.01</chancePerTick>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>DarkHighlightRing</fleckDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>.1</scale>
        <speed>0</speed>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <burstCount>1</burstCount>
        <chancePerTick>1</chancePerTick>
        <chancePeriodTicks>120</chancePeriodTicks>
        <positionOffset>(0, 0, 0)</positionOffset>
        <moteDef>Mote_DistortionPulse</moteDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>.5</scale>
        <speed>0~0</speed>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef> <!-- Played upon further investigation -->
    <defName>MonolithStage2</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <burstCount>1</burstCount>
        <chancePerTick>.03</chancePerTick>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>EnergyCrackle</fleckDef>
        <positionRadius>2</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>.25</scale>
        <speed>.001</speed>
        <rotation>0</rotation>
        <rotateTowardsTargetCenter>true</rotateTowardsTargetCenter>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnTarget</spawnLocType>
        <orbitOrigin>true</orbitOrigin>
        <orbitSnapStrength>1.0</orbitSnapStrength>
        <soundDef>Zap_Loud</soundDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <burstCount>1</burstCount>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>DarkHighlightRing</fleckDef>
        <positionRadius>1</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>3</scale>
        <speed>0</speed>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0</angle>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <burstCount>1</burstCount>
        <chancePerTick>1</chancePerTick>
        <chancePeriodTicks>100</chancePeriodTicks>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>DarkHighlightRing</fleckDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>.5</scale>
        <speed>0</speed>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0</angle>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <burstCount>1</burstCount>
        <chancePerTick>1</chancePerTick>
        <chancePeriodTicks>100</chancePeriodTicks>
        <positionOffset>(0, 0, 0)</positionOffset>
        <moteDef>Mote_DistortionPulse</moteDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>.75</scale>
        <speed>0~0</speed>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>MonolithGleaming_Transition</defName>
    <maintainTicks>480</maintainTicks>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <burstCount>1</burstCount>
        <chancePerTick>.1</chancePerTick>
        <chancePeriodTicks>10</chancePeriodTicks>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>EnergyCrackle</fleckDef>
        <soundDef>Zap_Loud</soundDef>
        <positionRadius>2</positionRadius>
        <rotation>0~100</rotation>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>.25</scale>
        <speed>.001</speed>
        <rotateTowardsTargetCenter>true</rotateTowardsTargetCenter>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnSource</spawnLocType>
        <orbitOrigin>true</orbitOrigin>
        <orbitSnapStrength>1.0</orbitSnapStrength>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <burstCount>1</burstCount>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>MonolithLevelChanged</fleckDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>1</scale>
        <speed>0~0</speed>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <burstCount>1</burstCount>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>DarkHighlightRing_FullDirty</fleckDef>
        <positionRadius>1</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>5</scale>
        <speed>0</speed>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <scale>.5~1</scale>
        <fleckDef>HoraxianField_LongDecay</fleckDef>
        <burstCount>50</burstCount>
        <angle>0</angle>
        <speed>6~10</speed>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <rotateTowardsTargetCenter>true</rotateTowardsTargetCenter>
        <positionRadius>4</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <rotationRate>-5~5</rotationRate>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <scale>3~14</scale>
        <fleckDef>HoraxianSparks</fleckDef>
        <burstCount>8~8</burstCount>
        <angle>0</angle>
        <positionRadius>1</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <rotationRate>0~0</rotationRate>
        <speed>0</speed>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef> <!-- Played upon further investigation - excludes hovering pyramid -->
    <defName>MonolithGleaming_Sustained</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <burstCount>1</burstCount>
        <chancePerTick>.03</chancePerTick>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>EnergyCrackle</fleckDef>
        <soundDef>Zap_Loud</soundDef>
        <positionRadius>1</positionRadius>
        <positionRadiusMin>1</positionRadiusMin>
        <scale>.12~.25</scale>
        <speed>.001</speed>
        <rotation>0</rotation>
        <rotateTowardsTargetCenter>true</rotateTowardsTargetCenter>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnTarget</spawnLocType>
        <orbitOrigin>true</orbitOrigin>
        <orbitSnapStrength>1.0</orbitSnapStrength>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <burstCount>1</burstCount>
        <chancePerTick>1</chancePerTick>
        <color>(1, 0, 0, .25)</color>
        <chancePeriodTicks>200</chancePeriodTicks>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>MonolithLowLightningRing</fleckDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>.6~.7</scale>
        <speed>0</speed>
        <rotation>0</rotation>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <burstCount>1</burstCount>
        <chancePerTick>1</chancePerTick>
        <chancePeriodTicks>120</chancePeriodTicks>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>GleamingMonolithLights</fleckDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>1</scale>
        <speed>0</speed>
        <rotation>0</rotation>
      </li>
    </children>
  </EffecterDef>
  
  <EffecterDef> <!-- Played upon further investigation - for the hovering pyramid -->
    <defName>MonolithGleamingPyramid_Sustained</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <chancePerTick>1.0</chancePerTick>
        <chancePeriodTicks>120</chancePeriodTicks>
        <scale>1.25</scale>
        <fleckDef>GleamingLensFlare</fleckDef>
        <burstCount>1</burstCount>
        <color>(.8, .7, .7, .5)</color>
        <angle>0</angle>
        <positionOffset>(0, 0, 2)</positionOffset>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <rotationRate>0</rotationRate>
        <rotation>0</rotation>
        <speed>0</speed>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>
  
  <EffecterDef> 
    <defName>EnergyCrackle_Sketch</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <burstCount>15</burstCount>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>EnergyCrackle</fleckDef>
        <positionRadius>2</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>.25</scale>
        <speed>.001</speed>
        <rotation>0</rotation>
        <rotateTowardsTargetCenter>true</rotateTowardsTargetCenter>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnSource</spawnLocType>
        <orbitOrigin>true</orbitOrigin>
        <orbitSnapStrength>1.0</orbitSnapStrength>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef> <!-- Played upon monolith level changed further -->
    <defName>MonolithLevelChanged</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <burstCount>1</burstCount>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>MonolithLevelChanged</fleckDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>1</scale>
        <speed>0~0</speed>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <burstCount>1</burstCount>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>DarkHighlightRing_FullDirty</fleckDef>
        <positionRadius>1</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>5</scale>
        <speed>0</speed>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <scale>.5~1</scale>
        <fleckDef>HoraxianField_LongDecay</fleckDef>
        <burstCount>40</burstCount>
        <angle>0</angle>
        <rotateTowardsTargetCenter>true</rotateTowardsTargetCenter>
        <positionRadius>4</positionRadius>
        <positionRadiusMin>4</positionRadiusMin>
        <rotationRate>-5~5</rotationRate>
        <speed>0</speed>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <scale>3~14</scale>
        <fleckDef>HoraxianSparks</fleckDef>
        <burstCount>8~8</burstCount>
        <angle>0</angle>
        <positionRadius>1</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <rotationRate>0~0</rotationRate>
        <speed>0</speed>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>PsychicRitual_Candles</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <burstCount>6~9</burstCount>
        <positionOffset>(0, 0, 0)</positionOffset>
        <moteDef>Mote_RitualCandleA</moteDef>
        <positionRadius>4</positionRadius>
        <positionRadiusMin>2</positionRadiusMin>
        <rotation>0</rotation>
        <scale>1</scale>
        <avoidLastPositionRadius>2</avoidLastPositionRadius>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>VoidNodeAttached</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <burstCount>1</burstCount>
        <positionOffset>(0, 0, 0)</positionOffset>
        <moteDef>VoidNodeCore_DustA</moteDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>.9</scale>
        <speed>0</speed>
        <rotation>60</rotation>
        <rotationRate>200</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnTarget</spawnLocType>
      </li>

      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <burstCount>1</burstCount>
        <positionOffset>(0, 0, 0)</positionOffset>
        <moteDef>VoidNodeCore_DustB</moteDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>1.05</scale>
        <speed>0</speed>
        <rotation>0</rotation>
        <rotationRate>100</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnTarget</spawnLocType>
      </li>

      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <burstCount>1</burstCount>
        <positionOffset>(0, 0, 0)</positionOffset>
        <ticksBetweenMotes>30</ticksBetweenMotes>
        <fleckDef>VoidNodeCore_DebrisBit</fleckDef>
        <scale>.8~1.2</scale>
        <positionRadius>5</positionRadius>
        <positionRadiusMin>3</positionRadiusMin>
        <speed>-10~10</speed>
        <rotation>0</rotation>
        <rotationRate>-2000~2000</rotationRate>
        <angle>0~0</angle>
        <orbitOrigin>true</orbitOrigin>
        <orbitSpeed>-5~-1</orbitSpeed>
        <orbitSnapStrength>.8</orbitSnapStrength>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnTarget</spawnLocType>
      </li>

      <li> 
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <burstCount>3</burstCount>
        <ticksBetweenMotes>30</ticksBetweenMotes>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>VoidNodeCore_DebrisBit</fleckDef>
        <scale>.8~1.2</scale>
        <positionRadius>6</positionRadius>
        <positionRadiusMin>5</positionRadiusMin>
        <speed>-10~10</speed>
        <rotation>0</rotation>
        <rotationRate>-2000~2000</rotationRate>
        <angle>0~0</angle>
        <orbitOrigin>true</orbitOrigin>
        <orbitSpeed>-6~-1</orbitSpeed>
        <orbitSnapStrength>.8</orbitSnapStrength>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnTarget</spawnLocType>
      </li>

      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <burstCount>1</burstCount>
        <chancePerTick>.03</chancePerTick>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>EnergyCrackle</fleckDef>
        <soundDef>Zap_Quiet</soundDef>
        <positionRadius>2.7</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>.35</scale>
        <speed>.001</speed>
        <rotation>0</rotation>
        <rotateTowardsTargetCenter>true</rotateTowardsTargetCenter>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnTarget</spawnLocType>
        <orbitOrigin>true</orbitOrigin>
        <orbitSnapStrength>1.0</orbitSnapStrength>
      </li>

      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <burstCount>1</burstCount>
        <chancePerTick>.05</chancePerTick>
        <chancePeriodTicks>20</chancePeriodTicks>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>VoidNodeLowLightningRing</fleckDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>1</scale>
        <speed>0</speed>
        <rotation>0</rotation>
      </li>

      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <burstCount>1</burstCount>
        <chancePerTick>.05</chancePerTick>
        <chancePeriodTicks>100</chancePeriodTicks>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>VoidNodeHighLightningRing</fleckDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>1</scale>
        <rotation>0~360</rotation>
        <speed>0</speed>
      </li>

      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <burstCount>1</burstCount>
        <positionOffset>(0, 0, 0)</positionOffset>
        <moteDef>VoidNodeDistortion</moteDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>1</scale>
        <rotation>0</rotation>
        <speed>0</speed>
      </li>

      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <burstCount>1</burstCount>
        <chancePerTick>.01</chancePerTick>
        <chancePeriodTicks>50</chancePeriodTicks>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>VoidNodeLowestGroundFlashes</fleckDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>.5~1</scale>
        <speed>0</speed>
        <rotation>0~360</rotation>
      </li>
      
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>VoidNodeDisruptedFlashes</defName>
    <maintainTicks>720</maintainTicks>
    <children>
      <!-- Flashes 1 -->
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <burstCount>1</burstCount>
        <chancePerTick>.05</chancePerTick>
        <chancePeriodTicks>20</chancePeriodTicks>
        <initialDelayTicks>240</initialDelayTicks>
        <lifespanMaxTicks>240</lifespanMaxTicks>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>VoidNodeLowLightningRing</fleckDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>3</scale>
        <speed>0</speed>
        <rotation>0</rotation>
      </li>
      
      <!-- Flashes 2 -->
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <burstCount>1</burstCount>
        <chancePerTick>.05</chancePerTick>
        <chancePeriodTicks>10</chancePeriodTicks>
        <initialDelayTicks>480</initialDelayTicks>
        <lifespanMaxTicks>1</lifespanMaxTicks>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>VoidNodeLowLightningRing</fleckDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>4</scale>
        <speed>0</speed>
        <rotation>0</rotation>
      </li>

    </children>
  </EffecterDef>
  
  <EffecterDef>
    <defName>VoidNodeDisrupted</defName>
    <maintainTicks>720</maintainTicks>
    <children>

      <!--Warmup -->
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <fleckDef>VoidNodeCollapseContractRingSlowLow</fleckDef>
        <initialDelayTicks>0</initialDelayTicks>
        <burstCount>1~1</burstCount>
        <scale>2</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>


      <!-- PULSE INWARD -->
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <fleckDef>VoidNodeCollapsingRing</fleckDef>
        <initialDelayTicks>60</initialDelayTicks>
        <burstCount>1</burstCount>
        <positionOffset>(0, -.1, 0)</positionOffset>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>5</scale>
        <speed>0~0</speed>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      
      <!-- PULSE OUTWARD -->
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <burstCount>1</burstCount>
        <initialDelayTicks>120</initialDelayTicks>
        <positionOffset>(0, .1, 0)</positionOffset>
        <fleckDef>DirtyExpandingRingShort</fleckDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>.75</scale>
        <speed>0~0</speed>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      
      
      <!-- PULSE INWARD AFTER DELAY -->
      <li> 
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>VoidNodeCollapsingRing</fleckDef>
        <burstCount>1</burstCount>
        <initialDelayTicks>220</initialDelayTicks>
        <lifespanMaxTicks>440</lifespanMaxTicks>
        <chancePerTick>1</chancePerTick>
        <chancePeriodTicks>20</chancePeriodTicks>
        <positionOffset>(0, -.1, 0)</positionOffset>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>4</scale>
        <speed>0~0</speed>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      
      <!-- VERY SLOW CONTRACTION -->
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>VoidNodeCollapseDirtyGroundFogSlow</fleckDef>
        <initialDelayTicks>240</initialDelayTicks>
        <burstCount>1~1</burstCount>
        <scale>3</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    
      <!-- VERY SLOW CONTRACTION-->
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <fleckDef>VoidNodeCollapseContractRingSlow</fleckDef>
        <initialDelayTicks>360</initialDelayTicks>
        <burstCount>1~1</burstCount>
        <scale>10</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      
      <!-- BIG BURST AT END -->
      <li> 
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <burstCount>1</burstCount>
        <initialDelayTicks>660</initialDelayTicks>
        <positionOffset>(0, .1, 0)</positionOffset>
        <fleckDef>DirtyExpandingRingShort</fleckDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>1</scale>
        <speed>0~0</speed>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <!--
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <chancePerTick>1.0</chancePerTick>
        <chancePeriodTicks>200</chancePeriodTicks>
        <scale>2</scale>
        <fleckDef>VoidNodeDisrupted</fleckDef>
        <burstCount>1</burstCount>
        <angle>0</angle>
        <positionRadius>3</positionRadius>
        <positionRadiusMin>3</positionRadiusMin>
        <rotationRate>50</rotationRate>
        <speed>0</speed>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      -->
    </children>
  </EffecterDef>



  <EffecterDef> 
    <defName>DeathRefusalAvailable</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <chancePerTick>1.0</chancePerTick>
        <chancePeriodTicks>200</chancePeriodTicks>
        <scale>2</scale>
        <fleckDef>DeathRefusalPulse</fleckDef>
        <burstCount>1</burstCount>
        <angle>0</angle>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <rotationRate>50</rotationRate>
        <speed>0</speed>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>DeathRefusalUse</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass> <!-- Initial burst -->
        <fleckDef>DeathRefusalSplatter</fleckDef>
        <burstCount>2~5</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>0.3</positionRadius>
        <speed>0.6~1.1</speed>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>DeathRefusalSplatter</fleckDef>
        <chancePerTick>0.3</chancePerTick>
        <chancePeriodTicks>50</chancePeriodTicks>
        <rotation>-150~150</rotation>
        <positionRadius>0.5</positionRadius>
        <speed>0.6~1.1</speed>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>SputteringBlackPuff</fleckDef>
        <chancePerTick>0.1</chancePerTick>
        <chancePeriodTicks>10</chancePeriodTicks>
        <rotation>-150~150</rotation>
        <positionRadius>0.5</positionRadius>
        <speed>0.6~1.1</speed>
        <fleckUsesAngleForVelocity>false</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>PitGateAboveGroundCollapseStage1</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>PitGateCollapseDust</fleckDef>
        <chancePerTick>0.01</chancePerTick>
        <chancePeriodTicks>10</chancePeriodTicks>
        <rotation>-150~150</rotation>
        <scale>1~2</scale>
        <positionOffset>(0, 0, -2)</positionOffset>
        <positionRadius>1</positionRadius>
        <speed>0.6~1.1</speed>
        <fleckUsesAngleForVelocity>false</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>
  
  
  <EffecterDef>
    <defName>PitGateAboveGroundCollapseStage2</defName>
    <children>
      
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>PitGateCollapseDust</fleckDef>
        <chancePerTick>0.1</chancePerTick>
        <chancePeriodTicks>5</chancePeriodTicks>
        <rotation>-150~150</rotation>
        <positionOffset>(0, 0, -2)</positionOffset>
        <positionRadius>1</positionRadius>
        <speed>1~2.1</speed>
        <fleckUsesAngleForVelocity>false</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>

      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>PitGateCollapseDustThick</fleckDef>
        <chancePerTick>0.01</chancePerTick>
        <chancePeriodTicks>10</chancePeriodTicks>
        <rotation>-60~60</rotation>
        <scale>3~10</scale>
        <positionOffset>(0, 0, -1)</positionOffset>
        <positionRadius>1</positionRadius>
        <speed>0.6~3</speed>
        <fleckUsesAngleForVelocity>false</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>PitGateCollapseDustVeryFine</fleckDef>
        <burstCount>2~5</burstCount>
        <chancePerTick>0.05</chancePerTick>
        <chancePeriodTicks>150</chancePeriodTicks>
        <rotation>0~360</rotation>
        <rotationRate>-100~100</rotationRate>
        <scale>1</scale>
        <positionOffset>(0, 0, -1)</positionOffset>
        <positionRadius>1</positionRadius>
        <speed>0.6~5</speed>
        <angle>-20~20</angle>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>PitGateAboveGroundCollapsed</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>PitGateCollapsedDistortion</fleckDef>
        <chancePerTick>0.1</chancePerTick>
        <chancePeriodTicks>10</chancePeriodTicks>
        <rotation>-150~150</rotation>
        <positionRadius>0</positionRadius>
        <speed>0.6~1.1</speed>
        <fleckUsesAngleForVelocity>false</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>

      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>PitGateCollapseForce</fleckDef>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>PitGateCollapseRays</fleckDef>
        <burstCount>2</burstCount>
        <rotation>0~30</rotation>
        <rotationRate>-10~10</rotationRate>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>PitGateCollapseCenterFlash</fleckDef>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>



  <EffecterDef>
    <defName>FillIn</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_Sustainer</subEffecterClass>
        <soundDef>Interact_GraveDig</soundDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>PitGateCollapseDust</fleckDef>
        <chancePerTick>0.2</chancePerTick>
        <chancePeriodTicks>100</chancePeriodTicks>
        <positionLerpFactor>.3</positionLerpFactor>
        <scale>0.4~1.2</scale>
        <rotationRate>-60~60</rotationRate>
        <speed>0.6~1</speed>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>PitGateCollapseDustThick</fleckDef>
        <chancePerTick>0.1</chancePerTick>
        <chancePeriodTicks>100</chancePeriodTicks>
        <positionLerpFactor>.3</positionLerpFactor>
        <scale>0.4~1.2</scale>
        <rotationRate>-60~60</rotationRate>
        <speed>0.6~1</speed>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>DirtBitsArching</fleckDef>
        <spawnLocType>OnSource</spawnLocType>
        <chancePerTick>0.05</chancePerTick>
        <scale>0.5~1</scale>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <angle>-10~10</angle>
        <rotation>0~360</rotation>
        <positionOffset>(0, 0, -.2)</positionOffset>
        <speed>5~6</speed>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>DeadlifeReleasing</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>DeadlifeRelease</soundDef>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.3</positionRadius>
        <fleckDef>Fleck_DeadlifeSmall</fleckDef>
        <chancePerTick>0.1</chancePerTick>
        <scale>0.5~1</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <maintainTicks>300</maintainTicks>
    <defName>VoidStructureSpawningSkipSequence</defName>
    <children>
        <li>
          <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
          <soundDef>VoidStructure_Emerge</soundDef>
        </li>
        <li>
          <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
          <fleckDef>PsycastSkipFlashEntry</fleckDef>
          <burstCount>1~1</burstCount>
          <scale>3</scale>
          <rotation>0~0</rotation>
          <spawnLocType>OnSource</spawnLocType>
        </li>
        <li>
          <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
          <fleckDef>PlainFlash</fleckDef>
          <burstCount>1~1</burstCount>
          <scale>21</scale>
          <spawnLocType>OnSource</spawnLocType>
        </li>
        <li>
          <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
          <fleckDef>PsycastSkipInnerEntry</fleckDef>
          <initialDelayTicks>11</initialDelayTicks>
          <burstCount>1~1</burstCount>
          <scale>3</scale>
          <rotation>0~0</rotation>
          <spawnLocType>OnSource</spawnLocType>
        </li>
        <li>
          <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
          <fleckDef>PsycastSkipOuterRingEntry</fleckDef>
          <initialDelayTicks>11</initialDelayTicks>
          <burstCount>1~1</burstCount>
          <scale>3</scale>
          <rotation>0~0</rotation>
          <spawnLocType>OnSource</spawnLocType>
        </li>
        <li>
          <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
          <positionRadius>3.9</positionRadius>
          <fleckDef>ElectricalSpark</fleckDef>
          <burstCount>7~9</burstCount>
          <scale>2.5</scale>
          <spawnLocType>OnSource</spawnLocType>
        </li>
        <li>
          <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
          <positionRadius>3.9</positionRadius>
          <initialDelayTicks>5</initialDelayTicks>
          <fleckDef>ElectricalSpark</fleckDef>
          <burstCount>5~7</burstCount>
          <scale>2.5</scale>
          <spawnLocType>OnSource</spawnLocType>
        </li>
        <li>
          <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
          <positionRadius>3.9</positionRadius>
          <initialDelayTicks>10</initialDelayTicks>
          <fleckDef>ElectricalSpark</fleckDef>
          <burstCount>3~7</burstCount>
          <scale>1.5</scale>
          <spawnLocType>OnSource</spawnLocType>
        </li>
      
      <!-- and then do the rest -->

        <li>
          <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
          <fleckDef>DarkHighlightRing</fleckDef>
          <initialDelayTicks>15</initialDelayTicks>
          <burstCount>1</burstCount>
          <scale>3</scale>
          <spawnLocType>OnSource</spawnLocType>
        </li>
        <li>
          <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
          <fleckDef>DirtyExpandingRingShort</fleckDef>
          <initialDelayTicks>15</initialDelayTicks>
          <burstCount>1</burstCount>
          <scale>1</scale>
          <spawnLocType>OnSource</spawnLocType>
        </li>
        <li>
          <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
          <initialDelayTicks>15</initialDelayTicks>
          <fleckDef>HoraxianClouds</fleckDef>
          <burstCount>30</burstCount>
          <positionRadius>5</positionRadius>
          <scale>2~4</scale>
          <rotationRate>-5~5</rotationRate>
          <angle>0~360</angle>
          <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
          <speed>1.0~1.0</speed>
        </li>
      
      
    </children>
  </EffecterDef>
  

  <EffecterDef>
    <defName>VoidStructureActivating</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>VoidStructureActivatingRing</fleckDef>
        <chancePerTick>1</chancePerTick>
        <positionOffset>(-.5, 0, -.75)</positionOffset>
        <chancePeriodTicks>60</chancePeriodTicks>
        <burstCount>1</burstCount>
        <scale>3</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>1</positionRadius>
        <positionRadiusMin>.75</positionRadiusMin>
        <positionOffset>(-.2, 0, -.2)</positionOffset>
        <fleckDef>EnergyCrackle</fleckDef>
        <soundDef>Zap_Quiet</soundDef>
        <chancePerTick>.1</chancePerTick>
        <chancePeriodTicks>30</chancePeriodTicks>
        <burstCount>1</burstCount>
        <scale>.15~.25</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>VoidStructureActivated</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <burstCount>1</burstCount>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>VoidAwakeningStructureActivated</fleckDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>1</scale>
        <speed>0~0</speed>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <scale>.5~1</scale>
        <fleckDef>HoraxianField_LongDecay</fleckDef>
        <burstCount>40</burstCount>
        <angle>0</angle>
        <rotateTowardsTargetCenter>true</rotateTowardsTargetCenter>
        <positionRadius>4</positionRadius>
        <positionRadiusMin>4</positionRadiusMin>
        <rotationRate>-5~5</rotationRate>
        <speed>0</speed>
      </li>
    </children>
  </EffecterDef>
  
  <EffecterDef>
    <defName>VoidStructureActivatedAmbience</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <burstCount>1</burstCount>
        <chancePerTick>1</chancePerTick>
        <chancePeriodTicks>240</chancePeriodTicks>
        <positionOffset>(-.5, 0, -.75)</positionOffset>
        <fleckDef>VoidStructureLowRing</fleckDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>.2</scale>
        <speed>0</speed>
        <rotation>0</rotation>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>1</positionRadius>
        <positionRadiusMin>.5</positionRadiusMin>
        <fleckDef>EnergyCrackle</fleckDef>
        <soundDef>Zap_Quiet</soundDef>
        <positionOffset>(-.2, 0, -.2)</positionOffset>
        <chancePerTick>.007</chancePerTick>
        <chancePeriodTicks>10</chancePeriodTicks>
        <burstCount>1~2</burstCount>
        <scale>.2~.25</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>ProximityDetectorAlert</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <fleckDef>ProximityDetectorFlare</fleckDef>
        <ticksBetweenMotes>67</ticksBetweenMotes>
        <spawnLocType>OnSource</spawnLocType>
        <positionOffset>(0, 0, 0.2)</positionOffset>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <fleckDef>ProximityDetectorGem</fleckDef>
        <ticksBetweenMotes>120</ticksBetweenMotes>
        <spawnLocType>OnSource</spawnLocType>
        <rotation>0</rotation>
        <positionOffset>(0, 0, .11)</positionOffset>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>DisruptorFlareAttached</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>  <!-- CAREFUL REORDERING ME! Index of this effect must match what's in CompDisruptorFlare -->
        <fleckDef>DisruptorFlareGlow</fleckDef>
        <chancePerTick>.2</chancePerTick>
        <chancePeriodTicks>20</chancePeriodTicks>
        <spawnLocType>OnSource</spawnLocType>
        <positionOffset>(0, 0, 0.1)</positionOffset>
        <rotation>0</rotation>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>Smoke</fleckDef>
        <chancePerTick>0.01</chancePerTick>
        <scale>0.6~1.1</scale>
        <spawnLocType>OnSource</spawnLocType>
        <absoluteAngle>true</absoluteAngle>
        <angle>10~20</angle>
        <speed>0.2~0.35</speed>
        <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>DisruptorDestroyWarning</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>DisruptorDestroyWarnSmoke</fleckDef>
        <chancePerTick>1</chancePerTick>
        <scale>0.6~1.1</scale>
        <burstCount>2~3</burstCount>
        <chancePeriodTicks>30</chancePeriodTicks>
        <spawnLocType>OnSource</spawnLocType>
        <absoluteAngle>true</absoluteAngle>
        <angle>10~20</angle>
        <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
        <speed>0.3~0.55</speed>
      </li>
    </children>
  </EffecterDef>
  
  <EffecterDef>
    <defName>DisruptorFlareLanded</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>DisruptorFlareImpactPulse</fleckDef>
        <scale>.1</scale>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <absoluteAngle>true</absoluteAngle>
        <angle>0</angle>
        <speed>0</speed>
        <rotation>0</rotation>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>FlareImpact</soundDef>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
    </children>
  </EffecterDef>
  
  <EffecterDef>
    <defName>Fire_SpewBioferrite</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.15</positionRadius>
        <fleckDef>FireSpew_A_Bioferrite</fleckDef>
        <chancePerTick>0.9</chancePerTick>
        <speed>9.5</speed>
        <scale>0.8~1.2</scale>
        <angle>-15~15</angle>
        <positionLerpFactor>0.85</positionLerpFactor>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.1</positionRadius>
        <fleckDef>FireSpew_Base_Bioferrite</fleckDef>
        <chancePerTick>0.1</chancePerTick>
        <speed>0.6</speed>
        <scale>0.8</scale>
        <angle>-9~9</angle>
        <positionLerpFactor>0.75</positionLerpFactor>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.1</positionRadius>
        <moteDef>Mote_SparkThrownFast</moteDef>
        <chancePerTick>0.5</chancePerTick>
        <speed>3.3~5</speed>
        <scale>0.1~0.2</scale>
        <angle>-12~12</angle>
        <positionLerpFactor>0.8</positionLerpFactor>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.2</positionRadius>
        <fleckDef>FireSpew_Glow_Bioferrite</fleckDef>
        <chancePerTick>0.16</chancePerTick>
        <positionLerpFactor>0.65</positionLerpFactor>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>IncineratorBeam_End</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>Smoke</fleckDef>
        <chancePerTick>0.4</chancePerTick>
        <scale>0.6~1.1</scale>
        <spawnLocType>OnSource</spawnLocType>
        <absoluteAngle>true</absoluteAngle>
        <angle>30~70</angle>
        <speed>0.2~0.35</speed>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.1</positionRadius>
        <moteDef>Mote_SparkThrownFast</moteDef>
        <spawnLocType>OnSource</spawnLocType>
        <chancePerTick>0.9</chancePerTick>
        <speed>3.3~5</speed>
        <scale>0.1~0.2</scale>
        <angle>88~104</angle>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.1</positionRadius>
        <spawnLocType>OnSource</spawnLocType>
        <fleckDef>IncineratorBeamEnd_Glow</fleckDef>
        <chancePerTick>0.5</chancePerTick>
        <scale>0.8</scale>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>Shell_AcidSpitStream</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>Fleck_AcidSpitLaunchedDenseMist</fleckDef>
        <scale>.5</scale>
        <chancePerTick>.2</chancePerTick>
        <spawnLocType>OnSource</spawnLocType>
        <absoluteAngle>false</absoluteAngle>
        <angle>-30~30</angle>
        <speed>-2~2</speed>
        <rotation>0</rotation>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>Fleck_AcidSpitLaunchedGlob</fleckDef>
        <scale>.3~.6</scale>
        <chancePerTick>.6</chancePerTick>
        <spawnLocType>OnSource</spawnLocType>
        <absoluteAngle>true</absoluteAngle>
        <angle>-30~30</angle>
        <speed>-4~0</speed>
        <positionOffset>(0, 0, .75)</positionOffset>
        <rotation>0</rotation>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Shell_AcidSpitLaunched</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>Fleck_AcidSpitLaunchedMist</fleckDef>
        <scale>1</scale>
        <burstCount>10~15</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <absoluteAngle>false</absoluteAngle>
        <positionRadius>2</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <angle>-40~40</angle>
        <speed>1~3</speed>
        <rotation>0~360</rotation>
        <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
      </li>
      
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>Fleck_AcidSpitLaunchedGlob</fleckDef>
        <burstCount>10~15</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <absoluteAngle>false</absoluteAngle>
        <positionRadius>.4</positionRadius>
        <positionRadiusMin>.4</positionRadiusMin>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <angle>-20~20</angle>
        <scale>.2~.5</scale>
        <speed>2~5</speed>
        <rotation>0~360</rotation>
      </li>
    </children>
  </EffecterDef>
  
  <EffecterDef>
    <defName>Shell_AcidSpitImpact</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>Fleck_AcidSpitImpact</fleckDef>
        <burstCount>1</burstCount>
        <chancePerTick>1</chancePerTick>
        <chancePeriodTicks>2</chancePeriodTicks>
        <lifespanMaxTicks>8</lifespanMaxTicks>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>Fleck_AcidSpitLaunchedMist</fleckDef>
        <scale>4~5</scale>
        <burstCount>3~6</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <absoluteAngle>false</absoluteAngle>
        <angle>0~100</angle>
        <positionRadius>1</positionRadius>
        <speed>-1~1</speed>
        <rotation>0</rotation>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>Fleck_AcidSpitLaunchedGlobFast</fleckDef>
        <burstCount>6~10</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <absoluteAngle>false</absoluteAngle>
        <positionRadius>.7</positionRadius>
        <positionRadiusMin>.7</positionRadiusMin>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <rotateTowardsTargetCenter>true</rotateTowardsTargetCenter>
        <angle>0~100</angle>
        <scale>.3~.5</scale>
        <speed>20~45</speed>
        <rotation>0~360</rotation>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>BurnerUsed</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <fleckDef>Fleck_BurnerUsedSmoke</fleckDef>
        <initialDelayTicks>5</initialDelayTicks>
        <scale>.1~1</scale>
        <burstCount>7~10</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <absoluteAngle>True</absoluteAngle>
        <positionRadius>2.5</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <angle>0</angle>
        <speed>0~.6</speed>
        <rotation>0~360</rotation>
        <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
      </li>

      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <fleckDef>Fleck_BurnerUsedEmber</fleckDef>
        <initialDelayTicks>7</initialDelayTicks>
        <scale>.1~.5</scale>
        <burstCount>1~4</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <absoluteAngle>True</absoluteAngle>
        <positionRadius>3</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <angle>0</angle>
        <speed>.1~.9</speed>
        <rotation>0~360</rotation>
        <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
      </li>

      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <fleckDef>Fleck_BurnerUsedEmberBlack</fleckDef>
        <initialDelayTicks>7</initialDelayTicks>
        <scale>.1~.3</scale>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <absoluteAngle>True</absoluteAngle>
        <positionRadius>3</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <angle>0</angle>
        <speed>.3~1.5</speed>
        <rotation>0~360</rotation>
        <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
      </li>
      
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>StudyHoraxian</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass> <!-- Using this type instead of InteractSymbol to get better positioning -->
        <spawnLocType>OnSource</spawnLocType>
        <maxMoteCount>1</maxMoteCount>
        <destroyMoteOnCleanup>True</destroyMoteOnCleanup>
        <positionOffset>(0, 0, .5)</positionOffset>
        <moteDef>Mote_Clipboard</moteDef>
        <angle>0</angle>
        <rotation>0</rotation>
        <absoluteAngle>True</absoluteAngle>
        <attachToSpawnThing>true</attachToSpawnThing>
        <useTargetAInitialRotation>true</useTargetAInitialRotation>
        <perRotationOffsets>
          <li>(0, 0, 0)</li>
          <li>(0, 0, 0)</li>
          <li>(0, 1, 0)</li> <!-- make it render above the pawn when the pawn is facing south -->
          <li>(0, 0, 0)</li>
        </perRotationOffsets>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <fleckDef>Fleck_Studying</fleckDef>
        <scale>.6</scale>
        <positionLerpFactor>.5</positionLerpFactor>
        <burstCount>1</burstCount>
        <ticksBetweenMotes>45</ticksBetweenMotes>
        <spawnLocType>BetweenPositions</spawnLocType>
        <absoluteAngle>True</absoluteAngle>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <angle>0</angle>
        <speed>0.7~1</speed>
        <rotation>-20~20</rotation>
        <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>GhoulFrenzy</defName>
    <offsetMode>Free</offsetMode> <!-- don't spawn attached, since the flecks already anchor themselves correctly, spawning attached can introduce bad offsets -->
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <fleckDef>StressAura</fleckDef>
        <ticksBetweenMotes>30</ticksBetweenMotes>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <positionRadius>0</positionRadius>
        <scale>1</scale>
        <rotation>0~0</rotation>
        <rotationRate>0</rotationRate>
        <absoluteAngle>True</absoluteAngle>
        <angle>0</angle>
        <fleckUsesAngleForVelocity>false</fleckUsesAngleForVelocity>
        <speed>0</speed>
      </li>
      
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <fleckDef>StressSpikes</fleckDef>
        <ticksBetweenMotes>20</ticksBetweenMotes>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <positionRadius>0</positionRadius>
        <scale>.8</scale>
        <positionOffset>(0, 0, .75)</positionOffset>
        <rotation>-2~2</rotation>
        <rotationRate>0</rotationRate>
        <absoluteAngle>True</absoluteAngle>
        <fleckUsesAngleForVelocity>false</fleckUsesAngleForVelocity>
        <speed>0</speed>
      </li>
      
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>RevenantDeath</defName>
    <maintainTicks>300</maintainTicks>
    <children>
      
      <!-- POP, Good on-kill feedback and helps cover up any transition weirdness --> 
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>HoraxianBurstDistortionSmall</fleckDef>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <positionRadius>0</positionRadius>
        <scale>.01</scale>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <absoluteAngle>True</absoluteAngle>
        <positionOffset>(0, 0, 0)</positionOffset>
        <angle>0</angle>
        <fleckUsesAngleForVelocity>false</fleckUsesAngleForVelocity>
        <speed>0</speed>
      </li>

      <li>
        <subEffecterClass>SubEffecter_CameraShakeSingle</subEffecterClass>
        <cameraShake>.5</cameraShake>
        <distanceAttenuationScale>1</distanceAttenuationScale>
      </li>
      
      <!-- DISSOLVING BODY -->
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>RevenantDeath</fleckDef>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <positionRadius>0</positionRadius>
        <scale>1</scale>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <absoluteAngle>True</absoluteAngle>
        <positionOffset>(0, 0, 0)</positionOffset>
        <angle>0</angle>
        <fleckUsesAngleForVelocity>false</fleckUsesAngleForVelocity>
        <speed>0</speed>
      </li>
      
      <!-- EARLY DRIPS, STARTING AT FOOT -->
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>RevenantDeathDrip1</fleckDef>
        <lifespanMaxTicks>60</lifespanMaxTicks>
        <burstCount>1~2</burstCount>
        <chancePerTick>.05</chancePerTick>
        <spawnLocType>OnSource</spawnLocType>
        <positionRadius>.15</positionRadius>
        <scale>.5</scale>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <absoluteAngle>True</absoluteAngle>
        <positionOffset>(0, 0, -1)</positionOffset>
        <angle>0</angle>
        <fleckUsesAngleForVelocity>false</fleckUsesAngleForVelocity>
        <speed>0</speed>
      </li>

      <!-- MID DRIPS, STARTING AT MID_SECTION -->
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>RevenantDeathDrip2</fleckDef>
        <initialDelayTicks>60</initialDelayTicks>
        <lifespanMaxTicks>120</lifespanMaxTicks>
        <burstCount>3~6</burstCount>
        <chancePerTick>.1</chancePerTick>
        <spawnLocType>OnSource</spawnLocType>
        <positionRadius>.22</positionRadius>
        <scale>.5</scale>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <absoluteAngle>True</absoluteAngle>
        <positionOffset>(0, 0, -1)</positionOffset>
        <angle>0</angle>
        <fleckUsesAngleForVelocity>false</fleckUsesAngleForVelocity>
        <speed>0</speed>
      </li>

      <!-- MID_HIGH DRIPS, STARTING AT HIGH_BODY -->
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>RevenantDeathDrip3</fleckDef>
        <initialDelayTicks>120</initialDelayTicks>
        <lifespanMaxTicks>200</lifespanMaxTicks>
        <burstCount>2~4</burstCount>
        <chancePerTick>.1</chancePerTick>
        <spawnLocType>OnSource</spawnLocType>
        <positionRadius>.25</positionRadius>
        <scale>.5</scale>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <absoluteAngle>True</absoluteAngle>
        <positionOffset>(0, 0, -1)</positionOffset>
        <angle>0</angle>
        <fleckUsesAngleForVelocity>false</fleckUsesAngleForVelocity>
        <speed>0</speed>
      </li>

      
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <fleckDef>RevenantDeathPuddle</fleckDef>
        <burstCount>1</burstCount>
        <initialDelayTicks>45</initialDelayTicks>
        <spawnLocType>OnSource</spawnLocType>
        <positionRadius>0</positionRadius>
        <scale>.5</scale>
        <rotation>0~360</rotation>
        <rotationRate>0</rotationRate>
        <absoluteAngle>True</absoluteAngle>
        <positionOffset>(0, 0, -.75)</positionOffset>
        <angle>0</angle>
        <fleckUsesAngleForVelocity>false</fleckUsesAngleForVelocity>
        <speed>0</speed>
      </li>
      
    </children>
  </EffecterDef>
  
  <EffecterDef>
    <defName>TestCameraFX</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_CameraShakeSingle</subEffecterClass>
        <cameraShake>1</cameraShake>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>VoidStructureIncoming</defName>
    <maintainTicks>300</maintainTicks>
    <children>
      <li>
        <subEffecterClass>SubEffecter_Sustainer</subEffecterClass>
        <soundDef>VoidStructure_Emerging</soundDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>VoidStructureIncomingSlow</fleckDef>
        <chancePerTick>1</chancePerTick>
        <lifespanMaxTicks>150</lifespanMaxTicks>
        <chancePeriodTicks>40</chancePeriodTicks>
        <burstCount>1~1</burstCount>
        <scale>15</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <initialDelayTicks>150</initialDelayTicks>
        <fleckDef>VoidStructureIncomingFast</fleckDef>
        <chancePerTick>.2</chancePerTick>
        <chancePeriodTicks>10</chancePeriodTicks>
        <burstCount>1~1</burstCount>
        <scale>10</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>PsycastSkipFlashEntry</fleckDef>
        <chancePerTick>1</chancePerTick>
        <chancePeriodTicks>20</chancePeriodTicks>
        <burstCount>1~1</burstCount>
        <scale>1</scale>
        <rotation>0~0</rotation>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <chancePerTick>.1</chancePerTick>
        <chancePeriodTicks>5</chancePeriodTicks>
        <fleckDef>PlainFlash</fleckDef>
        <burstCount>1~1</burstCount>
        <scale>11</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>3.9</positionRadius>
        <chancePeriodTicks>10</chancePeriodTicks>
        <chancePerTick>.1</chancePerTick>
        <fleckDef>ElectricalSpark</fleckDef>
        <soundDef>VoidStructure_EmergeSpark</soundDef>
        <burstCount>7~9</burstCount>
        <scale>1~5</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
  <defName>UndercaveMapAmbience</defName>
    <randomWeight>1</randomWeight>
    <children>
      <li>
        <subEffecterClass>SubEffecter_Random</subEffecterClass>
        <children>
            
          <li>
            <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
            <fleckDef>Lightshaft</fleckDef>
            <randomWeight>.3</randomWeight>
            <burstCount>1</burstCount>
            <spawnLocType>OnSource</spawnLocType>
            <positionRadius>6</positionRadius>
            <positionOffset>(1, 0, 8)</positionOffset>
            <scale>2</scale>
            <absoluteAngle>True</absoluteAngle>
            <rotationRate>0</rotationRate>
            <rotation>15</rotation>
            <chancePerTick>.25</chancePerTick>
            <chancePeriodTicks>30</chancePeriodTicks>
            <speed>0</speed>
          </li>
        </children>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>UndercaveMapAmbienceWater</defName>
    <randomWeight>.3</randomWeight>
    <maintainTicks>350</maintainTicks>
    <children>
        <li>
          <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
          <fleckDef>CaveWaterDrip</fleckDef>
          <maxMoteCount>1</maxMoteCount>
          <burstCount>1</burstCount>
          <spawnLocType>OnSource</spawnLocType>
          <positionRadius>0</positionRadius>
          <positionOffset>(0, 0, -7)</positionOffset>
          <scale>1</scale>
          <absoluteAngle>True</absoluteAngle>
          <rotationRate>0</rotationRate>
          <rotation>0</rotation>
          <speed>0</speed>
        </li>
      
        <li>
          <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
          <initialDelayTicks>30</initialDelayTicks>
          <fleckDef>GroundWaterSplash</fleckDef>
          <burstCount>2~5</burstCount>
          <spawnLocType>OnSource</spawnLocType>
          <positionRadius>.12</positionRadius>
          <positionOffset>(0, 0, 0)</positionOffset>
          <color>(1, 1, 1, .5)</color>
          <scale>.3~.5</scale>
          <absoluteAngle>True</absoluteAngle>
          <rotationRate>0</rotationRate>
          <rotation>0</rotation>
          <speed>0</speed>
        </li>
    </children>
  </EffecterDef>

  
  <EffecterDef>
    <maintainTicks>9999</maintainTicks>
    <defName>UndercaveMapExitLightshafts</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>Lightshaft</fleckDef>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <positionRadius>6</positionRadius>
        <positionOffset>(1, 0, 8)</positionOffset>
        <scale>2</scale>
        <absoluteAngle>True</absoluteAngle>
        <rotationRate>0</rotationRate>
        <rotation>15</rotation>
        <chancePerTick>.25</chancePerTick>
        <chancePeriodTicks>30</chancePeriodTicks>
        <speed>0</speed>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>BiomutationLanceWarmup</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <chancePerTick>.3</chancePerTick>
        <chancePeriodTicks>6</chancePeriodTicks>
        <fleckDef>HoraxianFieldQuickRingCurved</fleckDef>
        <spawnLocType>OnTarget</spawnLocType>
        <burstCount>2~2</burstCount>
        <positionRadius>1</positionRadius>
        <positionRadiusMin>.5</positionRadiusMin>
        <scale>.45~.5</scale>
        <rotation>50~120</rotation>
        <rotationRate>-5~5</rotationRate>
        <angle>0~0</angle>
        <speed>0</speed>
        <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
        <rotateTowardsTargetCenter>true</rotateTowardsTargetCenter>
        <attachToSpawnThing>true</attachToSpawnThing>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>PulsingDistortionRing</fleckDef>
        <spawnLocType>OnTarget</spawnLocType>
        <burstCount>1</burstCount>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>.3</scale>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <speed>0</speed>
        <attachToSpawnThing>true</attachToSpawnThing>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>BiomutationPulserWarmup</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <chancePerTick>.3</chancePerTick>
        <chancePeriodTicks>6</chancePeriodTicks>
        <fleckDef>HoraxianFieldQuickRingCurved</fleckDef>
        <spawnLocType>OnTarget</spawnLocType>
        <burstCount>2~2</burstCount>
        <positionRadius>1</positionRadius>
        <positionRadiusMin>.5</positionRadiusMin>
        <scale>.25~.35</scale>
        <rotation>50~120</rotation>
        <rotationRate>-5~5</rotationRate>
        <angle>0~0</angle>
        <speed>-.1~.15</speed>
        <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
        <rotateTowardsTargetCenter>true</rotateTowardsTargetCenter>
        <attachToSpawnThing>true</attachToSpawnThing>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>PulsingDistortionRing</fleckDef>
        <spawnLocType>OnTarget</spawnLocType>
        <burstCount>1</burstCount>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>.3</scale>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <speed>0</speed>
        <attachToSpawnThing>true</attachToSpawnThing>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>BiomutationPulserUsed</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_CameraShakeSingle</subEffecterClass>
        <cameraShake>1</cameraShake>
        <distanceAttenuationScale>1</distanceAttenuationScale>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>HoraxianSnakeQuick</fleckDef>
        <spawnLocType>OnTarget</spawnLocType>
        <burstCount>17~20</burstCount>
        <positionRadius>.1</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>.45~.5</scale>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <speed>15~25</speed>
        <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
        <rotateTowardsTargetCenter>true</rotateTowardsTargetCenter>
        <attachToSpawnThing>true</attachToSpawnThing>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>MonolithTwisting</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_GroupedChance</subEffecterClass> <!-- For synchronizing effects from the same random event -->
        <chancePerTick>.013</chancePerTick>
        <chancePeriodTicks>20</chancePeriodTicks>
        <children>
          
          <li>
            <subEffecterClass>SubEffecter_CameraShakeChance</subEffecterClass>
            <chancePerTick>.5</chancePerTick>
            <scale>.2</scale>
            <distanceAttenuationScale>1</distanceAttenuationScale>
            <distanceAttenuationMax>25</distanceAttenuationMax>
          </li>
          <li>
            <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
            <fleckDef>DustPuffThick</fleckDef>
            <initialDelayTicks>0</initialDelayTicks>
            <burstCount>2~4</burstCount>
            <chancePerTick>.2</chancePerTick>
            <spawnLocType>OnSource</spawnLocType>
            <positionRadius>4</positionRadius>
            <positionRadiusMin>2</positionRadiusMin>
            <scale>1.5~3</scale>
            <rotation>0</rotation>
            <rotationRate>-60~60</rotationRate>
            <absoluteAngle>False</absoluteAngle>
            <positionOffset>(0, 0, 0)</positionOffset>
            <angle>0~360</angle>
            <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
            <speed>.6~.75</speed>
            <makeMoteOnSubtrigger>true</makeMoteOnSubtrigger>
          </li>

          <li>
            <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
            <chancePerTick>.5</chancePerTick>
            <chancePeriodTicks>40</chancePeriodTicks>
            <avoidLastPositionRadius>1</avoidLastPositionRadius>
            <fleckDef>GroundCrack</fleckDef>
            <initialDelayTicks>0</initialDelayTicks>
            <burstCount>1</burstCount>
            <color>(1, 1, 1, .75)</color>
            <spawnLocType>OnSource</spawnLocType>
            <positionRadius>4</positionRadius>
            <positionRadiusMin>2</positionRadiusMin>
            <scale>.75~2</scale>
            <rotation>0~360</rotation>
            <rotationRate>0</rotationRate>
            <absoluteAngle>True</absoluteAngle>
            <positionOffset>(-.5, 0, -.5)</positionOffset>
            <angle>0</angle>
            <fleckUsesAngleForVelocity>False</fleckUsesAngleForVelocity>
            <speed>0</speed>
            <makeMoteOnSubtrigger>true</makeMoteOnSubtrigger>
          </li>

          <li>
            <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
            <chancePerTick>.1</chancePerTick>
            <chancePeriodTicks>420</chancePeriodTicks>
            <avoidLastPositionRadius>1</avoidLastPositionRadius>
            <fleckDef>ThrownDebris</fleckDef>
            <initialDelayTicks>0</initialDelayTicks>
            <burstCount>5~10</burstCount>
            <spawnLocType>OnSource</spawnLocType>
            <rotationRate>100~200</rotationRate>
            <positionRadius>4</positionRadius>
            <positionRadiusMin>2</positionRadiusMin>
            <scale>.5~1</scale>
            <rotation>0~360</rotation>
            <absoluteAngle>False</absoluteAngle>
            <positionOffset>(-.5, 0, -.5)</positionOffset>
            <angle>0~360</angle>
            <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
            <speed>1~3</speed>
            <makeMoteOnSubtrigger>true</makeMoteOnSubtrigger>
          </li>

          <li>
            <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
            <burstCount>1</burstCount>
            <chancePerTick>1</chancePerTick>
            <positionOffset>(0, 0, 0)</positionOffset>
            <fleckDef>DarkHighlightRing</fleckDef>
            <positionRadius>0</positionRadius>
            <positionRadiusMin>0</positionRadiusMin>
            <scale>.1</scale>
            <speed>0</speed>
            <rotation>0</rotation>
            <rotationRate>0</rotationRate>
            <angle>0~0</angle>
            <spawnLocType>OnTarget</spawnLocType>
          </li>
          
        </children>
      </li>

      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <burstCount>1</burstCount>
        <chancePerTick>.1</chancePerTick>
        <chancePeriodTicks>15</chancePeriodTicks>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>TwistingMonolithLights</fleckDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <speed>0</speed>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
      
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <burstCount>1</burstCount>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>DarkHighlightRing</fleckDef>
        <positionRadius>1</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>1~1</scale>
        <speed>0~.3</speed>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnTarget</spawnLocType>
      </li>

      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>MonolithTwistingRingSlow</fleckDef>
        <isDarkeningEffect>True</isDarkeningEffect>
        <initialDelayTicks>0</initialDelayTicks>
        <chancePerTick>.5</chancePerTick>
        <chancePeriodTicks>60</chancePeriodTicks>
        <burstCount>1~1</burstCount>
        <scale>.1</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>
  
  <EffecterDef>
    <defName>MonolithTwistingIntense</defName>
    <maintainTicks>300</maintainTicks>
    <children>
      <!-- Just before it actually activates -->
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <burstCount>1</burstCount>
        <chancePerTick>1</chancePerTick>
        <chancePeriodTicks>9</chancePeriodTicks>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>TwistingMonolithLightsIntense</fleckDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <speed>0</speed>
        <rotation>0</rotation>
        <absoluteAngle>True</absoluteAngle>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <initialDelayTicks>0</initialDelayTicks>
        <fleckDef>VoidStructureIncomingSlow</fleckDef>
        <isDarkeningEffect>True</isDarkeningEffect>
        <chancePerTick>.5</chancePerTick>
        <chancePeriodTicks>20</chancePeriodTicks>
        <burstCount>1~1</burstCount>
        <scale>7</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>MonolithAutoActivating</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_GroupedChance</subEffecterClass> <!-- For synchronizing effects from the same random event -->
        <chancePerTick>.013</chancePerTick>
        <chancePeriodTicks>20</chancePeriodTicks>
        <children>

          <li>
            <subEffecterClass>SubEffecter_CameraShakeChance</subEffecterClass>
            <chancePerTick>.5</chancePerTick>
            <scale>.2</scale>
            <distanceAttenuationScale>1</distanceAttenuationScale>
            <distanceAttenuationMax>25</distanceAttenuationMax>
          </li>
          <li>
            <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
            <fleckDef>DustPuffThick</fleckDef>
            <initialDelayTicks>0</initialDelayTicks>
            <burstCount>2~4</burstCount>
            <chancePerTick>.2</chancePerTick>
            <spawnLocType>OnSource</spawnLocType>
            <positionRadius>4</positionRadius>
            <positionRadiusMin>2</positionRadiusMin>
            <scale>1.5~3</scale>
            <rotation>0</rotation>
            <rotationRate>-60~60</rotationRate>
            <absoluteAngle>False</absoluteAngle>
            <positionOffset>(0, 0, 0)</positionOffset>
            <angle>0~360</angle>
            <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
            <speed>.6~.75</speed>
            <makeMoteOnSubtrigger>true</makeMoteOnSubtrigger>
          </li>

          <li>
            <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
            <chancePerTick>.5</chancePerTick>
            <chancePeriodTicks>40</chancePeriodTicks>
            <avoidLastPositionRadius>1</avoidLastPositionRadius>
            <fleckDef>GroundCrack</fleckDef>
            <initialDelayTicks>0</initialDelayTicks>
            <burstCount>1</burstCount>
            <color>(1, 1, 1, .75)</color>
            <spawnLocType>OnSource</spawnLocType>
            <positionRadius>4</positionRadius>
            <positionRadiusMin>2</positionRadiusMin>
            <scale>.75~2</scale>
            <rotation>0~360</rotation>
            <rotationRate>0</rotationRate>
            <absoluteAngle>True</absoluteAngle>
            <positionOffset>(-.5, 0, -.5)</positionOffset>
            <angle>0</angle>
            <fleckUsesAngleForVelocity>False</fleckUsesAngleForVelocity>
            <speed>0</speed>
            <makeMoteOnSubtrigger>true</makeMoteOnSubtrigger>
          </li>

          <li>
            <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
            <chancePerTick>.1</chancePerTick>
            <chancePeriodTicks>420</chancePeriodTicks>
            <avoidLastPositionRadius>1</avoidLastPositionRadius>
            <fleckDef>ThrownDebris</fleckDef>
            <initialDelayTicks>0</initialDelayTicks>
            <burstCount>5~10</burstCount>
            <spawnLocType>OnSource</spawnLocType>
            <rotationRate>100~200</rotationRate>
            <positionRadius>4</positionRadius>
            <positionRadiusMin>2</positionRadiusMin>
            <scale>.5~1</scale>
            <rotation>0~360</rotation>
            <absoluteAngle>False</absoluteAngle>
            <positionOffset>(-.5, 0, -.5)</positionOffset>
            <angle>0~360</angle>
            <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
            <speed>1~3</speed>
            <makeMoteOnSubtrigger>true</makeMoteOnSubtrigger>
          </li>

          <li>
            <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
            <burstCount>1</burstCount>
            <chancePerTick>1</chancePerTick>
            <positionOffset>(0, 0, 0)</positionOffset>
            <fleckDef>DarkHighlightRing</fleckDef>
            <positionRadius>0</positionRadius>
            <positionRadiusMin>0</positionRadiusMin>
            <scale>.1</scale>
            <speed>0</speed>
            <rotation>0</rotation>
            <rotationRate>0</rotationRate>
            <angle>0~0</angle>
            <spawnLocType>OnTarget</spawnLocType>
          </li>

        </children>
      </li>

      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <burstCount>1</burstCount>
        <positionOffset>(0, 0, 0)</positionOffset>
        <fleckDef>DarkHighlightRing</fleckDef>
        <positionRadius>1</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>1~1</scale>
        <speed>0~.3</speed>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnTarget</spawnLocType>
      </li>

      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>VoidStructureIncomingSlow</fleckDef>
        <initialDelayTicks>300</initialDelayTicks>
        <isDarkeningEffect>True</isDarkeningEffect>
        <chancePerTick>1</chancePerTick>
        <lifespanMaxTicks>150</lifespanMaxTicks>
        <chancePeriodTicks>40</chancePeriodTicks>
        <burstCount>1~1</burstCount>
        <scale>15</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>
  
  <EffecterDef>
    <defName>AtmosphericHeaterAmbience</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <burstCount>1</burstCount>
        <positionOffset>(0, 0, 1.1)</positionOffset>
        <moteDef>AtmosphericHeaterGlowingVents</moteDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>3</scale>
        <speed>0</speed>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0</angle>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <burstCount>1</burstCount>
        <chancePerTick>1</chancePerTick>
        <chancePeriodTicks>542</chancePeriodTicks>
        <attachPoint>Exhaust</attachPoint>
        <fleckDef>HeatDistortion</fleckDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>3</scale>
        <speed>0</speed>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0</angle>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>BioferriteHarvesterAmbience</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <burstCount>1</burstCount>
        <chancePerTick>.01</chancePerTick>
        <chancePeriodTicks>10</chancePeriodTicks>
        <fleckDef>BioferriteHarvesterWorking</fleckDef>
        <attachPoint>Exhaust</attachPoint>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>.125</scale>
        <speed>.5~1.5</speed>
        <rotation>0~360</rotation>
        <rotationRate>-20~20</rotationRate>
        <angle>5</angle>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>TerrifyingHallucinations</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <scale>2</scale>
        <chancePerTick>.1</chancePerTick>
        <chancePeriodTicks>60</chancePeriodTicks>
        <positionOffset>(0, 0, .2)</positionOffset>
        <positionRadius>0.8</positionRadius>
        <fleckDef>TerrifyingHallucinationsHigh</fleckDef>
        <speed>0.2~0.3</speed>
        <angle>0</angle>
        <rotation>0</rotation>
        <spawnLocType>OnTarget</spawnLocType>
        <attachToSpawnThing>true</attachToSpawnThing>
        <absoluteAngle>True</absoluteAngle>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <scale>2</scale>
        <chancePerTick>.3</chancePerTick>
        <chancePeriodTicks>40</chancePeriodTicks>
        <positionOffset>(0, 0, .2)</positionOffset>
        <positionRadius>0.8</positionRadius>
        <fleckDef>TerrifyingHallucinationsLow</fleckDef>
        <speed>0.2~0.3</speed>
        <angle>0</angle>
        <rotation>0</rotation>
        <spawnLocType>OnTarget</spawnLocType>
        <attachToSpawnThing>true</attachToSpawnThing>
        <absoluteAngle>True</absoluteAngle>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>ChimeraRage</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <scale>.1</scale>
        <positionRadius>0</positionRadius>
        <fleckDef>ChimeraRage</fleckDef>
        <speed>0</speed>
        <angle>0</angle>
        <rotation>0</rotation>
        <spawnLocType>OnTarget</spawnLocType>
        <attachToSpawnThing>true</attachToSpawnThing>
        <absoluteAngle>True</absoluteAngle>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>ExpandingDistortionRingFast</fleckDef>
        <spawnLocType>OnTarget</spawnLocType>
        <burstCount>1</burstCount>
        <color>(0, 0, 0, .75)</color>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>1</scale>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <speed>0</speed>
        <attachToSpawnThing>true</attachToSpawnThing>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>Pawn_Chimera_SpeedupRoar</soundDef>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>HarbingerTreeConsume</defName>
    <children>
      
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>MeatExplosionSmall</soundDef>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
      
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>MeatExplosionSplatterTerrain</fleckDef>
        <burstCount>5</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>1</positionRadius>
        <speed>0</speed>
        <scale>1</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MeatExplosionMist</moteDef>
        <burstCount>2~3</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>.75</positionRadius>
        <spawnLocType>OnSource</spawnLocType>
      </li>
           
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MeatExplosionFlyingPiece</moteDef>
        <burstCount>5</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>0.5</positionRadius>
        <speed>2~3</speed>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>HarbingerTreeWillConsume</defName>
    <children>

      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredChance</subEffecterClass>
        <fleckDef>MeatExplosionSplatterTerrain</fleckDef>
        <chancePerTick>.5</chancePerTick>
        <burstCount>2~3</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>0</positionRadius>
        <speed>0</speed>
        <scale>.6</scale>
        <color>(1, 1, 1, .75)</color>
        <spawnLocType>OnSource</spawnLocType>
      </li>

    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>RevenantHypnosis</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>RevenantHypnosis</fleckDef>
        <burstCount>1</burstCount>
        <chancePeriodTicks>120</chancePeriodTicks>
        <chancePerTick>1</chancePerTick>
        <rotation>-150~150</rotation>
        <rotationRate>0</rotationRate>
        <positionRadius>.04</positionRadius>
        <positionOffset>(0, 0, -.15)</positionOffset>
        <useTargetABodyAngle>true</useTargetABodyAngle>
        <scale>.25~.4</scale>
        <color>(1, 1, 1, .6)</color>
        <fleckUsesAngleForVelocity>false</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>RageSpeedState</defName>
    <offsetMode>Free</offsetMode> <!-- don't spawn attached, since the flecks already anchor themselves correctly, spawning attached can introduce bad offsets -->
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <scale>.5~.6</scale>
        <ticksBetweenMotes>50</ticksBetweenMotes>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <positionRadius>0</positionRadius>
        <fleckDef>RageSpeedBit</fleckDef>
        <attachToSpawnThing>true</attachToSpawnThing>
        <absoluteAngle>True</absoluteAngle>
        <fleckUsesAngleForVelocity>false</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
        <positionOffset>(0, 0, 1)</positionOffset>
        <speed>0</speed>
        <angle>0</angle>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>MetalHellAmbience</defName>
    <children>
      
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <burstCount>1</burstCount>
        <positionOffset>(0, 0, 0)</positionOffset>
        <moteDef>MetalHellFog</moteDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>.9</scale>
        <color>(1, 1, 1, .7)</color>
        <speed>0</speed>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnTarget</spawnLocType>
      </li>

      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <burstCount>1</burstCount>
        <moteDef>MetalHellFogHigh</moteDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>.9</scale>
        <speed>0</speed>
        <color>(1, 1, 1, .9)</color>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnTarget</spawnLocType>
      </li>

    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>ActivitySuppression</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass> <!-- Using this type instead of InteractSymbol to get better positioning -->
        <spawnLocType>OnSource</spawnLocType>
        <maxMoteCount>1</maxMoteCount>
        <destroyMoteOnCleanup>True</destroyMoteOnCleanup>
        <positionOffset>(0, 0, .5)</positionOffset>
        <moteDef>Mote_Clipboard</moteDef>
        <angle>0</angle>
        <rotation>0</rotation>
        <absoluteAngle>True</absoluteAngle>
        <attachToSpawnThing>true</attachToSpawnThing>
        <useTargetAInitialRotation>true</useTargetAInitialRotation>
        <perRotationOffsets>
          <li>(0, 0, 0)</li>
          <li>(0, 0, 0)</li>
          <li>(0, 1, 0)</li> <!-- make it render above the pawn when the pawn is facing south -->
          <li>(0, 0, 0)</li>
        </perRotationOffsets>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>MonolithL0Glow</defName>
    <children>
      
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <fleckDef>MonolithL0FlareHint</fleckDef>
        <ticksBetweenMotes>350</ticksBetweenMotes>
        <spawnLocType>OnSource</spawnLocType>
        <scale>1.3</scale>
        <angle>0</angle>
        <positionOffset>(.9, 0, -.5)</positionOffset>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <rotationRate>0</rotationRate>
        <rotation>0</rotation>
        <speed>0</speed>
      </li>
      
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>MetalhorrorDeath</defName>
    <maintainTicks>400</maintainTicks>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>MetalhorrorDeath</fleckDef>
        <spawnLocType>OnSource</spawnLocType>
        <scale>1~2</scale>
        <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
        <burstCount>8~10</burstCount>
        <angle>0~360</angle>
        <positionOffset>(0, 0, 0)</positionOffset>
        <positionRadius>.5</positionRadius>
        <positionRadiusMin>.5</positionRadiusMin>
        <rotationRate>-600~600</rotationRate>
        <rotation>0~360</rotation>
        <speed>15~35</speed>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>MetalhorrorDeathFlash</fleckDef>
        <spawnLocType>OnSource</spawnLocType>
        <scale>3</scale>
        <angle>0</angle>
        <positionOffset>(0, 0, 0)</positionOffset>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <rotationRate>0</rotationRate>
        <rotation>0</rotation>
        <speed>0</speed>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>MonolithGleamingVoidNode</defName>
    <children>
      
      <!-- Void Node -->
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <burstCount>1</burstCount>
        <positionOffset>(0, 0, 2)</positionOffset>
        <moteDef>Mote_VoidNode</moteDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>1</scale>
        <speed>0</speed>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnTarget</spawnLocType>
      </li>

      
      <!-- Void Node FX -->
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <burstCount>1</burstCount>
        <positionOffset>(0, 0, 2)</positionOffset>
        <moteDef>VoidNodeCore_DustA</moteDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>.6</scale>
        <speed>0</speed>
        <rotation>60</rotation>
        <rotationRate>200</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnTarget</spawnLocType>
      </li>

      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <burstCount>1</burstCount>
        <positionOffset>(0, 0, 2)</positionOffset>
        <moteDef>VoidNodeCore_DustB</moteDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>.8</scale>
        <speed>0</speed>
        <rotation>0</rotation>
        <rotationRate>100</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
      
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <burstCount>1</burstCount>
        <chancePerTick>.03</chancePerTick>
        <positionOffset>(0, 0, 2)</positionOffset>
        <fleckDef>EnergyCrackle</fleckDef>
        <soundDef>Zap_Quiet</soundDef>
        <positionRadius>1.5</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>.35</scale>
        <speed>.001</speed>
        <rotation>0</rotation>
        <rotateTowardsTargetCenter>true</rotateTowardsTargetCenter>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnTarget</spawnLocType>
        <orbitOrigin>true</orbitOrigin>
        <orbitSnapStrength>1.0</orbitSnapStrength>
      </li>
      
      <!-- Metal Hell Ambience -->
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <burstCount>1</burstCount>
        <positionOffset>(0, 0, 2)</positionOffset>
        <moteDef>MetalHellFog</moteDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>1</scale>
        <color>(1, 1, 1, .7)</color>
        <speed>0</speed>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnTarget</spawnLocType>
      </li>

      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <burstCount>1</burstCount>
        <moteDef>MetalHellFogHigh</moteDef>
        <positionRadius>0</positionRadius>
        <positionRadiusMin>0</positionRadiusMin>
        <scale>1</scale>
        <positionOffset>(0, 0, 2)</positionOffset>
        <speed>0</speed>
        <color>(1, 1, 1, .9)</color>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <angle>0~0</angle>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
      
    </children>
  </EffecterDef>
  
  
  <EffecterDef>
    <defName>ObeliskExplosionWarmup</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>2</positionRadius>
        <fleckDef>EnergyCrackle</fleckDef>
        <chancePerTick>.05</chancePerTick>
        <chancePeriodTicks>15</chancePeriodTicks>
        <burstCount>1~4</burstCount>
        <scale>.15~.25</scale>
        <spawnLocType>OnSource</spawnLocType>
        <soundDef>Pawn_Nociosphere_Arc</soundDef>
      </li>

      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0</positionRadius>
        <moteDef>Mote_ObeliskExplosionWarmupArea</moteDef>
        <burstCount>1</burstCount>
        <scale>1</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>HoldingPlatformChainSparks</defName>
    <maintainTicks>60</maintainTicks>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0</positionRadius>
        <speed>.1~.6</speed>
        <fleckDef>HoldingPlatformChainSparks</fleckDef>
        <burstCount>0~1</burstCount>
        <angle>-60~60</angle>
        <rotation>0~360</rotation>
        <rotationRate>-40~40</rotationRate>
        <scale>.1~.25</scale>
        <spawnLocType>OnSource</spawnLocType>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>RevenantKilledCompleteBurst</defName>
    <maintainTicks>120</maintainTicks>
    <children>

      <!-- POP, Good on-kill feedback and helps cover up any transition weirdness -->
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>HoraxianBurstDistortionSmallDark</fleckDef>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <positionRadius>0</positionRadius>
        <scale>.03</scale>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <absoluteAngle>True</absoluteAngle>
        <positionOffset>(0, 0, 0)</positionOffset>
        <angle>0</angle>
        <fleckUsesAngleForVelocity>false</fleckUsesAngleForVelocity>
        <speed>0</speed>
      </li>

    </children>
  </EffecterDef>
  
</Defs>