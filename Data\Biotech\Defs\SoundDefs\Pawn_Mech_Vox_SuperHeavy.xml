<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <!-- Warqueen -->
  <SoundDef>
    <defName>Pawn_Mech_Warqueen_Wounded</defName>  
    <context>MapOnly</context>  
    <maxVoices>1</maxVoices>  
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Mechanoid/Warqueen/Wounded</clipFolderPath>
          </li>
        </grains>      
        <repeatMode>NeverTwice</repeatMode>      
        <sustainLoop>False</sustainLoop>
        <volumeRange>50</volumeRange>      
        <pitchRange>0.95~1.05</pitchRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Mech_Warqueen_Death</defName>  
    <context>MapOnly</context>  
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Mechanoid/Warqueen/Death</clipFolderPath>
          </li>
        </grains>      
        <sustainLoop>False</sustainLoop>
        <volumeRange>50</volumeRange>      
        <pitchRange>0.95~1.05</pitchRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Mech_Warqueen_Call</defName>  
    <context>MapOnly</context>  
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Mechanoid/Warqueen/Call</clipFolderPath>
          </li>
        </grains>      
        <sustainLoop>False</sustainLoop>
        <volumeRange>50</volumeRange>      
        <pitchRange>0.95~1.05</pitchRange>
      </li>
    </subSounds>
  </SoundDef>

  <!-- Centurion -->
  <SoundDef>
    <defName>Pawn_Mech_Centurion_Wounded</defName>  
    <context>MapOnly</context>  
    <maxVoices>1</maxVoices>  
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Mechanoid/Centurion/Wounded</clipFolderPath>
          </li>
        </grains>      
        <volumeRange>50</volumeRange>      
        <pitchRange>0.95~1.05</pitchRange>
        <sustainLoop>False</sustainLoop>
        <repeatMode>NeverTwice</repeatMode>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Mech_Centurion_Death</defName>  
    <context>MapOnly</context>  
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Mechanoid/Centurion/Death</clipFolderPath>
          </li>
        </grains>      
        <volumeRange>50</volumeRange>      
        <pitchRange>0.95~1.05</pitchRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Mech_Centurion_Call</defName>  
    <context>MapOnly</context>  
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Mechanoid/Centurion/Call</clipFolderPath>
          </li>
        </grains>      
        <volumeRange>50</volumeRange>      
        <pitchRange>0.95~1.05</pitchRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <!-- Diabolus -->
  <SoundDef>
    <defName>Pawn_Mech_Diabolus_Wounded</defName>  
    <context>MapOnly</context>  
    <maxVoices>1</maxVoices>  
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Mechanoid/Diabolus/Wounded</clipFolderPath>
          </li>
        </grains>      
        <volumeRange>50</volumeRange>      
        <pitchRange>0.95~1.05</pitchRange>
        <repeatMode>NeverTwice</repeatMode>      
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Mech_Diabolus_Death</defName>  
    <context>MapOnly</context>  
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Mechanoid/Diabolus/Death</clipFolderPath>
          </li>
        </grains>      
        <volumeRange>50</volumeRange>      
        <pitchRange>0.95~1.05</pitchRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Mech_Diabolus_Call</defName>  
    <context>MapOnly</context>  
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Mechanoid/Diabolus/Call</clipFolderPath>
          </li>
        </grains>      
        <volumeRange>50</volumeRange>      
        <pitchRange>0.95~1.05</pitchRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

</Defs>