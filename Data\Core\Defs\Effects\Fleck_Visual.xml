<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <FleckDef Name="FleckBase" Abstract="True">
    <fleckSystemClass>FleckSystemStatic</fleckSystemClass>
    <graphicData>
      <graphicClass>Graphic_Fleck</graphicClass>
      <shaderType>Mote</shaderType>
    </graphicData>
    <altitudeLayer>MoteLow</altitudeLayer>
  </FleckDef>

  <FleckDef Name="FleckBase_RandomGraphic" Abstract="True">
    <fleckSystemClass>FleckSystemStatic</fleckSystemClass>
    <altitudeLayer>MoteLow</altitudeLayer>
  </FleckDef>

  <FleckDef ParentName="FleckBase">
    <defName>ExplosionFlash</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <solidTime>0.05</solidTime>
    <fadeOutTime>0.1</fadeOutTime>
    <graphicData>
      <texPath>Things/Mote/ExplosionFlash</texPath>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="FleckBase" Name="FleckBase_Thrown" Abstract="True">
    <fleckSystemClass>FleckSystemThrown</fleckSystemClass>
  </FleckDef>

  <FleckDef ParentName="FleckBase_RandomGraphic" Name="FleckBase_RandomGraphic_Thrown" Abstract="True">
    <fleckSystemClass>FleckSystemThrown</fleckSystemClass>
  </FleckDef>
  
  <!--=============== Puffs ==============-->
  
  <FleckDef ParentName="FleckBase_Thrown">
    <defName>AirPuff</defName>
    <graphicData>
      <texPath>Things/Mote/AirPuff</texPath>
      <renderInstanced>true</renderInstanced>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.04</fadeInTime>
    <solidTime>0.03</solidTime>
    <fadeOutTime>1</fadeOutTime>
  </FleckDef>
  
  <FleckDef ParentName="FleckBase_Thrown">
    <defName>DustPuff</defName>
    <graphicData>
      <texPath>Things/Mote/DustPuff</texPath>
      <renderInstanced>true</renderInstanced>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.04</fadeInTime>
    <solidTime>0.03</solidTime>
    <fadeOutTime>1</fadeOutTime>
  </FleckDef>

  <FleckDef ParentName="FleckBase_Thrown">
    <defName>DustPuffThick</defName>
    <graphicData>
      <texPath>Things/Mote/DustPuff</texPath>
      <renderInstanced>true</renderInstanced>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.1</fadeInTime>
    <solidTime>0.6</solidTime>
    <fadeOutTime>1.4</fadeOutTime>
  </FleckDef>
  
  <FleckDef ParentName="FleckBase_Thrown">
    <defName>DustPuffLong</defName>
    <graphicData>
      <texPath>Things/Mote/DustPuff</texPath>
      <renderInstanced>true</renderInstanced>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>1.5</fadeInTime>
    <solidTime>3</solidTime>
    <fadeOutTime>1.5</fadeOutTime>
  </FleckDef>
  
  <FleckDef ParentName="FleckBase_Thrown">
    <defName>MetaPuff</defName>
    <graphicData>
      <texPath>Things/Mote/MetaPuff</texPath>
      <renderInstanced>true</renderInstanced>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <realTime>true</realTime>
    <fadeInTime>0.04</fadeInTime>
    <solidTime>0.03</solidTime>
    <fadeOutTime>1</fadeOutTime>
  </FleckDef>

  <FleckDef ParentName="FleckBase_Thrown">
    <defName>TornadoDustPuff</defName>
    <graphicData>
      <texPath>Things/Mote/MetaPuff</texPath>
      <renderInstanced>true</renderInstanced>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.04</fadeInTime>
    <solidTime>0.03</solidTime>
    <fadeOutTime>1</fadeOutTime>
  </FleckDef>

  <FleckDef ParentName="FleckBase_Thrown">
    <defName>Smoke</defName>
    <graphicData>
      <texPath>Things/Mote/Smoke</texPath>
      <renderInstanced>true</renderInstanced>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.50</fadeInTime>
    <solidTime>6</solidTime>
    <fadeOutTime>3.2</fadeOutTime>
    <growthRate>0.005</growthRate>
  </FleckDef>

  <FleckDef ParentName="FleckBase_Thrown">
    <defName>SmokeGrowing</defName>
    <graphicData>
      <texPath>Things/Mote/Smoke</texPath>
      <renderInstanced>true</renderInstanced>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.50</fadeInTime>
    <solidTime>6</solidTime>
    <fadeOutTime>3.2</fadeOutTime>
    <growthRate>0.04</growthRate>
  </FleckDef>

  <FleckDef ParentName="FleckBase_Thrown">
    <defName>DustSlow</defName>
    <graphicData>
      <texPath>Things/Mote/DustPuff</texPath>
      <renderInstanced>true</renderInstanced>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.40</fadeInTime>
    <solidTime>2</solidTime>
    <fadeOutTime>2</fadeOutTime>
  </FleckDef>

  <!--=================== Visual - Fire ==================-->
  
  <FleckDef ParentName="FleckBase_Thrown">
    <defName>FireGlow</defName>
    <graphicData>
      <texPath>Things/Mote/FireGlow</texPath>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.75</fadeInTime>
    <solidTime>1.08</solidTime>
    <fadeOutTime>0.8</fadeOutTime>
  </FleckDef>

  <FleckDef ParentName="FleckBase_Thrown" Name="MicroSparks">
    <defName>MicroSparks</defName>
    <graphicData>
      <texPath>Things/Mote/MicroSparks</texPath>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.75</fadeInTime>
    <solidTime>0.5</solidTime>
    <fadeOutTime>0.65</fadeOutTime>
  </FleckDef>

  <FleckDef ParentName="MicroSparks">
    <defName>MicroSparksFast</defName>
    <fadeInTime>0.15</fadeInTime>
    <solidTime>0.3</solidTime>
    <fadeOutTime>0.45</fadeOutTime>
  </FleckDef>

  <!--=================== Visual - Heat ==================-->

  <FleckDef ParentName="FleckBase_Thrown">
    <defName>HeatGlow</defName>
    <graphicData>
      <texPath>Things/Mote/FireGlow</texPath>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>2.6</fadeInTime>
    <solidTime>1.5</solidTime>
    <fadeOutTime>3.3</fadeOutTime>
  </FleckDef>

  <FleckDef ParentName="FleckBase_Thrown">
    <defName>HeatGlow_Intense</defName>
    <graphicData>
      <texPath>Things/Mote/HeatGlow</texPath>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.2</fadeInTime>
    <solidTime>0.3</solidTime>
    <fadeOutTime>0.2</fadeOutTime>
  </FleckDef>


  <!--=================== Visual - Lightning ==================-->

  <FleckDef ParentName="FleckBase_Thrown">
    <defName>LightningGlow</defName>
    <graphicData>
      <texPath>Things/Mote/LightningGlow</texPath>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.08</fadeInTime>
    <solidTime>0.08</solidTime>
    <fadeOutTime>0.35</fadeOutTime>
  </FleckDef>

  <!--=============== Flashes ================-->

  <FleckDef ParentName="FleckBase">
    <defName>ShotFlash</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <solidTime>0.05</solidTime>
    <fadeOutTime>0.05</fadeOutTime>
    <graphicData>
      <texPath>Things/Mote/ShotFlash</texPath>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="FleckBase">
    <defName>SparkFlash</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <solidTime>0.05</solidTime>
    <fadeOutTime>0.1</fadeOutTime>
    <graphicData>
      <texPath>Things/Mote/SparkFlash</texPath>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="FleckBase">
    <defName>YellowSparkFlash</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <solidTime>0.05</solidTime>
    <fadeOutTime>0.1</fadeOutTime>
    <graphicData>
      <texPath>Things/Mote/YellowSparkFlash</texPath>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="FleckBase">
    <defName>BloodSplash</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <solidTime>0.07</solidTime>
    <fadeOutTime>0.08</fadeOutTime>
    <graphicData>
      <texPath>Things/Mote/BloodSplash</texPath>
      <shaderType>Mote</shaderType>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="FleckBase_Thrown">
    <defName>BloodSplashLong</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <solidTime>0.5</solidTime>
    <fadeOutTime>0.2</fadeOutTime>
    <graphicData>
      <texPath>Things/Mote/BloodSplash</texPath>
      <shaderType>Mote</shaderType>
      <color>(0.9,0,0,0.8)</color>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="FleckBase">
    <defName>BodyImpact</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <solidTime>0.07</solidTime>
    <fadeOutTime>0.05</fadeOutTime>
    <graphicData>
      <texPath>Things/Mote/BodyImpact</texPath>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="FleckBase">
    <defName>GrowingFlash</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <solidTime>0.2</solidTime>
    <fadeOutTime>0.3</fadeOutTime>
    <growthRate>57</growthRate>
    <graphicData>
      <texPath>Things/Mote/PlainFlash</texPath>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="FleckBase">
    <defName>GrowingFlashSmall</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <solidTime>0.2</solidTime>
    <fadeOutTime>0.3</fadeOutTime>
    <growthRate>20</growthRate>
    <graphicData>
      <texPath>Things/Mote/BrightFlash</texPath>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="FleckBase">
    <defName>PlainFlash</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <solidTime>0.05</solidTime>
    <fadeOutTime>0.05</fadeOutTime>
    <graphicData>
      <texPath>Things/Mote/PlainFlash</texPath>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="FleckBase">
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <defName>ActivatorCountdownFlash</defName>
    <solidTime>0.15</solidTime>
    <fadeOutTime>0.1</fadeOutTime>
    <growthRate>37</growthRate>
    <graphicData>
      <drawSize>0.32</drawSize>
      <color>(135, 215, 40, 200)</color>
      <texPath>Things/Mote/BrightFlash</texPath>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
  </FleckDef>

  <!--=============== Psycasts ==============-->

  <FleckDef Abstract="True" Name="FleckGlowDistortBackground" ParentName="FleckBase">
    <graphicData>
      <shaderType>MoteGlowDistortBackground</shaderType>
      <shaderParameters>
        <_DistortionTex>/Things/Mote/PsycastDistortionMask</_DistortionTex>
      </shaderParameters>
    </graphicData>
  </FleckDef>


  <FleckDef ParentName="FleckGlowDistortBackground" Name="FleckPsycastAreaEffect">
    <defName>PsycastAreaEffect</defName>
    <altitudeLayer>MoteLow</altitudeLayer>
    <fadeInTime>0.02</fadeInTime>
    <fadeOutTime>0.6</fadeOutTime>
    <solidTime>0.12</solidTime>
    <growthRate>1.5</growthRate>
    <graphicData>
      <texPath>Things/Mote/Black</texPath>
      <shaderParameters>
        <_distortionIntensity>0.05</_distortionIntensity>
        <_brightnessMultiplier>1.15</_brightnessMultiplier>
      </shaderParameters>
      <drawSize>2.3</drawSize>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="FleckGlowDistortBackground">
    <defName>PsycastPsychicLine</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.04</fadeInTime>
    <fadeOutTime>0.1</fadeOutTime>
    <solidTime>0.0</solidTime>
    <graphicData>
      <texPath>Things/Mote/PsycastPsychicLine</texPath>
      <shaderParameters>
        <_distortionIntensity>0.15</_distortionIntensity>
        <_DistortionTex>/Things/Mote/PsycastPsychicLineMask</_DistortionTex>
      </shaderParameters>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="FleckGlowDistortBackground">
    <defName>PsycastPsychicEffect</defName>
    <fleckSystemClass>FleckSystemThrown</fleckSystemClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.03</fadeInTime>
    <fadeOutTime>0.05</fadeOutTime>
    <solidTime>0.15</solidTime>
    <growthRate>1.5</growthRate>
    <graphicData>
      <texPath>Things/Mote/Black</texPath>
      <shaderParameters>
        <_distortionIntensity>0.024</_distortionIntensity>
        <_brightnessMultiplier>1.1</_brightnessMultiplier>
      </shaderParameters>
      <drawSize>2.5</drawSize>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="FleckBase_Thrown">
    <defName>PsycastSkipFlashEntry</defName>
    <altitudeLayer>VisEffects</altitudeLayer>
    <fadeInTime>0.15</fadeInTime>
    <fadeOutTime>0.15</fadeOutTime>
    <solidTime>0.08</solidTime>
    <graphicData>
      <graphicClass>Graphic_FleckPulse</graphicClass>
      <shaderType>MotePsychicSkipFlash</shaderType>
      <texPath>Things/Mote/PsycastSkipFlash</texPath>
      <shaderParameters>
        <_Noise>/Things/Mote/SkipFlashNoise</_Noise>
      </shaderParameters>
      <drawSize>2.6</drawSize>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="FleckBase_Thrown" Name="PsycastSkipInner" Abstract="True">
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <solidTime>0.4</solidTime>
    <graphicData>
      <graphicClass>Graphic_FleckPulse</graphicClass>
      <shaderType>MotePsychicSkipInner</shaderType>
      <texPath>Things/Mote/SkipInnerDimension</texPath>
      <shaderParameters>
        <_innerRingSize>0.65</_innerRingSize>
      </shaderParameters>
      <drawSize>3.4</drawSize>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="PsycastSkipInner">
    <defName>PsycastSkipInnerEntry</defName>
    <graphicData>
      <shaderParameters>
        <_inTime>0.0</_inTime>
        <_solidTime>0.2</_solidTime>
        <_outTime>0.1</_outTime>
      </shaderParameters>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="PsycastSkipInner">
    <defName>PsycastSkipInnerExit</defName>
    <graphicData>
      <shaderParameters>
        <_inTime>0.2</_inTime>
        <_solidTime>0.2</_solidTime>
        <_outTime>0.1</_outTime>
        <_AgeOffset>0.07</_AgeOffset>
      </shaderParameters>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="FleckBase_Thrown" Name="PsycastSkipOuterRing" Abstract="True">
    <altitudeLayer>MoteOverheadLow</altitudeLayer>
    <solidTime>0.4</solidTime>
    <graphicData>
      <graphicClass>Graphic_FleckPulse</graphicClass>
      <shaderType>MotePsychicSkipRing</shaderType>
      <texPath>Things/Mote/Black</texPath>
      <shaderParameters>
        <_innerRingSize>0.55</_innerRingSize>
        <_outerRingSize>1.0</_outerRingSize>
        <_distortionIntensity>0.01</_distortionIntensity>
        <_brightnessMultiplier>1.1</_brightnessMultiplier>
        <_solidTime>0.2</_solidTime>
        <_outTime>0.1</_outTime>
        <_AgeOffset>0.07</_AgeOffset>
      </shaderParameters>
      <drawSize>4</drawSize>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="PsycastSkipOuterRing">
    <defName>PsycastSkipOuterRingEntry</defName>
    <graphicData>
      <shaderParameters>
        <_inTime>0.0</_inTime>
      </shaderParameters>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="PsycastSkipOuterRing">
    <defName>PsycastSkipOuterRingExit</defName>
    <graphicData>
      <shaderParameters>
        <_inTime>0.2</_inTime>
      </shaderParameters>
    </graphicData>
  </FleckDef>

  <!--=============== Misc visuals ================-->

  <FleckDef ParentName="FleckBase">
    <defName>ShotHit_Dirt</defName>
    <graphicData>
      <texPath>Things/Mote/ShotHit_Dirt</texPath>
      <shaderType>Transparent</shaderType>
    </graphicData>
    <altitudeLayer>MoteLow</altitudeLayer>
    <solidTime>0.03</solidTime>
    <fadeOutTime>0.1</fadeOutTime>
  </FleckDef>

  <FleckDef ParentName="FleckBase">
    <defName>SandInEyes</defName>
    <graphicData>
      <texPath>Things/Mote/SandInEyes</texPath>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.04</fadeInTime>
    <solidTime>0.5</solidTime>
    <fadeOutTime>2</fadeOutTime>
  </FleckDef>

  <FleckDef ParentName="FleckBase">
    <defName>WoodBit</defName>
    <graphicData>
      <texPath>Things/Mote/WoodBit</texPath>
      <shaderType>Transparent</shaderType>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.04</fadeInTime>
    <solidTime>0.15</solidTime>
    <fadeOutTime>0.1</fadeOutTime>
  </FleckDef>

  <FleckDef ParentName="FleckBase_Thrown">
    <defName>Music</defName>
    <graphicData>
      <texPath>Things/Mote/MusicNote</texPath>
    </graphicData>
    <altitudeLayer>MetaOverlays</altitudeLayer>
    <fadeInTime>0.08</fadeInTime>
    <solidTime>1.4</solidTime>
    <fadeOutTime>1.5</fadeOutTime>
  </FleckDef>
  
  <FleckDef ParentName="FleckBase_Thrown">
    <defName>HarvestWood</defName>
    <graphicData>
      <texPath>Things/Mote/WoodBit</texPath>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.04</fadeInTime>
    <solidTime>0.15</solidTime>
    <fadeOutTime>0.1</fadeOutTime>
    <speedPerTime>-40</speedPerTime>
  </FleckDef>
  
  <FleckDef ParentName="FleckBase_Thrown">
    <defName>DirtBits</defName>
    <graphicData>
      <texPath>Things/Mote/WoodBit</texPath>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.04</fadeInTime>
    <solidTime>0.15</solidTime>
    <fadeOutTime>0.1</fadeOutTime>
    <speedPerTime>-40</speedPerTime>
  </FleckDef>

  <FleckDef ParentName="FleckBase_Thrown">
    <defName>DirtBitsArching</defName>
    <graphicData>
      <texPath>Things/Mote/WoodBit</texPath>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.1</fadeInTime>
    <solidTime>0.3</solidTime>
    <fadeOutTime>0.1</fadeOutTime>
    <archHeight>.5</archHeight>
    <archDuration>.5</archDuration>
    <speedPerTime>-2</speedPerTime>
  </FleckDef>
  
  <FleckDef ParentName="FleckBase_Thrown">
    <defName>SnowBits</defName>
    <graphicData>
      <texPath>Things/Mote/SnowBit</texPath>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.04</fadeInTime>
    <solidTime>0.15</solidTime>
    <fadeOutTime>0.1</fadeOutTime>
    <speedPerTime>-40</speedPerTime>
  </FleckDef>
  
  <FleckDef ParentName="FleckBase_Thrown">
    <defName>ResearchPage</defName>
    <graphicData>
      <texPath>Things/Mote/ResearchPage</texPath>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.04</fadeInTime>
    <solidTime>1</solidTime>
    <fadeOutTime>0.1</fadeOutTime>
    <speedPerTime>-40</speedPerTime>
  </FleckDef>
  
  <!--=============== Explosions ================-->

  <FleckDef ParentName="FleckBase">
    <defName>BlastDry</defName>
    <graphicData>
      <texPath>Things/Mote/BlastDry</texPath>
      <shaderType>TransparentPostLight</shaderType>
      <renderInstanced>true</renderInstanced>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <solidTime>0</solidTime>
    <fadeOutTime>0.2</fadeOutTime>
  </FleckDef>
  
  <FleckDef ParentName="FleckBase">
    <defName>BlastEMP</defName>
    <graphicData>
      <texPath>Things/Mote/BlastEMP</texPath>
      <shaderType>TransparentPostLight</shaderType>
      <renderInstanced>true</renderInstanced>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <solidTime>0</solidTime>
    <fadeOutTime>0.2</fadeOutTime>
  </FleckDef>

  <FleckDef ParentName="FleckBase">
    <defName>LineEMP</defName>
    <graphicData>
      <texPath>Things/Mote/LineEMP</texPath>
      <shaderType>TransparentPostLight</shaderType>
      <renderInstanced>true</renderInstanced>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <solidTime>0</solidTime>
    <fadeOutTime>0.4</fadeOutTime>
  </FleckDef>

  <FleckDef ParentName="FleckBase">
    <defName>ArcLargeEMP_A</defName>
    <graphicData>
      <texPath>Things/Mote/ArcLargeEMP_A</texPath>
      <shaderType>TransparentPostLight</shaderType>
      <renderInstanced>true</renderInstanced>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <solidTime>0</solidTime>
    <fadeOutTime>0.2</fadeOutTime>
  </FleckDef>

  <FleckDef ParentName="FleckBase">
    <defName>ArcLargeEMP_B</defName>
    <graphicData>
      <texPath>Things/Mote/ArcLargeEMP_B</texPath>
      <shaderType>TransparentPostLight</shaderType>
      <renderInstanced>true</renderInstanced>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <solidTime>0</solidTime>
    <fadeOutTime>0.2</fadeOutTime>
  </FleckDef>

  <FleckDef ParentName="FleckBase">
    <defName>BlastFlame</defName>
    <graphicData>
      <texPath>Things/Mote/BlastFlame</texPath>
      <shaderType>TransparentPostLight</shaderType>
      <renderInstanced>true</renderInstanced>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <solidTime>0</solidTime>
    <fadeOutTime>0.2</fadeOutTime>
  </FleckDef>
  
  <FleckDef ParentName="FleckBase">
    <defName>ElectricalSpark</defName>
    <graphicData>
      <texPath>Things/Mote/ElectricalSpark</texPath>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.04</fadeInTime>
    <solidTime>0.08</solidTime>
    <fadeOutTime>0.13</fadeOutTime>
  </FleckDef>
  
  <FleckDef ParentName="FleckBase">
    <defName>BlastExtinguisher</defName>
    <graphicData>
      <texPath>Things/Mote/BlastExtinguisher</texPath>
      <shaderType>TransparentPostLight</shaderType>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <solidTime>0</solidTime>
    <fadeOutTime>0.2</fadeOutTime>
  </FleckDef>

  <FleckDef ParentName="FleckBase_Thrown">
    <defName>AcidSpray</defName>
    <graphicData>
      <texPath>Things/Mote/AcidSpray</texPath>
      <renderInstanced>true</renderInstanced>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.04</fadeInTime>
    <solidTime>0.2</solidTime>
    <fadeOutTime>1</fadeOutTime>
    <growthRate>0.9</growthRate>
  </FleckDef>

  <FleckDef ParentName="FleckBase_RandomGraphic_Thrown">
    <defName>FoamSpray</defName>
    <randomGraphics>
      <li>
        <graphicClass>Graphic_Fleck</graphicClass>
        <texPath>Things/Mote/FoamSpray/Foam_A</texPath>
        <shaderType>TransparentPostLight</shaderType>
        <renderInstanced>true</renderInstanced>
      </li>
      <li>
        <graphicClass>Graphic_Fleck</graphicClass>
        <texPath>Things/Mote/FoamSpray/Foam_B</texPath>
        <shaderType>TransparentPostLight</shaderType>
        <renderInstanced>true</renderInstanced>
      </li>
      <li>
        <graphicClass>Graphic_Fleck</graphicClass>
        <texPath>Things/Mote/FoamSpray/Foam_C</texPath>
        <shaderType>TransparentPostLight</shaderType>
        <renderInstanced>true</renderInstanced>
      </li>
    </randomGraphics>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0</fadeInTime>
    <solidTime>0.6</solidTime>
    <fadeOutTime>0.35</fadeOutTime>
    <growthRate>0.6</growthRate>
  </FleckDef>

  <FleckDef ParentName="FleckBase">
    <defName>FlashRed</defName>
    <graphicData>
      <texPath>UI/Misc/Flash</texPath>
      <shaderType>MoteGlow</shaderType>
      <color>(0.9, 0.2, 0.2)</color>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.01</fadeInTime>
    <solidTime>0.07</solidTime>
    <fadeOutTime>0.01</fadeOutTime>
  </FleckDef>

  <FleckDef ParentName="FleckBase_Thrown">
    <defName>JumpWarmupSmoke</defName>
    <graphicData>
      <texPath>Things/Mote/Smoke</texPath>
      <drawSize>(2,2)</drawSize>
    </graphicData>
    <altitudeLayer>Projectile</altitudeLayer>
    <fadeInTime>0.02</fadeInTime>
    <solidTime>0.8</solidTime>
    <fadeOutTime>0.2</fadeOutTime>
    <growthRate>3</growthRate>
  </FleckDef>

  <FleckDef ParentName="FleckBase_Thrown">
    <defName>JumpSmoke</defName>
    <graphicData>
      <texPath>Things/Mote/Smoke</texPath>
    </graphicData>
    <altitudeLayer>Projectile</altitudeLayer>
    <solidTime>0.2</solidTime>
    <fadeOutTime>0.2</fadeOutTime>
    <growthRate>1.2</growthRate>
  </FleckDef>

  <FleckDef ParentName="FleckBase_Thrown">
    <defName>Fleck_HeatDiffusion</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.2</fadeInTime>
    <fadeOutTime>0.3</fadeOutTime>
    <solidTime>0.25</solidTime>
    <growthRate>1.25</growthRate>
    <graphicData>
      <texPath>Things/Mote/HeatDiffusion</texPath>
      <graphicClass>Graphic_FleckPulse</graphicClass>
      <color>(0.85, 0.3, 0.14, 1.0)</color>
      <shaderType>MoteHeatDiffusion</shaderType>
      <shaderParameters>
        <_MultiplyTex>/Things/Mote/RadiationDistortion_A</_MultiplyTex>
        <_texBScale>0.2</_texBScale>
        <_texBScrollSpeed>-0.5</_texBScrollSpeed>
        <_Intensity>13</_Intensity>
        <_Clip>0</_Clip>
      </shaderParameters>
      <drawSize>(9, 9)</drawSize>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="FleckGlowDistortBackground">
    <defName>Fleck_HeatWaveDistortion</defName>
    <fleckSystemClass>FleckSystemThrown</fleckSystemClass>
    <altitudeLayer>MoteOverheadLow</altitudeLayer>
    <graphicData>
      <texPath>Things/Mote/Black</texPath>
      <shaderParameters>
        <_distortionIntensity>0.03</_distortionIntensity>
        <_brightnessMultiplier>1.0</_brightnessMultiplier>
        <_DistortionTex>/Things/Mote/MoteHellfireCannon_HeatWaveDistortion</_DistortionTex>
      </shaderParameters>
      <drawSize>(3, 3)</drawSize>
    </graphicData>
    <fadeInTime>0.075</fadeInTime>
    <solidTime>0.3</solidTime>
    <fadeOutTime>0.35</fadeOutTime>
    <growthRate>11</growthRate>
  </FleckDef>

  <FleckDef ParentName="FleckBase_Thrown">
    <defName>Fleck_RadialSparks</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.1</fadeInTime>
    <fadeOutTime>0.1</fadeOutTime>
    <solidTime>0.6</solidTime>
    <growthRate>0.33</growthRate>
    <graphicData>
      <texPath>Things/Mote/Sparks_Radial_A</texPath>
      <graphicClass>Graphic_FleckPulse</graphicClass>
      <shaderType>MoteCircularSparks</shaderType>
      <shaderParameters>
        <_ExtraTex>/Things/Mote/Sparks_Radial_B</_ExtraTex>
        <_texScale>0.2</_texScale>
        <_texAScrollSpeed>-0.75</_texAScrollSpeed>
        <_texBScrollSpeed>1.25</_texBScrollSpeed>
        <_Intensity>1</_Intensity>
      </shaderParameters>
      <drawSize>(8, 8)</drawSize>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="FleckBase_RandomGraphic">
    <defName>Fleck_Vaporize</defName>
    <randomGraphics>
      <li>
        <graphicClass>Graphic_Fleck</graphicClass>
        <texPath>Things/Mote/Vaporize_A</texPath>
        <shaderType>MoteGlow</shaderType>
        <renderInstanced>true</renderInstanced>
        <drawSize>(1.52, 1.52)</drawSize>
      </li>
      <li>
        <graphicClass>Graphic_Fleck</graphicClass>
        <texPath>Things/Mote/Vaporize_B</texPath>
        <shaderType>MoteGlow</shaderType>
        <renderInstanced>true</renderInstanced>
        <drawSize>(1.52, 1.52)</drawSize>
      </li>
      <li>
        <graphicClass>Graphic_Fleck</graphicClass>
        <texPath>Things/Mote/Vaporize_C</texPath>
        <shaderType>MoteGlow</shaderType>
        <renderInstanced>true</renderInstanced>
        <drawSize>(1.52, 1.52)</drawSize>
      </li>
    </randomGraphics>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.25</fadeInTime>
    <solidTime>0.3</solidTime>
    <fadeOutTime>0.7</fadeOutTime>
  </FleckDef>

  <FleckDef ParentName="FleckBase_Thrown">
    <defName>Fleck_VaporizeGlow</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fleckSystemClass>FleckSystemThrown</fleckSystemClass>
    <fadeInTime>0.1</fadeInTime>
    <fadeOutTime>0.55</fadeOutTime>
    <solidTime>0.4</solidTime>
    <growthRate>2</growthRate>
    <graphicData>
      <texPath>Things/Mote/VaporizeExplosionGlow</texPath>
      <shaderType>MoteGlow</shaderType>
      <drawSize>(4.5, 4.5)</drawSize>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="FleckBase_Thrown">
    <defName>Fleck_VaporizeRays_A</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fleckSystemClass>FleckSystemThrown</fleckSystemClass>
    <fadeInTime>0.2</fadeInTime>
    <fadeOutTime>0.6</fadeOutTime>
    <solidTime>0.4</solidTime>
    <graphicData>
      <texPath>Things/Mote/VaporizeExplosionRays_A</texPath>
      <shaderType>MoteGlow</shaderType>
      <drawSize>(14, 14)</drawSize>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="FleckBase_Thrown">
    <defName>Fleck_VaporizeRays_B</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fleckSystemClass>FleckSystemThrown</fleckSystemClass>
    <fadeInTime>0.2</fadeInTime>
    <fadeOutTime>0.6</fadeOutTime>
    <solidTime>0.4</solidTime>
    <graphicData>
      <texPath>Things/Mote/VaporizeExplosionRays_A</texPath>
      <shaderType>MoteGlow</shaderType>
      <drawSize>(14, 14)</drawSize>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="FleckBase_Thrown">
    <defName>Fleck_VaporizeCenterFlash</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fleckSystemClass>FleckSystemThrown</fleckSystemClass>
    <fadeInTime>0.05</fadeInTime>
    <fadeOutTime>0.1</fadeOutTime>
    <solidTime>0.1</solidTime>
    <graphicData>
      <texPath>Things/Mote/VaporizeExplosion_Center</texPath>
      <shaderType>MoteGlow</shaderType>
      <drawSize>(7, 7)</drawSize>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="FleckBase">
    <defName>FlashPowerCell</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <solidTime>0.09</solidTime>
    <fadeOutTime>0.35</fadeOutTime>
    <graphicData>
      <texPath>Things/Mote/Flash</texPath>
      <shaderType>MoteGlow</shaderType>
      <color>(255, 150, 140, 70)</color>
      <drawSize>(1.33, 1.33)</drawSize>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="FleckBase">
    <defName>FlashPowerCellIntense</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <solidTime>0.09</solidTime>
    <fadeOutTime>0.25</fadeOutTime>
    <graphicData>
      <texPath>Things/Mote/Flash</texPath>
      <shaderType>MoteGlow</shaderType>
      <color>(255, 150, 140, 140)</color>
      <drawSize>(1.33, 1.33)</drawSize>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="FleckBase_Thrown">
    <defName>Fleck_ToxGasSmall</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.3</fadeInTime>
    <solidTime>0</solidTime>
    <fadeOutTime>0.3</fadeOutTime>
    <growthRate>0.8</growthRate>
    <acceleration>(0, 0, 2)</acceleration>
    <graphicData>
      <texPath>Things/Gas/GasCloudThickA</texPath>
      <shaderType>TransparentPostLight</shaderType>
      <color>(180, 214, 24, 64)</color>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="FleckBase_Thrown">
    <defName>Fleck_BeamSpark</defName>
    <fadeInTime>0.03</fadeInTime>
    <solidTime>0.1</solidTime>
    <fadeOutTime>0.5</fadeOutTime>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <graphicData>
      <texPath>Things/Mote/SparkThrownBlue</texPath>
      <shaderType>MoteGlow</shaderType>
      <drawSize>0.08</drawSize>
      <color>(0.7, 0.9, 1.0, 1.0)</color>
    </graphicData>
    <acceleration>(0, 0, 0.5)</acceleration>
    <speedPerTime>0.2~0.5</speedPerTime>
  </FleckDef>
  
  <FleckDef ParentName="FleckBase_Thrown">
    <defName>PsycastSkipFlashExit</defName>
    <altitudeLayer>VisEffects</altitudeLayer>
    <fadeInTime>0.03</fadeInTime>
    <fadeOutTime>1.3</fadeOutTime>
    <solidTime>0.08</solidTime>
    <graphicData>
      <graphicClass>Graphic_FleckPulse</graphicClass>
      <shaderType>MotePsychicSkipFlash</shaderType>
      <texPath>Things/Mote/PsycastSkipFlash</texPath>
      <shaderParameters>
        <_Noise>/Things/Mote/SkipFlashNoise</_Noise>
      </shaderParameters>
      <drawSize>2.6</drawSize>
    </graphicData>
  </FleckDef>

  <FleckDef ParentName="FleckBase_RandomGraphic">
    <defName>GroundCrack</defName>
    <randomGraphics>
      <li>
        <texPath>Things/Mote/GroundCracks/GroundCrackA</texPath>
        <shaderType>Mote</shaderType>
        <renderInstanced>false</renderInstanced>
        <graphicClass>Graphic_Fleck</graphicClass>
      </li> 
      <li>
        <texPath>Things/Mote/GroundCracks/GroundCrackB</texPath>
        <shaderType>Mote</shaderType>
        <renderInstanced>false</renderInstanced>
        <graphicClass>Graphic_Fleck</graphicClass>
      </li>
      <li>
        <texPath>Things/Mote/GroundCracks/GroundCrackC</texPath>
        <shaderType>Mote</shaderType>
        <renderInstanced>false</renderInstanced>
        <graphicClass>Graphic_Fleck</graphicClass>
      </li>
      <li>
        <texPath>Things/Mote/GroundCracks/GroundCrackD</texPath>
        <shaderType>Mote</shaderType>
        <renderInstanced>false</renderInstanced>
        <graphicClass>Graphic_Fleck</graphicClass>
      </li>
    </randomGraphics>
    <growthRate>0</growthRate>
    <altitudeLayer>Filth</altitudeLayer>
    <fadeInTime>.2</fadeInTime>
    <solidTime>5</solidTime>
    <fadeOutTime>3</fadeOutTime>
  </FleckDef>

  <FleckDef ParentName="FleckBase_Thrown">
    <defName>ThrownDebris</defName>
    <graphicData>
      <texPath>Things/Mote/ThrownDebris</texPath>
      <shaderType>Mote</shaderType>
      <renderInstanced>false</renderInstanced>
      <graphicClass>Graphic_Fleck</graphicClass>
    </graphicData>
    <altitudeLayer>Terrain</altitudeLayer>
    <fadeInTime>.2</fadeInTime>
    <solidTime>1</solidTime>
    <fadeOutTime>0</fadeOutTime>
    <growthRate>-.5</growthRate>
    <archHeight>.5~2</archHeight>
    <archDuration>1.2</archDuration>
    <archCurve>
      <points>
        <li>(0,0)</li>
        <li>(0.04,0.1279548)</li>
        <li>(0.08,0.2692297)</li>
        <li>(0.12,0.4009928)</li>
        <li>(0.16,0.5004116)</li>
        <li>(0.2,0.5456898)</li>
        <li>(0.24,0.5447004)</li>
        <li>(0.28,0.5114149)</li>
        <li>(0.32,0.4533074)</li>
        <li>(0.36,0.3778524)</li>
        <li>(0.3999999,0.2925241)</li>
        <li>(0.4399999,0.2047971)</li>
        <li>(0.4799999,0.1221454)</li>
        <li>(0.5199999,0.05204353)</li>
        <li>(0.5599999,0.001965821)</li>
        <li>(0.6,0.0494144)</li>
        <li>(0.64,0.07938939)</li>
        <li>(0.68,0.08880626)</li>
        <li>(0.72,0.07998239)</li>
        <li>(0.7600001,0.05523518)</li>
        <li>(0.8000001,0.01688196)</li>
        <li>(0.8400001,0.009637574)</li>
        <li>(0.8800001,0.02145397)</li>
        <li>(0.9200001,0.02456769)</li>
        <li>(0.9600002,0.0142423)</li>
        <li>(1,0)</li>
      </points>
    </archCurve>
  </FleckDef>

  <FleckDef ParentName="FleckBase">
    <defName>ShockwaveFast</defName>
    <altitudeLayer>MoteLow</altitudeLayer>
    <fadeInTime>0.0</fadeInTime>
    <solidTime>0.0</solidTime>
    <fadeOutTime>0.4</fadeOutTime>
    <growthRate>100.0</growthRate>
    <graphicData>
      <graphicClass>Graphic_FleckPulse</graphicClass>
      <texPath>Things/Mote/Black</texPath>
      <shaderType>MoteLargeDistortionWave</shaderType>
      <shaderParameters>
        <_DistortionTex>/Things/Mote/PsychicDistortionCurrents</_DistortionTex>
        <_NoiseTex>/Things/Mote/PsycastNoise</_NoiseTex>
        <_distortionIntensity>0.2</_distortionIntensity>
        <_brightnessMultiplier>5.15</_brightnessMultiplier>
      </shaderParameters>
      <drawSize>1.0</drawSize>
    </graphicData>
  </FleckDef>


  <FleckDef ParentName="FleckBase_RandomGraphic_Thrown">
    <defName>GroundWaterSplash</defName>
    <randomGraphics>
      <li>
        <graphicClass>Graphic_FleckPulse</graphicClass>
        <shaderType>Mote</shaderType>
        <texPath>Things/Mote/GroundSplash/GroundSplashA</texPath>
        <drawSize>(1,1)</drawSize>
        <color>(0.53, 0.69, 0.74, .5)</color>
      </li>
      <li>
        <graphicClass>Graphic_FleckPulse</graphicClass>
        <shaderType>Mote</shaderType>
        <texPath>Things/Mote/GroundSplash/GroundSplashB</texPath>
        <drawSize>(1,1)</drawSize>
        <color>(0.53, 0.69, 0.74, .5)</color>
      </li>
    </randomGraphics>
    <unattachedDrawOffset>(0, 0, 0)</unattachedDrawOffset>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0</fadeInTime>
    <solidTime>.3</solidTime>
    <fadeOutTime>.35</fadeOutTime>
    <archHeight>0</archHeight>
    <growthRate>1</growthRate>
    <scalingAnchor>(.5, .2, 0)</scalingAnchor>
    <scalers>
      <li>
        <scaleAmt>1</scaleAmt>
        <scaleTime>.65</scaleTime>
        <axisMask>(1, 0, 0)</axisMask>
        <curve>
          <points>
            <li>(0,0.5)</li>
            <li>(0.04,0.4548468)</li>
            <li>(0.08,0.402833)</li>
            <li>(0.12,0.3486849)</li>
            <li>(0.16,0.2971286)</li>
            <li>(0.2,0.2528901)</li>
            <li>(0.24,0.2206958)</li>
            <li>(0.28,0.2052717)</li>
            <li>(0.32,0.2082831)</li>
            <li>(0.36,0.2212332)</li>
            <li>(0.3999999,0.2424916)</li>
            <li>(0.4399999,0.2712372)</li>
            <li>(0.4799999,0.3066487)</li>
            <li>(0.5199999,0.3479051)</li>
            <li>(0.5599999,0.3941854)</li>
            <li>(0.6,0.4446683)</li>
            <li>(0.64,0.4985328)</li>
            <li>(0.68,0.5549577)</li>
            <li>(0.72,0.613122)</li>
            <li>(0.7600001,0.6722045)</li>
            <li>(0.8000001,0.7313842)</li>
            <li>(0.8400001,0.7898397)</li>
            <li>(0.8800001,0.8467504)</li>
            <li>(0.9200001,0.9012946)</li>
            <li>(0.9600002,0.9526517)</li>
            <li>(1,1)</li>
          </points>
        </curve>
      </li>
      <li>
        <scaleAmt>1</scaleAmt>
        <scaleTime>.65</scaleTime>
        <axisMask>(0, 0, 1)</axisMask>
        <curve>
          <points>
            <li>(0,0)</li>
            <li>(0.04,0.08772291)</li>
            <li>(0.08,0.1736811)</li>
            <li>(0.12,0.2568415)</li>
            <li>(0.16,0.3361711)</li>
            <li>(0.2,0.4106368)</li>
            <li>(0.24,0.4792057)</li>
            <li>(0.28,0.5408444)</li>
            <li>(0.32,0.5945203)</li>
            <li>(0.36,0.6392)</li>
            <li>(0.3999999,0.6738507)</li>
            <li>(0.4399999,0.6974391)</li>
            <li>(0.4799999,0.7089323)</li>
            <li>(0.5199999,0.7097846)</li>
            <li>(0.5599999,0.7065364)</li>
            <li>(0.6,0.6982319)</li>
            <li>(0.64,0.683339)</li>
            <li>(0.68,0.6603254)</li>
            <li>(0.72,0.6276588)</li>
            <li>(0.7600001,0.5838069)</li>
            <li>(0.8000001,0.5272376)</li>
            <li>(0.8400001,0.4564185)</li>
            <li>(0.8800001,0.3698174)</li>
            <li>(0.9200001,0.265902)</li>
            <li>(0.9600002,0.1431402)</li>
            <li>(1,0)</li>
          </points>
        </curve>
      </li>
    </scalers>
  </FleckDef>


  <FleckDef ParentName="FleckBase_Thrown">
    <defName>WaterChurnNoise</defName>
    <graphicData>
      <texPath>Things/Mote/WaterChurnNoise</texPath>
      <renderInstanced>true</renderInstanced>
      <shaderParameters>
        <_Color2>(0.84, 1.00, 1.00, .6)</_Color2>
      </shaderParameters>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.04</fadeInTime>
    <solidTime>1</solidTime>
    <fadeOutTime>2</fadeOutTime>
  </FleckDef>


  
  
  </Defs>