﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <HediffDef>
    <defName>Heatstroke</defName>
    <label>heatstroke</label>
    <description>A generalized disruption to body functioning caused by excessive exposure to heat and sun. The victim becomes dizzy, weak, and confused. Recovery is quick once in a cool area, but if heat exposure continues, heat stroke gets worse until death.</description>
    <defaultLabelColor>(0.8, 0.8, 0.35)</defaultLabelColor>
    <lethalSeverity>1</lethalSeverity>
    <canApplyDodChanceForCapacityChanges>true</canApplyDodChanceForCapacityChanges>
    <taleOnVisible>HeatstrokeRevealed</taleOnVisible>
    <stages>
      <li>
        <label>initial</label>
        <becomeVisible>false</becomeVisible>
      </li>
      <li>
        <label>initial</label>
        <minSeverity>0.04</minSeverity>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.05</offset>
          </li>
        </capMods>
      </li>
      <li>
        <label>minor</label>
        <minSeverity>0.2</minSeverity>
        <capMods>
          <li>
            <capacity>Moving</capacity>
            <offset>-0.1</offset>
          </li>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.10</offset>
          </li>
        </capMods>
      </li>
      <li>
        <label>serious</label>
        <minSeverity>0.35</minSeverity>
        <painOffset>0.15</painOffset>
        <capMods>
          <li>
            <capacity>Moving</capacity>
            <offset>-0.3</offset>
          </li>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.20</offset>
          </li>
        </capMods>
      </li>
      <li>
        <label>extreme</label>
        <minSeverity>0.62</minSeverity>
        <lifeThreatening>true</lifeThreatening>
        <painOffset>0.30</painOffset>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <setMax>0.1</setMax>
          </li>
        </capMods>
      </li>
    </stages>
  </HediffDef>

  <HediffDef>
    <defName>Hypothermia</defName>
    <label>hypothermia</label>
    <description>Dangerously low core body temperature. Unless re-warmed, hypothermia gets worse and ends in death. Recovery is quick once the victim is re-warmed. Avoid hypothermia by wearing warm clothes in cold environments.</description>
    <defaultLabelColor>(0.8, 0.8, 1)</defaultLabelColor>
    <lethalSeverity>1</lethalSeverity>
    <canApplyDodChanceForCapacityChanges>true</canApplyDodChanceForCapacityChanges>
    <taleOnVisible>HypothermiaRevealed</taleOnVisible>
    <stages>
      <li>
        <label>shivering</label>
        <becomeVisible>false</becomeVisible>
      </li>
      <li>
        <label>shivering</label>
        <minSeverity>0.04</minSeverity>
        <capMods>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.08</offset>
          </li>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.05</offset>
          </li>
        </capMods>
      </li>
      <li>
        <label>minor</label>
        <minSeverity>0.2</minSeverity>
        <capMods>
          <li>
            <capacity>Moving</capacity>
            <offset>-0.1</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.2</offset>
          </li>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.10</offset>
          </li>
        </capMods>
      </li>
      <li>
        <label>serious</label>
        <minSeverity>0.35</minSeverity>
        <painOffset>0.15</painOffset>
        <capMods>
          <li>
            <capacity>Moving</capacity>
            <offset>-0.3</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.5</offset>
          </li>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.20</offset>
          </li>
        </capMods>
      </li>
      <li>
        <label>extreme</label>
        <minSeverity>0.62</minSeverity>
        <lifeThreatening>true</lifeThreatening>
        <painOffset>0.30</painOffset>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <setMax>0.1</setMax>
          </li>
        </capMods>
      </li>
    </stages>
  </HediffDef>

  <HediffDef>
    <defName>HypothermicSlowdown</defName>
    <label>hypothermic slowdown</label>
    <description>A special biological state used by some creatures to survive extreme cold. Instead of trying to stay warm, the creature's body chemistry adapts to prevent internal freezing despite very low temperature. Bodily functions are slowed and capacities are reduced, but the cold does no permanent damage. Some biologists call it a wakeful form of hibernation.</description>
    <defaultLabelColor>(0.8, 0.8, 1)</defaultLabelColor>
    <stages>
      <li>
        <label>minor</label>
        <becomeVisible>false</becomeVisible>
      </li>
      <li>
        <label>minor</label>
        <minSeverity>0.04</minSeverity>
        <capMods>
          <li>
            <capacity>Moving</capacity>
            <offset>-0.08</offset>
          </li>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.05</offset>
          </li>
        </capMods>
      </li>
      <li>
        <label>moderate</label>
        <minSeverity>0.2</minSeverity>
        <hungerRateFactorOffset>-0.1</hungerRateFactorOffset>
        <capMods>
          <li>
            <capacity>Moving</capacity>
            <offset>-0.2</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.2</offset>
          </li>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.2</offset>
          </li>
        </capMods>
      </li>
      <li>
        <label>serious</label>
        <minSeverity>0.35</minSeverity>
        <hungerRateFactorOffset>-0.4</hungerRateFactorOffset>
        <capMods>
          <li>
            <capacity>Moving</capacity>
            <offset>-0.4</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.5</offset>
          </li>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.4</offset>
          </li>
        </capMods>
      </li>
      <li>
        <label>extreme</label>
        <minSeverity>0.62</minSeverity>
        <hungerRateFactorOffset>-0.95</hungerRateFactorOffset>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <setMax>0.1</setMax>
          </li>
        </capMods>
      </li>
    </stages>
  </HediffDef>

</Defs>