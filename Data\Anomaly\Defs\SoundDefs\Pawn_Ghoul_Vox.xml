﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <SoundDef>
    <defName>Pawn_Ghoul_Call</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Ghoul/Call</clipFolderPath>
          </li>
        </grains>
        <volumeRange>10~18</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <distRange>0~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Ghoul_Attack</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Ghoul/Attack</clipFolderPath>
          </li>
        </grains>
        <volumeRange>10~18</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <distRange>0~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Ghoul_Scratch</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Ghoul/Scratch</clipFolderPath>
          </li>
        </grains>
        <volumeRange>10~18</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <distRange>0~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Ghoul_Pain</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Ghoul/Pain</clipFolderPath>
          </li>
        </grains>
        <volumeRange>10~18</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <distRange>0~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Ghoul_Killed</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Ghoul/Death</clipFolderPath>
          </li>
        </grains>
        <volumeRange>20~25</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <distRange>0~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Ghoul_Frenzy</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Ghoul/Frenzy</clipFolderPath>
          </li>
        </grains>
        <volumeRange>20~25</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <distRange>0~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>
</Defs>