<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <RecipeDef Name="BaseMechanoidResurrectionRecipe" ParentName="BaseMechanoidRecipe" Abstract="True">
    <mechResurrection>true</mechResurrection>
    <jobString>Resurrecting mech.</jobString>
    <fixedIngredientFilter>
      <specialFiltersToAllow>
        <li>AllowCorpsesMechFriendly</li>
      </specialFiltersToAllow>
    </fixedIngredientFilter>
    <forceHiddenSpecialFilters>
      <li>AllowCorpsesMechFriendly</li>
    </forceHiddenSpecialFilters>
  </RecipeDef>

  <RecipeDef ParentName="BaseMechanoidResurrectionRecipe">
    <defName>ResurrectLightMech</defName>
    <label>resurrect light mechanoid</label>
    <description>Resurrect a friendly mechanoid of the light weight class. So long as the mechanoid's body is not completely destroyed, it can be resurrected.</description>
    <ingredients>
      <li>
        <filter>
          <categories>
            <li>Corpses</li>
          </categories>
        </filter>
        <count>1</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>Steel</li>
          </thingDefs>
        </filter>
        <count>25</count>
      </li>
    </ingredients>
    <fixedIngredientFilter>
      <thingDefs>
        <li>Corpse_Mech_Lifter</li>
        <li>Corpse_Mech_Constructoid</li>
        <li>Corpse_Mech_Agrihand</li>
        <li>Corpse_Mech_Militor</li>
        <li>Corpse_Mech_Cleansweeper</li>
        <li>Corpse_Mech_Paramedic</li>
        <li>Corpse_Mech_Fabricor</li>
      </thingDefs>
    </fixedIngredientFilter>
  </RecipeDef>

  <RecipeDef ParentName="BaseMechanoidResurrectionRecipe">
    <defName>ResurrectMediumMech</defName>
    <label>resurrect medium mechanoid</label>
    <description>Resurrect a friendly mechanoid of the medium weight class. So long as the mechanoid's body is not completely destroyed, it can be resurrected.</description>
    <ingredients>
      <li>
        <filter>
          <categories>
            <li>Corpses</li>
          </categories>
        </filter>
        <count>1</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>Steel</li>
          </thingDefs>
        </filter>
        <count>50</count>
      </li>
    </ingredients>
    <fixedIngredientFilter>
      <thingDefs>
        <li>Corpse_Mech_Pikeman</li>
        <li>Corpse_Mech_Scorcher</li>
        <li>Corpse_Mech_Scyther</li>
        <li>Corpse_Mech_Lancer</li>
        <li>Corpse_Mech_Tesseron</li>
        <li>Corpse_Mech_Legionary</li>
      </thingDefs>
    </fixedIngredientFilter>
  </RecipeDef>

  <RecipeDef ParentName="BaseMechanoidResurrectionRecipe">
    <defName>ResurrectHeavyMech</defName>
    <label>resurrect heavy mechanoid</label>
    <description>Resurrect a friendly mechanoid of the heavy weight class. So long as the mechanoid's body is not completely destroyed, it can be resurrected.</description>
    <ingredients>
      <li>
        <filter>
          <categories>
            <li>Corpses</li>
          </categories>
        </filter>
        <count>1</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>Steel</li>
          </thingDefs>
        </filter>
        <count>100</count>
      </li>
    </ingredients>
    <fixedIngredientFilter>
      <thingDefs>
        <li>Corpse_Mech_Tunneler</li>
        <li>Corpse_Mech_CentipedeGunner</li>
        <li>Corpse_Mech_CentipedeBurner</li>
        <li>Corpse_Mech_CentipedeBlaster</li>
      </thingDefs>
    </fixedIngredientFilter>
  </RecipeDef>

  <RecipeDef ParentName="BaseMechanoidResurrectionRecipe">
    <defName>ResurrectUltraheavyMech</defName>
    <label>resurrect ultraheavy mechanoid</label>
    <description>Resurrect a friendly mechanoid of the ultraheavy weight class. So long as the mechanoid's body is not completely destroyed, it can be resurrected.</description>
    <ingredients>
      <li>
        <filter>
          <categories>
            <li>Corpses</li>
          </categories>
        </filter>
        <count>1</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>Steel</li>
          </thingDefs>
        </filter>
        <count>150</count>
      </li>
    </ingredients>
    <fixedIngredientFilter>
      <thingDefs>
        <li>Corpse_Mech_Diabolus</li>
        <li>Corpse_Mech_Centurion</li>
        <li>Corpse_Mech_Warqueen</li>
      </thingDefs>
    </fixedIngredientFilter>
  </RecipeDef>

</Defs>