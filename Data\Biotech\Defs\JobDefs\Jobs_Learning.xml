<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <JobDef>
    <defName>Skydreaming</defName>
    <driverClass>JobDriver_Skydreaming</driverClass>
    <reportString>skydreaming.</reportString>
  </JobDef>

  <JobDef>
    <defName>NatureRunning</defName>
    <driverClass>JobDriver_NatureRunning</driverClass>
    <reportString>nature running.</reportString>
  </JobDef>

  <jobDef>
    <defName>Radiotalking</defName>
    <driverClass>JobDriver_RadioTalking</driverClass>
    <reportString>radiotalking.</reportString>
  </jobDef>

  <jobDef>
    <defName>Floordrawing</defName>
    <driverClass>JobDriver_Floordrawing</driverClass>
    <reportString>floordrawing.</reportString>
  </jobDef>

  <jobDef>
    <defName>Workwatching</defName>
    <driverClass>JobDriver_Workwatching</driverClass>
    <reportString>watching work be performed.</reportString>
  </jobDef>

  <jobDef>
    <defName>Lessontaking</defName>
    <driverClass>JobDriver_Lessontaking</driverClass>
    <reportString>learning a lesson.</reportString>
  </jobDef>

  <jobDef>
    <defName>Lessongiving</defName>
    <driverClass>JobDriver_Lessongiving</driverClass>
    <reportString>teaching a lesson.</reportString>
  </jobDef>

</Defs>