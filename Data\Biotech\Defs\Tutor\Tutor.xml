<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <ConceptDef>
    <defName>PollutedTerrain</defName>
    <label>polluted terrain</label>
    <helpText>Keep your colonists away from polluted terrain. Whenever they are on polluted terrain, they will continuously gain toxic buildup. This eventually becomes lethal.\n\nClean polluted terrain by placing a POLLUTION REMOVAL AREA. Colonists will convert the pollution into toxic wastepacks, which must be kept frozen.\n\nCertain apparel, implants and gene modifications can protect from toxic buildup.\n\nNormal crops cannot survive on polluted terrain.</helpText>
    <priority>51</priority>
    <needsOpportunity>True</needsOpportunity>
    <highlightTags>
      <li>MainTab-Architect-Closed</li>
      <li>DesignationCategoryButton-Zone-Closed</li>
      <li>Designator-AreaPollutionClearExpand</li>
    </highlightTags>
  </ConceptDef>

  <ConceptDef>
    <defName>Babies</defName>
    <label>babies</label>
    <helpText>Babies can't walk and must be taken care of. In the work tab, assign someone to childcare.\n\nYou can use the baby's 'feeding' tab to assign specific people to feed the baby. Alternatively, colonists assigned to childcare will feed babies food they can eat, such as baby food, milk, and insect jelly. You can produce baby food at a stove or campfire.\n\nBabies can sleep in beds or cribs. A high quality crib will make a baby happy. You can also place multiple cribs in a bedroom without turning it into a barracks.\n\nBabies become children at age 3. By default, babies age 4x faster than adults. You can adjust this in storyteller challenge settings.</helpText>
    <priority>45</priority>
    <needsOpportunity>True</needsOpportunity>
  </ConceptDef>

  <ConceptDef>
    <defName>Children</defName>
    <label>children</label>
    <helpText>As they get older, children gain traits and passions, and become able to do more work types.\n\nChildren have a learning need which can be fulfilled several ways. You can see the learning activities that a child desires on their Needs tab. Children will only do learning activities when their schedule is set to Recreation or Anything. A satisfied learning need helps a child gain growth tiers faster, resulting in more passions and choices during growth moments.\n\nChildren can only wear special child-sized apparel.\n\nChildren become adults at age 13. By default, children age 4x faster than adults. You can adjust this in storyteller challenge settings.</helpText>
    <priority>46</priority>
    <needsOpportunity>True</needsOpportunity>
  </ConceptDef>

  <ConceptDef>
    <defName>Deathrest</defName>
    <label>Deathrest</label>
    <helpText>Colonists with the deathrest gene must periodically regenerate themselves in a special coma called deathrest. This takes several days.\n\nSpecial machines placed near the casket can give bonuses to the deathrester. A person can only connect to a limited number of machines - this number is shown by their deathrest capacity.\n\nYou can increase deathrest capacity by injecting deathrest capacity serums. These can be purchased from traders.</helpText>
    <priority>52</priority>
    <needsOpportunity>true</needsOpportunity>
  </ConceptDef>

  <ConceptDef>
    <defName>GenesAndXenotypes</defName>
    <label>Genes and xenotypes</label>
    <helpText>Some people have different genes from baseline humans. View a person's genes by opening their Bio tab and clicking the xenotype name near the top.\n\nA xenotype is a set of genes which defines a "species" of alternate human.\n\nThere are two types of genes: Germline genes are passed on to children, while xenogenes are not and must be implanted using xenogerms.\n\nCreate custom xenotypes by researching xenogenetics and building a gene assembler.</helpText>
    <priority>60</priority>
    <needsOpportunity>true</needsOpportunity>
  </ConceptDef>

  <ConceptDef>
    <defName>Mechanitors</defName>
    <label>Mechanitors</label>
    <helpText>Mechanitors use a mechlink implant to control mechanoids.\n\nMechanoids are assigned to control groups. You can change the behavior mode for a control group by selecting the mechanoid or mechanitor, then clicking the behavior mode icon on the control group. Gear and implants can increase the number of control groups.\n\nMechanitors only have the bandwidth to control a certain number of mechanoids. Special mechanitor gear and band node buildings can increase a mechanitor's total bandwidth.\n\nThe MECHS tab provides an overview of your colony's mechanoids. Here, you can draft mechanoids, change their control group, set allowed areas, and more.\n\nMechanitors can also summon powerful enemy super-mechanoids. These super-mechanoids drop unique chips which can be used to advance your mechanoid technology and build special units.</helpText>
    <priority>50</priority>
    <needsOpportunity>true</needsOpportunity>
  </ConceptDef>

  <ConceptDef>
    <defName>MechsInCaravans</defName>
    <label>Mechs in caravans</label>
    <helpText>While caravanning, mechanoids enter a semi-dormant long-travel state to preserve power. This allows them to travel long distances without losing energy. After arriving at their destination, mechanoids will automatically return to their previous work mode.</helpText>
    <priority>10</priority>
    <needsOpportunity>true</needsOpportunity>
  </ConceptDef>

</Defs>