﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <DutyDef>
    <defName>TravelOrLeave</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_GotoTravelDestination" />
        <!-- Note this exit node can happen at odd times
            like if you block siegers from hitting their siege location before they arrive-->
        <li Class="JobGiver_ExitMapRandom">
          <defaultLocomotion>Walk</defaultLocomotion>
        </li>
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>TravelOrWait</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <!-- Goto travel destination if reachable -->
        <li Class="JobGiver_GotoTravelDestination" />

        <!-- Basic needs -->
        <li Class="ThinkNode_Subtree">
          <treeDef>SatisfyVeryUrgentNeeds</treeDef>
        </li>

        <!-- Wander -->
        <li Class="JobGiver_WanderAnywhere" />
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>Idle</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_Idle" />
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>IdleNoInteraction</defName>
    <socialModeMax>Off</socialModeMax>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_Idle" />
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>Defend</defName>
    <alwaysShowWeapon>true</alwaysShowWeapon>
    <thinkNode Class="ThinkNode_Priority">
      <!-- Todo here - if outside of duty radius, go to within duty radius-->
      <!-- Currently this is handled by a hack in JobGiver_AIFightBase-->
      <subNodes>
        <li Class="JobGiver_TakeCombatEnhancingDrug">
          <onlyIfInDanger>true</onlyIfInDanger>
        </li>
        <li Class="JobGiver_AIDefendPoint">
          <targetAcquireRadius>65</targetAcquireRadius>
          <targetKeepRadius>72</targetKeepRadius>
        </li>
        <li Class="ThinkNode_ForbidOutsideFlagRadius">
          <maxDistToSquadFlag>16</maxDistToSquadFlag>
          <subNodes>
            <li Class="ThinkNode_Subtree">
              <treeDef>SatisfyBasicNeedsAndWork</treeDef>
            </li>
          </subNodes>
        </li>
        <li Class="JobGiver_WanderNearDutyLocation">
          <wanderRadius>8</wanderRadius>
          <locomotionUrgencyOutsideRadius>Sprint</locomotionUrgencyOutsideRadius>
        </li>
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>Follow</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_AIFollowEscortee" />
        <li Class="ThinkNode_ForbidOutsideFlagRadius">
          <maxDistToSquadFlag>16</maxDistToSquadFlag>
          <subNodes>
            <li Class="ThinkNode_Subtree">
              <treeDef>SatisfyBasicNeedsAndWork</treeDef>
            </li>
          </subNodes>
        </li>
        <li Class="JobGiver_WanderNearDutyLocation">
          <wanderRadius>5</wanderRadius>
        </li>
      </subNodes>
    </thinkNode>
  </DutyDef>
  
  <DutyDef>
    <defName>Build</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_FightFiresNearPoint">
          <maxDistFromPoint>25</maxDistFromPoint>
        </li>
        <li Class="ThinkNode_ForbidOutsideFlagRadius">
          <maxDistToSquadFlag>16</maxDistToSquadFlag>
          <subNodes>
            <li Class="JobGiver_Work" />
          </subNodes>
        </li>
        <li Class="JobGiver_AIDefendPoint">
          <targetAcquireRadius>35</targetAcquireRadius>
          <targetKeepRadius>40</targetKeepRadius>
        </li>
        <li Class="ThinkNode_ForbidOutsideFlagRadius">
          <maxDistToSquadFlag>16</maxDistToSquadFlag>
          <subNodes>
            <li Class="ThinkNode_Subtree">
              <treeDef>SatisfyBasicNeedsAndWork</treeDef>
            </li>
          </subNodes>
        </li>
        <li Class="JobGiver_ManTurretsNearPoint">
          <maxDistFromPoint>25</maxDistFromPoint>
        </li>
        <li Class="JobGiver_WanderNearDutyLocation">
          <wanderRadius>8</wanderRadius>
          <locomotionUrgencyOutsideRadius>Sprint</locomotionUrgencyOutsideRadius>
        </li>
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>AssaultColony</defName>
    <alwaysShowWeapon>true</alwaysShowWeapon>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_TakeCombatEnhancingDrug" />
        <li Class="ThinkNode_Subtree">
          <treeDef>Abilities_Aggressive</treeDef>
        </li>
        <li Class="JobGiver_AIFightEnemies">
          <targetAcquireRadius>65</targetAcquireRadius>
          <targetKeepRadius>72</targetKeepRadius>
        </li>
        <li Class="JobGiver_AITrashColonyClose" />
        <li Class="JobGiver_AITrashBuildingsDistant" />
        <li Class="JobGiver_AIGotoNearestHostile" />
        <li Class="JobGiver_AITrashBuildingsDistant">
          <attackAllInert>true</attackAllInert>
        </li>
        <li Class="JobGiver_AISapper">
          <canMineNonMineables>false</canMineNonMineables>
        </li>
      </subNodes>
    </thinkNode>
    <constantThinkNode Class="ThinkNode_ConditionalCanPickupOpportunisticWeapon">
      <subNodes>
        <!-- Try to pick up nearby weapons -->
        <li Class="JobGiver_PickUpOpportunisticWeapon" />
      </subNodes>
    </constantThinkNode>
  </DutyDef>

  <DutyDef>
    <defName>Sapper</defName>
    <alwaysShowWeapon>true</alwaysShowWeapon>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_TakeCombatEnhancingDrug" />
        <li Class="JobGiver_AIFightEnemies">
          <targetAcquireRadius>12</targetAcquireRadius>
          <targetKeepRadius>15</targetKeepRadius>
          <needLOSToAcquireNonPawnTargets>true</needLOSToAcquireNonPawnTargets>
        </li>
        <li Class="JobGiver_AISapper" />
        <li Class="JobGiver_AITrashColonyClose" />
        <li Class="JobGiver_AITrashBuildingsDistant" />
        <li Class="JobGiver_AIGotoNearestHostile" />
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>Escort</defName>
    <alwaysShowWeapon>true</alwaysShowWeapon>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_TakeCombatEnhancingDrug">
          <onlyIfInDanger>true</onlyIfInDanger>
        </li>
        <li Class="JobGiver_AIDefendEscortee">
          <targetAcquireRadius>65</targetAcquireRadius>
          <targetKeepRadius>72</targetKeepRadius>
        </li>
        <li Class="JobGiver_AIFollowEscortee"/>
        <li Class="ThinkNode_Subtree">
          <treeDef>SatisfyVeryUrgentNeeds</treeDef>
        </li> 
        <li Class="JobGiver_WanderNearDutyLocation">
          <wanderRadius>8</wanderRadius>
        </li>
      </subNodes>
    </thinkNode>
  </DutyDef>
  
  <DutyDef>
    <defName>HuntEnemiesIndividual</defName>
    <alwaysShowWeapon>true</alwaysShowWeapon>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_TakeCombatEnhancingDrug" />
        <li Class="JobGiver_AIFightEnemies">
          <targetAcquireRadius>65</targetAcquireRadius>
          <targetKeepRadius>72</targetKeepRadius>
        </li>
        <li Class="JobGiver_AITrashColonyClose" />
        <li Class="JobGiver_AITrashBuildingsDistant" />
        <li Class="JobGiver_AIGotoNearestHostile" />
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>Kidnap</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_Kidnap" />
        <li Class="JobGiver_ExitMapBest">
          <defaultLocomotion>Jog</defaultLocomotion>
          <jobMaxDuration>200</jobMaxDuration>
          <forceCanDigIfCantReachMapEdge>true</forceCanDigIfCantReachMapEdge>
        </li>
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>TakeWoundedGuest</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_TakeWoundedGuest" />
        <li Class="JobGiver_ExitMapBest">
          <defaultLocomotion>Jog</defaultLocomotion>
          <jobMaxDuration>500</jobMaxDuration>
          <forceCanDigIfAnyHostileActiveThreat>true</forceCanDigIfAnyHostileActiveThreat>
          <forceCanDigIfCantReachMapEdge>true</forceCanDigIfCantReachMapEdge>
        </li>
      </subNodes>
    </thinkNode>
  </DutyDef>
  
  <DutyDef>
    <defName>Steal</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_Steal" />
        <li Class="JobGiver_ExitMapBest">
          <defaultLocomotion>Jog</defaultLocomotion>
          <jobMaxDuration>200</jobMaxDuration>
          <forceCanDig>true</forceCanDig>
        </li>
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>PrisonerEscape</defName>
    <alwaysShowWeapon>true</alwaysShowWeapon>
    <thinkNode Class="ThinkNode_Tagger">
      <tagToGive>Escaping</tagToGive>
      <subNodes>
        <!-- Take combat enhancing drugs -->
        <li Class="JobGiver_TakeCombatEnhancingDrug" />
        
        <li Class="ThinkNode_ConditionalHasDutyPawnTarget">
          <subNodes>
            <!-- Follow and defend leader -->
            <li Class="ThinkNode_ConditionalCapableOfWorkTag">
              <workTags>
                <li>Violent</li>
              </workTags>
              <subNodes>
                <li Class="JobGiver_AIDefendEscortee">
                  <targetAcquireRadius>15</targetAcquireRadius>
                  <targetKeepRadius>18</targetKeepRadius>
                  <needLOSToAcquireNonPawnTargets>true</needLOSToAcquireNonPawnTargets>
                </li>
              </subNodes>
            </li>
            <li Class="JobGiver_AIFollowEscortee"/>
            <li Class="JobGiver_WanderNearDutyLocation">
              <wanderRadius>3</wanderRadius>
            </li>
          </subNodes>
        </li>
      
        <!-- Leader -->

        <!-- Fight nearby enemies -->
        <li Class="ThinkNode_ConditionalCapableOfWorkTag">
          <workTags>
            <li>Violent</li>
          </workTags>
          <subNodes>
            <li Class="JobGiver_AIFightEnemies">
              <targetAcquireRadius>14</targetAcquireRadius>
              <targetKeepRadius>17</targetKeepRadius>
              <needLOSToAcquireNonPawnTargets>true</needLOSToAcquireNonPawnTargets>
            </li>
          </subNodes>
        </li>
        
        <!-- Escape -->
        <li Class="JobGiver_GotoTravelDestination">
          <locomotionUrgency>Jog</locomotionUrgency>
          <jobMaxDuration>500</jobMaxDuration>
        </li>
        <li Class="JobGiver_WanderNearDutyLocation">
          <wanderRadius>3</wanderRadius>
        </li>
      </subNodes>
    </thinkNode>
    <constantThinkNode Class="ThinkNode_Tagger">
      <tagToGive>Escaping</tagToGive>
      <subNodes>
        <!-- Try to pick up nearby weapons -->
        <li Class="JobGiver_PickUpOpportunisticWeapon">
          <pickUpUtilityItems>true</pickUpUtilityItems>
        </li>
      </subNodes>
    </constantThinkNode>
  </DutyDef>
  
  <DutyDef>
    <defName>PrisonerEscapeSapper</defName>
    <alwaysShowWeapon>true</alwaysShowWeapon>
    <thinkNode Class="ThinkNode_Tagger">
      <tagToGive>Escaping</tagToGive>
      <subNodes>
        <!-- Take combat enhancing drugs -->
        <li Class="JobGiver_TakeCombatEnhancingDrug" />
        
        <!-- Fight nearby enemies -->
        <li Class="ThinkNode_ConditionalCapableOfWorkTag">
          <workTags>
            <li>Violent</li>
          </workTags>
          <subNodes>
            <li Class="JobGiver_AIFightEnemies">
              <targetAcquireRadius>12</targetAcquireRadius>
              <targetKeepRadius>15</targetKeepRadius>
              <needLOSToAcquireNonPawnTargets>true</needLOSToAcquireNonPawnTargets>
            </li>
          </subNodes>
        </li>
        
        <!-- Dig out of the colony to get to the exit point -->
        <li Class="JobGiver_AISapper" />
        
        <!-- Wait until the lord tells to exit the map -->
        <li Class="JobGiver_WanderNearDutyLocation">
          <wanderRadius>3</wanderRadius>
        </li>
      </subNodes>
    </thinkNode>
    <constantThinkNode Class="ThinkNode_Tagger">
      <tagToGive>Escaping</tagToGive>
      <subNodes>
        <!-- Try to pick up nearby weapons -->
        <li Class="JobGiver_PickUpOpportunisticWeapon">
          <preferBuildingDestroyers>true</preferBuildingDestroyers> <!-- prefer building destroyers (e.g. grenades) because I'm a sapper -->
          <pickUpUtilityItems>true</pickUpUtilityItems>
        </li>
      </subNodes>
    </constantThinkNode>
  </DutyDef>

  <DutyDef>
    <defName>PrisonerAssaultColony</defName>
    <alwaysShowWeapon>true</alwaysShowWeapon>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_AIFightEnemies">
          <targetAcquireRadius>65</targetAcquireRadius>
          <targetKeepRadius>72</targetKeepRadius>
        </li>
        <li Class="JobGiver_AITrashColonyClose" />
        <li Class="JobGiver_AITrashBuildingsDistant" />
        <li Class="JobGiver_AIGotoNearestHostile" />
        <li Class="JobGiver_AITrashBuildingsDistant" />
        <li Class="JobGiver_AISapper">
          <canMineNonMineables>false</canMineNonMineables>
        </li>
      </subNodes>
    </thinkNode>
    <constantThinkNode Class="ThinkNode_Tagger">
      <subNodes>
        <!-- Try to pick up nearby weapons -->
        <li Class="JobGiver_PickUpOpportunisticWeapon">
        </li>
      </subNodes>
    </constantThinkNode>
  </DutyDef>
  
  <DutyDef>
    <defName>DefendAndExpandHive</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_HiveDefense">
          <needLOSToAcquireNonPawnTargets>true</needLOSToAcquireNonPawnTargets>
          <targetAcquireRadius>30</targetAcquireRadius>
          <chaseTarget>false</chaseTarget>
        </li>
        <li Class="ThinkNode_ChancePerHour_Constant">
          <mtbHours>2.5</mtbHours>
          <subNodes>
            <li Class="JobGiver_MaintainHives" />
          </subNodes>
        </li>
        <li Class="JobGiver_MaintainHives">
          <onlyIfDamagingState>true</onlyIfDamagingState>
        </li>
        <li Class="ThinkNode_ConditionalBodySize">
          <min>0.7</min>
          <subNodes>
            <li Class="ThinkNode_ConditionalHiveCanReproduce">
              <subNodes>
                <li Class="ThinkNode_ChancePerHour_InsectDigChance">
                  <subNodes>
                    <li Class="JobGiver_MineRandom" />
                  </subNodes>
                </li>
              </subNodes>
            </li>
          </subNodes>
        </li>
        <li Class="ThinkNode_Tagger">
          <tagToGive>SatisfyingNeeds</tagToGive>
          <subNodes>
            <li Class="ThinkNode_PrioritySorter">
              <subNodes>
                <li Class="JobGiver_GetFood" />
                <li Class="JobGiver_GetRest" />
              </subNodes>
            </li>
          </subNodes>
        </li>
        <li Class="JobGiver_WanderHive">
          <maxDanger>Deadly</maxDanger>
        </li>
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>DefendHiveAggressively</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_HiveDefense">
          <needLOSToAcquireNonPawnTargets>true</needLOSToAcquireNonPawnTargets>
          <targetAcquireRadius>50</targetAcquireRadius>
          <chaseTarget>false</chaseTarget>
        </li>
        <li Class="JobGiver_WanderHive">
          <maxDanger>Deadly</maxDanger>
        </li>
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>ExitMapRandom</defName>
    <thinkNode Class="JobGiver_ExitMapRandom">
      <defaultLocomotion>Walk</defaultLocomotion>
    </thinkNode>
  </DutyDef>
  
  <DutyDef>
    <defName>ExitMapBest</defName>
    <thinkNode Class="JobGiver_ExitMapBest">
      <defaultLocomotion>Walk</defaultLocomotion>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>ExitMapBestAndDefendSelf</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_AIDefendSelf">
          <targetAcquireRadius>35</targetAcquireRadius>
          <targetKeepRadius>40</targetKeepRadius>
        </li>
        <li Class="JobGiver_ExitMapBest">
          <defaultLocomotion>Walk</defaultLocomotion>
          <jobMaxDuration>120</jobMaxDuration>
        </li>
      </subNodes>
    </thinkNode>
  </DutyDef>
  
  <DutyDef>
    <defName>ExitMapNearDutyTarget</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_ExitMapNearDutyTarget">
          <defaultLocomotion>Walk</defaultLocomotion>
        </li>
        <li Class="JobGiver_ExitMapBest">
          <defaultLocomotion>Walk</defaultLocomotion>
        </li>
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>LoadAndEnterTransporters</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <!-- Basic needs -->
        <li Class="ThinkNode_Subtree">
          <treeDef>SatisfyVeryUrgentNeeds</treeDef>
        </li>
        
        <!-- Load transport pods -->
        <li Class="JobGiver_LoadTransporters" />

        <!-- Drop items if you're assigned to unload everything -->
        <!-- (before entering the transporter!) -->
        <li Class="JobGiver_UnloadYourInventory" />

        <!-- Enter -->
        <li Class="JobGiver_EnterTransporter" />
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>EnterTransporter</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_EnterTransporter" />
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>EnterTransporterAndDefendSelf</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_AIDefendSelf">
          <targetAcquireRadius>35</targetAcquireRadius>
          <targetKeepRadius>40</targetKeepRadius>
        </li>
        <li Class="JobGiver_EnterTransporter" />
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>LoadAndEnterPortal</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <!-- Basic needs -->
        <li Class="ThinkNode_Subtree">
          <treeDef>SatisfyVeryUrgentNeeds</treeDef>
        </li>

        <!-- Haul to portal -->
        <li Class="JobGiver_HaulToPortal" />

        <!-- Drop items if you're assigned to unload everything -->
        <!-- (before entering the portal!) -->
        <li Class="JobGiver_UnloadYourInventory" />

        <!-- Enter -->
        <li Class="JobGiver_EnterPortal" />
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>ManClosestTurret</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="ThinkNode_Subtree">
          <treeDef>SatisfyBasicNeeds</treeDef>
        </li> 
        <li Class="JobGiver_ManTurretsNearSelf">
          <maxDistFromPoint>50</maxDistFromPoint>
        </li>
        <li Class="JobGiver_AIDefendPoint">
          <targetAcquireRadius>35</targetAcquireRadius>
          <targetKeepRadius>40</targetKeepRadius>
        </li>
        <li Class="JobGiver_WanderNearDutyLocation">
          <wanderRadius>12</wanderRadius>
          <locomotionUrgencyOutsideRadius>Sprint</locomotionUrgencyOutsideRadius>
        </li>
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>SleepForever</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="ThinkNode_PrioritySorter">
          <subNodes>
            <li Class="JobGiver_GetFood">
              <minCategory>Starving</minCategory>
            </li>
          </subNodes>
        </li>
        <li Class="JobGiver_ForceSleepNow" />
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>Breaching</defName>
    <alwaysShowWeapon>true</alwaysShowWeapon>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_TakeCombatEnhancingDrug" />
        <li Class="ThinkNode_HarmedRecently">
          <thresholdTicks>600</thresholdTicks>
          <subNodes>
            <li Class="JobGiver_AIFightEnemies"> <!-- respond if attacked -->
              <targetAcquireRadius>65</targetAcquireRadius>
              <targetKeepRadius>72</targetKeepRadius>
              <needLOSToAcquireNonPawnTargets>true</needLOSToAcquireNonPawnTargets>
            </li>
          </subNodes>
        </li>
        <li Class="JobGiver_AIFightEnemies"> <!-- respond to close threats -->
          <targetAcquireRadius>12</targetAcquireRadius>
          <targetKeepRadius>15</targetKeepRadius>
          <needLOSToAcquireNonPawnTargets>true</needLOSToAcquireNonPawnTargets>
        </li>
        <li Class="JobGiver_AIBreaching" />
        <li Class="JobGiver_AIFightEnemies"> <!-- respond to distant threats -->
          <targetAcquireRadius>65</targetAcquireRadius>
          <targetKeepRadius>72</targetKeepRadius>
        </li>
        <li Class="JobGiver_WanderNearBreacher">
          <wanderRadius>5</wanderRadius>
          <expiryInterval>120</expiryInterval>
        </li>
        <li Class="JobGiver_AITrashColonyClose" />
        <li Class="JobGiver_AITrashBuildingsDistant" />
        <li Class="JobGiver_AIGotoNearestHostile" />
        <li Class="JobGiver_AITrashBuildingsDistant">
          <attackAllInert>true</attackAllInert>
        </li>
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>AssaultThing</defName>
    <alwaysShowWeapon>true</alwaysShowWeapon>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_TakeCombatEnhancingDrug" />
        <li Class="ThinkNode_Subtree">
          <treeDef>Abilities_Aggressive</treeDef>
        </li>
        <li Class="JobGiver_AIFightEnemies">
          <targetAcquireRadius>20</targetAcquireRadius>
          <targetKeepRadius>23</targetKeepRadius>
        </li>
        <li Class="JobGiver_AITrashDutyFocus" />
        <li Class="JobGiver_AISapper" />
      </subNodes>
    </thinkNode>
  </DutyDef>
  
  <DutyDef>
    <defName>WanderClose</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_GetFood">
          <minCategory>UrgentlyHungry</minCategory>
        </li>
        <li Class="JobGiver_GetRest">
          <minCategory>VeryTired</minCategory>
        </li>
        <li Class="JobGiver_WanderNearDutyLocation">
          <wanderRadius>3</wanderRadius>
        </li> 
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>WanderClose_NoNeeds</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_WanderNearDutyLocation">
          <wanderRadius>3</wanderRadius>
        </li>
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>LayDownAwake</defName>
    <hook>HighPriority</hook>
    <socialModeMax>Off</socialModeMax>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_LayDownAwake">
        </li>
      </subNodes>
    </thinkNode>
  </DutyDef>

</Defs>