<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <StatCategoryDef>
    <defName>PsychicRituals</defName>
    <label>Psychic ritual</label>
    <displayOrder>500</displayOrder>
  </StatCategoryDef>

  <StatDef>
    <defName>PsychicRitualQuality</defName>
    <label>psychic ritual quality</label>
    <description>How much nearby buildings improve quality of psychic rituals performed here.</description>
    <category>PsychicRituals</category>
    <showOnDefaultValue>false</showOnDefaultValue>
    <defaultBaseValue>0</defaultBaseValue>
    <toStringStyle>PercentZero</toStringStyle>
  </StatDef>
  
  <StatDef>
    <defName>PsychicRitualQualityOffset</defName>
    <label>psychic ritual quality offset</label>
    <description>An offset applied to psychic ritual quality when this person participates. The total quality offset is divided by the total number of participants.</description>
    <category>PsychicRituals</category>
    <defaultBaseValue>0</defaultBaseValue>
    <showOnDefaultValue>false</showOnDefaultValue>
    <toStringStyle>PercentZero</toStringStyle>
  </StatDef>

</Defs>