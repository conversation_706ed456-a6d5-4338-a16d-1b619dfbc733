﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingDef ParentName="DrugBase">
    <defName>Ambrosia</defName>
    <label>ambrosia</label>
    <description>A soft, rare fruit. Ambrosia tastes wonderful and produces a subtle mood-increasing chemical high. However, if eaten too often, it can generate a mild addiction.</description>
    <descriptionHyperlinks>
      <HediffDef>AmbrosiaHigh</HediffDef>
      <HediffDef>AmbrosiaTolerance</HediffDef>
      <HediffDef>AmbrosiaAddiction</HediffDef>
    </descriptionHyperlinks>
    <tradeability>Sellable</tradeability>
    <socialPropernessMatters>true</socialPropernessMatters>
    <tickerType>Rare</tickerType>
    <graphicData>
      <texPath>Things/Item/Drug/Ambrosia</texPath>
      <graphicClass>Graphic_StackCount</graphicClass>
    </graphicData>
    <statBases>
      <MarketValue>15</MarketValue>
      <Mass>0.1</Mass>
      <DeteriorationRate>4</DeteriorationRate>
      <Nutrition>0.2</Nutrition>
    </statBases>
    <techLevel>Neolithic</techLevel>
    <ingestible>
      <baseIngestTicks>80</baseIngestTicks>
      <chairSearchRadius>4</chairSearchRadius>
      <preferability>DesperateOnly</preferability>
      <tasteThought></tasteThought>
      <foodType>VegetableOrFruit</foodType>
      <joyKind>Chemical</joyKind>
      <joy>0.5</joy>
      <nurseable>true</nurseable>
      <drugCategory>Social</drugCategory>
      <canAutoSelectAsFoodForCaravan>false</canAutoSelectAsFoodForCaravan>
      <outcomeDoers>
        <li Class="IngestionOutcomeDoer_GiveHediff">
          <hediffDef>AmbrosiaHigh</hediffDef>
          <severity>0.50</severity>
          <toleranceChemical>Ambrosia</toleranceChemical>
        </li>
        <li Class="IngestionOutcomeDoer_GiveHediff">
          <hediffDef>AmbrosiaTolerance</hediffDef>
          <toleranceChemical>Ambrosia</toleranceChemical>
          <severity>0.032</severity>
          <divideByBodySize>true</divideByBodySize>
          <multiplyByGeneToleranceFactors>true</multiplyByGeneToleranceFactors>
        </li>
      </outcomeDoers>
    </ingestible>
    <comps>
      <li Class="CompProperties_Rottable">
        <daysToRotStart>30</daysToRotStart>
        <rotDestroys>true</rotDestroys>
      </li>
      <li Class="CompProperties_Drug">
        <chemical>Ambrosia</chemical>
        <addictiveness>0.010</addictiveness>
        <minToleranceToAddict>0.15</minToleranceToAddict>
        <existingAddictionSeverityOffset>0.1</existingAddictionSeverityOffset>
        <needLevelOffset>0.9</needLevelOffset>
        <listOrder>30</listOrder>
      </li>
    </comps>
    <allowedArchonexusCount>20</allowedArchonexusCount>
  </ThingDef>

  <HediffDef>
    <defName>AmbrosiaHigh</defName>
    <label>ambrosia warmth</label>
    <description>Ambrosia chemicals in the bloodstream. It lifts the mood.</description>
    <hediffClass>Hediff_High</hediffClass>
    <defaultLabelColor>(1,0,0.5)</defaultLabelColor>
    <scenarioCanAdd>true</scenarioCanAdd>
    <maxSeverity>1.0</maxSeverity>
    <isBad>false</isBad>
    <comps>
      <li Class="HediffCompProperties_SeverityPerDay">
        <severityPerDay>-0.75</severityPerDay>
        <showHoursToRecover>true</showHoursToRecover>
      </li>
    </comps>
    <stages>
      <li/>
    </stages>
  </HediffDef>

  <ThoughtDef>
    <defName>AmbrosiaHigh</defName>
    <workerClass>ThoughtWorker_Hediff</workerClass>
    <hediff>AmbrosiaHigh</hediff>
    <validWhileDespawned>true</validWhileDespawned>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
    <stages>
      <li>
        <label>ambrosia warmth</label>
        <description>That ambrosia makes me feel more relaxed, and gives me energy at the same time.</description>
        <baseMoodEffect>5</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <!-- Ambrosia addiction -->
  
  <ChemicalDef>
    <defName>Ambrosia</defName>
    <label>ambrosia</label>
    <addictionHediff>AmbrosiaAddiction</addictionHediff>
    <toleranceHediff>AmbrosiaTolerance</toleranceHediff>
    <onGeneratedAddictedToleranceChance>0.8</onGeneratedAddictedToleranceChance>
    <generateAddictionGenes>false</generateAddictionGenes>
  </ChemicalDef>
  
  <NeedDef ParentName="DrugAddictionNeedBase">
    <defName>Chemical_Ambrosia</defName>
    <needClass>Need_Chemical</needClass>
    <label>ambrosia</label>
    <description>Because of an ambrosia addiction, this person needs to regularly consume ambrosia to avoid withdrawal symptoms.</description>
    <listPriority>20</listPriority>
  </NeedDef>

  <HediffDef ParentName="DrugToleranceBase">
    <defName>AmbrosiaTolerance</defName>
    <label>ambrosia tolerance</label>
    <description>A built-up tolerance to ambrosia. The more severe this tolerance is, the more ambrosia it takes to get the same effect.</description>
    <comps>
      <li Class="HediffCompProperties_SeverityPerDay">
        <severityPerDay>-0.020</severityPerDay>
      </li>
      <li Class="HediffCompProperties_DrugEffectFactor">
        <chemical>Ambrosia</chemical>
      </li>
    </comps>
  </HediffDef>
  
  <HediffDef ParentName="AddictionBase">
    <defName>AmbrosiaAddiction</defName>
    <label>ambrosia addiction</label>
    <description>A chemical addiction to ambrosia. Long-term presence of ambrosia has caused neurological adaptations at the cellular level, so the brain can no longer function properly without the drug.
\nWithout regular doses of ambrosia, withdrawal symptoms will begin. However, extended abstinence will force the brain to adapt back to its normal state, resolving the addiction.</description>
    <hediffClass>Hediff_Addiction</hediffClass>
    <causesNeed>Chemical_Ambrosia</causesNeed>
    <comps>
      <li Class="HediffCompProperties_SeverityPerDay">
        <severityPerDay>-0.1</severityPerDay>
        <showDaysToRecover>true</showDaysToRecover>
      </li>
    </comps>
    <stages>
      <li>
      </li>
      <li>
        <label>withdrawal</label>
      </li>
    </stages>
  </HediffDef>

  <ThoughtDef>
    <defName>AmbrosiaWithdrawal</defName>
    <workerClass>ThoughtWorker_Hediff</workerClass>
    <hediff>AmbrosiaAddiction</hediff>
    <validWhileDespawned>true</validWhileDespawned>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
    <stages>
      <li>
        <visible>false</visible>
      </li>
      <li>
        <label>ambrosia withdrawal</label>
        <description>I feel heavy and cold all the time. I never thought I'd want a piece of fruit so much.</description>
        <baseMoodEffect>-10</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

</Defs>
