<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <FactionDef ParentName="TribeRoughBase">
    <defName>TribeRoughNeanderthal</defName>
    <label>fierce neanderthal tribe</label>
    <description>An unfriendly, territorial tribe of neanderthals. They are open to peaceful trade, but are otherwise unfriendly and territorial. Warriors from their tribe won't hesitate to bash in the skulls of anyone they deem dangerous. Their numbers, and the extreme amounts of damage they absorb before falling make them serious foes despite their lack of technology and analytical ability.\n\nHaving been on this planet for countless generations, they have no memory of how they got here. They might be the descendants of a neanderthal service caste from a dead civilization, or even the leftovers from some ancient science experiment.</description>
    <factionIconPath>World/WorldObjects/Expanding/FierceNeanderthalTribe</factionIconPath>
    <factionNameMaker>NamerFactionTribalNeanderthal</factionNameMaker>
    <settlementNameMaker>NamerSettlementTribalNeaderthal</settlementNameMaker>
    <pawnSingular>neanderthal</pawnSingular>
    <pawnsPlural>neanderthals</pawnsPlural>
    <replacesFaction>TribeRough</replacesFaction>
    <melaninRange>0~0.5</melaninRange>
    <xenotypeSet Inherit="False">
      <xenotypeChances>
        <Neanderthal>999</Neanderthal>
      </xenotypeChances>
    </xenotypeSet>
    <dialogFactionGreetingHostile>NeanderthalFactionGreetingHostile</dialogFactionGreetingHostile>
    <dialogFactionGreetingHostileAppreciative>NeanderthalFactionGreetingHostileAppreciative</dialogFactionGreetingHostileAppreciative>
    <dialogFactionGreetingWary>NeanderthalFactionGreetingWary</dialogFactionGreetingWary>
    <dialogFactionGreetingWarm>NeanderthalFactionGreetingWarm</dialogFactionGreetingWarm>
    <pawnGroupMakers Inherit="False">
      <li>
        <!-- Normal fights, ranged with melee mix-ins -->
        <kindDef>Combat</kindDef>
        <commonality>100</commonality>
        <options>
          <Tribal_Penitent>5</Tribal_Penitent>
          <Tribal_Warrior>5</Tribal_Warrior>
          <Tribal_Archer>1</Tribal_Archer>
          <Tribal_Hunter>1</Tribal_Hunter>
          <Tribal_HeavyArcher>1</Tribal_HeavyArcher>
          <Tribal_Berserker>5</Tribal_Berserker>
          <Tribal_ChiefRanged>1</Tribal_ChiefRanged>
          <Tribal_ChiefMelee>3</Tribal_ChiefMelee>
        </options>
      </li>
      <li>
        <!-- Normal fights, melee only -->
        <kindDef>Combat</kindDef>
        <commonality>150</commonality>
        <options>
          <Tribal_Penitent>10</Tribal_Penitent>
          <Tribal_Warrior>10</Tribal_Warrior>
          <Tribal_Berserker>10</Tribal_Berserker>
          <Tribal_ChiefMelee>5</Tribal_ChiefMelee>
      </options>
      </li>
      <li>
        <!-- breach-capable fights, breachers plus ranged with melee mix-ins -->
        <kindDef>Combat</kindDef>
        <commonality>5</commonality>
        <options>
          <Tribal_Breacher>5</Tribal_Breacher>
          <Tribal_Penitent>5</Tribal_Penitent>
          <Tribal_Warrior>5</Tribal_Warrior>
          <Tribal_Archer>1</Tribal_Archer>
          <Tribal_Hunter>1</Tribal_Hunter>
          <Tribal_HeavyArcher>1</Tribal_HeavyArcher>
          <Tribal_Berserker>5</Tribal_Berserker>
          <Tribal_ChiefRanged>1</Tribal_ChiefRanged>
          <Tribal_ChiefMelee>3</Tribal_ChiefMelee>
      </options>
      </li>
      <li>
        <!-- Peaceful -->
        <kindDef>Peaceful</kindDef>
        <options>
          <Tribal_Warrior>20</Tribal_Warrior>
          <Tribal_Child>10</Tribal_Child>
          <Tribal_Archer>1</Tribal_Archer>
          <Tribal_Hunter>1</Tribal_Hunter>
          <Tribal_HeavyArcher>1</Tribal_HeavyArcher>
          <Tribal_Berserker>5</Tribal_Berserker>
          <Tribal_ChiefRanged>1</Tribal_ChiefRanged>
          <Tribal_ChiefMelee>3</Tribal_ChiefMelee>
        </options>
      </li>
      <li>
        <kindDef>Trader</kindDef>
        <traders>
          <Tribal_Trader>1</Tribal_Trader>
        </traders>
        <carriers>
          <Muffalo>6</Muffalo>
          <Dromedary>5</Dromedary>
          <Alpaca>2.5</Alpaca>
          <Elephant>2</Elephant>
        </carriers>
        <guards>
          <Tribal_Warrior>7</Tribal_Warrior>
          <Tribal_Archer>1</Tribal_Archer>
          <Tribal_Hunter>1</Tribal_Hunter>
          <Tribal_HeavyArcher>1</Tribal_HeavyArcher>
          <Tribal_Berserker>7</Tribal_Berserker>
        </guards>
      </li>
      <li>
        <kindDef>Settlement</kindDef>
        <options>
          <Tribal_Warrior>7</Tribal_Warrior>
          <Tribal_Archer>1</Tribal_Archer>
          <Tribal_Hunter>1</Tribal_Hunter>
          <Tribal_HeavyArcher>1</Tribal_HeavyArcher>
          <Tribal_Berserker>7</Tribal_Berserker>
          <Tribal_ChiefRanged>1</Tribal_ChiefRanged>
          <Tribal_ChiefMelee>3</Tribal_ChiefMelee>
        </options>
      </li>
      <li>
        <kindDef>Settlement_RangedOnly</kindDef>
        <options>
          <Tribal_Archer>10</Tribal_Archer>
          <Tribal_Hunter>10</Tribal_Hunter>
          <Tribal_HeavyArcher>10</Tribal_HeavyArcher>
          <Tribal_ChiefRanged>1</Tribal_ChiefRanged>
          <Tribal_ChiefMelee>10</Tribal_ChiefMelee>
        </options>
      </li>
      <li MayRequire="Ludeon.RimWorld.Ideology">
        <kindDef>Miners</kindDef>
        <commonality>1</commonality>
        <options>
          <Tribal_Miner>1</Tribal_Miner>
        </options>
      </li>
      <li MayRequire="Ludeon.RimWorld.Ideology">
        <kindDef>Hunters</kindDef>
        <commonality>1</commonality>
        <options>
          <Tribal_Hunter>1</Tribal_Hunter>
        </options>
      </li>
      <li MayRequire="Ludeon.RimWorld.Ideology">
        <kindDef>Loggers</kindDef>
        <commonality>1</commonality>
        <options>
          <Tribal_Logger>1</Tribal_Logger>
        </options>
      </li>
      <li MayRequire="Ludeon.RimWorld.Ideology">
        <kindDef>Farmers</kindDef>
        <commonality>1</commonality>
        <options>
          <Tribal_Farmer>1</Tribal_Farmer>
        </options>
      </li>
    </pawnGroupMakers>
  </FactionDef>

  <FactionDef ParentName="PirateBandBase">
    <defName>PirateYttakin</defName>
    <label>yttakin pirates</label>
    <description>A close-knit band of hulking, fur-bodied pirates of the yttakin xenotype. They refuse to deal with outsiders and are quick to call their animal warriors against those who disrespect them - or whose wealth they intend to take.\n\nOriginally engineered to populate the icy planet Yttak, yttakin now pursue their traditional lifestyle on many worlds.</description>
    <factionIconPath>World/WorldObjects/Expanding/YttakinPirates</factionIconPath>
    <factionNameMaker>NamerFactionPirateYttakin</factionNameMaker>
    <settlementNameMaker>NamerSettlementPirateYttakin</settlementNameMaker>
    <pawnSingular>yttakin</pawnSingular>
    <pawnsPlural>yttakin</pawnsPlural>
    <disallowedRaidStrategies> <!-- Yttakin mining skill is too low to be effective sappers or breachers -->
      <li>ImmediateAttackSappers</li>
      <li>ImmediateAttackBreaching</li>
      <li>ImmediateAttackBreachingSmart</li>
    </disallowedRaidStrategies>
    <requiredMemes Inherit="False">
      <li MayRequire="Ludeon.RimWorld.Ideology">AnimalPersonhood</li>
      <li MayRequire="Ludeon.RimWorld.Ideology">Raider</li>
    </requiredMemes>
    <xenotypeSet Inherit="False">
      <xenotypeChances>
        <Yttakin>999</Yttakin>
      </xenotypeChances>
    </xenotypeSet>
    <dialogFactionGreetingHostile>YttakinFactionGreetingHostile</dialogFactionGreetingHostile>
    <dialogFactionGreetingHostileAppreciative>YttakinFactionGreetingHostileAppreciative</dialogFactionGreetingHostileAppreciative>
    <dialogFactionGreetingWary>YttakinFactionGreetingWary</dialogFactionGreetingWary>
    <dialogFactionGreetingWarm>YttakinFactionGreetingWarm</dialogFactionGreetingWarm>
    <disallowedPrecepts>
      <li MayRequire="Ludeon.RimWorld.Ideology">ApparelDesired_Strong_Subordinate</li> <!-- Never generate with any apparel precepts -->
      <li MayRequire="Ludeon.RimWorld.Ideology">ApparelDesired_Soft_Subordinate</li>
    </disallowedPrecepts>
    <pawnGroupMakers Inherit="False">
      <li>
        <!-- Normal fights, ranged with melee mix-ins -->
        <kindDef>Combat</kindDef>
        <commonality>100</commonality>
        <options>
          <Drifter_Yttakin>10</Drifter_Yttakin>
          <Thrasher_Yttakin>3</Thrasher_Yttakin>
          <Scavenger_Yttakin>10</Scavenger_Yttakin>
          <Pirate_Yttakin>10</Pirate_Yttakin>
          <Grenadier_EMP>0.25</Grenadier_EMP>
          <Grenadier_Smoke>0.25</Grenadier_Smoke>
          <Grenadier_Destructive>2</Grenadier_Destructive>
          <Mercenary_Gunner_Yttakin>10</Mercenary_Gunner_Yttakin>
          <Mercenary_Heavy_Yttakin>6</Mercenary_Heavy_Yttakin>
          <Mercenary_Slasher_Yttakin>3</Mercenary_Slasher_Yttakin>
          <Mercenary_Sniper_Yttakin>7</Mercenary_Sniper_Yttakin>
          <Mercenary_Elite_Yttakin>10</Mercenary_Elite_Yttakin>
          <PirateBoss>5</PirateBoss>
          <Warg>15</Warg>
          <WildBoar>15</WildBoar>
        </options>
      </li>
      <li>
        <!-- Normal fights, melee-only -->
        <kindDef>Combat</kindDef>
        <commonality>30</commonality>
        <disallowedStrategies>
          <li>Siege</li>
        </disallowedStrategies>
        <options>
          <Thrasher_Yttakin>10</Thrasher_Yttakin>
          <Mercenary_Slasher_Yttakin>10</Mercenary_Slasher_Yttakin>
          <PirateBoss>5</PirateBoss>
          <Warg>5</Warg>
        </options>
      </li>
      <li>
        <!-- Normal fights, explosives with ranged mix-ins -->
        <kindDef>Combat</kindDef>
        <commonality>15</commonality>
        <disallowedStrategies>
          <li>Siege</li>
        </disallowedStrategies>
        <options>
          <Grenadier_EMP>0.5</Grenadier_EMP>
          <Grenadier_Smoke>0.5</Grenadier_Smoke>
          <Grenadier_Destructive>10</Grenadier_Destructive>
          <Mercenary_Gunner_Yttakin>1</Mercenary_Gunner_Yttakin>
          <Mercenary_Heavy_Yttakin>10</Mercenary_Heavy_Yttakin>
          <Mercenary_Elite_Yttakin>1</Mercenary_Elite_Yttakin>
          <PirateBoss>1</PirateBoss>
          <WildBoar>5</WildBoar>
        </options>
      </li>
      <li>
        <!-- Normal fights, drifters only (very rare) -->
        <kindDef>Combat</kindDef>
        <commonality>2.5</commonality>
        <maxTotalPoints>1000</maxTotalPoints>
        <options>
          <Drifter_Yttakin>10</Drifter_Yttakin>
          <WildBoar>3</WildBoar>
        </options>
      </li>
      <li>
        <!-- Base defense, mainly ranged with melee mix-ins -->
        <kindDef>Settlement</kindDef>
        <options>
          <Thrasher_Yttakin>3</Thrasher_Yttakin>
          <Pirate_Yttakin>10</Pirate_Yttakin>
          <Grenadier_Destructive>2</Grenadier_Destructive>
          <Mercenary_Slasher_Yttakin>3</Mercenary_Slasher_Yttakin>
          <Mercenary_Sniper_Yttakin>10</Mercenary_Sniper_Yttakin>
          <Mercenary_Gunner_Yttakin>10</Mercenary_Gunner_Yttakin>
          <Mercenary_Elite_Yttakin>10</Mercenary_Elite_Yttakin>
          <PirateBoss>10</PirateBoss>
          <Warg>12</Warg>
          <WildBoar>8</WildBoar>
          <Bear_Grizzly>3</Bear_Grizzly>
        </options>
      </li>
      <li MayRequire="Ludeon.RimWorld.Ideology">
        <kindDef>Miners</kindDef>
        <commonality>1</commonality>
        <options>
          <Miner>1</Miner>
        </options>
      </li>
      <li MayRequire="Ludeon.RimWorld.Ideology">
        <kindDef>Hunters</kindDef>
        <commonality>1</commonality>
        <options>
          <Hunter>1</Hunter>
        </options>
      </li>
      <li MayRequire="Ludeon.RimWorld.Ideology">
        <kindDef>Loggers</kindDef>
        <commonality>1</commonality>
        <options>
          <Logger>1</Logger>
        </options>
      </li>
      <li MayRequire="Ludeon.RimWorld.Ideology">
        <kindDef>Farmers</kindDef>
        <commonality>1</commonality>
        <options>
          <Farmer>1</Farmer>
        </options>
      </li>
    </pawnGroupMakers>
  </FactionDef>

  <FactionDef ParentName="TribeSavageBase">
    <defName>TribeSavageImpid</defName>
    <label>savage impid tribe</label>
    <description>A vicious tribe of yellow-skinned, fire-spewing impids. This particular tribe has no intention of coexisting peacefully with anyone else. Their warriors are experts at using their natural speed and flame attacks to burn the towns and children of other peoples.\n\nHaving been here for thousands of years, the origins of this tribe are lost in myth. They might descend from a group of settlers who lost their technology, or an impid community in some long-ruined civilization.</description>
    <factionIconPath>World/WorldObjects/Expanding/SavageImpidTribe</factionIconPath>
    <factionNameMaker>NamerFactionTribalImpid</factionNameMaker>
    <settlementNameMaker>NamerSettlementTribalImpid</settlementNameMaker>
    <pawnSingular>imp</pawnSingular>
    <pawnsPlural>imps</pawnsPlural>
    <replacesFaction>TribeSavage</replacesFaction>
    <earliestRaidDays>20</earliestRaidDays>
    <xenotypeSet Inherit="False">
      <xenotypeChances>
        <Impid>999</Impid>
      </xenotypeChances>
    </xenotypeSet>
    <pawnGroupMakers Inherit="False">
      <li>
        <kindDef>Combat</kindDef>
        <commonality>100</commonality>
        <options>
          <Tribal_Penitent_Fire>5</Tribal_Penitent_Fire>
          <Tribal_Warrior_Fire>5</Tribal_Warrior_Fire>
          <Tribal_Archer>8</Tribal_Archer>
          <Tribal_Archer_Fire>10</Tribal_Archer_Fire>
          <Tribal_Hunter>8</Tribal_Hunter>
          <Tribal_Hunter_Fire>10</Tribal_Hunter_Fire>
          <Tribal_HeavyArcher>8</Tribal_HeavyArcher>
          <Tribal_Berserker>5</Tribal_Berserker>
          <Tribal_ChiefRanged>3</Tribal_ChiefRanged>
          <Tribal_ChiefMelee>1.5</Tribal_ChiefMelee>
        </options>
      </li>
      <li>
        <kindDef>Combat</kindDef>
        <commonality>60</commonality>
        <options>
          <Tribal_Archer>8</Tribal_Archer>
          <Tribal_Archer_Fire>10</Tribal_Archer_Fire>
          <Tribal_Hunter>8</Tribal_Hunter>
          <Tribal_Hunter_Fire>10</Tribal_Hunter_Fire>
          <Tribal_HeavyArcher>8</Tribal_HeavyArcher>
          <Tribal_ChiefRanged>5</Tribal_ChiefRanged>
        </options>
      </li>
      <li>
        <kindDef>Combat</kindDef>
        <commonality>60</commonality>
        <options>
          <Tribal_Penitent_Fire>10</Tribal_Penitent_Fire>
          <Tribal_Warrior_Fire>10</Tribal_Warrior_Fire>
          <Tribal_Berserker>10</Tribal_Berserker>
          <Tribal_ChiefMelee>5</Tribal_ChiefMelee>
      </options>
      </li>
      <li>
        <kindDef>Combat</kindDef>
        <commonality>5</commonality>
        <options>
          <Tribal_Breacher>5</Tribal_Breacher>
          <Tribal_Penitent_Fire>5</Tribal_Penitent_Fire>
          <Tribal_Warrior_Fire>5</Tribal_Warrior_Fire>
          <Tribal_Archer>4</Tribal_Archer>
          <Tribal_Archer_Fire>5</Tribal_Archer_Fire>
          <Tribal_Hunter>8</Tribal_Hunter>
          <Tribal_Hunter_Fire>10</Tribal_Hunter_Fire>
          <Tribal_HeavyArcher>8</Tribal_HeavyArcher>
          <Tribal_Berserker>5</Tribal_Berserker>
          <Tribal_ChiefRanged>3</Tribal_ChiefRanged>
          <Tribal_ChiefMelee>1.5</Tribal_ChiefMelee>
      </options>
      </li>
      <li>
        <kindDef>Peaceful</kindDef>
        <options>
          <Tribal_Child_Fire>20</Tribal_Child_Fire>
          <Tribal_Child>8</Tribal_Child>
          <Tribal_Warrior_Fire>20</Tribal_Warrior_Fire>
          <Tribal_Archer>8</Tribal_Archer>
          <Tribal_Archer_Fire>10</Tribal_Archer_Fire>
          <Tribal_Hunter>8</Tribal_Hunter>
          <Tribal_Hunter_Fire>10</Tribal_Hunter_Fire>
          <Tribal_HeavyArcher>8</Tribal_HeavyArcher>
          <Tribal_Berserker>5</Tribal_Berserker>
          <Tribal_ChiefRanged>3</Tribal_ChiefRanged>
          <Tribal_ChiefMelee>1.5</Tribal_ChiefMelee>
        </options>
      </li>
      <li>
        <kindDef>Trader</kindDef>
        <traders>
          <Tribal_Trader>1</Tribal_Trader>
        </traders>
        <carriers>
          <Muffalo>6</Muffalo>
          <Dromedary>5</Dromedary>
          <Alpaca>2.5</Alpaca>
          <Elephant>2</Elephant>
        </carriers>
        <guards>
          <Tribal_Warrior_Fire>7</Tribal_Warrior_Fire>
          <Tribal_Archer>8</Tribal_Archer>
          <Tribal_Archer_Fire>10</Tribal_Archer_Fire>
          <Tribal_Hunter>8</Tribal_Hunter>
          <Tribal_Hunter_Fire>10</Tribal_Hunter_Fire>
          <Tribal_HeavyArcher>8</Tribal_HeavyArcher>
          <Tribal_Berserker>7</Tribal_Berserker>
        </guards>
      </li>
      <li>
        <kindDef>Settlement</kindDef>
        <options>
          <Tribal_Warrior_Fire>7</Tribal_Warrior_Fire>
          <Tribal_Archer>8</Tribal_Archer>
          <Tribal_Archer_Fire>10</Tribal_Archer_Fire>
          <Tribal_Hunter>8</Tribal_Hunter>
          <Tribal_Hunter_Fire>10</Tribal_Hunter_Fire>
          <Tribal_HeavyArcher>8</Tribal_HeavyArcher>
          <Tribal_Berserker>7</Tribal_Berserker>
          <Tribal_ChiefRanged>10</Tribal_ChiefRanged>
        </options>
      </li>
      <li>
        <kindDef>Settlement_RangedOnly</kindDef>
        <options>
          <Tribal_Archer>8</Tribal_Archer>
          <Tribal_Archer_Fire>10</Tribal_Archer_Fire>
          <Tribal_Hunter>8</Tribal_Hunter>
          <Tribal_Hunter_Fire>10</Tribal_Hunter_Fire>
          <Tribal_HeavyArcher>8</Tribal_HeavyArcher>
          <Tribal_ChiefRanged>10</Tribal_ChiefRanged>
        </options>
      </li>
      <li MayRequire="Ludeon.RimWorld.Ideology">
        <kindDef>Miners</kindDef>
        <commonality>1</commonality>
        <options>
          <Tribal_Miner>1</Tribal_Miner>
        </options>
      </li>
      <li MayRequire="Ludeon.RimWorld.Ideology">
        <kindDef>Hunters</kindDef>
        <commonality>1</commonality>
        <options>
          <Tribal_Hunter>1</Tribal_Hunter>
          <Tribal_Hunter_Fire>1</Tribal_Hunter_Fire>
        </options>
      </li>
      <li MayRequire="Ludeon.RimWorld.Ideology">
        <kindDef>Loggers</kindDef>
        <commonality>1</commonality>
        <options>
          <Tribal_Logger>1</Tribal_Logger>
        </options>
      </li>
      <li MayRequire="Ludeon.RimWorld.Ideology">
        <kindDef>Farmers</kindDef>
        <commonality>1</commonality>
        <options>
          <Tribal_Farmer>1</Tribal_Farmer>
        </options>
      </li>
    </pawnGroupMakers>
  </FactionDef>

  <FactionDef ParentName="OutlanderRoughBase">
    <defName>OutlanderRoughPig</defName>
    <label>rough pig union</label>
    <description>A loose union of hardy pigskin townships. They're willing to make friends, even with thinsnouts, but they're also ready to toss a bomb and gnaw human gristle when it suits them. Treat them with respect and they'll offer a trotter to an ally in need. Turn against them and the last thing you hear might be a triumphant battle squeal.</description>
    <factionIconPath>World/WorldObjects/Expanding/RoughPigUnion</factionIconPath>
    <factionNameMaker>NamerFactionOutlanderPig</factionNameMaker>
    <settlementNameMaker>NamerSettlementOutlanderPig</settlementNameMaker>
    <pawnSingular>pigskin</pawnSingular>
    <pawnsPlural>pigskins</pawnsPlural>
    <replacesFaction>OutlanderRough</replacesFaction>
    <xenotypeSet Inherit="False">
      <xenotypeChances>
        <Pigskin>999</Pigskin>
      </xenotypeChances>
    </xenotypeSet>
    <dialogFactionGreetingHostile>PigFactionGreetingHostile</dialogFactionGreetingHostile>
    <dialogFactionGreetingHostileAppreciative>PigFactionGreetingHostileAppreciative</dialogFactionGreetingHostileAppreciative>
    <dialogFactionGreetingWary>PigFactionGreetingWary</dialogFactionGreetingWary>
    <dialogFactionGreetingWarm>PigFactionGreetingWarm</dialogFactionGreetingWarm>
    <dialogMilitaryAidSent>PigMilitaryAidSent</dialogMilitaryAidSent>
    <pawnGroupMakers Inherit="False">
      <li>
        <kindDef>Combat</kindDef>
        <options>
          <Villager_Pig>5</Villager_Pig>
          <Town_Guard_Pig>10</Town_Guard_Pig>
          <Grenadier_Destructive>10</Grenadier_Destructive>
          <Mercenary_Slasher>10</Mercenary_Slasher>
          <Mercenary_Gunner_Pig>7</Mercenary_Gunner_Pig>
          <Mercenary_Elite_Pig>10</Mercenary_Elite_Pig>
          <Town_Councilman_Pig>10</Town_Councilman_Pig>
        </options>
      </li>
      <li>
        <kindDef>Peaceful</kindDef>
        <options>
          <Villager_Pig>20</Villager_Pig>
          <Villager_Child_Pig>10</Villager_Child_Pig>
          <Town_Guard_Pig>10</Town_Guard_Pig>
          <Town_Councilman_Pig>10</Town_Councilman_Pig>
        </options>
      </li>
      <li>
        <kindDef>Trader</kindDef>
        <traders>
          <Town_Trader_Pig>1</Town_Trader_Pig>
        </traders>
        <carriers>
          <Muffalo>6</Muffalo>
          <Dromedary>5</Dromedary>
          <Alpaca>2</Alpaca>
          <Elephant>1</Elephant>
        </carriers>
        <guards>
          <Villager_Pig>3</Villager_Pig>
          <Town_Guard_Pig>10</Town_Guard_Pig>
          <Grenadier_Destructive>10</Grenadier_Destructive>
          <Mercenary_Slasher>10</Mercenary_Slasher>
          <Mercenary_Gunner_Pig>7</Mercenary_Gunner_Pig>
          <Mercenary_Elite_Pig>10</Mercenary_Elite_Pig>
        </guards>
      </li>
      <li>
        <kindDef>Settlement</kindDef>
        <options>
          <Villager_Pig>10</Villager_Pig>
          <Town_Guard_Pig>10</Town_Guard_Pig>
          <Grenadier_Destructive>10</Grenadier_Destructive>
          <Mercenary_Slasher>10</Mercenary_Slasher>
          <Mercenary_Gunner_Pig>7</Mercenary_Gunner_Pig>
          <Mercenary_Elite_Pig>10</Mercenary_Elite_Pig>
          <Town_Councilman_Pig>10</Town_Councilman_Pig>
        </options>
      </li>
      <li MayRequire="Ludeon.RimWorld.Ideology">
        <kindDef>Miners</kindDef>
        <commonality>1</commonality>
        <options>
          <Miner>1</Miner>
        </options>
      </li>
      <li MayRequire="Ludeon.RimWorld.Ideology">
        <kindDef>Hunters</kindDef>
        <commonality>1</commonality>
        <options>
          <Hunter>1</Hunter>
        </options>
      </li>
      <li MayRequire="Ludeon.RimWorld.Ideology">
        <kindDef>Loggers</kindDef>
        <commonality>1</commonality>
        <options>
          <Logger>1</Logger>
        </options>
      </li>
      <li MayRequire="Ludeon.RimWorld.Ideology">
        <kindDef>Farmers</kindDef>
        <commonality>1</commonality>
        <options>
          <Farmer>1</Farmer>
        </options>
      </li>
    </pawnGroupMakers>
  </FactionDef>

  <FactionDef ParentName="PirateBandBase">
    <defName>PirateWaster</defName>
    <label>waster pirates</label>
    <description>A loose collection of violent pirate bands made up primarily of wasters - xenohumans engineered to thrive around toxins and pollution. They have little interest in building, or farming, preferring to take their sustenance from others using violence.\n\nTheir technology level depends mostly on who they've managed to steal from recently. Mostly they carry gunpowder weapons, though some prefer to stab victims at close range.</description>
    <factionIconPath>World/WorldObjects/Expanding/WasterPirates</factionIconPath>
    <factionNameMaker>NamerFactionPirateWaster</factionNameMaker>
    <settlementNameMaker>NamerSettlementPirateWaster</settlementNameMaker>
    <pawnSingular>waster</pawnSingular>
    <pawnsPlural>wasters</pawnsPlural>
    <replacesFaction>Pirate</replacesFaction>
    <allowedMemes Inherit="False">
      <li MayRequire="Ludeon.RimWorld.Ideology">PainIsVirtue</li>
    </allowedMemes>
    <xenotypeSet Inherit="False">
      <xenotypeChances>
        <Waster>0.725</Waster>
        <Hussar>0.05</Hussar>
        <Neanderthal>0.05</Neanderthal>
        <Dirtmole>0.05</Dirtmole>
        <Genie>0.025</Genie>
        <Pigskin>0.025</Pigskin>
        <Yttakin>0.025</Yttakin>
        <Impid>0.025</Impid>
      </xenotypeChances>
    </xenotypeSet>
    <pawnGroupMakers Inherit="False">
      <li>
        <!-- Normal fights, ranged with melee mix-ins -->
        <kindDef>Combat</kindDef>
        <commonality>100</commonality>
        <options>
          <Drifter>10</Drifter>
          <Thrasher>3</Thrasher>
          <Scavenger>10</Scavenger>
          <Pirate>10</Pirate>
          <Grenadier_Tox>2</Grenadier_Tox>
          <Grenadier_Destructive>0.25</Grenadier_Destructive>
          <Mercenary_GunnerTox>10</Mercenary_GunnerTox>
          <Mercenary_Gunner>5</Mercenary_Gunner>
          <Mercenary_HeavyTox>6</Mercenary_HeavyTox>
          <Mercenary_Heavy>1</Mercenary_Heavy>
          <Mercenary_SlasherTox>3</Mercenary_SlasherTox>
          <Mercenary_Slasher>1</Mercenary_Slasher>
          <Mercenary_Sniper>7</Mercenary_Sniper>
          <Mercenary_EliteTox>5</Mercenary_EliteTox>
          <Mercenary_Elite>1</Mercenary_Elite>
          <PirateBossTox>5</PirateBossTox>
        </options>
      </li>
      <li>
        <!-- Normal fights, melee-only -->
        <kindDef>Combat</kindDef>
        <commonality>30</commonality>
        <disallowedStrategies>
          <li>Siege</li>
        </disallowedStrategies>
        <options>
          <Thrasher>10</Thrasher>
          <Mercenary_SlasherTox>10</Mercenary_SlasherTox>
          <Mercenary_Slasher>1</Mercenary_Slasher>
          <PirateBossTox>5</PirateBossTox>
        </options>
      </li>
      <li>
        <!-- Normal fights, ranged only -->
        <kindDef>Combat</kindDef>
        <commonality>20</commonality>
        <options>
          <Scavenger>10</Scavenger>
          <Pirate>10</Pirate>
          <Grenadier_Tox>2</Grenadier_Tox>
          <Grenadier_Destructive>0.25</Grenadier_Destructive>
          <Mercenary_GunnerTox>10</Mercenary_GunnerTox>
          <Mercenary_Gunner>1</Mercenary_Gunner>
          <Mercenary_HeavyTox>6</Mercenary_HeavyTox>
          <Mercenary_Heavy>1</Mercenary_Heavy>
          <Mercenary_Sniper>7</Mercenary_Sniper>
          <Mercenary_EliteTox>5</Mercenary_EliteTox>
          <Mercenary_Elite>1</Mercenary_Elite>
          <PirateBossTox>5</PirateBossTox>
        </options>
      </li>
      <li>
        <!-- Normal fights, explosives with ranged mix-ins -->
        <kindDef>Combat</kindDef>
        <commonality>15</commonality>
        <disallowedStrategies>
          <li>Siege</li>
        </disallowedStrategies>
        <options>
          <Grenadier_Tox>10</Grenadier_Tox>
          <Grenadier_Destructive>1</Grenadier_Destructive>
          <Mercenary_GunnerTox>2</Mercenary_GunnerTox>
          <Mercenary_Gunner>0.25</Mercenary_Gunner>
          <Mercenary_HeavyTox>10</Mercenary_HeavyTox>
          <Mercenary_Heavy>1</Mercenary_Heavy>
          <Mercenary_EliteTox>1</Mercenary_EliteTox>
          <Mercenary_Elite>0.25</Mercenary_Elite>
          <PirateBossTox>1</PirateBossTox>
        </options>
      </li>
      <li>
        <!-- Normal fights, snipers only -->
        <kindDef>Combat</kindDef>
        <commonality>10</commonality>
        <options>
          <Mercenary_Sniper>10</Mercenary_Sniper>
        </options>
      </li>
      <li>
        <!-- Normal fights, drifters only (very rare) -->
        <kindDef>Combat</kindDef>
        <commonality>2.5</commonality>
        <maxTotalPoints>1000</maxTotalPoints>
        <options>
          <Drifter>10</Drifter>
        </options>
      </li>
      <li>
        <!-- Base defense, mainly ranged with melee mix-ins -->
        <kindDef>Settlement</kindDef>
        <options>
          <Thrasher>3</Thrasher>
          <Pirate>10</Pirate>
          <Grenadier_Tox>2</Grenadier_Tox>
          <Grenadier_Destructive>0.25</Grenadier_Destructive>
          <Mercenary_SlasherTox>3</Mercenary_SlasherTox>
          <Mercenary_Slasher>1</Mercenary_Slasher>
          <Mercenary_Sniper>10</Mercenary_Sniper>
          <Mercenary_GunnerTox>10</Mercenary_GunnerTox>
          <Mercenary_Gunner>1</Mercenary_Gunner>
          <Mercenary_EliteTox>10</Mercenary_EliteTox>
          <Mercenary_Elite>1</Mercenary_Elite>
          <PirateBossTox>10</PirateBossTox>
        </options>
      </li>
      <li MayRequire="Ludeon.RimWorld.Ideology">
        <kindDef>Miners</kindDef>
        <commonality>1</commonality>
        <options>
          <Miner>1</Miner>
        </options>
      </li>
      <li MayRequire="Ludeon.RimWorld.Ideology">
        <kindDef>Hunters</kindDef>
        <commonality>1</commonality>
        <options>
          <Hunter>1</Hunter>
        </options>
      </li>
      <li MayRequire="Ludeon.RimWorld.Ideology">
        <kindDef>Loggers</kindDef>
        <commonality>1</commonality>
        <options>
          <Logger>1</Logger>
        </options>
      </li>
      <li MayRequire="Ludeon.RimWorld.Ideology">
        <kindDef>Farmers</kindDef>
        <commonality>1</commonality>
        <options>
          <Farmer>1</Farmer>
        </options>
      </li>
    </pawnGroupMakers>
  </FactionDef>

  <FactionDef ParentName="FactionBase">
    <defName>Sanguophages</defName>
    <label>sanguophages</label>
    <pawnSingular>sanguophage</pawnSingular>
    <pawnsPlural>sanguophages</pawnsPlural>
    <categoryTag>Sanguophages</categoryTag>
    <canSiege>false</canSiege>
    <canStageAttacks>false</canStageAttacks>
    <hidden>true</hidden>
    <description>A group of ancient sanguophages.</description>
    <factionIconPath>World/WorldObjects/Expanding/SettlementTemporary</factionIconPath>
    <factionNameMaker>NamerFactionOutlander</factionNameMaker>
    <settlementNameMaker>NamerSettlementOutlander</settlementNameMaker>
    <colorSpectrum>
      <li>(0.93, 0.39, 0.32)</li>
      <li>(0.35, 0.90, 0.57)</li>
      <li>(0.25, 0.66, 0.94)</li>
      <li>(0.98, 0.75, 0.37)</li>
      <li>(0.97, 0.62, 0.52)</li>
    </colorSpectrum>
    <generateNewLeaderFromMapMembersOnly>true</generateNewLeaderFromMapMembersOnly>
    <leaderTitle>leader</leaderTitle>
    <requiredCountAtGameStart>0</requiredCountAtGameStart>
    <mustStartOneEnemy>false</mustStartOneEnemy>
    <techLevel>Spacer</techLevel>
    <basicMemberKind>Sanguophage</basicMemberKind>
    <allowedCultures>
      <li>Astropolitan</li>
    </allowedCultures>
    <requiredMemes>
      <li MayRequire="Ludeon.RimWorld.Ideology">Cannibal</li>
      <li>Bloodfeeding</li>
    </requiredMemes>
    <disallowedMemes>
      <li MayRequire="Ludeon.RimWorld.Ideology">Blindsight</li>
      <li MayRequire="Ludeon.RimWorld.Ideology">Nudism</li>
    </disallowedMemes>
    <disallowedPrecepts>
      <li MayRequire="Ludeon.RimWorld.Ideology">ApparelDesired_Strong_Subordinate</li> <!-- Never generate with any apparel precepts -->
      <li MayRequire="Ludeon.RimWorld.Ideology">ApparelDesired_Soft_Subordinate</li>
      <li MayRequire="Ludeon.RimWorld.Ideology">Nudity_Male_Mandatory</li> <!-- Never generate nudity precepts -->
      <li MayRequire="Ludeon.RimWorld.Ideology">Nudity_Female_Mandatory</li>
      <li MayRequire="Ludeon.RimWorld.Ideology">Nudity_Male_CoveringAnythingButGroinDisapproved</li>
      <li MayRequire="Ludeon.RimWorld.Ideology">Nudity_Female_CoveringAnythingButGroinDisapproved</li>
    </disallowedPrecepts>
    <settlementTexturePath>World/WorldObjects/DefaultSettlement</settlementTexturePath>
    <allowedArrivalTemperatureRange>-5~45</allowedArrivalTemperatureRange>
    <backstoryFilters>
      <li>
        <categories>
          <li>Offworld</li>
          <li>Outlander</li>
        </categories>
      </li>
    </backstoryFilters>
  </FactionDef>

</Defs>