﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingDef ParentName="MakeableDrugPillBase">
    <defName>Penoxycyline</defName>
    <label>penoxycyline</label>
    <description>A drug for preventing infections before they take hold. Blocks malaria, sleeping sickness, plague. Must be taken every five days to remain effective.\n\nThis drug only prevents new infections. It does not cure existing infections - even those that are not yet discovered.</description>
    <possessionCount>10</possessionCount>
    <descriptionHyperlinks><HediffDef>PenoxycylineHigh</HediffDef></descriptionHyperlinks>
    <graphicData>
      <texPath>Things/Item/Drug/Penoxycyline</texPath>
      <graphicClass>Graphic_StackCount</graphicClass>
    </graphicData>
    <socialPropernessMatters>false</socialPropernessMatters>
    <rotatable>false</rotatable>
    <statBases>
      <WorkToMake>600</WorkToMake>
      <MarketValue>18</MarketValue>
      <Mass>0.005</Mass>
      <Flammability>0.7</Flammability>
    </statBases>
    <techLevel>Industrial</techLevel>
    <ingestible>
      <drugCategory>Medical</drugCategory>
      <outcomeDoers>
        <li Class="IngestionOutcomeDoer_GiveHediff">
          <hediffDef>PenoxycylineHigh</hediffDef>
          <severity>1.0</severity>
        </li>
      </outcomeDoers>
    </ingestible>
    <recipeMaker>
      <researchPrerequisite>PenoxycylineProduction</researchPrerequisite>
      <recipeUsers>
        <li>DrugLab</li>
      </recipeUsers>
      <displayPriority>2100</displayPriority>
    </recipeMaker>
    <costList>
      <Neutroamine>2</Neutroamine>
    </costList>
    <comps>
      <li Class="CompProperties_Drug">
        <listOrder>1000</listOrder>
        <overdoseSeverityOffset>0.08~0.14</overdoseSeverityOffset>
      </li>
    </comps>
  </ThingDef>
  
  <HediffDef>
    <defName>PenoxycylineHigh</defName>
    <hediffClass>Hediff_High</hediffClass>
    <label>penoxycyline</label>
    <description>Penoxycyline-induced immunity to certain illnesses. This only blocks new infections; it does nothing for those who are already infected, even if their infection is dormant.</description>
    <defaultLabelColor>(1,0,0.5)</defaultLabelColor>
    <scenarioCanAdd>true</scenarioCanAdd>
    <maxSeverity>1.0</maxSeverity>
    <isBad>false</isBad>
    <comps>
      <li Class="HediffCompProperties_SeverityPerDay">
        <severityPerDay>-0.18</severityPerDay>
        <showHoursToRecover>true</showHoursToRecover>
      </li>
    </comps>
      <stages>
        <li>
          <makeImmuneTo>
            <li>Malaria</li>
            <li>SleepingSickness</li>
            <li>Plague</li>
          </makeImmuneTo>
        </li>
      </stages>
  </HediffDef>
  
</Defs>
