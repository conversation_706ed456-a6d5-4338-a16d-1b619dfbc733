﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <RulePackDef>
    <defName>DamageEvent_UnnaturalDarkness</defName>
    <include>
      <li>DamageEvent_Include</li>
    </include>
    <rulePack>
      <rulesStrings>
        <li>damage_source->[source] [attacked]</li>

        <li>source->something from within the [dark]</li>
        <li>source->something in the [dark]</li>
        <li>source->a creature in the [dark]</li>
        <li>source->an invisible creature</li>
        <li>source->a shadowy creature</li>
        <li>source->an invisible foe within the [dark]</li>

        <li>dark->darkness</li>
        <li>dark->unnatural darkness</li>
        <li>dark->inky blackness</li>
        <li>dark->shadows</li>

        <li>attacked->attacked</li>
        <li>attacked->struck</li>
        <li>attacked->hit</li>

        <li>damaged_present->wounding</li>
        <li>damaged_present->injuring</li>

        <li>destroyed_present->removing</li>
        <li>destroyed_present->crippling</li>
        <li>destroyed_present->severing</li>
      </rulesStrings>
    </rulePack>
  </RulePackDef>

</Defs>