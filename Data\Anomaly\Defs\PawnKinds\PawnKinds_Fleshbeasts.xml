<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <PawnKindDef Name="BaseFleshbeastKind" Abstract="True">
    <defaultFactionType>Entities</defaultFactionType>
    <overrideDebugActionCategory>Entity</overrideDebugActionCategory>
    <meleeAttackInfectionPathways>
      <li>EntityAttacked</li>
    </meleeAttackInfectionPathways>
  </PawnKindDef>

  <PawnKindDef ParentName="BaseFleshbeastKind">
    <defName>Bulbfreak</defName>
    <label>bulbfreak</label>
    <overrideDeathOnDownedChance>0.9</overrideDeathOnDownedChance>
    <race>Bulbfreak</race>
    <combatPower>360</combatPower>
    <lifeStages>
      <li>
        <bodyGraphicData>
          <texPath>Things/Pawn/Fleshbeast/Bulbfreak/Bulbfreak</texPath>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>3</drawSize>
        </bodyGraphicData>
      </li>
    </lifeStages>
  </PawnKindDef>

  <PawnKindDef ParentName="BaseFleshbeastKind">
    <defName>Fingerspike</defName>
    <label>fingerspike</label>
    <race>Fingerspike</race>
    <abilities>
      <li>SpikeLaunch_Fingerspike</li>
    </abilities>
    <combatPower>25</combatPower>
    <lifeStages>
      <li>
        <bodyGraphicData>
          <texPath>Things/Pawn/Fleshbeast/Fingerspike/Fingerspike</texPath>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>2</drawSize>
        </bodyGraphicData>
        <silhouetteGraphicData>
          <texPath>Things/Pawn/Fleshbeast/Fingerspike/Fingerspike_MenuIcon</texPath>
          <graphicClass>Graphic_Single</graphicClass>
          <drawSize>2</drawSize>
        </silhouetteGraphicData>
      </li>
    </lifeStages>
  </PawnKindDef>

  <PawnKindDef ParentName="BaseFleshbeastKind">
    <defName>Toughspike</defName>
    <label>toughspike</label>
    <race>Toughspike</race>
    <abilities>
      <li>SpikeLaunch_Toughspike</li>
    </abilities>
    <combatPower>70</combatPower>
    <lifeStages>
      <li>
        <bodyGraphicData>
          <texPath>Things/Pawn/Fleshbeast/Toughspike/Toughspike</texPath>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>2.2</drawSize>
        </bodyGraphicData>
        <dessicatedBodyGraphicData>
          <texPath>Things/Pawn/Fleshbeast/Toughspike/Dessicated_Toughspike</texPath>
          <drawSize>2</drawSize>
          <attachPoints>
            <li>
              <offset>(-.3, 0, .45)</offset>
              <type>PlatformRestraint0</type>
            </li>
            <li>
              <offset>(.3, 0, .45)</offset>
              <type>PlatformRestraint1</type>
            </li>
            <li>
              <offset>(.17, 0, -.43)</offset>
              <type>PlatformRestraint2</type>
            </li>
            <li>
              <offset>(-.2, 0, -.45)</offset>
              <type>PlatformRestraint3</type>
            </li>
          </attachPoints>
        </dessicatedBodyGraphicData>
        <silhouetteGraphicData>
          <texPath>Things/Pawn/Fleshbeast/Toughspike/Toughspike_MenuIcon</texPath>
          <graphicClass>Graphic_Single</graphicClass>
          <drawSize>2</drawSize>
        </silhouetteGraphicData>
      </li>
    </lifeStages>
  </PawnKindDef>

  <PawnKindDef ParentName="BaseFleshbeastKind">
    <defName>Trispike</defName>
    <label>trispike</label>
    <race>Trispike</race>
    <overrideDeathOnDownedChance>0.9</overrideDeathOnDownedChance>
    <abilities>
      <li>SpikeLaunch_Toughspike</li>
    </abilities>
    <combatPower>90</combatPower>
    <lifeStages>
      <li>
        <bodyGraphicData>
          <texPath>Things/Pawn/Fleshbeast/Trispike/Trispike</texPath>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>2</drawSize>
          <drawOffset>(0, 0, 0.1)</drawOffset>
        </bodyGraphicData>
        <silhouetteGraphicData>
          <texPath>Things/Pawn/Fleshbeast/Trispike/Trispike_MenuIcon</texPath>
          <graphicClass>Graphic_Single</graphicClass>
          <drawSize>2</drawSize>
        </silhouetteGraphicData>
      </li>
    </lifeStages>
  </PawnKindDef>

  <PawnKindDef ParentName="BaseFleshbeastKind">
    <defName>Dreadmeld</defName>
    <label>dreadmeld</label>
    <race>Dreadmeld</race>
    <forceDeathOnDowned>true</forceDeathOnDowned>
    <combatPower>650</combatPower>
    <isBoss>true</isBoss>
    <lifeStages>
      <li>
        <bodyGraphicData>
          <texPath>Things/Pawn/Fleshbeast/Dreadmeld/Dreadmeld</texPath>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>4</drawSize>
        </bodyGraphicData>
      </li>
    </lifeStages>
    <startingHediffs>
      <li>
        <def>Regeneration</def>
        <severity>1</severity>
      </li>
    </startingHediffs>
  </PawnKindDef>

</Defs>