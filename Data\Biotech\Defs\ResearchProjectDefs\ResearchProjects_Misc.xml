<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ResearchProjectDef>
    <defName>ToxifierGenerator</defName>
    <label>toxifier generator</label>
    <description>Create toxifier generators which generate electricity, but also spread pollution around themselves.</description>
    <baseCost>500</baseCost>
    <researchViewX>9.00</researchViewX>
    <researchViewY>2.70</researchViewY>
    <techLevel>Industrial</techLevel>
    <prerequisites>
      <li>Electricity</li>
    </prerequisites>
    <generalRules>
      <rulesStrings>
        <li>subject->toxifier generator</li>

        <li>subject_story->led a clean-up crew to a toxic planetoid</li>
        <li>subject_story->learned about toxifiers in the deep sump of a skyripper</li>
        <li>subject_story->researched cheap and dirty power sources for an urbcorp</li>

        <li>subject_gerund->constructing toxifier generators</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>Xenogermination</defName>
    <label>xenogenetics</label>
    <description>Create xenogerms which can be implanted in a person to genetically modify them. Extract genepacks from living people using a gene extractor so you can reimplant their genes into someone else.</description>
    <techLevel>Industrial</techLevel>
    <baseCost>1000</baseCost>
    <researchViewX>6.00</researchViewX>
    <researchViewY>5.40</researchViewY>
    <prerequisites>
      <li>Electricity</li>
    </prerequisites>
    <generalRules>
      <rulesStrings>
        <li>subject->xenogermination</li>
        <li>subject->genetic implantation</li>

        <li>subject_story->was uplifted by a glitterworld charity that focused on the genetically disadvantaged</li>
        <li>subject_story->had [ANYPAWN_possessive] genes stolen by a rogue biologist</li>
        <li>subject_story->ran a genetic transfer clinic for the glitterworld elite</li>

        <li>subject_gerund->crafting xenogerms</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>GeneProcessor</defName>
    <label>gene processor</label>
    <description>Build gene processors, which allow a gene assembler to create xenogerms of higher genetic complexity.</description>
    <techLevel>Industrial</techLevel>
    <baseCost>1500</baseCost>
    <researchViewX>11.00</researchViewX>
    <researchViewY>5.40</researchViewY>
    <requiredResearchBuilding>HiTechResearchBench</requiredResearchBuilding>
    <prerequisites>
      <li>Xenogermination</li>
      <li>MicroelectronicsBasics</li>
    </prerequisites>
    <generalRules>
      <rulesStrings>
        <li>subject->gene processors</li>

        <li>subject_story->extracted Glitterworld genes from a drifting derelict</li>
        <li>subject_story->processed an entire village to create one set of higher genes</li>
        <li>subject_story->began the slow process of uplift by implanting xenogerms into a primitive tribe</li>

        <li>subject_gerund->constructing gene processors</li>
        <li>subject_gerund->processing genes</li>

      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>Archogenetics</defName>
    <label>archogenetics</label>
    <description>Allows a gene assembler to use archite capsules to create seemingly impossible genes. Archite genes and archite capsules can be obtained from traders.</description>
    <techLevel>Industrial</techLevel>
    <baseCost>2500</baseCost>
    <researchViewX>16.00</researchViewX>
    <researchViewY>5.40</researchViewY>
    <requiredResearchBuilding>HiTechResearchBench</requiredResearchBuilding>
    <requiredResearchFacilities>
      <li>MultiAnalyzer</li>
    </requiredResearchFacilities>
    <prerequisites>
      <li>Fabrication</li>
      <li>GeneProcessor</li>
    </prerequisites>
    <generalRules>
      <rulesStrings>
        <li>subject->archogenetics</li>

        <li>subject_story->excavated archite genes from the transcendent moon of Ix</li>
        <li>subject_story->made a good living as an archite gene trader</li>
        <li>subject_story->led a research expedition to an ancient monolith and returned changed</li>

        <li>subject_gerund->assembling archite genes</li>
        <li>subject_gerund->creating seemingly impossible genes</li>

      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>Deathrest</defName>
    <label>deathrest</label>
    <description>Those with the deathrest gene must periodically undergo a special deathrest coma that lasts several days. This research project allows you to construct buildings which accelerate the deathrest coma and confer specific bonuses on the deathrester. Over time, you can build a grand deathrest temple.</description>
    <techLevel>Industrial</techLevel>
    <baseCost>1000</baseCost>
    <prerequisites>
      <li>Electricity</li>
    </prerequisites>
    <researchViewX>8.00</researchViewX>
    <researchViewY>2.70</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->deathrest</li>

        <li>subject_story->dissected corpses from a deathrest temple, finally understanding how it worked</li>
        <li>subject_story->interviewed a celebrity who never seemed to age, eventually learning her secrets</li>
        <li>subject_story->attempted to replicate the technology and fell into a coma, only to wake weeks later</li>

        <li>subject_gerund->constructing deathrest facilities</li>
        <li>subject_gerund->improving deathrest</li>

      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>FertilityProcedures</defName>
    <label>fertility procedures</label>
    <description>Perform the surgeries necessary to create a test tube embryo from a fertile man and woman, then implant the embryo in a surrogate mother or growth vat. Also, perform sterilization surgeries on men and women.</description>
    <techLevel>Industrial</techLevel>
    <baseCost>1000</baseCost>
    <prerequisites>
      <li>Electricity</li>
    </prerequisites>
    <researchViewX>8.00</researchViewX>
    <researchViewY>0.30</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->fertility procedures</li>

        <li>subject_story->helped families in the coreworlds achieve their dream of parenthood with test-tube embryos</li>
        <li>subject_story->saved the rare Callopian boomrat from extinction with dangerous hand-fertilization techniques</li>
        <li>subject_story->solved the population crisis on an urbworld with fertility interventions and growth vats</li>

        <li>subject_gerund->altering human fertility</li>

        <li>subject_gerund->improving or blocking human fertility</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>ToxGas</defName>
    <label>tox gas</label>
    <description>Produce weapons that utilize caustic tox gas.</description>
    <techLevel>Industrial</techLevel>
    <baseCost>600</baseCost>
    <prerequisites>
      <li>Machining</li>
    </prerequisites>
    <researchViewX>9.00</researchViewX>
    <researchViewY>4.80</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->gas weaponry</li>
        <li>subject->chemical warfare</li>

        <li>subject_story->dreamed of a weapon to end all wars</li>
        <li>subject_story->skipped ethics classes for extra chemistry practice</li>
        <li>subject_story->researched cloud control tech for the tyrannocracy of a deathworld</li>

        <li>subject_gerund->crafting toxic gas weapons</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>ToxFiltration</defName>
    <label>toxin filtration</label>
    <description>Craft and install bionic organs that can filter out toxins from the environment and from enemy attacks.</description>
    <techLevel>Spacer</techLevel>
    <baseCost>2000</baseCost>
    <requiredResearchBuilding>HiTechResearchBench</requiredResearchBuilding>
    <requiredResearchFacilities>
      <li>MultiAnalyzer</li>
    </requiredResearchFacilities>
    <prerequisites>
      <li>Fabrication</li>
    </prerequisites>
    <hiddenPrerequisites>
      <li>Prosthetics</li>
    </hiddenPrerequisites>
    <researchViewX>18.00</researchViewX>
    <researchViewY>4.80</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->toxin filtration</li>
        <li>subject->toxin metabolisation</li>

        <li>subject_story->helped milworld combat survivors breathe again</li>
        <li>subject_story->skipped chemistry practicals for extra ethics classes</li>
        <li>subject_story->joined a mine-corp as a filtration specialist</li>

        <li>subject_gerund->metabolising toxins</li>
        <li>subject_gerund->crafting antitoxin organs</li>

      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>GrowthVats</defName>
    <label>growth vats</label>
    <description>Construct growth vats which can gestate an embryo or accelerate a child's growth.</description>
    <techLevel>Spacer</techLevel>
    <baseCost>500</baseCost>
    <prerequisites>
      <li>MicroelectronicsBasics</li>
    </prerequisites>
    <researchViewX>12.00</researchViewX>
    <researchViewY>1.50</researchViewY>
    <requiredResearchBuilding>HiTechResearchBench</requiredResearchBuilding>
    <generalRules>
      <rulesStrings>
        <li>subject->advanced gestation</li>
        <li>subject->artificial wombs</li>

        <li>subject_story->ran a cloning facility to supply servants for the coreworlds</li>
        <li>subject_story->created a new army for a warworld junta</li>
        <li>subject_story->helped families of all backgrounds have children</li>

        <li>subject_gerund->gestating embryos in artificial wombs</li>
        <li>subject_gerund->accelerating human growth</li>
        <li>subject_gerund->constructing growth vats</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

</Defs>