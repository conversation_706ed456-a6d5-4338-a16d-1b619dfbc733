﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <DamageDef>
    <defName>ToxGas</defName>
    <label>tox gas</label>
    <canInterruptJobs>false</canInterruptJobs>
    <makesBlood>false</makesBlood>
    <defaultDamage>0</defaultDamage>
    <explosionCellFleck>BlastExtinguisher</explosionCellFleck>
    <explosionColorEdge>(0.706, 0.839, 0.09, 0.05)</explosionColorEdge>
    <harmsHealth>false</harmsHealth>
    <soundExplosion>Explosion_Smoke</soundExplosion>
    <combatLogRules>Damage_Smoke</combatLogRules>
  </DamageDef>

  <DamageDef>
    <defName>Beam</defName>
    <label>beam</label>
    <workerClass>DamageWorker_AddInjury</workerClass>
    <externalViolence>true</externalViolence>
    <deathMessage>{0} has been beamed to death.</deathMessage>
    <hediff>BeamWound</hediff>
    <harmAllLayersUntilOutside>true</harmAllLayersUntilOutside>
    <impactSoundType>Bullet</impactSoundType>
    <armorCategory>Heat</armorCategory>
    <overkillPctToDestroyPart>0~0.7</overkillPctToDestroyPart>
    <isRanged>true</isRanged>
    <makesAnimalsFlee>true</makesAnimalsFlee>
    <defaultDamage>10</defaultDamage>
    <defaultArmorPenetration>0.5</defaultArmorPenetration>
    <buildingDamageFactorImpassable>0.4</buildingDamageFactorImpassable>
    <buildingDamageFactorPassable>0.2</buildingDamageFactorPassable>
  </DamageDef>

</Defs>
