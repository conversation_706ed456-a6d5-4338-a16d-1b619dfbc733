<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingDef ParentName="BaseGun">
    <defName>Gun_BeamGraser</defName>
    <label>beam graser</label>
    <description>A high-energy gamma ray laser which fires a sweeping beam that pierces thick armor and burns targets. Because of the health risks, grasers are usually only used by mechanoids. This one was made to interface directly with a mechanoid wielder and draw from its power source.</description>
    <tradeability>None</tradeability>
    <destroyOnDrop>true</destroyOnDrop>
    <relicChance>0</relicChance>
    <graphicData>
      <texPath>Things/Item/Equipment/WeaponRanged/BeamGraser</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <statBases>
      <WorkToMake>40000</WorkToMake>
      <Mass>4</Mass>
      <AccuracyTouch>0.60</AccuracyTouch>
      <AccuracyShort>0.70</AccuracyShort>
      <AccuracyMedium>0.65</AccuracyMedium>
      <AccuracyLong>0.55</AccuracyLong>
      <RangedWeapon_Cooldown>1.70</RangedWeapon_Cooldown>
    </statBases>
    <verbs>
      <li>
        <verbClass>Verb_ShootBeam</verbClass>
        <hasStandardCommand>true</hasStandardCommand>
        <warmupTime>1.0</warmupTime>
        <range>24.9</range>
        <minRange>3.9</minRange>
        <beamFullWidthRange>6.9</beamFullWidthRange>
        <burstShotCount>5</burstShotCount>
        <showBurstShotStats>false</showBurstShotStats>
        <beamWidth>6</beamWidth>
        <ticksBetweenBurstShots>22</ticksBetweenBurstShots>
        <beamDamageDef>Beam</beamDamageDef>
        <soundCastTail>GunTail_Medium</soundCastTail>
        <muzzleFlashScale>9</muzzleFlashScale>
        <soundCastBeam>BeamGraser_Shooting</soundCastBeam>
        <beamGroundFleckDef>Fleck_BeamBurn</beamGroundFleckDef>
        <beamFleckChancePerTick>0.32</beamFleckChancePerTick>
        <beamMaxDeviation>1.5</beamMaxDeviation>
        <beamCurvature>0.6</beamCurvature>
        <beamMoteDef>Mote_GraserBeamBase</beamMoteDef>
        <beamEndEffecterDef>GraserBeam_End</beamEndEffecterDef>
        <beamChanceToStartFire>0.85</beamChanceToStartFire>
        <beamChanceToAttachFire>0.85</beamChanceToAttachFire>
        <beamFireSizeRange>0.55~0.85</beamFireSizeRange>
        <beamLineFleckDef>Fleck_BeamSpark</beamLineFleckDef>
        <beamStartOffset>0.8</beamStartOffset>
        <beamLineFleckChanceCurve>
          <points>
            <li>(0, 0)</li>
            <li>(0.65, 0.4)</li>
            <li>(1, 0.75)</li>
          </points>
        </beamLineFleckChanceCurve>
      </li>
    </verbs>
    <weaponTags>
      <li>BeamGraserGun</li>
    </weaponTags>
    <tools>
      <li>
        <label>barrel</label>
        <capacities>
          <li>Blunt</li>
          <li>Poke</li>
        </capacities>
        <power>9</power>
        <cooldownTime>2</cooldownTime>
      </li>
    </tools>
  </ThingDef>
  
  <ThingDef ParentName="BaseGun">
    <defName>Gun_ToxicNeedle</defName>
    <label>toxic needle gun</label>
    <description>A mechanoid-built gun that fires toxin-infused metal needles. It can fire long distances and will poison the target in addition to doing physical damage.</description>
    <techLevel>Spacer</techLevel>
    <graphicData>
      <texPath>Things/Item/Equipment/WeaponRanged/ToxicNeedleGun</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <tradeability>None</tradeability>
    <destroyOnDrop>true</destroyOnDrop>
    <relicChance>0</relicChance>
    <statBases>
      <MarketValue>1400</MarketValue>
      <Mass>2.6</Mass>
      <AccuracyTouch>0.60</AccuracyTouch>
      <AccuracyShort>0.80</AccuracyShort>
      <AccuracyMedium>0.90</AccuracyMedium>
      <AccuracyLong>0.92</AccuracyLong>
      <RangedWeapon_Cooldown>2.1</RangedWeapon_Cooldown>
    </statBases>
    <weaponTags>
      <li>MechanoidGunToxicNeedle</li>
    </weaponTags>
    <weaponClasses>
      <li>RangedHeavy</li>
      <li>LongShots</li>
    </weaponClasses>
    <verbs>
      <li>
        <verbClass>Verb_Shoot</verbClass>
        <hasStandardCommand>true</hasStandardCommand>
        <defaultProjectile>Bullet_ToxicNeedleGun</defaultProjectile>
        <warmupTime>2.35</warmupTime>
        <range>44.9</range>
        <soundCast>Shot_ToxicNeedleGun</soundCast>
        <soundCastTail>GunTail_Heavy</soundCastTail>
        <muzzleFlashScale>9</muzzleFlashScale>
      </li>
    </verbs>
    <tools>
      <li>
        <label>barrel</label>
        <capacities>
          <li>Blunt</li>
          <li>Poke</li>
        </capacities>
        <power>9</power>
        <cooldownTime>2.6</cooldownTime>
      </li>
    </tools>
  </ThingDef>

  <ThingDef ParentName="BaseBullet">
    <defName>Bullet_ToxicNeedleGun</defName>
    <label>toxic needle</label>
    <graphicData>
      <texPath>Things/Projectile/ToxicNeedleShot</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <projectile>
      <damageDef>BulletToxic</damageDef>
      <damageAmountBase>25</damageAmountBase>
      <armorPenetrationBase>0.35</armorPenetrationBase>
      <stoppingPower>1.5</stoppingPower>
      <speed>90</speed>
    </projectile>
  </ThingDef>

  <!-- Similar to needle launcher with lower range and mass -->
  <ThingDef ParentName="NeedleGunBase">
    <defName>Gun_NeedleLauncher</defName>
    <label>needle launcher</label>
    <description>This mechanoid weapon is a compact version of the needle gun. Named after its needle-like projectiles, it fires single shots with great accuracy. The needle launcher has less range than its counterpart but is lightweight, allowing it to be wielded by fast-moving mechs.</description>
    <statBases>
      <Mass>2.6</Mass>
    </statBases>
    <graphicData>
      <texPath>Things/Item/Equipment/WeaponRanged/NeedleLauncher</texPath>
    </graphicData>
    <weaponTags Inherit="False">
      <li>MechanoidGunNeedleLauncher</li>
    </weaponTags>
    <verbs Inherit="False">
      <li>
        <verbClass>Verb_Shoot</verbClass>
        <hasStandardCommand>true</hasStandardCommand>
        <defaultProjectile>Bullet_NeedleGun</defaultProjectile>
        <warmupTime>2.5</warmupTime>
        <range>24.9</range>
        <soundCast>Shot_NeedleGun</soundCast>
        <soundCastTail>GunTail_Heavy</soundCastTail>
        <muzzleFlashScale>9</muzzleFlashScale>
      </li>
    </verbs>
  </ThingDef>

</Defs>