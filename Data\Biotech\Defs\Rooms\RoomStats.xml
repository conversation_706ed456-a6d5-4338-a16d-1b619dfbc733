<?xml version="1.0" encoding="utf-8" ?>

<Defs>

  <RoomStatDef>
    <defName>AssemblySpeedFactor</defName>
    <label>work speed factor</label>
    <workerClass>RoomStatWorker_FromStatByCurve</workerClass>
    <updatePriority>0</updatePriority>
    <isHidden>true</isHidden>
    <roomlessScore>0.75</roomlessScore>
    <inputStat>Cleanliness</inputStat>
    <curve>
      <points>
        <li>(-5.0, 0.75 )</li>
        <li>(-2.5, 0.85 )</li>
        <li>( 0.0, 1.00 )</li>
        <li>( 1.0, 1.15 )</li>
      </points>
    </curve>
  </RoomStatDef>

</Defs>