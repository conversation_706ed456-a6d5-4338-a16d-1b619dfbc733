<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <StatDef>
    <defName>BabyPlayGainFactor</defName>
    <label>baby play power</label>
    <description>How effectively this object can be used to fulfill a baby's need for play.</description>
    <category>Building</category>
    <defaultBaseValue>1</defaultBaseValue>
    <minValue>0.3</minValue>
    <toStringStyle>PercentZero</toStringStyle>
    <showIfUndefined>false</showIfUndefined>
    <displayPriorityInCategory>4020</displayPriorityInCategory>
  </StatDef>

  <StatDef>
    <defName>GeneticComplexityIncrease</defName>
    <label>genetic complexity increase</label>
    <description>When placed near a gene assembler, this building increases the maximum genetic complexity that can be assembled at once.</description>
    <category>Building</category>
    <defaultBaseValue>0</defaultBaseValue>
    <minValue>0</minValue>
    <showOnDefaultValue>false</showOnDefaultValue>
    <showIfUndefined>false</showIfUndefined>
    <showOnPawns>false</showOnPawns>
    <toStringNumberSense>Offset</toStringNumberSense>
    <displayPriorityInCategory>4200</displayPriorityInCategory>
    <alwaysHide>true</alwaysHide>
  </StatDef>

  <!-- Similar to ResearchSpeedFactor -->
  <StatDef>
    <defName>AssemblySpeedFactor</defName>
    <label>work speed factor</label>
    <description>The speed at which people assemble genes here is multiplied by this value.</description>
    <category>Building</category>
    <defaultBaseValue>1</defaultBaseValue>
    <minValue>0.25</minValue>
    <toStringStyle>PercentZero</toStringStyle>
    <showIfUndefined>false</showIfUndefined>
    <parts>
      <li Class="StatPart_Outdoors">
        <factorIndoors>1.00</factorIndoors>
        <factorOutdoors>0.75</factorOutdoors>
      </li>
      <li Class="StatPart_RoomStat">
        <roomStat>AssemblySpeedFactor</roomStat>
        <customLabel>Room cleanliness</customLabel>
      </li>
    </parts>
    <displayPriorityInCategory>1000</displayPriorityInCategory>
  </StatDef>

  <StatDef>
    <defName>BirthRitualQualityOffset</defName>
    <label>birth quality offset</label>
    <description>This alters the quality of a birth when this is used as a birth place. Birth quality affects the chance of the baby being born healthy.</description>
    <category>Building</category>
    <defaultBaseValue>0</defaultBaseValue>
    <hideAtValue>0</hideAtValue>
    <minValue>0</minValue>
    <toStringStyle>FloatOne</toStringStyle>
    <displayPriorityInCategory>4110</displayPriorityInCategory>
  </StatDef>

</Defs>