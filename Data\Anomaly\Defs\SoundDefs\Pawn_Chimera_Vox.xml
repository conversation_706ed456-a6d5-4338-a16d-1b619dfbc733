<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <SoundDef>
    <defName>Pawn_Chimera_SpeedupRoar</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <name>RageSpeed</name>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Chimera/RageSpeed</clipFolderPath>
          </li>
        </grains>
        <volumeRange>20~25</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <distRange>0~70</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Chimera_Call</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Chimera/Call</clipFolderPath>
          </li>
        </grains>
        <volumeRange>10~18</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <distRange>0~70</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Chimera_Death</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Chimera/Death</clipFolderPath>
          </li>
        </grains>
        <volumeRange>20~25</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <distRange>5~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Chimera_Wounded</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Chimera/Pain</clipFolderPath>
          </li>
        </grains>
        <volumeRange>10~18</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <distRange>5~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>
</Defs>