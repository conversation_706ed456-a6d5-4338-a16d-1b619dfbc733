<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <PawnKindDef ParentName="MercenaryGunnerBase">
    <defName>Mercenary_GunnerTox</defName>
    <label>waster gunner</label>
    <initialResistanceRange>6~10</initialResistanceRange>
    <defaultFactionType>PirateWaster</defaultFactionType>
    <apparelTags Inherit="False">
      <li>IndustrialBasic</li>
      <li>IndustrialAdvanced</li>
      <li>IndustrialMilitaryBasic</li>
      <li>IndustrialMilitaryAdvanced</li>
      <li>BeltDefenseTox</li>
    </apparelTags>
  </PawnKindDef>

  <PawnKindDef ParentName="MercenarySlasherBase">
    <defName>Mercenary_SlasherTox</defName>
    <label>waster slasher</label>
    <initialResistanceRange>10~16</initialResistanceRange>
    <defaultFactionType>PirateWaster</defaultFactionType>
    <apparelRequired Inherit="False">
      <li>Apparel_PackTox</li>
    </apparelRequired>
  </PawnKindDef>

  <PawnKindDef ParentName="MercenaryEliteBase">
    <defName>Mercenary_EliteTox</defName>
    <label>elite waster</label>
    <initialResistanceRange>15~23</initialResistanceRange>
    <defaultFactionType>PirateWaster</defaultFactionType>
    <apparelTags Inherit="False">
      <li>IndustrialBasic</li>
      <li>IndustrialAdvanced</li>
      <li>IndustrialMilitaryBasic</li>
      <li>IndustrialMilitaryAdvanced</li>
      <li>SpacerMilitary</li>
      <li>BeltDefenseTox</li>
    </apparelTags>
  </PawnKindDef>

  <PawnKindDef ParentName="GrenadierBase">
    <defName>Grenadier_Tox</defName>
    <label>waster grenadier</label>
    <combatPower>55</combatPower>
    <defaultFactionType>PirateWaster</defaultFactionType>
    <apparelTags Inherit="False">
      <li>IndustrialBasic</li>
      <li>IndustrialAdvanced</li>
      <li>IndustrialMilitaryBasic</li>
    </apparelTags>
    <weaponTags>
      <li>GrenadeTox</li>
    </weaponTags>
    <initialResistanceRange>15~24</initialResistanceRange>
  </PawnKindDef>

  <PawnKindDef ParentName="MercenaryMidTierBase">
    <defName>Mercenary_HeavyTox</defName>
    <label>heavy waster</label>
    <combatPower>140</combatPower>
    <defaultFactionType>PirateWaster</defaultFactionType>
    <gearHealthRange>0.7~3.2</gearHealthRange>
    <apparelTags Inherit="False">
      <li>IndustrialBasic</li>
      <li>IndustrialAdvanced</li>
      <li>IndustrialMilitaryBasic</li>
      <li>IndustrialMilitaryAdvanced</li>
    </apparelTags>
    <apparelMoney>200~350</apparelMoney>
    <apparelAllowHeadgearChance>0.5</apparelAllowHeadgearChance>
    <weaponMoney>1200~1200</weaponMoney>
    <weaponTags>
      <li>HeavyTox</li>
    </weaponTags>
    <initialResistanceRange>14~23</initialResistanceRange>
  </PawnKindDef>

  <PawnKindDef ParentName="MercenaryEliteTierBase">
    <defName>PirateBossTox</defName>
    <label>waster boss</label>
    <labelPlural>waster bosses</labelPlural>
    <factionLeader>true</factionLeader>
    <canBeSapper>true</canBeSapper>
    <initialResistanceRange>17~27</initialResistanceRange>
    <defaultFactionType>PirateWaster</defaultFactionType>
    <apparelTags Inherit="False">
      <li>IndustrialBasic</li>
      <li>IndustrialAdvanced</li>
      <li>IndustrialMilitaryBasic</li>
      <li>IndustrialMilitaryAdvanced</li>
      <li>SpacerMilitary</li>
    </apparelTags>
  </PawnKindDef>

</Defs>