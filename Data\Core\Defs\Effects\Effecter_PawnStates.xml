﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <EffecterDef>
    <defName>Drunk</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_DrifterEmoteChance</subEffecterClass>
        <scale>0.5~0.7</scale>
        <chancePerTick>0.03</chancePerTick>
        <positionRadius>0.3</positionRadius>
        <moteDef>Mote_DrunkBubble</moteDef>
        <speed>0.18~0.18</speed>
        <angle>10~80</angle>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Berserk</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_DrifterEmoteChance</subEffecterClass>
        <scale>0.5~0.7</scale>
        <chancePerTick>0.02</chancePerTick>
        <positionRadius>0.3</positionRadius>
        <moteDef>Mote_BerserkBit</moteDef>
        <speed>0.18~0.18</speed>
        <angle>10~80</angle>
      </li>
    </children>
  </EffecterDef>
  
  
</Defs>
