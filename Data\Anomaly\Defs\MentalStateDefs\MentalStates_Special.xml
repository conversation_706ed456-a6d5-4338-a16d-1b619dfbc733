<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <MentalBreakDef>
    <defName>HumanityBreak</defName>
    <label>humanity break</label>
    <intensity>Major</intensity>
    <baseCommonality>1</baseCommonality>
    <requiredPrecept>Inhumanizing_Required</requiredPrecept>
    <workerClass>MentalBreakWorker_HumanityBreak</workerClass>
    <mentalState>HumanityBreak</mentalState>
  </MentalBreakDef>

  <MentalStateDef ParentName="BaseMentalState">
    <defName>HumanityBreak</defName>
    <label>humanity break</label>
    <stateClass>MentalState_HumanityBreak</stateClass>
    <category>Misc</category>
    <nameColor>(0.65, 0.9, 0.93)</nameColor>
    <baseInspectLine>Mental state: Humanity break</baseInspectLine>
    <beginLetter>{PAWN_nameDef} has relinquished {PAWN_possessive} remaining ties to humanity and embraced {PAWN_possessive} ideology’s worship of the void. {PAWN_possessive} concern for health, family, friendship, and love have been broken.\n\n{PAWN_nameDef} is now inhumanized, and will suffer a breakdown as {PAWN_pronoun} tries to grapple with {PAWN_possessive} new mental alignment.</beginLetter>
    <blockNormalThoughts>true</blockNormalThoughts>
    <downedCanDo>true</downedCanDo>
    <recoverFromDowned>false</recoverFromDowned>
    <recoverFromCollapsingExhausted>false</recoverFromCollapsingExhausted>
    <inCaravanCanDo>true</inCaravanCanDo>
    <unspawnedNotInCaravanCanDo>true</unspawnedNotInCaravanCanDo>
    <blockInteractionInitiationExcept>
      <li>Insult</li>
      <li>Slight</li>
    </blockInteractionInitiationExcept>
    <blockInteractionRecipientExcept>
      <li>Insult</li>
      <li>Slight</li>
    </blockInteractionRecipientExcept>
  </MentalStateDef>

  <MentalBreakDef>
    <defName>BerserkShort</defName>
    <label>berserk</label>
    <mentalState>BerserkShort</mentalState>
    <baseCommonality>0</baseCommonality>
    <intensity>Extreme</intensity>
  </MentalBreakDef>
  
  <MentalStateDef ParentName="Berserk" Name="BerserkShort">
    <defName>BerserkShort</defName>
    <minTicksBeforeRecovery>10000</minTicksBeforeRecovery>
    <maxTicksBeforeRecovery>15000</maxTicksBeforeRecovery>
    <recoveryMtbDays>0.166</recoveryMtbDays>
  </MentalStateDef>
  
  <MentalStateDef ParentName="BaseMentalState" Name="ManhunterBloodRain">
    <defName>ManhunterBloodRain</defName>
    <stateClass>MentalState_Manhunter</stateClass>
    <label>manhunter</label>
    <minTicksBeforeRecovery>15000</minTicksBeforeRecovery>
    <maxTicksBeforeRecovery>30000</maxTicksBeforeRecovery>
    <category>Aggro</category>
    <nameColor>(0.9,0.2,0.5)</nameColor>
    <recoveryMessage>{0}'s manhunting bloodlust has come to an end.</recoveryMessage>
    <baseInspectLine>Maddened: Manhunter</baseInspectLine>
    <inCaravanCanDo>true</inCaravanCanDo>
    <recoverFromSleep>false</recoverFromSleep>
    <recoverFromDowned>false</recoverFromDowned>
    <recoverFromCollapsingExhausted>false</recoverFromCollapsingExhausted>
  </MentalStateDef>
  
  <MentalBreakDef>
    <defName>CubeSculpting</defName>
    <label>cube sculpting</label>
    <mentalState>CubeSculpting</mentalState>
    <baseCommonality>0</baseCommonality>
    <intensity>Minor</intensity>
  </MentalBreakDef>
  
  <MentalStateDef ParentName="BaseMentalState">
    <defName>CubeSculpting</defName>
    <label>cube sculpting</label> 
    <stateClass>MentalState_CubeSculpting</stateClass>
    <minTicksBeforeRecovery>20000</minTicksBeforeRecovery>
    <maxTicksBeforeRecovery>35000</maxTicksBeforeRecovery>
    <blockNormalThoughts>true</blockNormalThoughts>
    <nameColor>(0.65, 0.9, 0.93)</nameColor>
    <category>Misc</category>
    <baseInspectLine>Mental state: Cube sculpting</baseInspectLine> 
    <beginLetterLabel>cube obsession</beginLetterLabel> 
    <beginLetter>{PAWN_nameDef}'s obsession with the cube has caused {PAWN_objective} to fall into a daze. {PAWN_pronoun} is going to build a sculpture to release the pressure growing in {PAWN_possessive} mind.</beginLetter>
    <beginLetterDef>NeutralEvent</beginLetterDef>
    <recoveryMessage>{PAWN_nameDef} has finished {PAWN_possessive} cube structure.</recoveryMessage> 
    <recoverFromSleep>false</recoverFromSleep>
  </MentalStateDef>

</Defs>