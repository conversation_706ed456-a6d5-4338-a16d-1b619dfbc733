<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <MentalBreakDef Name="BaseAnomalyMentalBreak" Abstract="True">
    <anomalousBreak>true</anomalousBreak>
    <baseCommonality>1</baseCommonality>
  </MentalBreakDef>

  <MentalStateDef ParentName="BaseMentalState" Name="BaseAnomalyMentalState" Abstract="True">
    <category>Misc</category>
    <colonistsOnly>true</colonistsOnly>
    <moodRecoveryThought>VoidCatharsis</moodRecoveryThought>
    <beginLetterDef>NegativeEvent</beginLetterDef>
  </MentalStateDef>

  <MentalBreakDef ParentName="BaseAnomalyMentalBreak">
    <defName>DarkVisions</defName>
    <label>dark visions</label>
    <intensity>Minor</intensity>
    <mentalState>DarkVisions</mentalState>
  </MentalBreakDef>
  <MentalStateDef ParentName="BaseAnomalyMentalState">
    <defName>DarkVisions</defName>
    <label>dark visions</label>
    <stateClass>MentalState_InhumanRambling</stateClass>
    <minTicksBeforeRecovery>30000</minTicksBeforeRecovery>
    <maxTicksBeforeRecovery>45000</maxTicksBeforeRecovery>
    <recoveryMtbDays>0.166</recoveryMtbDays>
    <recoverFromSleep>true</recoverFromSleep>
    <blockNormalThoughts>true</blockNormalThoughts>
    <nameColor>(0.65, 0.9, 0.93)</nameColor>
    <ticksBetweenRamblingRange>1200~3600</ticksBetweenRamblingRange> <!-- 20~60 seconds -->
    <recoveryMessage>{PAWN_nameDef}'s dark visions have stopped.</recoveryMessage>
    <beginLetterLabel>dark visions</beginLetterLabel>
    <beginLetter>{PAWN_nameDef} has become overwhelmed with horrible visions, rambling about things that only {PAWN_pronoun} can see. {PAWN_pronoun} will snap out of it in a few hours.</beginLetter>
    <baseInspectLine>Mental state: Dark visions</baseInspectLine>
    <allowBeatfire>true</allowBeatfire>
    <inCaravanCanDo>true</inCaravanCanDo>
    <escapingPrisonersIgnore>true</escapingPrisonersIgnore>
  </MentalStateDef>

  <MentalBreakDef ParentName="BaseAnomalyMentalBreak">
    <defName>InsaneRamblings</defName>
    <label>insane ramblings</label>
    <intensity>Major</intensity>
    <mentalState>InsaneRamblings</mentalState>
  </MentalBreakDef>
  <MentalStateDef ParentName="BaseAnomalyMentalState">
    <defName>InsaneRamblings</defName>
    <label>insane ramblings</label>
    <stateClass>MentalState_InhumanRambling</stateClass>
    <minTicksBeforeRecovery>40000</minTicksBeforeRecovery>
    <maxTicksBeforeRecovery>60000</maxTicksBeforeRecovery>
    <recoveryMtbDays>0.166</recoveryMtbDays>
    <recoverFromSleep>true</recoverFromSleep>
    <blockNormalThoughts>true</blockNormalThoughts>
    <nameColor>(0.65, 0.9, 0.93)</nameColor>
    <ticksBetweenRamblingRange>600~900</ticksBetweenRamblingRange> <!-- 10~15 seconds -->
    <recoveryMessage>{PAWN_nameDef} has stopped rambling.</recoveryMessage>
    <beginLetterLabel>insane ramblings</beginLetterLabel>
    <beginLetter>{PAWN_nameDef} has become completely untethered from reality, ranting about impossible subjects. {PAWN_pronoun} will snap out of it in a few hours.</beginLetter>
    <baseInspectLine>Mental state: Insane ramblings</baseInspectLine>
    <allowBeatfire>true</allowBeatfire>
    <inCaravanCanDo>true</inCaravanCanDo>
    <escapingPrisonersIgnore>true</escapingPrisonersIgnore>
  </MentalStateDef>

  <MentalBreakDef ParentName="BaseAnomalyMentalBreak">
    <defName>EntityKiller</defName>
    <label>entity slayer</label>
    <intensity>Major</intensity>
    <mentalState>EntityKiller</mentalState>
  </MentalBreakDef>
  <MentalStateDef ParentName="BaseAnomalyMentalState">
    <defName>EntityKiller</defName>
    <label>entity slayer</label>
    <stateClass>MentalState_EntityKiller</stateClass>
    <workerClass>MentalStateWorker_EntityKiller</workerClass>
    <category>Aggro</category>
    <blockInteractionInitiationExcept />
    <blockInteractionRecipientExcept />
    <blockRandomInteraction>true</blockRandomInteraction>
    <colonistsOnly>true</colonistsOnly>
    <prisonersCanDo>false</prisonersCanDo>
    <minTicksBeforeRecovery>8000</minTicksBeforeRecovery>
    <maxTicksBeforeRecovery>12000</maxTicksBeforeRecovery>
    <recoveryMtbDays>0.033</recoveryMtbDays>
    <recoverFromSleep>true</recoverFromSleep>
    <nameColor>(0.88,0.48,0.16)</nameColor>
    <blockNormalThoughts>true</blockNormalThoughts>
    <recoveryMessage>{0} is no longer slaughtering random entities.</recoveryMessage>
    <beginLetter>{0} has snapped. {PAWN_pronoun} can no longer tolerate holding such dangerous entities in captivity and intends to destroy as many as {PAWN_pronoun} can.</beginLetter>
    <beginLetterDef>ThreatSmall</beginLetterDef>
    <baseInspectLine>Mental state: Entity slayer</baseInspectLine>
    <allowBeatfire>true</allowBeatfire>
  </MentalStateDef>
  
  <MentalBreakDef ParentName="BaseAnomalyMentalBreak">
    <defName>EntityLiberator</defName>
    <label>entity liberator</label>
    <intensity>Extreme</intensity>
    <mentalState>EntityLiberator</mentalState>
  </MentalBreakDef>
  <MentalStateDef ParentName="BaseAnomalyMentalState">
    <defName>EntityLiberator</defName>
    <label>entity liberator</label>
    <stateClass>MentalState_EntityLiberator</stateClass>
    <workerClass>MentalStateWorker_EntityLiberator</workerClass>
    <category>Malicious</category>
    <blockInteractionInitiationExcept />
    <blockInteractionRecipientExcept />
    <blockRandomInteraction>true</blockRandomInteraction>
    <colonistsOnly>true</colonistsOnly>
    <minTicksBeforeRecovery>40000</minTicksBeforeRecovery>
    <maxTicksBeforeRecovery>60000</maxTicksBeforeRecovery>
    <recoveryMtbDays>0.166</recoveryMtbDays>
    <recoverFromSleep>true</recoverFromSleep>
    <nameColor>(0.8,0.36,1)</nameColor>
    <blockNormalThoughts>true</blockNormalThoughts>
    <recoveryMessage>{0} is no longer freeing entities.</recoveryMessage>
    <beginLetter>{0} has become suddenly allured by a captive entity and is convinced it must be freed.</beginLetter>
    <beginLetterDef>ThreatSmall</beginLetterDef>
    <baseInspectLine>Mental state: Entity liberator</baseInspectLine>
    <allowBeatfire>true</allowBeatfire>
  </MentalStateDef>
  
  <MentalBreakDef ParentName="BaseAnomalyMentalBreak">
    <defName>TerrifyingHallucinations</defName>
    <label>terrifying hallucinations</label>
    <intensity>Extreme</intensity>
    <mentalState>TerrifyingHallucinations</mentalState>
  </MentalBreakDef>
  <MentalStateDef ParentName="BaseAnomalyMentalState">
    <defName>TerrifyingHallucinations</defName>
    <label>terrifying hallucinations</label>
    <stateEffecter>TerrifyingHallucinations</stateEffecter>
    <stateClass>MentalState_InhumanRambling</stateClass>
    <minTicksBeforeRecovery>40000</minTicksBeforeRecovery>
    <maxTicksBeforeRecovery>60000</maxTicksBeforeRecovery>
    <recoveryMtbDays>0.166</recoveryMtbDays>
    <recoverFromSleep>true</recoverFromSleep>
    <blockNormalThoughts>true</blockNormalThoughts>
    <nameColor>(0.65, 0.9, 0.93)</nameColor>
    <ticksBetweenRamblingRange>600~1800</ticksBetweenRamblingRange> <!-- 10~30 seconds -->
    <recoveryMessage>{0} is no longer hallucinating.</recoveryMessage>
    <beginLetterLabel>terrifying hallucinations</beginLetterLabel>
    <beginLetter>Vivid  hallucinations swirl around {0}, twisting and distorting allies into horrifying abominations. {PAWN_pronoun} will flee from anyone {PAWN_pronoun} perceives as a threat.</beginLetter>
    <baseInspectLine>Mental state: Terrifying hallucinations</baseInspectLine>
    <allowBeatfire>true</allowBeatfire>
    <inCaravanCanDo>true</inCaravanCanDo>
    <escapingPrisonersIgnore>true</escapingPrisonersIgnore>
  </MentalStateDef>
  
</Defs>