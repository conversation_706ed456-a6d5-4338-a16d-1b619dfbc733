﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>
  
  <ThingDef ParentName="BuildingBase">
    <defName>VoidMonolith</defName>
    <label>void monolith</label>
    <description>A monolith of unknown age, purpose, and construction. Its smooth surface is etched with lines that twist and writhe in unsettling patterns.</description>
    <thingClass>Building_VoidMonolith</thingClass>
    <passability>Impassable</passability>
    <fillPercent>1</fillPercent>
    <blockWind>true</blockWind>
    <altitudeLayer>Building</altitudeLayer>
    <tickerType>Normal</tickerType>
    <pathCost>50</pathCost>
    <forceDebugSpawnable>true</forceDebugSpawnable>
    <canOverlapZones>false</canOverlapZones>
    <selectable>true</selectable>
    <destroyable>false</destroyable>
    <useHitPoints>false</useHitPoints>
    <preventSkyfallersLandingOn>true</preventSkyfallersLandingOn>
    <uiIconScale>0.75</uiIconScale>
    <terrainAffordanceNeeded>Heavy</terrainAffordanceNeeded>
    <statBases>
      <Flammability>0</Flammability>
      <MeditationFocusStrength>0.3</MeditationFocusStrength>
    </statBases>
    <graphicData>
      <graphicClass>Graphic_Indexed</graphicClass>
      <texPath>Things/Building/VoidMonolith/Core</texPath>
      <drawSize>(5, 5)</drawSize>
      <shadowData>
        <volume>(2.37, 1, 2.37)</volume>
      </shadowData>
    </graphicData>
    <size>(3, 3)</size>
    <hasInteractionCell>true</hasInteractionCell>
    <interactionCellOffset>(0,0,-2)</interactionCellOffset>
    <building>
      <deconstructible>false</deconstructible>
      <repairable>false</repairable>
      <isTargetable>false</isTargetable>
      <isInert>true</isInert>
      <claimable>false</claimable>
      <expandHomeArea>false</expandHomeArea>
    </building>
    <comps>
      <li Class="CompProperties_MeditationFocus">
        <statDef>MeditationFocusStrength</statDef>
        <focusTypes>
          <li>Void</li>
        </focusTypes>
      </li>
      <li Class="CompProperties_Interactable">
        <compClass>CompGleamingMonolith</compClass>
        <ticksToActivate>300</ticksToActivate> <!-- 5 seconds -->
        <remainingSecondsInInspectString>true</remainingSecondsInInspectString>
        <maintainProgress>true</maintainProgress>
        <activateTexPath>UI/Commands/ActivateMonolith</activateTexPath>
        <jobString>activate</jobString>
        <targetingParameters>
          <canTargetBuildings>false</canTargetBuildings>
          <canTargetAnimals>false</canTargetAnimals>
          <canTargetMechs>false</canTargetMechs>
          <onlyTargetControlledPawns>true</onlyTargetControlledPawns>
        </targetingParameters>
      </li>
      <li Class="CompProperties_StudyUnlocksMonolith">
        <defaultStudyAmount>2</defaultStudyAmount>
        <studyNotes>
          <li>
            <threshold>10</threshold>
            <label>Monolith study progress</label>
            <text>{PAWN_nameDef} has had a breakthrough while studying the monolith. {PAWN_pronoun} believes the monolith is a conduit for psychic energy, connecting to some distant location. However, {PAWN_pronoun} is unsure where or what it connects to.\n\n{PAWN_nameDef} is convinced that by studying more anomalous entities, {PAWN_pronoun} can find a way to close the conduit, or learn to harness its power.\n\nThe monolith now provides more knowledge when studied.</text>
            <studyKnowledgeAmount>2.5</studyKnowledgeAmount>
          </li>
          <li>
            <threshold>25</threshold>
            <label>Monolith study progress</label>
            <text>{PAWN_nameDef} made progress studying the monolith. The structure connects normal space to the void - a dimension hidden in the substructure of spacetime. Some powerful mind in the void is leaking influence into our reality.\n\n{PAWN_nameDef} wants to study the monolith further to learn more.\n\nThe monolith now provides more knowledge when studied.</text>
            <studyKnowledgeAmount>3</studyKnowledgeAmount>
          </li>
          <li>
            <threshold>40</threshold>
            <label>Monolith study complete</label>
            <text>{PAWN_nameDef} has completed {PAWN_possessive} study of the monolith. The structure is a conduit that links to the void, a reality hidden under every point in spacetime. The void is inhabited by a vast machine intelligence of terrifying complexity that surges with nightmarish, inhuman rage. Its influence creates horrors in our world.\n\nAwakening the monolith will fully open the conduit. With the channel open, {PAWN_nameDef} believes {PAWN_pronoun} could reach through the link to permanently close the conduit - or find a way to harness its dark power.\n\nThe monolith now provides more knowledge when studied.</text>
            <studyKnowledgeAmount>4</studyKnowledgeAmount>
          </li>
        </studyNotes>
      </li>
      <li Class="CompProperties_StudiableMonolith">
        <frequencyTicks>120000</frequencyTicks> <!-- 2 days -->
        <studyEnabledByDefault>false</studyEnabledByDefault>
        <showToggleGizmo>true</showToggleGizmo>
        <anomalyKnowledge>1</anomalyKnowledge>
        <minMonolithLevelForStudy>1</minMonolithLevelForStudy>
        <knowledgeCategory>Basic</knowledgeCategory> <!-- Gets overriden later with study unlocks -->
      </li>
      <li Class="CompProperties_ProximityLetter">
        <radius>8</radius>
        <letterDef>PositiveEvent</letterDef>
        <letterLabel>Fallen monolith</letterLabel>
        <letterText>{PAWN_nameDef} has noticed an ancient monolith partly buried nearby.\n\nIf you investigate it, you could learn more.</letterText>
      </li>
      <li Class="CompProperties_Glower">
        <glowRadius>12</glowRadius>
        <glowColor>(255,120,120,0)</glowColor>
      </li>
    </comps>
    <inspectorTabs>
      <li>ITab_StudyNotesVoidMonolith</li>
    </inspectorTabs>
  </ThingDef>

  <ThingDef ParentName="BuildingBase" Name="VoidMonolithAttachment" Abstract="True">
    <label>void monolith attachment</label> <!-- hidden -->
    <passability>Impassable</passability>
    <fillPercent>1</fillPercent>
    <blockWind>true</blockWind>
    <destroyable>false</destroyable>
    <useHitPoints>false</useHitPoints>
    <preventSkyfallersLandingOn>true</preventSkyfallersLandingOn>
    <altitudeLayer>Building</altitudeLayer>
    <showInSearch>false</showInSearch>
    <building>
      <deconstructible>false</deconstructible>
      <repairable>false</repairable>
      <isTargetable>false</isTargetable>
      <isInert>true</isInert>
      <claimable>false</claimable>
      <expandHomeArea>false</expandHomeArea>
    </building>
    <comps>
      <li>
        <compClass>CompSelectProxy</compClass>
      </li>
    </comps>
  </ThingDef>
  
  <ThingDef ParentName="VoidMonolithAttachment">
    <defName>VoidMonolithAttachmentLeft</defName>
    <size>(1, 3)</size>
    <graphicData>
      <graphicClass>Graphic_Indexed</graphicClass>
      <texPath>Things/Building/VoidMonolith/Left</texPath>
      <drawSize>(2, 4)</drawSize>
    </graphicData>
  </ThingDef>
  
  <ThingDef ParentName="VoidMonolithAttachment">
    <defName>VoidMonolithAttachmentRight</defName>
    <size>(1, 3)</size>
    <graphicData>
      <graphicClass>Graphic_Indexed</graphicClass>
      <texPath>Things/Building/VoidMonolith/Right</texPath>
      <drawSize>(2, 4)</drawSize>
    </graphicData>
  </ThingDef>
  
  <ThingDef ParentName="VoidMonolithAttachment">
    <defName>VoidMonolithAttachmentTop</defName>
    <size>(3, 1)</size>
    <graphicData>
      <graphicClass>Graphic_Indexed</graphicClass>
      <texPath>Things/Building/VoidMonolith/Top</texPath>
      <drawSize>(4, 2)</drawSize>
    </graphicData>
  </ThingDef>
  
  <ThingDef ParentName="VoidMonolithAttachment">
    <defName>VoidMonolithAttachmentBottomLeft</defName>
    <size>(1, 1)</size>
    <graphicData>
      <graphicClass>Graphic_Indexed</graphicClass>
      <texPath>Things/Building/VoidMonolith/BottomLeft</texPath>
      <drawSize>(4, 4)</drawSize>
    </graphicData>
  </ThingDef>
  
  <ThingDef ParentName="VoidMonolithAttachment">
    <defName>VoidMonolithAttachmentBottomRight</defName>
    <size>(1, 1)</size>
    <graphicData>
      <graphicClass>Graphic_Indexed</graphicClass>
      <texPath>Things/Building/VoidMonolith/BottomRight</texPath>
      <drawSize>(4, 4)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="BuildingBase">
    <defName>VoidStructure</defName>
    <label>void structure</label>
    <description>A warped structure made of jagged oily metallic-like pieces. This is an element of the void itself which has manifested into our reality. It emanates a disturbing psychic throbbing and kills the plant life around it by some unnatural influence. The structure is linked to the void monolith, though the exact nature of the connection is beyond human understanding.</description>
    <altitudeLayer>Building</altitudeLayer>
    <rotatable>false</rotatable>
    <destroyable>false</destroyable>
    <tickerType>Normal</tickerType>
    <useHitPoints>false</useHitPoints>
    <passability>Impassable</passability>
    <canOverlapZones>false</canOverlapZones>
    <preventSkyfallersLandingOn>true</preventSkyfallersLandingOn>
    <fillPercent>1</fillPercent>
    <blockWind>true</blockWind>
    <building>
      <deconstructible>false</deconstructible>
      <repairable>false</repairable>
      <isTargetable>false</isTargetable>
      <isInert>true</isInert>
      <claimable>false</claimable>
      <expandHomeArea>false</expandHomeArea>
    </building>
    <size>(5, 5)</size>
    <graphicData>
      <graphicClass>Graphic_Indexed</graphicClass>
      <texPath>Things/Building/VoidStructure</texPath>
      <drawSize>(6, 6)</drawSize>
      <drawOffset>(0, 0, 0.5)</drawOffset>
      <addTopAltitudeBias>true</addTopAltitudeBias>
      <shadowData>
        <volume>(3, 2, 3)</volume>
      </shadowData>
    </graphicData>
    <comps>
      <li Class="CompProperties_Interactable">
        <compClass>CompVoidStructure</compClass>
        <ticksToActivate>2160</ticksToActivate> <!-- 36 seconds -->
        <remainingSecondsInInspectString>true</remainingSecondsInInspectString>
        <maintainProgress>true</maintainProgress>
        <interactionEffecter>VoidStructureActivating</interactionEffecter>
        <soundActivate>VoidStructure_Activate</soundActivate>
        <activateTexPath>UI/Commands/ActivateVoidStructure</activateTexPath>
        <jobString>activate</jobString>
        <ignoreForbidden>true</ignoreForbidden>
        <targetingParameters>
          <canTargetBuildings>false</canTargetBuildings>
          <canTargetAnimals>false</canTargetAnimals>
          <canTargetMechs>false</canTargetMechs>
          <onlyTargetControlledPawns>true</onlyTargetControlledPawns>
        </targetingParameters>
      </li>
      <li Class="CompProperties_Glower">
        <glowRadius>8</glowRadius>
        <glowColor>(140,160,184,0)</glowColor>
      </li>
      <li Class="CompProperties_PlantHarmRadius">
        <radiusPerDayCurve>
          <points>
            <li>0, 4</li>
            <li>1.5, 25</li>
          </points>
        </radiusPerDayCurve>
        <ignoreSpecialTrees>true</ignoreSpecialTrees>
        <messageOnCropDeath>false</messageOnCropDeath>
        <harmFrequencyPerArea>0.0077</harmFrequencyPerArea>
      </li>
    </comps>
  </ThingDef>

  <ThingDef ParentName="BuildingBase">
    <defName>VoidmetalWall</defName>
    <label>void metal wall</label>
    <description>A solid mass of dark metal. Its surface is covered in jagged crystalline spikes. The material feels wet and oily to the touch but leaves no residue. It is impervious to damage.</description>
    <useHitPoints>false</useHitPoints>
    <uiIconPath>Things/Building/Linked/VoidMetalWall_MenuIcon</uiIconPath>
    <graphicData>
      <texPath>Things/Building/Linked/VoidmetalWall_Atlas</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <linkType>CornerFiller</linkType>
      <linkFlags>
        <li>Wall</li>
        <li>Rock</li>
        <li>MapEdge</li>
      </linkFlags>
      <damageData>
        <cornerTL>Damage/Corner</cornerTL>
        <cornerTR>Damage/Corner</cornerTR>
        <cornerBL>Damage/Corner</cornerBL>
        <cornerBR>Damage/Corner</cornerBR>
        <edgeTop>Damage/Edge</edgeTop>
        <edgeBot>Damage/Edge</edgeBot>
        <edgeLeft>Damage/Edge</edgeLeft>
        <edgeRight>Damage/Edge</edgeRight>
      </damageData>
    </graphicData>
    <statBases>
      <Flammability>0</Flammability>
    </statBases>
    <altitudeLayer>Building</altitudeLayer>
    <passability>Impassable</passability>
    <blockWind>true</blockWind>
    <castEdgeShadows>true</castEdgeShadows>
    <fillPercent>1</fillPercent>
    <coversFloor>true</coversFloor>
    <holdsRoof>true</holdsRoof>
    <staticSunShadowHeight>1.0</staticSunShadowHeight>
    <blockLight>true</blockLight>
    <rotatable>false</rotatable>
    <selectable>true</selectable>
    <neverMultiSelect>true</neverMultiSelect>
    <canOverlapZones>false</canOverlapZones>
    <mineable>false</mineable>
    <building>
      <isInert>true</isInert>
      <repairable>false</repairable>
      <claimable>false</claimable>
      <canBuildNonEdificesUnder>false</canBuildNonEdificesUnder>
      <deconstructible>false</deconstructible>
      <isTargetable>false</isTargetable>
      <supportsWallAttachments>true</supportsWallAttachments>
    </building>
  </ThingDef>

  <ThingDef Name="VoidmetalMass" ParentName="BuildingBase" Abstract="True">
    <defName>VoidmetalMass</defName>
    <label>void metal mass</label>
    <description>A jagged, irregular mass of material. Knife-like shapes protrude chaotically at every angle, inhuman and threatening. The material is an oily metallic substance that feels wet to the touch but leaves no residue. It is impervious to damage.</description>
    <thingClass>ThingWithComps</thingClass>
    <drawerType>RealtimeOnly</drawerType>
    <altitudeLayer>Building</altitudeLayer>
    <category>Building</category>
    <rotatable>false</rotatable>
    <useHitPoints>false</useHitPoints>
    <destroyable>false</destroyable>
    <passability>Impassable</passability>
    <uiIconScale>0.8</uiIconScale>
    <fillPercent>1</fillPercent>
    <blockWind>true</blockWind>
    <building>
      <repairable>false</repairable>
      <claimable>false</claimable>
      <deconstructible>false</deconstructible>
      <isTargetable>false</isTargetable>
    </building>
    <statBases>
      <Beauty>-10</Beauty>
    </statBases>
  </ThingDef>
  <ThingDef ParentName="VoidmetalMass">
    <defName>VoidmetalMassSmall</defName>
    <size>(1, 1)</size>
    <graphicData>
      <texPath>Things/Building/VoidmetalMass/Small</texPath>
      <graphicClass>Graphic_Random</graphicClass>
      <drawSize>(2.3, 2.3)</drawSize>
    </graphicData>
    <comps>
      <li Class="CompProperties_DrawAdditionalGraphics">
        <graphics>
          <li>
            <graphicClass>Graphic_Random</graphicClass>
            <texPath>Things/Building/VoidmetalMass/Outline_Small</texPath>
            <drawSize>(2.3, 2.3)</drawSize>
            <drawOffset>(0, -0.01, 0)</drawOffset>
          </li>
        </graphics>
      </li>
    </comps>
  </ThingDef>
  <ThingDef ParentName="VoidmetalMass">
    <defName>VoidmetalMassMedium</defName>
    <size>(2, 2)</size>
    <rotatable>true</rotatable>
    <graphicData>
      <texPath>Things/Building/VoidmetalMass/Medium</texPath>
      <graphicClass>Graphic_Random</graphicClass>
      <drawSize>(3.3, 3.3)</drawSize>
      <drawOffset>(0, 0.02, 0)</drawOffset>
    </graphicData>
    <comps>
      <li Class="CompProperties_DrawAdditionalGraphics">
        <graphics>
          <li>
            <graphicClass>Graphic_Random</graphicClass>
            <texPath>Things/Building/VoidmetalMass/Outline_Medium</texPath>
            <drawSize>(3.3, 3.3)</drawSize>
            <drawOffset>(0, 0.01, 0)</drawOffset>
          </li>
        </graphics>
      </li>
    </comps>
  </ThingDef>
  <ThingDef ParentName="VoidmetalMass">
    <defName>VoidmetalMassLarge</defName>
    <size>(3, 3)</size>
    <graphicData>
      <texPath>Things/Building/VoidmetalMass/Large</texPath>
      <graphicClass>Graphic_Random</graphicClass>
      <drawSize>(4.3, 4.3)</drawSize>
      <drawOffset>(0, 0.02, 0)</drawOffset>
    </graphicData>
    <comps>
      <li Class="CompProperties_DrawAdditionalGraphics">
        <graphics>
          <li>
            <graphicClass>Graphic_Random</graphicClass>
            <texPath>Things/Building/VoidmetalMass/Outline_Large</texPath>
            <drawSize>(4.3, 4.3)</drawSize>
            <drawOffset>(0, 0.01, 0)</drawOffset>
          </li>
        </graphics>
      </li>
    </comps>
  </ThingDef>

  <ThingDef ParentName="BuildingBase">
    <defName>VoidNode</defName>
    <label>void node</label>
    <description>A roiling sphere of twisted psychic energy. Powered by nearby archotech machinery, it is one part of a bridge from our world into the black void.</description>
    <size>(7, 7)</size>
    <drawerType>RealtimeOnly</drawerType>
    <thingClass>Building</thingClass>
    <graphicData>
      <texPath>Things/Building/VoidNode/Core</texPath>
      <graphicClass>Graphic_Single_AgeSecs</graphicClass>
      <drawSize>(7, 7)</drawSize>
      <shaderType>VoidNodeParallax</shaderType>
      <shaderParameters>
        <_MaskTex>/Things/Building/VoidNode/InternalDustMask</_MaskTex>
        <_DustTex>/Things/Building/VoidNode/InternalDust</_DustTex>
        <_DissolveTex>/Things/Building/VoidNode/Core_m</_DissolveTex>
        <_AmbientShake>.05</_AmbientShake>
      </shaderParameters>
    </graphicData>
    <altitudeLayer>Building</altitudeLayer>
    <tickerType>Normal</tickerType>
    <category>Building</category>
    <rotatable>false</rotatable>
    <useHitPoints>false</useHitPoints>
    <destroyable>false</destroyable>
    <passability>Impassable</passability>
    <building>
      <repairable>false</repairable>
      <claimable>false</claimable>
      <deconstructible>false</deconstructible>
      <isTargetable>false</isTargetable>
    </building>
    <statBases>
      <Flammability>0</Flammability>
    </statBases>
    <comps>
      <li Class="CompProperties_Interactable">
        <compClass>CompVoidNode</compClass>
        <ticksToActivate>0</ticksToActivate> <!-- 10 seconds -->
        <activateTexPath>UI/Commands/ActivateVoidNode</activateTexPath>

        <activateLabelString>Touch...</activateLabelString>
        <activateDescString>Order a colonist to touch this.</activateDescString>
        <guiLabelString>Choose who should touch this.</guiLabelString>
        <jobString>Touch void node</jobString>
        <activatingStringPending>Touching void node</activatingStringPending>
        <inspectString>Send a colonist to touch this.</inspectString>

        <forceNormalSpeedOnInteracting>true</forceNormalSpeedOnInteracting>
        <targetingParameters>
          <canTargetBuildings>false</canTargetBuildings>
          <canTargetAnimals>false</canTargetAnimals>
          <canTargetMechs>false</canTargetMechs>
          <onlyTargetControlledPawns>true</onlyTargetControlledPawns>
        </targetingParameters>
      </li>
      <li Class="CompProperties_Glower">
        <glowRadius>30</glowRadius>
        <glowColor>(300,300,370,0)</glowColor>
      </li>
      <li Class="CompProperties_Effecter">
        <effecterDef>VoidNodeAttached</effecterDef>
      </li>
    </comps>
  </ThingDef>

  <ThingDef Name="TwistedArchotechSupport" ParentName="BuildingBase" Abstract="True">
    <label>twisted archotech support</label>
    <description>A twisted archotech structure. Visible psychic power flows through it into a nearby void node. It is impervious to damage.</description>
    <altitudeLayer>Building</altitudeLayer>
    <category>Building</category>
    <rotatable>false</rotatable>
    <useHitPoints>false</useHitPoints>
    <passability>Impassable</passability>
    <building>
      <repairable>false</repairable>
      <claimable>false</claimable>
      <deconstructible>false</deconstructible>
      <isTargetable>false</isTargetable>
    </building>
    <statBases>
      <Flammability>0</Flammability>
    </statBases>
  </ThingDef>
  
  <ThingDef ParentName="TwistedArchotechSupport">
    <defName>TwistedArchotechSupport_Small</defName>
    <size>(3, 4)</size>
    <graphicData>
      <texPath>Things/Building/TwistedArchotechSupport/TwistedArchotechSupport_small</texPath>
      <graphicClass>Graphic_Multi</graphicClass>
      <drawSize>(7, 7)</drawSize>
      <drawOffset>(0, 0, 0.8)</drawOffset>
    </graphicData>
  </ThingDef>
  
  <ThingDef ParentName="TwistedArchotechSupport">
    <defName>TwistedArchotechSupport_Large</defName>
    <size>(3, 5)</size>
    <graphicData>
      <texPath>Things/Building/TwistedArchotechSupport/TwistedArchotechSupport</texPath>
      <graphicClass>Graphic_Multi</graphicClass>
      <drawSize>(8, 8)</drawSize>
      <drawOffset>(0, 0, 1.1)</drawOffset>
    </graphicData>
  </ThingDef>
  
  <ThingDef ParentName="TwistedArchotechSupport">
    <defName>TwistedArchotechSupport_Vertical</defName>
    <size>(3, 3)</size>
    <graphicData>
      <texPath>Things/Building/TwistedArchotechSupport/TwistedArchotechSupport_vertical</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>(6, 6)</drawSize>
      <drawOffset>(0, 0, 0.5)</drawOffset>
    </graphicData>
  </ThingDef>

</Defs>