﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <EffecterDef>
    <defName>ProgressBar</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_ProgressBar</subEffecterClass>
        <moteDef>Mote_ProgressBar</moteDef>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>ProgressBarAlwaysVisible</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_ProgressBar</subEffecterClass>
        <moteDef>Mote_ProgressBarAlwaysVisible</moteDef>
      </li>
    </children>
  </EffecterDef>
  
  <EffecterDef>
    <defName>Research</defName>
    <children>
    <li>
      <subEffecterClass>SubEffecter_Sustainer</subEffecterClass>
      <soundDef>Interact_Research</soundDef>
    </li>
    <li>
      <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
      <burstCount>2~4</burstCount>
      <positionRadius>0.5</positionRadius>
      <scale>1~1</scale>
      <ticksBetweenMotes>500</ticksBetweenMotes>
      <positionLerpFactor>1</positionLerpFactor>
      <fleckDef>AirPuff</fleckDef>
    </li>
    <li>
      <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
      <spawnLocType>OnTarget</spawnLocType>
      <positionRadius>0.5</positionRadius>
      <scale>0.5</scale>
      <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
      <ticksBetweenMotes>40</ticksBetweenMotes>
      <positionLerpFactor>1</positionLerpFactor>
      <fleckDef>ResearchPage</fleckDef>
      <angle>0~360</angle>
      <speed>5</speed>
    </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Repair</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_Sustainer</subEffecterClass>
        <soundDef>Interact_Repair</soundDef>
        <ticksBeforeSustainerStart>35</ticksBeforeSustainerStart>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>SparkFlash</fleckDef>
        <spawnLocType>BetweenTouchingCells</spawnLocType>
        <positionLerpFactor>0.49</positionLerpFactor>
        <chancePerTick>0.2</chancePerTick>
        <scale>2.5~3.5</scale>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <moteDef>Mote_SparkThrown</moteDef>
        <spawnLocType>BetweenTouchingCells</spawnLocType>
        <positionLerpFactor>0.49</positionLerpFactor>
        <chancePerTick>0.2</chancePerTick>
        <scale>0.24~0.34</scale>
        <airTime>0.08~0.16</airTime>
        <rotationRate>-240~240</rotationRate>
        <speed>7.2~24</speed>
        <angle>135~225</angle>
        <positionRadius>0.02</positionRadius>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Mine</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>PickHit</soundDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>SparkFlash</fleckDef>
        <spawnLocType>BetweenTouchingCells</spawnLocType>
        <positionLerpFactor>0.6</positionLerpFactor>
        <chancePerTick>0.2</chancePerTick>
        <scale>2.5~4.5</scale>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Drill</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_Sustainer</subEffecterClass>
        <soundDef>Interact_Drill</soundDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>DustSlow</fleckDef>
        <ticksBetweenMotes>60</ticksBetweenMotes>
        <scale>1.6~2.0</scale>
        <rotationRate>-5~5</rotationRate>
        <angle>30~60</angle>
        <speed>0.4~0.9</speed>
      </li>
    </children>
  </EffecterDef>

  <SoundDef>
    <defName>Interact_Drill</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Interact/Work/Drill/Drill_loop</clipPath>
          </li>
        </grains>
        <volumeRange>6~6</volumeRange>
        <pitchRange>0.93~1.07</pitchRange>
        <distRange>25~40</distRange>
        <sustainAttack>1.1</sustainAttack>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Interact_Paint</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <maxSimultaneous>3</maxSimultaneous>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Interact/Work/Paint</clipFolderPath>
          </li>
        </grains>
        <muteWhenPaused>true</muteWhenPaused>
        <volumeRange>6</volumeRange>
        <distRange>25~40</distRange>
        <sustainIntervalRange>-0.2</sustainIntervalRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Interact_RemovePaint</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <maxSimultaneous>3</maxSimultaneous>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Interact/Work/RemovePaint</clipFolderPath>
          </li>
        </grains>
        <muteWhenPaused>true</muteWhenPaused>
        <volumeRange>5</volumeRange>
        <distRange>25~40</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <EffecterDef>
    <defName>Harvest_Plant</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_InteractSymbol</subEffecterClass>
        <moteDef>Mote_Harvest</moteDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <fleckDef>DirtBits</fleckDef>
        <spawnLocType>RandomCellOnTarget</spawnLocType>
        <ticksBetweenMotes>14</ticksBetweenMotes>
        <scale>0.5~0.7</scale>
        <angle>0~360</angle>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <speed>3~5</speed>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Harvest_Tree</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_InteractSymbol</subEffecterClass>
        <moteDef>Mote_Harvest</moteDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <fleckDef>HarvestWood</fleckDef>
        <spawnLocType>RandomCellOnTarget</spawnLocType>
        <ticksBetweenMotes>60</ticksBetweenMotes>
        <burstCount>5~7</burstCount>
        <scale>0.5~0.6</scale>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <angle>-25~25</angle>
        <speed>5</speed>
      </li>
    </children>
  </EffecterDef>
  
  <!--========================== Interact symbols ===========================-->
  
  <EffecterDef>
    <defName>Sow</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_InteractSymbol</subEffecterClass>
        <moteDef>Mote_Sow</moteDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <fleckDef>DustPuffThick</fleckDef>
        <spawnLocType>RandomCellOnTarget</spawnLocType>
        <ticksBetweenMotes>17</ticksBetweenMotes>
        <scale>1.0~1.6</scale>
        <angle>0~360</angle>
        <rotationRate>-15~25</rotationRate>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <speed>0.1~0.3</speed>
        <positionRadius>0.5</positionRadius>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <fleckDef>DirtBits</fleckDef>
        <spawnLocType>RandomCellOnTarget</spawnLocType>
        <ticksBetweenMotes>14</ticksBetweenMotes>
        <scale>0.5~0.7</scale>
        <angle>0~360</angle>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <speed>3~5</speed>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Harvest_MetaOnly</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_InteractSymbol</subEffecterClass>
        <moteDef>Mote_Harvest</moteDef>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Clean</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_InteractSymbol</subEffecterClass>
        <moteDef>Mote_Clean</moteDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <fleckDef>DustPuffThick</fleckDef>
        <spawnLocType>RandomCellOnTarget</spawnLocType>
        <ticksBetweenMotes>17</ticksBetweenMotes>
        <scale>1.0~1.6</scale>
        <angle>0~360</angle>
        <rotationRate>-15~25</rotationRate>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <speed>0.1~0.3</speed>
        <positionRadius>0.5</positionRadius>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Paint</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_InteractSymbol</subEffecterClass>
        <moteDef>Mote_Paint</moteDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_Sustainer</subEffecterClass>
        <soundDef>Interact_Paint</soundDef>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>ClearSnow</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_InteractSymbol</subEffecterClass>
        <moteDef>Mote_ClearSnow</moteDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>SnowBits</fleckDef>
        <spawnLocType>RandomCellOnTarget</spawnLocType>
        <chancePerTick>0.06</chancePerTick>
        <burstCount>5</burstCount>
        <scale>0.5~0.6</scale>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <angle>-25~25</angle>
        <speed>5</speed>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>BuryPawn</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_Sustainer</subEffecterClass>
        <soundDef>Interact_GraveDig</soundDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>DustPuff</fleckDef>
        <chancePerTick>0.035</chancePerTick>
        <scale>0.9~1.2</scale>
        <rotationRate>-60~60</rotationRate>
        <speed>0.6~1.8</speed>
      </li>
    </children>
  </EffecterDef>

</Defs>