<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <SoundDef>
    <defName>Pawn_Devourer_Call</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Devourer/Call</clipFolderPath>
          </li>
        </grains>
        <volumeRange>10~18</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <distRange>0~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Devourer_Attack</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Devourer/Attack</clipFolderPath>
          </li>
        </grains>
        <volumeRange>20~25</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <distRange>5~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Devourer_Wounded</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Devourer/Pain</clipFolderPath>
          </li>
        </grains>
        <volumeRange>10~18</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <distRange>5~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Devourer_Digesting</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <maxSimultaneous>5</maxSimultaneous>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Pawn/Devourer/Digesting/Devourer_Digesting_Loop_A</clipPath>
          </li>
        </grains>
        <volumeRange>50</volumeRange>
        <distRange>5~50</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Devourer_Jump</defName>
    <context>MapOnly</context>
    <maxSimultaneous>5</maxSimultaneous>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Devourer/Jump</clipFolderPath>
          </li>
        </grains>
        <volumeRange>60</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Devourer_Land</defName>
    <context>MapOnly</context>
    <maxSimultaneous>5</maxSimultaneous>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Devourer/Land</clipFolderPath>
          </li>
        </grains>
        <volumeRange>60</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
      </li>
    </subSounds>
  </SoundDef>
  
</Defs>