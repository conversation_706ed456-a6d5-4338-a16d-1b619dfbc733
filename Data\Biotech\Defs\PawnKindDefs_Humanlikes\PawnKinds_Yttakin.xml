<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <PawnKindDef ParentName="PirateKindBase">
    <defName>Pirate_Yttakin</defName>
    <apparelMoney>0</apparelMoney>
    <apparelRequired>
      <li>Appa<PERSON>_Bandolier</li>
    </apparelRequired>
    <apparelTags Inherit="False" />
    <apparelAllowHeadgearChance>0</apparelAllowHeadgearChance>
    <defaultFactionType>PirateYttakin</defaultFactionType>
  </PawnKindDef>

  <PawnKindDef ParentName="ScavengerBase">
    <defName>Scavenger_Yttakin</defName>
    <apparelMoney>0</apparelMoney>
    <apparelRequired>
      <li>Apparel_Sash</li>
    </apparelRequired>
    <apparelTags Inherit="False" />
    <apparelAllowHeadgearChance>0</apparelAllowHeadgearChance>
    <defaultFactionType>PirateYttakin</defaultFactionType>
  </PawnKindDef>

  <PawnKindDef ParentName="DrifterBase">
    <defName>Drifter_Yttakin</defName>
    <apparelMoney>0</apparelMoney>
    <apparelTags Inherit="False" />
    <apparelAllowHeadgearChance>0</apparelAllowHeadgearChance>
    <defaultFactionType>PirateYttakin</defaultFactionType>
  </PawnKindDef>

  <PawnKindDef ParentName="ThrasherBase">
    <defName>Thrasher_Yttakin</defName>
    <apparelMoney>0</apparelMoney>
    <apparelRequired>
      <li>Apparel_Sash</li>
    </apparelRequired>
    <apparelTags Inherit="False" />
    <apparelAllowHeadgearChance>0</apparelAllowHeadgearChance>
    <defaultFactionType>PirateYttakin</defaultFactionType>
  </PawnKindDef>

  <PawnKindDef ParentName="MercenaryGunnerBase">
    <defName>Mercenary_Gunner_Yttakin</defName>
    <initialResistanceRange>6~10</initialResistanceRange>
    <apparelTags Inherit="False">
      <li>IndustrialMilitaryBasic</li>
      <li>IndustrialMilitaryAdvanced</li>
      <li>BeltDefensePop</li>
    </apparelTags>
    <apparelAllowHeadgearChance>0.25</apparelAllowHeadgearChance>
    <defaultFactionType>PirateYttakin</defaultFactionType>
  </PawnKindDef>

  <PawnKindDef ParentName="MercenaryHeavyBase">
    <defName>Mercenary_Heavy_Yttakin</defName>
    <apparelTags Inherit="False">
      <li>IndustrialMilitaryBasic</li>
      <li>IndustrialMilitaryAdvanced</li>
      <li>BeltDefensePop</li>
    </apparelTags>
    <apparelAllowHeadgearChance>0.25</apparelAllowHeadgearChance>
    <defaultFactionType>PirateYttakin</defaultFactionType>
  </PawnKindDef>

  <PawnKindDef ParentName="MercenarySlasherBase">
    <defName>Mercenary_Slasher_Yttakin</defName>
    <apparelTags Inherit="False">
      <li>IndustrialMilitaryBasic</li>
      <li>IndustrialMilitaryAdvanced</li>
    </apparelTags>
  </PawnKindDef>

  <PawnKindDef ParentName="MercenarySniperBase">
    <defName>Mercenary_Sniper_Yttakin</defName>
    <apparelTags Inherit="False">
      <li>IndustrialMilitaryBasic</li>
      <li>IndustrialMilitaryAdvanced</li>
    </apparelTags>
    <apparelAllowHeadgearChance>0.25</apparelAllowHeadgearChance>
    <initialResistanceRange>13~20</initialResistanceRange>
  </PawnKindDef>

  <PawnKindDef ParentName="MercenaryEliteBase">
    <defName>Mercenary_Elite_Yttakin</defName>
    <apparelTags Inherit="False">
      <li>IndustrialMilitaryBasic</li>
      <li>IndustrialMilitaryAdvanced</li>
      <li>SpacerMilitary</li>
      <li>BeltDefensePop</li>
    </apparelTags>
    <initialResistanceRange>15~23</initialResistanceRange>
  </PawnKindDef>

</Defs>