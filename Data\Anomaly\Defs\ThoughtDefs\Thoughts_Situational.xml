﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThoughtDef>
    <defName>VoidPleasure</defName>
    <workerClass>ThoughtWorker_Hediff</workerClass>
    <validWhileDespawned>true</validWhileDespawned>
    <hediff>Inhumanized</hediff>
    <stages>
      <li>
        <label>void pleasure</label>
        <description>The vibrations of reality pulse into me and soothe everything.</description>
        <baseMoodEffect>8</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>DeathPall</defName>
    <durationDays>0.1</durationDays>
    <stackLimit>1</stackLimit>
    <stages>
      <!-- Unexposed -->
      <li>
        <label>death pall</label>
        <description>It smells like death.</description>
        <baseMoodEffect>-4</baseMoodEffect>
      </li>
      <!-- Exposed -->
      <li>
        <label>death pall exposure</label>
        <description>It smells like death. My eyes sting and the air grits between my teeth.</description>
        <baseMoodEffect>-8</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>
  
  <ThoughtDef>
    <defName>GrayPall</defName>
    <durationDays>0.1</durationDays>
    <stackLimit>1</stackLimit>
    <nullifyingHediffs>
      <li>Inhumanized</li>
    </nullifyingHediffs>
    <stages>
      <!-- Unexposed -->
      <li>
        <label>gray pall</label>
        <description>It feels like the world is sick and dying.</description>
        <baseMoodEffect>-3</baseMoodEffect>
      </li>
      <!-- Exposed -->
      <li>
        <label>gray pall exposure</label>
        <description>It feels like the world is sick and dying. The putrid air makes me want to vomit.</description>
        <baseMoodEffect>-6</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>UnnaturalDarkness</defName>
    <validWhileDespawned>true</validWhileDespawned>
    <workerClass>ThoughtWorker_UnnaturalDarkness</workerClass>
    <gameCondition>UnnaturalDarkness</gameCondition>
    <stages>
      <li>
        <label>unnatural darkness</label>
        <description>I am suffocated under a blanket of darkness.</description>
        <baseMoodEffect>-5</baseMoodEffect>
      </li>
      <li>
        <label>unnatural sky</label>
        <description>The truth finally comes and swallows this false world.</description>
        <baseMoodEffect>5</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>SleepSuppressor</defName>
    <validWhileDespawned>true</validWhileDespawned>
    <workerClass>ThoughtWorker_SleepSuppressor</workerClass>
    <nullifyingHediffs>
      <li>Inhumanized</li>
    </nullifyingHediffs>
    <stages>
      <li>
        <label>Sleep suppressor</label>
        <description>The hum of that devilish device grates. I feel it inside my head. What an evil piece of machinery.</description>
        <baseMoodEffect>-6</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>BioferriteGenerator</defName>
    <validWhileDespawned>true</validWhileDespawned>
    <workerClass>ThoughtWorker_BioferriteGenerator</workerClass>
    <nullifyingHediffs>
      <li>Inhumanized</li>
    </nullifyingHediffs>
    <stages>
      <li>
        <label>Bioferrite generator</label>
        <description>The buzz of that blasted machine will make me mad. It's like nails on a chalkboard.</description>
        <baseMoodEffect>-6</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>HateChantDrone</defName>
    <workerClass>ThoughtWorker_HateChantDrone</workerClass>
    <effectMultiplyingStat>PsychicSensitivity</effectMultiplyingStat>
    <validWhileDespawned>true</validWhileDespawned>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
    <stages>
      <li>
        <label>hate chant whispers</label>
        <description>I hear their whispers, no matter where I am. Saying wrong things to me over and over.</description>
        <baseMoodEffect>-5</baseMoodEffect>
      </li>
      <li>
        <label>quiet hate chanting</label>
        <description>I can hear them muttering, filling my mind with hatred, inside my head.</description>
        <baseMoodEffect>-10</baseMoodEffect>
      </li>
      <li>
        <label>hate chanting</label>
        <description>Those words being screamed into me, muttered, guttural moans of rage. Inside my head. It's driving me mad!</description>
        <baseMoodEffect>-20</baseMoodEffect>
      </li>
      <li>
        <label>loud hate chanting</label>
        <description>The screaming doesn't stop, the chanting, inside me, in my head!</description>
        <baseMoodEffect>-30</baseMoodEffect>
      </li>
      <li>
        <label>extreme hate chanting</label>
        <description>Rage in my head murderous rip the skin deserves it the black ocean terror deep...</description>
        <baseMoodEffect>-45</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>HarbingerScream</defName>
    <durationDays>2</durationDays>
    <stackLimit>3</stackLimit>
    <effectMultiplyingStat>PsychicSensitivity</effectMultiplyingStat>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
    <stages>
      <li>
        <label>harbinger tree scream</label>
        <description>I've had a pounding headache ever since that tree died. I can still hear it...</description>
        <baseMoodEffect>-6</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>FleshTentacle</defName>
    <workerClass>ThoughtWorker_Hediff</workerClass>
    <hediff>Tentacle</hediff>
    <stackLimit>2</stackLimit>
    <validWhileDespawned>true</validWhileDespawned>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
    <nullifyingTraits>
      <li>Transhumanist</li>
    </nullifyingTraits>
    <nullifyingHediffs>
      <li>Inhumanized</li>
    </nullifyingHediffs>
    <nullifyingPrecepts>
      <li MayRequire="Ludeon.RimWorld.Ideology">BodyMod_Approved</li>
    </nullifyingPrecepts>
    <stages>
      <li>
        <label>flesh tentacle</label>
        <description>Get this thing off of me!</description>
        <baseMoodEffect>-6</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>FleshWhip</defName>
    <workerClass>ThoughtWorker_Hediff</workerClass>
    <hediff>FleshWhip</hediff>
    <stackLimit>2</stackLimit>
    <validWhileDespawned>true</validWhileDespawned>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
    <nullifyingTraits>
      <li>Transhumanist</li>
    </nullifyingTraits>
    <nullifyingHediffs>
      <li>Inhumanized</li>
    </nullifyingHediffs>
    <nullifyingPrecepts>
      <li MayRequire="Ludeon.RimWorld.Ideology">BodyMod_Approved</li>
    </nullifyingPrecepts>
    <stages>
      <li>
        <label>flesh whip</label>
        <description>It never stops squirming. I hate it.</description>
        <baseMoodEffect>-6</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>JoyousPresence</defName>
    <thoughtClass>Thought_Situational</thoughtClass>
    <workerClass>ThoughtWorker_JoyousPresence</workerClass>
    <validWhileDespawned>true</validWhileDespawned>
    <stages>
      <li>
        <label>Joyous presence</label>
        <description>Wherever I go, I can feel the joyous one in the back of my mind. It makes me feel good. Very good.</description>
        <baseMoodEffect>3</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>SwallowedByDarkness</defName>
    <workerClass>ThoughtWorker_SwallowedByDarkness</workerClass>
    <validWhileDespawned>true</validWhileDespawned>
    <stages>
      <li>
        <label>swallowed by darkness</label>
        <description>The darkness is suffocating. I can't see or think!</description>
        <baseMoodEffect>-10</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>PleasurePulse</defName>
    <workerClass>ThoughtWorker_Hediff</workerClass>
    <hediff>PleasurePulse</hediff>
    <validWhileDespawned>true</validWhileDespawned>
    <effectMultiplyingStat>PsychicSensitivity</effectMultiplyingStat>
    <effectMultiplyingStatCurve>
      <points>
        <li>0, 0</li>
        <li>1, 1</li>
        <li>1.5, 1.5</li>
        <li>2, 1.75</li>
      </points>
    </effectMultiplyingStatCurve>
    <stages>
      <li>
        <label>pleasure pulse</label>
        <description>My body ripples with joy. The world is a soft and fuzzy place.</description>
        <baseMoodEffect>12</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>NeurosisPulse</defName>
    <workerClass>ThoughtWorker_Hediff</workerClass>
    <hediff>NeurosisPulse</hediff>
    <validWhileDespawned>true</validWhileDespawned>
    <effectMultiplyingStat>PsychicSensitivity</effectMultiplyingStat>
    <effectMultiplyingStatCurve>
      <points>
        <li>0, 0</li>
        <li>1, 1</li>
        <li>1.5, 1.5</li>
        <li>2, 1.75</li>
      </points>
    </effectMultiplyingStatCurve>
    <stages>
      <li>
        <label>neurosis pulse</label>
        <description>Leave me alone! I have work to do!</description>
        <baseMoodEffect>-4</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>
  
  <ThoughtDef>
    <defName>Voidsight</defName>
    <workerClass>ThoughtWorker_Hediff</workerClass>
    <hediff>Voidsight</hediff>
    <stackLimit>1</stackLimit>
    <validWhileDespawned>true</validWhileDespawned>
    <nullifyingHediffs>
      <li>Inhumanized</li>
    </nullifyingHediffs>
    <stages>
      <li>
        <label>voidsight</label>
        <description>The serum makes me see so much more. Connections everywhere, emotions flowing. Faces in the shadows. So much...</description>
        <baseMoodEffect>-4</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>BloodRage</defName>
    <workerClass>ThoughtWorker_Hediff</workerClass>
    <hediff>BloodRage</hediff>
    <stackLimit>1</stackLimit>
    <validWhileDespawned>true</validWhileDespawned>
    <stages>
      <li>
        <label>blood rage</label>
        <baseMoodEffect>0</baseMoodEffect>
        <visible>false</visible>
      </li>
      <li>
        <label>blood rage</label>
        <description>I'm really angry. I feel like I could hit someone.</description>
        <baseMoodEffect>-6</baseMoodEffect>
      </li>
      <li>
        <label>blood rage</label>
        <description>I want to hurt something. I want to hurt something so much.</description>
        <baseMoodEffect>-12</baseMoodEffect>
      </li>
      <li>
        <label>blood rage</label>
        <description>Smash everything into pieces! You all deserve it!</description>
        <baseMoodEffect>-18</baseMoodEffect>
      </li>
      <li>
        <label>blood rage</label>
        <description>I'll destroy you! I'll kill you!</description>
        <baseMoodEffect>-24</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>UnnaturalCorpse</defName>
    <thoughtClass>Thought_Situational</thoughtClass>
    <workerClass>ThoughtWorker_UnnaturalCorpse</workerClass>
    <validWhileDespawned>true</validWhileDespawned>
    <nullifyingHediffs>
      <li>Inhumanized</li>
    </nullifyingHediffs>
    <stages>
      <li>
        <label>unnatural corpse</label>
        <description>Wherever I go, I can feel it watching me. It never stops.</description>
        <baseMoodEffect>-6</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>
  
  <ThoughtDef>
    <defName>CubeWithdrawl</defName>
    <workerClass>ThoughtWorker_Hediff</workerClass>
    <hediff>CubeWithdrawal</hediff>
    <validWhileDespawned>true</validWhileDespawned>
    <stages>
      <li>
        <visible>false</visible>
      </li>
      <li>
        <label>cube withdrawal</label>
        <description>I need to see the golden cube. Just once more. Please.</description>
        <baseMoodEffect>-15</baseMoodEffect>
      </li>
      <li>
        <label>cube withdrawal</label>
        <description>Where's that cube? I miss it. I need it!</description>
        <baseMoodEffect>-20</baseMoodEffect>
      </li>
      <li>
        <label>cube withdrawal</label>
        <description>Where is my cube? Where is it?</description>
        <baseMoodEffect>-30</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>
  
  <ThoughtDef>
    <defName>CubeSculptures</defName>
    <thoughtClass>Thought_Situational</thoughtClass>
    <workerClass>ThoughtWorker_CubeSculptures</workerClass>
    <stages>
      <li>
        <label>cube sculpture</label>
        <description>My sculpture... my beautiful sculpture!</description>
        <baseMoodEffect>1</baseMoodEffect> <!-- Multiplied for each sculpture -->
      </li>
    </stages>
  </ThoughtDef>
  
  <ThoughtDef>
    <defName>DestroyedCubeSculpture</defName>
    <durationDays>5</durationDays>
    <validWhileDespawned>true</validWhileDespawned>
    <stackLimit>10</stackLimit>
    <requiredHediffs>
      <li>CubeInterest</li>
    </requiredHediffs>
    <stages>
      <li>
        <label>my cube sculpture destroyed</label>
        <description>My sculpture... my beautiful sculpture...</description>
        <baseMoodEffect>-5</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>
  
  <ThoughtDef>
    <defName>SpokeToDisturbingMood</defName>
    <durationDays>1</durationDays>
    <stackLimit>10</stackLimit>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
    <nullifyingTraits>
      <li>Disturbing</li>
    </nullifyingTraits>
    <nullifyingHediffs>
      <li>Inhumanized</li>
    </nullifyingHediffs>
    <stages>
      <li>
        <label>unsettling conversation</label>
        <description>An unsettling conversation made me very creeped out. Nobody should think or speak of such things.</description>
        <baseMoodEffect>-4</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>
  
  <ThoughtDef>
    <defName>ReadingTome</defName>
    <workerClass>ThoughtWorker_ReadingTome</workerClass>
    <stages>
      <li>
        <label>Fascinating tome</label>
        <description>Its almost as if it's speaking out to me personally.</description>
        <baseMoodEffect>8</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>DeadlifeDust</defName>
    <workerClass>ThoughtWorker_DeadlifeDust</workerClass>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
    <stages>
      <li>
        <label>deadlife dust</label>
        <description>This dust itches bad.</description>
        <baseMoodEffect>-4</baseMoodEffect>
      </li>
      <li>
        <label>deadlife dust</label>
        <description>I'm breaking out in rashes from this dust. It's burning my skin.</description>
        <baseMoodEffect>-8</baseMoodEffect>
      </li>
      <li>
        <label>deadlife dust</label>
        <description>This dust stings! I can feel it in my eyes and lungs.</description>
        <baseMoodEffect>-12</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>WearingDreadLeather</defName>
    <workerClass>ThoughtWorker_DreadLeatherApparel</workerClass>
    <validWhileDespawned>true</validWhileDespawned>
    <stages>
      <li>
        <label>dread leather {0}</label>
        <description>I hate this stuff. It stinks.</description>
        <baseMoodEffect>-2</baseMoodEffect>
      </li>
      <li>
        <label>dread leather {0} (+1)</label>
        <description>Two dread leather items. These make me uncomfortable.</description>
        <baseMoodEffect>-4</baseMoodEffect>
      </li>
      <li>
        <label>dread leather {0} (+2)</label>
        <description>These three dread leather items make me squirm. They smell awful.</description>
        <baseMoodEffect>-6</baseMoodEffect>
      </li>
      <li>
        <label>dread leather {0} etc</label>
        <description>I'm covered in this horrible stuff.</description>
        <baseMoodEffect>-8</baseMoodEffect>
      </li>
    </stages>
    <nullifyingTraits>
      <li>Psychopath</li>
    </nullifyingTraits>
    <nullifyingHediffs>
      <li>Inhumanized</li>
    </nullifyingHediffs>
  </ThoughtDef>

  <ThoughtDef>
    <defName>ImprisonedWithEntity</defName>
    <thoughtClass>Thought_Situational</thoughtClass>
    <workerClass>ThoughtWorker_ImprisonedWithEntity</workerClass>
    <stages>
      <li>
        <label>imprisoned with entity</label>
        <description>I'm trapped in here with that twisted thing!</description>
        <baseMoodEffect>-15</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>EntityInRoom</defName>
    <thoughtClass>Thought_Situational</thoughtClass>
    <workerClass>ThoughtWorker_EntityInRoom</workerClass>
    <stages>
      <li>
        <label>monster by my bed</label>
        <description>I'll never be able to sleep with that twisted thing around!</description>
        <baseMoodEffect>-15</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>VoidFascination</defName>
    <thoughtClass>Thought_Situational</thoughtClass>
    <workerClass>ThoughtWorker_VoidFascination</workerClass>
    <stages>
      <li>
        <label>captured entity</label>
        <description>Such a magnificent creature. So much mystery. So much power.</description>
        <baseMoodEffect>6</baseMoodEffect>
      </li>
    </stages>
    <requiredTraits>
      <li>VoidFascination</li>
    </requiredTraits>
  </ThoughtDef>

  <ThoughtDef>
    <defName>JuggernautSerum</defName>
    <workerClass>ThoughtWorker_Hediff</workerClass>
    <hediff>JuggernautSerum</hediff>
    <validWhileDespawned>true</validWhileDespawned> 
    <nullifyingHediffs>
      <li>Inhumanized</li>
    </nullifyingHediffs>
    <stages>
      <li>
        <label>juggernaut serum</label>
        <description>This stuff messes with my head. I can't stop thinking the worst things.</description>
        <baseMoodEffect>-10</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>DeathRefusalSickness</defName>
    <workerClass>ThoughtWorker_Hediff</workerClass>
    <hediff>DeathRefusalSickness</hediff>
    <validWhileDespawned>true</validWhileDespawned>
    <nullifyingHediffs>
      <li>Inhumanized</li>
    </nullifyingHediffs>
    <stages>
      <li>
        <label>death refusal</label>
        <description>I was dead… I died. It was cold and terrifying.</description>
        <baseMoodEffect>-8</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

</Defs>
