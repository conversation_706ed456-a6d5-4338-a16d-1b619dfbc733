<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <HediffDef ParentName="InjuryBase">
    <defName>BeamWound</defName>
    <label>beam burn</label>
    <labelNoun>a beam burn</labelNoun>
    <description>A beam burn.</description>
    <comps>
      <li Class="HediffCompProperties_TendDuration">
        <labelTendedWell>bandaged</labelTendedWell>
        <labelTendedWellInner>sutured</labelTendedWellInner>
        <labelSolidTendedWell>set</labelSolidTendedWell>
      </li>
      <li Class="HediffCompProperties_GetsPermanent">
        <permanentLabel>old beam wound</permanentLabel>
        <instantlyPermanentLabel>permanent beam injury</instantlyPermanentLabel>
      </li>
    </comps>
    <injuryProps>
      <painPerSeverity>0.0125</painPerSeverity>
      <averagePainPerSeverityPermanent>0.00625</averagePainPerSeverityPermanent>
      <bleedRate>0.06</bleedRate>
      <canMerge>false</canMerge>
      <destroyedLabel>Burned off</destroyedLabel>
      <destroyedOutLabel>Burned out</destroyedOutLabel>
    </injuryProps>
  </HediffDef>
  
</Defs>