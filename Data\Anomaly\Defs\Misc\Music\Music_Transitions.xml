﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <MusicTransitionDef>
    <defName>HorrorRelax</defName>
    <workerType>HorrorRelaxTransition</workerType>
    <sequence>HorrorRelax</sequence>
    <dangerRequirement>RequiresNoDanger</dangerRequirement>
    <priority>100</priority>
  </MusicTransitionDef>
  
  <MusicTransitionDef>
    <defName>HorrorTension</defName>
    <workerType>HorrorTensionTransition</workerType>
    <sequence>HorrorTension</sequence>
    <dangerRequirement>RequiresNoDanger</dangerRequirement>
    <priority>150</priority>
  </MusicTransitionDef>

  <MusicTransitionDef>
    <defName>HorrorMonolithAdvanced</defName>
    <workerType>HorrorMonolithAdvancedTransition</workerType>
    <sequence>HorrorMonolithAdvanced</sequence>
    <priority>175</priority>
  </MusicTransitionDef>
  
  <MusicTransitionDef>
    <defName>HorrorCombat</defName>
    <workerType>HorrorCombatTransition</workerType>
    <sequence>HorrorCombat</sequence>
    <dangerRequirement>RequiresDanger</dangerRequirement>
    <priority>200</priority>
  </MusicTransitionDef>

  <MusicTransitionDef>
    <defName>CorpseAttack</defName>
    <workerType>CorpseAttackTransition</workerType>
    <overridesInterruptions>true</overridesInterruptions>
    <sequence>CorpseAttack</sequence>
    <priority>600</priority>
  </MusicTransitionDef>

  <MusicTransitionDef>
    <defName>MetalHell</defName>
    <workerType>MetalHellTransition</workerType>
    <overridesInterruptions>true</overridesInterruptions>
    <sequence>MetalHell</sequence>
    <priority>999</priority>
  </MusicTransitionDef>

</Defs>
