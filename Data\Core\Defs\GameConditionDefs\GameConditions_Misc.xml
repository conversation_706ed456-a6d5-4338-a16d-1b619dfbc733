﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <GameConditionDef>
    <defName>SolarFlare</defName>
    <conditionClass>GameCondition_DisableElectricity</conditionClass>
    <label>solar flare</label>
    <description>A solar flare is blasting the planet. The electromagnetic interference will prevent most electrical devices from working.</description>
    <endMessage>The solar flare is ending.</endMessage>
    <letterText>A solar flare has begun.\n\nThe intense radiation will shut down all electrical devices.\n\nIt should pass in about a day.</letterText>
    <letterDef>NegativeEvent</letterDef>
    <canBePermanent>true</canBePermanent>
    <allowUnderground>false</allowUnderground>
    <silencedByConditions>
      <li MayRequire="Ludeon.RimWorld.Anomaly">UnnaturalDarkness</li>
    </silencedByConditions>
  </GameConditionDef>

  <GameConditionDef>
    <defName>Eclipse</defName>
    <conditionClass>GameCondition_NoSunlight</conditionClass>
    <label>eclipse</label>
    <description>An eclipse is shadowing the surface. It will be dark, even during the daytime.</description>
    <endMessage>The eclipse is ending.</endMessage>
    <letterText>One of the moons of this planet has orbited in front of the sun. An eclipse has begun.</letterText>
    <letterDef>NegativeEvent</letterDef>
    <canBePermanent>true</canBePermanent>
    <allowUnderground>false</allowUnderground>
    <silencedByConditions>
      <li MayRequire="Ludeon.RimWorld.Anomaly">UnnaturalDarkness</li>
    </silencedByConditions>
  </GameConditionDef>

  <GameConditionDef>
    <defName>PsychicDrone</defName>
    <conditionClass>GameCondition_PsychicEmanation</conditionClass>
    <label>psychic drone</label>
    <description>A distant archotech is emitting psychic signals through an orbital amplifier, driving people towards insanity.</description>
    <descriptionFuture>a [psychicDroneLevel] psychic drone will buzz the area around [map_definite] for [gameConditionDuration_duration], pushing all people of the [psychicDroneGender] gender towards insanity</descriptionFuture>
    <defaultDroneLevel>BadMedium</defaultDroneLevel>
    <endMessage>The psychic drone is ending.</endMessage>
    <letterText>Every {0} colonist feels a wave of anxiety and anger!\n\nSome distant engine of hatred is stirring. It is projecting a psychic drone onto this site through an orbital amplifier, tuned to only affect {0}s. For a few days, some people's mood will be quite a bit worse.\n\nThe drone level is {1}.</letterText>
    <letterDef>NegativeEvent</letterDef>
    <exclusiveConditions>
      <li>PsychicSoothe</li>
    </exclusiveConditions>
    <canBePermanent>true</canBePermanent>
    <natural>false</natural>
  </GameConditionDef>

  <GameConditionDef>
    <defName>PsychicSoothe</defName>
    <conditionClass>GameCondition_PsychicEmanation</conditionClass>
    <label>psychic soothe</label>
    <description>A distant archotech is emitting psychic signals through an orbital amplifier, calming people and stabilizing their minds.</description>
    <letterText>Every {0} colonist smiles with contentment!\n\nSome distant engine of happiness is stirring. It is projecting a psychic drone onto this site through an orbital amplifier, tuned to only affect {0}s. For a few days, some people's mood will be quite a bit better.</letterText>
    <defaultDroneLevel>GoodMedium</defaultDroneLevel>
    <endMessage>The psychic soothe is ending.</endMessage>
    <letterDef>PositiveEvent</letterDef>
    <exclusiveConditions>
      <li>PsychicDrone</li>
    </exclusiveConditions>
    <canBePermanent>true</canBePermanent>
    <natural>false</natural>
  </GameConditionDef>

  <GameConditionDef>
    <defName>ToxicFallout</defName>
    <conditionClass>GameCondition_ToxicFallout</conditionClass>
    <label>toxic fallout</label>
    <description>A plume of toxic dust from some distant source is slowly settling over this entire region. Any living thing not under a roof will be slowly poisoned, and items left outside will corrode.</description>
    <descriptionFuture>a cloud of toxic fallout will blanket the area around [map_definite] for [gameConditionDuration_duration], killing outdoor plant and animal life</descriptionFuture>
    <endMessage>The worst of the toxic fallout has settled.</endMessage>
    <letterText>A distant chemical fire has released a plume of poison over this entire region.\n\nAny person or creature not under a roof will be slowly sickened by the toxic dust settling out of the atmosphere.\n\nIt will last for anywhere between a few days to nearly a quadrum.</letterText>
    <letterDef>NegativeEvent</letterDef>
    <canBePermanent>true</canBePermanent>
    <allowUnderground>false</allowUnderground>
    <pennedAnimalsSeekShelter>true</pennedAnimalsSeekShelter>
  </GameConditionDef>

  <GameConditionDef>
    <defName>VolcanicWinter</defName>
    <conditionClass>GameCondition_VolcanicWinter</conditionClass>
    <label>volcanic winter</label>
    <description>Some distant volcano or fire is choking the atmosphere with ash, significantly reducing the amount of light which reaches the surface. Temperatures will be colder and plants will grow slower.</description>
    <descriptionFuture>a layer of ash will be desposited in the upper atmosphere, reducing sunlight around [map_definite] for [gameConditionDuration_duration], seriously harming the local ecosystem</descriptionFuture>
    <endMessage>Most of the ash has settled.</endMessage>
    <letterText>Some distant volcano or fire has begun spewing massive quantities of ash into the atmosphere.\n\nAsh in the atmosphere will obscure the sun. Temperatures will drop and plants will suffer for lack of sunlight. It could pass in a few weeks, or it might last many quadrums.</letterText>
    <letterDef>NegativeEvent</letterDef>
    <canBePermanent>true</canBePermanent>
    <allowUnderground>false</allowUnderground>
  </GameConditionDef>

  <GameConditionDef>
    <defName>HeatWave</defName>
    <conditionClass>GameCondition_HeatWave</conditionClass>
    <label>heat wave</label>
    <description>An extreme wave of heat is baking the area.</description>
    <descriptionFuture>a heat wave will bake the area around [map_definite] for [gameConditionDuration_duration], harming crops and possibly killing people or animals in uncooled areas</descriptionFuture>
    <endMessage>The heat wave is over.</endMessage>
    <letterText>An unusual heat wave has begun.\n\nHeat waves can induce deadly heatstroke. Stay cool by building either a passive or electric cooler, or by getting deep underground where the rocks are naturally cool.</letterText>
    <letterDef>NegativeEvent</letterDef>
    <exclusiveConditions>
      <li>ColdSnap</li>
      <li>VolcanicWinter</li>
    </exclusiveConditions>
    <letterHyperlinks>
      <li>PassiveCooler</li>
      <li>Cooler</li>
    </letterHyperlinks>
    <allowUnderground>false</allowUnderground>
  </GameConditionDef>

  <GameConditionDef>
    <defName>ColdSnap</defName>
    <conditionClass>GameCondition_ColdSnap</conditionClass>
    <label>cold snap</label>
    <description>An unusual cold snap is freezing the region.</description>
    <descriptionFuture>a front of cold air will cool the area around [map_definite] for [gameConditionDuration_duration], harming crops and any person or animal without adequate protection</descriptionFuture>
    <endMessage>The cold snap is over.</endMessage>
    <letterText>An unusual cold snap has set in.\n\nCold snaps can quickly kill by hypothermia. Be sure to build a heated space to survive, wear appropriate apparel, and try to harvest crops before they freeze and die.</letterText>
    <letterDef>NegativeEvent</letterDef>
    <exclusiveConditions>
      <li>HeatWave</li>
    </exclusiveConditions>
    <allowUnderground>false</allowUnderground>
  </GameConditionDef>
  
  <GameConditionDef>
    <defName>Flashstorm</defName>
    <conditionClass>GameCondition_Flashstorm</conditionClass>
    <label>flashstorm</label>
    <description>A freak localized storm is blasting a small area with repeated lightning strikes.</description>
    <endMessage>The flashstorm is over.</endMessage>
    <letterText>A freak localized flashstorm is striking a small area with lightning. This can cause massive fires.</letterText>
    <letterDef>NegativeEvent</letterDef>
    <preventRain>true</preventRain>
    <canBePermanent>true</canBePermanent>
    <jumpToSourceKey>ClickToJumpToFlashstorm</jumpToSourceKey>
    <allowUnderground>false</allowUnderground>
  </GameConditionDef>

  <GameConditionDef>
    <defName>ClimateCycle</defName>
    <conditionClass>GameCondition_ClimateCycle</conditionClass>
    <label>climate cycle</label>
    <description>A multi-year climate cycle due to an elliptical orbit.</description>
    <canBePermanent>true</canBePermanent>
    <allowUnderground>false</allowUnderground>
  </GameConditionDef>

  <GameConditionDef>
    <defName>Planetkiller</defName>
    <conditionClass>GameCondition_Planetkiller</conditionClass>
    <label>planetkiller</label>
    <description>An interstellar planetkiller weapon is approaching this planet. It will smash the planet apart like a rifle bullet hitting an egg, annihilating all life.</description>
  </GameConditionDef>
  
  <GameConditionDef>
    <defName>Aurora</defName>
    <conditionClass>GameCondition_Aurora</conditionClass>
    <label>aurora</label>
    <description>An aurora is lighting up the sky. The breathtaking display will boost the mood of anyone outside to see it.</description>
    <endMessage>The aurora is ending.</endMessage>
    <letterText>A beautiful aurora is lighting up the sky.\n\nThe undulating colors will boost the mood of anyone outside to see them, and make the night brighter.</letterText>
    <letterDef>PositiveEvent</letterDef>
    <canBePermanent>true</canBePermanent>
    <allowUnderground>false</allowUnderground>
    <silencedByConditions>
      <li MayRequire="Ludeon.RimWorld.Anomaly">UnnaturalDarkness</li>
    </silencedByConditions>
  </GameConditionDef>

</Defs>