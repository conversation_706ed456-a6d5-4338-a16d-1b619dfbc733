﻿<?xml version="1.0" encoding="utf-8" ?>

<Defs>
  
  <FleshTypeDef>
    <defName>EntityMechanical</defName>
    <corpseCategory>CorpsesEntity</corpseCategory>
    <damageEffecter>Damage_HitMechanoid</damageEffecter>
    <isOrganic>false</isOrganic>
    <genericWounds>
      <li>
        <texture>Things/Pawn/Wounds/WoundMechA</texture>
      </li>
      <li>
        <texture>Things/Pawn/Wounds/WoundMechB</texture>
      </li>
      <li>
        <texture>Things/Pawn/Wounds/WoundMechC</texture>
      </li>
    </genericWounds>
  </FleshTypeDef>

  <FleshTypeDef>
    <defName>EntityFlesh</defName>
    <corpseCategory>CorpsesEntity</corpseCategory>
    <damageEffecter>Damage_HitFlesh</damageEffecter>
    <isOrganic>true</isOrganic>
    <genericWounds>
      <li>
        <texture>Things/Pawn/Wounds/WoundFleshA</texture>
      </li>
      <li>
        <texture>Things/Pawn/Wounds/WoundFleshB</texture>
      </li>
      <li>
        <texture>Things/Pawn/Wounds/WoundFleshC</texture>
      </li>
    </genericWounds>
  </FleshTypeDef>

  <FleshTypeDef>
    <defName>Fleshbeast</defName>
    <corpseCategory>CorpsesEntity</corpseCategory>
    <damageEffecter>Damage_HitFlesh</damageEffecter>
    <isOrganic>true</isOrganic>
    <genericWounds>
      <li>
        <texture>Things/Pawn/Wounds/WoundFleshA</texture>
      </li>
      <li>
        <texture>Things/Pawn/Wounds/WoundFleshB</texture>
      </li>
      <li>
        <texture>Things/Pawn/Wounds/WoundFleshC</texture>
      </li>
    </genericWounds>
  </FleshTypeDef>

</Defs>
