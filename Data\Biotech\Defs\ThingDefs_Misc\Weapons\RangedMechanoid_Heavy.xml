<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <!-- Hellsphere cannon -->
  <ThingDef ParentName="BaseGun">
    <defName>Gun_HellsphereCannon</defName>
    <label>hellsphere cannon</label>
    <description>A ultra-high-power energy pulse projector designed for siegebreaking. The hellsphere cannon must hold a bead on its target for several seconds while charging, before it releases a devastating tsunami of power. The explosion is capable of melting concrete in its blast radius. The heat of the explosion will ignite anything nearby. It cannot fire at close-up targets.</description>
    <techLevel>Spacer</techLevel>
    <graphicData>
      <texPath>Things/Item/Equipment/WeaponRanged/HellsphereCannon</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <tradeability>None</tradeability>
    <destroyOnDrop>true</destroyOnDrop>
    <relicChance>0</relicChance>
    <statBases>
      <MarketValue>1400</MarketValue>
      <Mass>20</Mass>
      <AccuracyTouch>1</AccuracyTouch>
      <AccuracyShort>1</AccuracyShort>
      <AccuracyMedium>1</AccuracyMedium>
      <AccuracyLong>1</AccuracyLong>
      <RangedWeapon_Cooldown>6.0</RangedWeapon_Cooldown>
    </statBases>
    <weaponTags>
      <li>HellsphereCannonGun</li>
    </weaponTags>
    <verbs>
      <li>
        <verbClass>Verb_Shoot</verbClass>
        <hasStandardCommand>true</hasStandardCommand>
        <defaultProjectile>Bullet_HellsphereCannonGun</defaultProjectile>
        <warmupTime>7.5</warmupTime>
        <range>18.9</range>
        <soundCast>Shot_HellsphereCannonGun</soundCast>
        <soundCastTail>GunTail_Heavy</soundCastTail>
        <muzzleFlashScale>9</muzzleFlashScale>
        <ai_AvoidFriendlyFireRadius>6</ai_AvoidFriendlyFireRadius>
        <ai_RangedAlawaysShootGroundBelowTarget>true</ai_RangedAlawaysShootGroundBelowTarget>
        <ai_ProjectileLaunchingIgnoresMeleeThreats>true</ai_ProjectileLaunchingIgnoresMeleeThreats>
        <ai_IsBuildingDestroyer>true</ai_IsBuildingDestroyer>
        <ai_TargetHasRangedAttackScoreOffset>50</ai_TargetHasRangedAttackScoreOffset>
        <minRange>5.9</minRange>
        <targetParams>
          <canTargetLocations>true</canTargetLocations>
          <canTargetPawns>false</canTargetPawns>
        </targetParams>
        <aimingLineMote>Mote_HellsphereCannon_Aim</aimingLineMote>
        <aimingChargeMote>Mote_HellsphereCannon_Charge</aimingChargeMote>
        <aimingChargeMoteOffset>1.07</aimingChargeMoteOffset>
        <aimingLineMoteFixedLength>15.9</aimingLineMoteFixedLength>
        <aimingTargetMote>Mote_HellsphereCannon_Target</aimingTargetMote>
        <burstShotCount>1</burstShotCount>
        <beamTargetsGround>true</beamTargetsGround>
        <soundAiming>HellsphereCannon_Aiming</soundAiming>
        <canGoWild>false</canGoWild>
      </li>
    </verbs>
    <tools>
      <li>
        <label>barrel</label>
        <capacities>
          <li>Blunt</li>
          <li>Poke</li>
        </capacities>
        <power>9</power>
        <cooldownTime>2.6</cooldownTime>
      </li>
    </tools>
  </ThingDef>

  <ThingDef ParentName="BaseBullet">
    <defName>Bullet_HellsphereCannonGun</defName>
    <label>hellsphere cannon shot</label>
    <thingClass>Projectile_HellsphereCannon</thingClass>
    <graphicData>
      <texPath>Things/Projectile/HellsphereCannon</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <shaderType>MoteGlow</shaderType>
      <drawSize>(1.5, 3)</drawSize>
    </graphicData>
    <projectile>
      <damageDef>Vaporize</damageDef>
      <speed>75</speed>
      <explosionRadius>4.9</explosionRadius>
      <screenShakeFactor>1.5</screenShakeFactor>
    </projectile>
  </ThingDef>

</Defs>