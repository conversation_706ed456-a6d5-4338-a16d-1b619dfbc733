<?xml version="1.0" encoding="utf-8" ?>
<Defs>
  
  <IncidentDef>
    <defName>GiveQuest_AncientComplex_Mechanitor</defName>
    <category>GiveQuest</category>
    <label>ancient mechanitor complex</label>
    <targetTags>
      <li>Map_PlayerHome</li>
    </targetTags>
    <letterLabel>ancient mechanitor complex</letterLabel>
    <questScriptDef>OpportunitySite_AncientComplex_Mechanitor</questScriptDef>
    <workerClass>IncidentWorker_GiveQuest</workerClass>
    <baseChance>0</baseChance>
    <requireColonistsPresent>True</requireColonistsPresent>
  </IncidentDef>

  <IncidentDef>
    <defName>RefugeePodCrash_Baby</defName>
    <label>transport pod crash</label>
    <category>Misc</category>
    <targetTags>
      <li>Map_PlayerHome</li>
    </targetTags>
    <workerClass>IncidentWorker_GiveQuest</workerClass>
    <questScriptDef>RefugeePodCrash_Baby</questScriptDef>
    <populationEffect>IncreaseMedium</populationEffect>
    <requireColonistsPresent>True</requireColonistsPresent>
  </IncidentDef>

  <IncidentDef>
    <defName>PoluxTreeSpawn</defName>
    <label>polux tree</label>
    <category>Special</category>
    <targetTags>
      <li>Map_PlayerHome</li>
    </targetTags>
    <workerClass>IncidentWorker_PoluxTreeSpawn</workerClass>
    <treeDef>Plant_TreePolux</treeDef>
    <treeGenStepDef>PoluxTrees</treeGenStepDef>
    <treeGrowth>0.15</treeGrowth>
    <letterLabel>Polux tree sprout</letterLabel>
    <letterText>A polux tree has sprouted nearby!\n\nPolux trees slowly absorb pollution from the terrain around them. Unlike most methods of clearing polluted terrain, polux trees do not generate toxic wastepacks.</letterText>
    <letterDef>PositiveEvent</letterDef>
  </IncidentDef>

</Defs>