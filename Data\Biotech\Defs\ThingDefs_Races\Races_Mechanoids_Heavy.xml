<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingDef Name="HeavyMechanoid" ParentName="BaseMechanoid" Abstract="True">
    <statBases>
      <MoveSpeed>1.9</MoveSpeed>
      <ArmorRating_Blunt>0.20</ArmorRating_Blunt>
      <ArmorRating_Sharp>0.40</ArmorRating_Sharp>
      <ControlTakingTime>24</ControlTakingTime>
      <MechEnergyLossPerHP>0.2</MechEnergyLossPerHP>
    </statBases>
    <race>
      <intelligence>ToolUser</intelligence>
      <thinkTreeMain>Mechanoid</thinkTreeMain>
      <mechWeightClass>Heavy</mechWeightClass>
    </race>
    <butcherProducts>
      <Steel>20</Steel>
      <Plasteel>10</Plasteel>
    </butcherProducts>
  </ThingDef>
  
  <PawnKindDef Name="HeavyMechanoidKind" ParentName="BaseMechanoidKind" Abstract="True">
    <weaponMoney>9999~9999</weaponMoney>
    <combatPower>400</combatPower>
    <weaponTags></weaponTags>
    <techHediffsChance>1</techHediffsChance>
    <techHediffsMoney>9999~9999</techHediffsMoney>
    <controlGroupPortraitZoom>0.8</controlGroupPortraitZoom>
  </PawnKindDef>
  
  <!-- Tunneler -->
  <ThingDef ParentName="HeavyMechanoid">
    <defName>Mech_Tunneler</defName>
    <label>tunneler</label>
    <description>A heavy mechanoid built for mining in treacherous locations. While intended for excavation, the tunneler's gigantic power claws and ultra-thick armor makes it a dangerous force in combat.</description>
    <statBases>
      <MoveSpeed>1.9</MoveSpeed>
      <ArmorRating_Blunt>0.40</ArmorRating_Blunt>
      <ArmorRating_Sharp>0.80</ArmorRating_Sharp>
      <BandwidthCost>3</BandwidthCost>
      <WorkSpeedGlobal>1.5</WorkSpeedGlobal>
      <EnergyShieldEnergyMax>2.5</EnergyShieldEnergyMax>
    </statBases>
    <race>
      <body>Mech_Tunneler</body>
      <baseBodySize>3.5</baseBodySize>
      <baseHealthScale>1.5</baseHealthScale>
      <lifeStageAges>
        <li>
          <def>MechanoidFullyFormed</def>
          <minAge>0</minAge>
          <soundWounded>Pawn_Mech_Tunneler_Wounded</soundWounded>
          <soundDeath>Pawn_Mech_Tunneler_Death</soundDeath>
          <soundCall>Pawn_Mech_Tunneler_Call</soundCall>
        </li>
        <li>
          <def>MechanoidFullyFormed</def>
          <minAge>100</minAge>
          <soundWounded>Pawn_Mech_Tunneler_Wounded</soundWounded>
          <soundDeath>Pawn_Mech_Tunneler_Death</soundDeath>
          <soundCall>Pawn_Mech_Tunneler_Call</soundCall>
        </li>
      </lifeStageAges>
      <bulletStaggerEffecterDef>TunnelerSlowedDown</bulletStaggerEffecterDef>
      <mechEnabledWorkTypes>
        <li>Mining</li>
      </mechEnabledWorkTypes>
      <detritusLeavings>
        <li>
          <def>ChunkMechanoidSlag</def>
          <texOverrideIndex>0</texOverrideIndex>
          <spawnChance>0.5</spawnChance>
        </li>
        <li>
          <def>ChunkMechanoidSlag</def>
          <texOverrideIndex>3</texOverrideIndex>
          <spawnChance>0.5</spawnChance>
        </li>
        <li>
          <def>ChunkMechanoidSlag</def>
          <texOverrideIndex>1</texOverrideIndex>
          <spawnChance>0.5</spawnChance>
        </li>
      </detritusLeavings>
    </race>
    <tools>
      <li>
        <label>left power claw</label>
        <labelNoLocation>power claw</labelNoLocation>
        <capacities>
          <li>Scratch</li>
        </capacities>
        <power>22</power>
        <cooldownTime>2.9</cooldownTime>
        <linkedBodyPartsGroup>LeftPowerClaw</linkedBodyPartsGroup>
        <ensureLinkedBodyPartsGroupAlwaysUsable>true</ensureLinkedBodyPartsGroupAlwaysUsable>
      </li>
      <li>
        <label>right power claw</label>
        <labelNoLocation>power claw</labelNoLocation>
        <capacities>
          <li>Scratch</li>
        </capacities>
        <power>22</power>
        <cooldownTime>2.9</cooldownTime>
        <linkedBodyPartsGroup>RightPowerClaw</linkedBodyPartsGroup>
        <ensureLinkedBodyPartsGroupAlwaysUsable>true</ensureLinkedBodyPartsGroupAlwaysUsable>
      </li>
    </tools>
    <comps>
      <li Class="CompProperties_Shield" />
    </comps>
  </ThingDef>
  
  <PawnKindDef ParentName="HeavyMechanoidKind">
    <defName>Mech_Tunneler</defName>
    <label>tunneler</label>
    <race>Mech_Tunneler</race>
    <allowInMechClusters>false</allowInMechClusters>
    <combatPower>250</combatPower>
    <lifeStages>
      <li>
        <bodyGraphicData>
          <texPath>Things/Pawn/Mechanoid/Tunneler</texPath>
          <maskPath>Things/Pawn/Mechanoid/AllegianceOverlays/Mech_Paladin</maskPath>
          <shaderType>CutoutWithOverlay</shaderType>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>1.8</drawSize>
          <shadowData>
            <volume>(0.6, 0.8, 0.6)</volume>
          </shadowData>
        </bodyGraphicData>
      </li>
      <li>
        <bodyGraphicData>
          <texPath>Things/Pawn/Mechanoid/TunnelerAncient</texPath>
          <maskPath>Things/Pawn/Mechanoid/AllegianceOverlays/Mech_Paladin</maskPath>
          <shaderType>CutoutWithOverlay</shaderType>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>1.8</drawSize>
          <shadowData>
            <volume>(0.6, 0.8, 0.6)</volume>
          </shadowData>
        </bodyGraphicData>
      </li>
    </lifeStages>      
    <abilities>
      <li>SmokepopMech</li>
    </abilities>
    <controlGroupPortraitZoom>1</controlGroupPortraitZoom>  
  </PawnKindDef>

</Defs>