﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>
  
  <SoundDef>
    <defName>Sightstealer_DistantHowl</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <onCamera>True</onCamera>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Misc/SightstealerDistantHowl</clipFolderPath>
          </li>
        </grains>
        <volumeRange>45~50</volumeRange>
        <pitchRange>0.9311764~1.068824</pitchRange>
        <distRange>0~70</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>
  
  <SoundDef>
    <defName>Sightstealer_DistantHowlLarge</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <onCamera>True</onCamera>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Misc/SightstealerLargeDistantHowl</clipFolderPath>
          </li>
        </grains>
        <volumeRange>45~50</volumeRange>
        <pitchRange>0.9311764~1.068824</pitchRange>
        <distRange>0~70</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Sightstealer_SummonedHowl</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <onCamera>True</onCamera>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Misc/AnimaTree/Scream</clipFolderPath> <!-- placeholder -->
          </li>
        </grains>
        <volumeRange>25~35</volumeRange>
        <pitchRange>0.95~1.05</pitchRange>
        <distRange>0~70</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>PsychicBanshee</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <onCamera>True</onCamera>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Misc/AnimaTree/Scream</clipFolderPath>
          </li>
        </grains>
        <volumeRange>25~35</volumeRange>
        <pitchRange>0.95~1.05</pitchRange>
        <distRange>0~70</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Noctolith_Destroyed</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <onCamera>True</onCamera>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Building/Noctolith/Destroy</clipFolderPath>
          </li>
        </grains>
        <volumeRange>50</volumeRange>
        <distRange>0~70</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>PsychicRitual_Complete</defName>
    <context>MapOnly</context>
    <maxSimultaneous>3</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Pawn/PsychicRitual/Horax_PsychicRitual_Complete_A_04</clipPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <distRange>15~60</distRange>
        <sustainLoop>false</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>PsychicRitual_Interrupted</defName>
    <context>MapOnly</context>
    <maxSimultaneous>3</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipPath>Pawn/PsychicRitual/PsychicRitualInterrupted</clipPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <distRange>15~60</distRange>
        <sustainLoop>false</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>PsychicRitual_Interrupted_Human</defName>
    <context>MapOnly</context>
    <maxSimultaneous>3</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipPath>Pawn/PsychicRitual/PsychicRitualInterruptedHuman</clipPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <distRange>15~60</distRange>
        <sustainLoop>false</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>HarbingerTreeScream</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Misc/HarbingerTree/Scream</clipFolderPath>
          </li>
        </grains>
        <onCamera>True</onCamera>
        <volumeRange>15</volumeRange>
        <pitchRange>0.95~1.05</pitchRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>
  
  <SoundDef>
    <defName>Gorehulk_Spine_Launch</defName>
    <context>MapOnly</context>
    <maxSimultaneous>5</maxSimultaneous>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Gorehulk/SpineLaunch</clipFolderPath>
          </li>
        </grains>
        <volumeRange>60</volumeRange>
        <distRange>5~50</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Heatspikes_Shot</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Nociosphere/Heatspikes/Shot</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30~40</volumeRange>
        <distRange>5~50</distRange>
      </li>
    </subSounds>
  </SoundDef>
  
  <SoundDef>
    <defName>Heatspikes_Tail</defName>
    <context>MapOnly</context>
    <maxVoices>2</maxVoices>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <onCamera>True</onCamera>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Pawn/Nociosphere/Heatspikes/Nociosphere_Heatspikes_Tail_A_01</clipPath>
          </li>
        </grains>
        <volumeRange>38.23529~37.05882</volumeRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>AgonyPulse_Cast</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipPath>Pawn/Nociosphere/AgonyPulse</clipPath>
          </li>
        </grains>
        <volumeRange>30~30</volumeRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>FleshmelterBolt_Launch</defName>
    <sustain>true</sustain>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Pawn/Nociosphere/FleshmelterBolt/Nociosphere_FMBolt_Launch_A</clipPath>
          </li>
        </grains>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>FleshmelterBolt_Charging</defName>
    <sustain>true</sustain>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Pawn/Nociosphere/FleshmelterBolt/Nociosphere_FMBolt_Charging_A</clipPath>
          </li>
        </grains>
        <volumeRange>40</volumeRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>FleshmelterBolt_Blast</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Pawn/Nociosphere/FleshmelterBolt/Nociosphere_FMBolt_Blast_A</clipPath>
          </li>
        </grains>
        <volumeRange>80</volumeRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>DarknessDamage</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Misc/DarknessDamage</clipFolderPath>
          </li>
        </grains>
        <volumeRange>20</volumeRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>TraversePitGate</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Building/PitGate/PawnTraversed</clipFolderPath>
          </li>
        </grains>
        <volumeRange>20</volumeRange>
      </li>
    </subSounds>
  </SoundDef>
  
  <SoundDef>
    <defName>MeatExplosionSmall</defName>
    <context>MapOnly</context>
    <maxSimultaneous>3</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Misc/MeatExplosion/Small</clipFolderPath>
          </li>
        </grains>
        <volumeRange>20</volumeRange>
        <muteWhenPaused>True</muteWhenPaused>
        <tempoAffectedByGameSpeed>true</tempoAffectedByGameSpeed>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>MeatExplosionNormal</defName>
    <context>MapOnly</context>
    <maxSimultaneous>3</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Misc/MeatExplosion/Normal</clipFolderPath>
          </li>
        </grains>
        <volumeRange>24</volumeRange>
        <muteWhenPaused>True</muteWhenPaused>
        <tempoAffectedByGameSpeed>true</tempoAffectedByGameSpeed>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>MeatExplosionLarge</defName>
    <context>MapOnly</context>
    <maxSimultaneous>3</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Misc/MeatExplosion/Large</clipFolderPath>
          </li>
        </grains>
        <volumeRange>24</volumeRange>
        <muteWhenPaused>True</muteWhenPaused>
        <tempoAffectedByGameSpeed>true</tempoAffectedByGameSpeed>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>VoidTerrorCast</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Pawn/Abilities/VoidTerror/Void_Terror_Cast_A</clipPath>
          </li>
        </grains>
        <volumeRange>35</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <distRange>0~50</distRange>
      </li>
    </subSounds>
  </SoundDef>
  
</Defs>