﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <RulePackDef>
    <defName>NamerPersonCreepjoiner</defName>
    <rulePack>
      <rulesStrings>
        <!-- [creepjoinerFirstname] is taken from the same first name database as other standard human pawns. -->

        <li>r_name->[creepjoinerFirstname] [creepjoinerLastname]</li>
        <li>r_name->[creepjoinerFirstname] '[creepjoinerNickname]' [creepjoinerLastname]</li>

      </rulesStrings>
      <rulesFiles>
        <li>creepjoinerNickname->Names/Creepjoiner_Nick</li>
        <li>creepjoinerLastname->Names/Creepjoiner_Last</li>
      </rulesFiles>
    </rulePack>
  </RulePackDef>

</Defs>