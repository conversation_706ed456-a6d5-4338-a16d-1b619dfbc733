<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <SoundDef>
    <defName>RevenantSpineSmash</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Item/RevenantSpineSmash</clipFolderPath>
          </li>
        </grains>
        <volumeRange>45</volumeRange>
        <distRange>10~50</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Ability_SpineLaunch</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Abilities/SpineLaunch</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <distRange>10~50</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>AnomalyAbilityWarmup</defName>
    <context>MapOnly</context>
    <maxSimultaneous>5</maxSimultaneous>
    <priorityMode>PrioritizeNearest</priorityMode>
    <sustain>True</sustain>
    <sustainFadeoutTime>0.1</sustainFadeoutTime>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Pawn/Abilities/Anomalous/Anomalous_Ability_Warmup_A</clipPath>
          </li>
        </grains>
        <volumeRange>15</volumeRange>
        <distRange>10~30</distRange>
        <sustainLoop>False</sustainLoop>
        <muteWhenPaused>True</muteWhenPaused>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>FlareLaunch</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Item/DisruptorFlare/Launch</clipFolderPath>
          </li>
        </grains>
        <volumeRange>25</volumeRange>
      </li>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Item/DisruptorFlare/Flight</clipFolderPath>
          </li>
        </grains>
        <volumeRange>25</volumeRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>FlareImpact</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Item/DisruptorFlare/Impact</clipFolderPath>
          </li>
        </grains>
        <volumeRange>35</volumeRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>DeadlifeRelease</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Misc/DeadlifeRelease</clipFolderPath>
          </li>
        </grains>
        <volumeRange>25</volumeRange>
      </li>
    </subSounds>
  </SoundDef>

</Defs>