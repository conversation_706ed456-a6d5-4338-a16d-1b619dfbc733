<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <AbilityCategoryDef>
    <defName>Sanguophage</defName>
    <displayOrder>1000</displayOrder>
  </AbilityCategoryDef>

  <AbilityDef ParentName="AbilityTouchBase">
    <defName>Bloodfeed</defName>
    <label>bloodfeed</label>
    <description>Bite the target and extract hemogen directly from their blood. The target will lose blood and will be horrified by the interaction, but will be otherwise unharmed. A fast-acting coagulant will seal the wound to prevent bleeding.\n\nCan only target non-hemogenic humans. Hemogen gain is affected by the target's body size.</description>
    <iconPath>UI/Icons/Genes/Gene_Bloodfeeder</iconPath>
    <category>Sanguophage</category>
    <displayOrder>100</displayOrder>
    <warmupEffecter>Bloodfeed_Warmup</warmupEffecter>
    <warmupStartSound>Bloodfeed_Cast</warmupStartSound>
    <hostile>false</hostile>
    <casterMustBeCapableOfViolence>false</casterMustBeCapableOfViolence>
    <verbProperties>
      <verbClass>Verb_CastAbilityTouch</verbClass>
      <drawAimPie>false</drawAimPie>
      <range>-1</range>
      <warmupTime>2</warmupTime>
      <targetParams>
        <canTargetSelf>false</canTargetSelf>
        <canTargetBuildings>false</canTargetBuildings>
        <canTargetMechs>false</canTargetMechs>
        <canTargetBloodfeeders>false</canTargetBloodfeeders>
      </targetParams>
    </verbProperties>
    <comps>
      <li Class="CompProperties_AbilityBloodfeederBite">
        <hemogenGain>0.2</hemogenGain>
        <bloodFilthToSpawnRange>1</bloodFilthToSpawnRange>
        <thoughtDefToGiveTarget>FedOn</thoughtDefToGiveTarget>
        <opinionThoughtDefToGiveTarget>FedOn_Social</opinionThoughtDefToGiveTarget>
        <resistanceGain>1</resistanceGain>
      </li>
      <li Class="CompProperties_AbilityFleckOnTarget">
        <fleckDef>BloodSplash</fleckDef>
      </li>
      <li Class="CompProperties_AbilityRequiresCapacity">
        <capacity>Eating</capacity>
      </li>
    </comps>
  </AbilityDef>

  <AbilityDef ParentName="AbilityTouchBase">
    <defName>Coagulate</defName>
    <label>coagulate</label>
    <description>Use special glands in the wrists to quickly tend someone's wounds.</description>
    <iconPath>UI/Icons/Genes/Gene_Coagulate</iconPath>
    <stunTargetWhileCasting>true</stunTargetWhileCasting>
    <displayGizmoWhileUndrafted>true</displayGizmoWhileUndrafted>
    <disableGizmoWhileUndrafted>false</disableGizmoWhileUndrafted>
    <warmupMote>Mote_CoagulateStencil</warmupMote>
    <warmupEffecter>Coagulate</warmupEffecter>
    <warmupStartSound>Coagulate_Cast</warmupStartSound>
    <jobDef>CastAbilityOnThingMelee</jobDef>
    <category>Sanguophage</category>
    <displayOrder>400</displayOrder>
    <hostile>false</hostile>
    <casterMustBeCapableOfViolence>false</casterMustBeCapableOfViolence>
    <verbProperties>
      <verbClass>Verb_CastAbilityTouch</verbClass>
      <drawAimPie>false</drawAimPie>
      <range>-1</range>
      <warmupTime>1</warmupTime>
      <targetParams>
        <canTargetAnimals>true</canTargetAnimals>
        <canTargetSelf>false</canTargetSelf>
        <canTargetBuildings>false</canTargetBuildings>
        <canTargetMechs>false</canTargetMechs>
        <canTargetBloodfeeders>true</canTargetBloodfeeders>
      </targetParams>
    </verbProperties>
    <comps>
      <li Class="CompProperties_AbilityHemogenCost">
        <hemogenCost>0.2</hemogenCost>
      </li>
      <li Class="CompProperties_AbilityCoagulate">
        <tendQualityRange>0.4~0.8</tendQualityRange>
      </li>
      <li Class="CompProperties_AbilityRequiresCapacity">
        <capacity>Manipulation</capacity>
      </li>
    </comps>
  </AbilityDef>

  <AbilityDef ParentName="AbilityTouchBase">
    <defName>ReimplantXenogerm</defName>
    <label>implant genes</label>
    <description>Implant this person's xenogenes into a willing subject using a special injector organ. If this person's genes are currently regrowing, they will implant their xenogerm and die in the process.\n\nWhen implanted, a xenogerm will overwrite the target's xenogenes. Germline genes will be unaffected.</description>
    <iconPath>UI/Icons/Genes/Gene_XenogermReimplanter</iconPath>
    <warmupStartSound>ReimplantXenogerm_Cast</warmupStartSound>
    <warmupEffecter>Implant_Xenogerm</warmupEffecter>
    <category>Sanguophage</category>
    <displayOrder>500</displayOrder>
    <hostile>false</hostile>
    <casterMustBeCapableOfViolence>false</casterMustBeCapableOfViolence>
    <verbProperties>
      <verbClass>Verb_CastAbilityTouch</verbClass>
      <drawAimPie>false</drawAimPie>
      <range>-1</range>
      <warmupTime>4</warmupTime>
      <stunTargetOnCastStart>true</stunTargetOnCastStart>
      <targetParams>
        <canTargetAnimals>false</canTargetAnimals>
        <canTargetSelf>false</canTargetSelf>
        <canTargetBuildings>false</canTargetBuildings>
        <canTargetMechs>false</canTargetMechs>
      </targetParams>
    </verbProperties>
    <comps>
      <li Class="CompProperties_AbilityReimplantXenogerm" />
    </comps>
  </AbilityDef>

  <AbilityDef>
    <defName>PiercingSpine</defName>
    <label>piercing spine</label>
    <description>Use a hemogenic chemical reaction to launch a bony spine at the target. The spine exits through the skin from an opening between the collarbones. Given the method of launch, it is surprisingly easy to aim.</description>
    <iconPath>UI/Abilities/PiercingSpine</iconPath>
    <cooldownTicksRange>60</cooldownTicksRange>
    <category>Sanguophage</category>
    <displayOrder>200</displayOrder>
    <aiCanUse>true</aiCanUse>
    <verbProperties>
      <verbClass>Verb_CastAbility</verbClass>
      <range>3.9</range>
      <warmupTime>0.5</warmupTime>
      <soundCast>PiercingSpine_Launch</soundCast>
    </verbProperties>
    <comps>
      <li Class="CompProperties_AbilityHemogenCost">
        <hemogenCost>0.2</hemogenCost>
      </li>
      <li Class="CompProperties_AbilityLaunchProjectile">
        <projectileDef>PiercingSpine</projectileDef>
      </li>
    </comps>
  </AbilityDef>
  <ThingDef ParentName="BaseBullet">
    <defName>PiercingSpine</defName>
    <label>keratin spine</label>
    <graphicData>
      <texPath>Things/Projectile/PiercingSpine</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <projectile>
      <damageDef>RangedStab</damageDef>
      <damageAmountBase>30</damageAmountBase>
      <armorPenetrationBase>1</armorPenetrationBase>
      <stoppingPower>1</stoppingPower>
      <speed>55</speed>
    </projectile>
  </ThingDef>

  <AbilityDef ParentName="AbilityTouchBase">
    <defName>Resurrect</defName>
    <label>resurrect</label>
    <description>Use archites in the bloodstream to infuse a corpse's body with new life, repairing degenerated cell structures and kickstarting vital functions. The older and worse-preserved the corpse is, the more likely it is that they will come back with memory loss or health conditions. This ability seems to exhaust the archites somehow, so it can only be used very rarely.</description>
    <iconPath>UI/Abilities/Resurrect</iconPath>
    <cooldownTicksRange>6840000~7560000</cooldownTicksRange> <!-- 1.9~2.1 years -->
    <warmupMote>Mote_ResurrectAbility</warmupMote>
    <warmupStartSound>Resurrect_Cast</warmupStartSound>
    <category>Sanguophage</category>
    <displayOrder>600</displayOrder>
    <hostile>false</hostile>
    <sendMessageOnCooldownComplete>true</sendMessageOnCooldownComplete>
    <casterMustBeCapableOfViolence>false</casterMustBeCapableOfViolence>
    <verbProperties>
      <verbClass>Verb_CastAbilityTouch</verbClass>
      <drawAimPie>false</drawAimPie>
      <range>-1</range>
      <warmupTime>2</warmupTime>
      <soundCast>Resurrect_Resolve</soundCast>
      <targetParams>
        <canTargetPawns>false</canTargetPawns>
        <canTargetBuildings>false</canTargetBuildings>
        <canTargetCorpses>true</canTargetCorpses>
        <canTargetMechs>false</canTargetMechs>
      </targetParams>
    </verbProperties>
    <comps>
      <li Class="CompProperties_Resurrect" />
      <li Class="CompProperties_AbilityHemogenCost">
        <hemogenCost>0.90</hemogenCost>
      </li>
    </comps>
  </AbilityDef>

  <AbilityDef>
    <defName>AcidSpray</defName>
    <label>acid spray</label>
    <description>Spray a sticky acid substance from glands in the neck up through the mouth and all over the target area. The acid will stick to targets and burn them.</description>
    <iconPath>UI/Abilities/AcidSpray</iconPath>
    <cooldownTicksRange>30000</cooldownTicksRange>
    <aiCanUse>true</aiCanUse>
    <displayOrder>300</displayOrder>
    <category>Sanguophage</category>
    <sendMessageOnCooldownComplete>true</sendMessageOnCooldownComplete>
    <warmupStartSound>AcidSpray_Warmup</warmupStartSound>
    <verbProperties>
      <verbClass>Verb_CastAbility</verbClass>
      <range>8.9</range>
      <warmupTime>0.5</warmupTime>
      <soundCast>AcidSpray_Resolve</soundCast>
      <targetParams>
        <canTargetLocations>true</canTargetLocations>
      </targetParams>
    </verbProperties>
    <comps>
      <li Class="CompProperties_AbilitySprayLiquid">
        <projectileDef>Proj_Acid</projectileDef>
        <numCellsToHit>9</numCellsToHit>
        <sprayEffecter>AcidSpray_Directional</sprayEffecter>
      </li>
    </comps>
  </AbilityDef>
  <ThingDef>
    <defName>Proj_Acid</defName>
    <label>acid</label>
    <thingClass>Projectile_Liquid</thingClass>
    <category>Projectile</category>
    <tickerType>Normal</tickerType>
    <altitudeLayer>Projectile</altitudeLayer>
    <useHitPoints>False</useHitPoints>
    <neverMultiSelect>True</neverMultiSelect>
    <graphicData>
      <texPath>Things/Projectile/Acid</texPath>
      <graphicClass>Graphic_Random</graphicClass>
      <shaderType>MoteGlow</shaderType>
      <drawSize>0.85</drawSize>
    </graphicData>
    <projectile>
      <damageDef>AcidBurn</damageDef>
      <speed>25</speed>
      <damageAmountBase>30</damageAmountBase>
      <arcHeightFactor>0.4</arcHeightFactor>
      <armorPenetrationBase>0.8</armorPenetrationBase>
      <stoppingPower>1</stoppingPower>
      <shadowSize>0</shadowSize>
      <filth>Filth_SpentAcid</filth>
      <filthCount>1</filthCount>
    </projectile>
  </ThingDef>

  <AbilityDef>
    <defName>FoamSpray</defName>
    <label>foam spray</label>
    <description>Spray thick fire-retardant foam from glands in the neck up through the mouth. The foam will cover a small area and extinguish any fire it touches.</description>
    <iconPath>UI/Icons/Genes/Gene_FoamSpray</iconPath>
    <charges>3</charges>
    <cooldownTicksRange>30000</cooldownTicksRange>
    <warmupStartSound>FoamSpray_Warmup</warmupStartSound>
    <hostile>false</hostile>
    <sendMessageOnCooldownComplete>true</sendMessageOnCooldownComplete>
    <casterMustBeCapableOfViolence>false</casterMustBeCapableOfViolence>
    <verbProperties>
      <verbClass>Verb_CastAbility</verbClass>
      <range>4.9</range>
      <warmupTime>1</warmupTime>
      <soundCast>FoamSpray_Resolve</soundCast>
      <targetParams>
        <canTargetLocations>true</canTargetLocations>
      </targetParams>
    </verbProperties>
    <comps>
      <li Class="CompProperties_AbilitySprayLiquid">
        <projectileDef>Proj_Foam_Ability</projectileDef>
        <numCellsToHit>9</numCellsToHit>
        <sprayEffecter>FoamSpray_Directional</sprayEffecter>
      </li>
    </comps>
  </AbilityDef>
  <ThingDef ParentName="ProjectileFoamBase">
    <defName>Proj_Foam_Ability</defName>
    <projectile>
      <speed>25</speed>
      <numExtraHitCells>0</numExtraHitCells>
      <filthCount>1~3</filthCount>
    </projectile>
    <graphicData>
      <texPath>Things/Mote/FoamSpray</texPath>
      <graphicClass>Graphic_Random</graphicClass>
      <shaderType>MoteGlow</shaderType>
      <drawSize>1.35</drawSize>
    </graphicData>
  </ThingDef>

  <AbilityDef>
    <defName>FireSpew</defName>
    <label>fire spew</label>
    <description>Spit a stream of sticky, flammable bile from the mouth. The bile can ignite anything or anyone it hits, and also form flaming pools on the ground.\n\nThe bile is generated and stored by an organ in the neck, along with a separate pouch of hypergolic reactant for ignition.</description>
    <iconPath>UI/Abilities/FireSpew</iconPath>
    <cooldownTicksRange>300000</cooldownTicksRange>
    <aiCanUse>true</aiCanUse>
    <ai_IsIncendiary>true</ai_IsIncendiary>
    <sendMessageOnCooldownComplete>true</sendMessageOnCooldownComplete>
    <warmupStartSound>FireSpew_Warmup</warmupStartSound>
    <verbProperties>
      <verbClass>Verb_CastAbility</verbClass>
      <range>7.9</range>
      <warmupTime>1</warmupTime>
      <soundCast>FireSpew_Resolve</soundCast>
      <targetParams>
        <canTargetLocations>true</canTargetLocations>
      </targetParams>
    </verbProperties>
    <comps>
      <li Class="CompProperties_AbilityFireSpew">
        <range>7.9</range>
        <lineWidthEnd>3</lineWidthEnd>
        <filthDef>Filth_FlammableBile</filthDef>
        <effecterDef>Fire_Spew</effecterDef>
        <canHitFilledCells>true</canHitFilledCells>
      </li>
    </comps>
  </AbilityDef>

  <AbilityDef Name="LongJump">
    <defName>Longjump</defName>
    <label>longjump</label>
    <description>Jump to a distant location using super-strong hemogen-powered legs.</description>
    <iconPath>UI/Abilities/Longjump</iconPath>
    <cooldownTicksRange>60</cooldownTicksRange>
    <hostile>false</hostile>
    <casterMustBeCapableOfViolence>false</casterMustBeCapableOfViolence>
    <verbProperties>
      <verbClass>Verb_CastAbilityJump</verbClass>
      <label>jump</label>
      <violent>false</violent>
      <forceNormalTimeSpeed>false</forceNormalTimeSpeed>
      <warmupTime>0.5</warmupTime>
      <range>19.9</range>
      <requireLineOfSight>true</requireLineOfSight>
      <soundCast>Longjump_Jump</soundCast>
      <soundLanding>Longjump_Land</soundLanding>
      <targetParams>
        <canTargetLocations>true</canTargetLocations>
        <canTargetPawns>false</canTargetPawns>
        <canTargetBuildings>false</canTargetBuildings>
      </targetParams>
    </verbProperties>
    <jobDef>CastJump</jobDef>
    <comps>
      <li Class="CompProperties_AbilityHemogenCost">
        <hemogenCost>0.05</hemogenCost>
      </li>
    </comps>
  </AbilityDef>

  <AbilityDef>
    <defName>FireBurst</defName>
    <label>fire burst</label>
    <description>Emit a burst of burning fuel in all directions to form puddles of ignited liquid on the ground.</description>
    <jobDef>CastAbilityOnThingWithoutWeapon</jobDef>
    <iconPath>UI/Abilities/FireBurst</iconPath>
    <cooldownTicksRange>2700</cooldownTicksRange>
    <aiCanUse>true</aiCanUse>
    <ai_SearchAOEForTargets>true</ai_SearchAOEForTargets>
    <targetRequired>false</targetRequired>
    <displayGizmoWhileUndrafted>true</displayGizmoWhileUndrafted>
    <disableGizmoWhileUndrafted>false</disableGizmoWhileUndrafted>
    <showCastingProgressBar>true</showCastingProgressBar>
    <verbProperties>
      <verbClass>Verb_CastAbility</verbClass>
      <warmupEffecter>Fire_Burst_Warmup</warmupEffecter>
      <warmupTime>3</warmupTime>
      <requireLineOfSight>false</requireLineOfSight>
      <range>5.9</range>
      <drawAimPie>false</drawAimPie>      
      <targetParams>
        <canTargetSelf>true</canTargetSelf>
      </targetParams>
    </verbProperties>
    <comps>
      <li Class="CompProperties_AbilityFireBurst">
        <radius>5.9</radius>
      </li>
    </comps>
  </AbilityDef> 

  <AbilityDef>
    <defName>AnimalWarcall</defName>
    <label>animal warcall</label>
    <description>With a powerful bellow and psychic connection, call an animal to fight your enemies for a few hours.</description>
    <iconPath>UI/Abilities/AnimalWarcall</iconPath>
    <cooldownTicksRange>900000</cooldownTicksRange><!-- 15 days -->
    <canUseAoeToGetTargets>false</canUseAoeToGetTargets>
    <jobDef>CastAbilityOnThingWithoutWeaponInterruptible</jobDef>
    <sendMessageOnCooldownComplete>true</sendMessageOnCooldownComplete>
    <casterMustBeCapableOfViolence>false</casterMustBeCapableOfViolence>
    <groupAbility>true</groupAbility>
    <statBases>
      <Ability_Duration>500</Ability_Duration>
    </statBases>
    <charges>2</charges>
    <cooldownPerCharge>true</cooldownPerCharge>
    <verbProperties>
      <verbClass>Verb_CastAbility</verbClass>
      <label>warcall</label>
      <warmupTime>2</warmupTime>
      <range>49.9</range>
      <drawAimPie>true</drawAimPie>      
      <requireLineOfSight>false</requireLineOfSight>
      <warmupEffecter>AnimalWarcall_Warmup</warmupEffecter>
      <mouseTargetingText>Choose an animal to call</mouseTargetingText>
      <targetParams>
        <canTargetLocations>false</canTargetLocations>
        <canTargetPawns>true</canTargetPawns>
        <canTargetAnimals>true</canTargetAnimals>
        <canTargetBuildings>false</canTargetBuildings>
        <canTargetHumans>false</canTargetHumans>
        <canTargetMechs>false</canTargetMechs>
        <canTargetSelf>false</canTargetSelf>
      </targetParams>
    </verbProperties>
    <comps>
      <li Class="CompProperties_AbilityGiveMentalState">
        <compClass>CompAbilityEffect_GiveMentalState</compClass>
        <stateDef>BerserkWarcall</stateDef>
        <goodwillImpact>-75</goodwillImpact>
        <casterEffect>AnimalWarcall_Cast</casterEffect>
        <targetEffect>AnimalWarcall_CastOnTarget</targetEffect>
        <excludeNPCFactions>true</excludeNPCFactions>
      </li>
    </comps>
  </AbilityDef>

  <!-- Mech -->

  <AbilityDef Name="LongJumpMech">
    <defName>LongjumpMech</defName>
    <label>mech longjump</label>
    <description>Jump to a distant location using special ultrafast mechanoid muscles.</description>
    <iconPath>UI/Abilities/MechLongJump</iconPath>
    <cooldownTicksRange>480</cooldownTicksRange>
    <hostile>false</hostile>
    <casterMustBeCapableOfViolence>false</casterMustBeCapableOfViolence>
    <verbProperties>
      <verbClass>Verb_CastAbilityJump</verbClass>
      <label>jump</label>
      <violent>false</violent>
      <forceNormalTimeSpeed>false</forceNormalTimeSpeed>
      <warmupTime>0.5</warmupTime>
      <minRange>5.9</minRange>
      <range>9.9</range>
      <requireLineOfSight>true</requireLineOfSight>
      <soundCast>Longjump_Jump</soundCast>
      <soundLanding>Longjump_Land</soundLanding>
      <flyWithCarriedThing>false</flyWithCarriedThing>
      <targetParams>
        <canTargetLocations>true</canTargetLocations>
        <canTargetPawns>false</canTargetPawns>
        <canTargetBuildings>false</canTargetBuildings>
      </targetParams>
    </verbProperties>
  </AbilityDef>

  <AbilityDef ParentName="LongJumpMech">
    <defName>LongjumpMechLauncher</defName>
    <label>jump launcher</label>
    <description>Jump to a distant location using a built-in jump launcher.</description>
    <displayGizmoWhileUndrafted>false</displayGizmoWhileUndrafted>
    <disableGizmoWhileUndrafted>true</disableGizmoWhileUndrafted>
    <charges>2</charges>
    <cooldownTicksRange>20000</cooldownTicksRange><!-- 8h -->
    <hostile>false</hostile>
    <casterMustBeCapableOfViolence>false</casterMustBeCapableOfViolence>
    <verbProperties>
      <range>15</range>
      <minRange>6</minRange>
      <warmupEffecter>JumpMechWarmupEffect</warmupEffecter>
      <requireLineOfSight>true</requireLineOfSight>
      <flightEffecterDef>JumpMechFlightEffect</flightEffecterDef>
      <flyWithCarriedThing>true</flyWithCarriedThing>
      <soundLanding>JumpMechLand</soundLanding>
      <soundCast Inherit="False" />
    </verbProperties>
    <jobDef>CastJump</jobDef>
  </AbilityDef>

  <AbilityDef>
    <defName>SmokepopMech</defName>
    <label>mech smokepop</label>
    <description>Release compressed smoke from concealed vents. Smoke reduces the accuracy of any shot fired through it, and prevents turrets from locking on entirely.</description>
    <iconPath>UI/Abilities/MechSmokepop</iconPath>
    <cooldownTicksRange>900000</cooldownTicksRange><!-- 15 days -->
    <targetRequired>false</targetRequired>
    <hostile>false</hostile>
    <casterMustBeCapableOfViolence>false</casterMustBeCapableOfViolence>
    <verbProperties>
      <verbClass>Verb_CastAbility</verbClass>
      <warmupTime>1</warmupTime>
      <range>3.9</range>
      <drawAimPie>false</drawAimPie>    
      <targetParams>
        <canTargetSelf>true</canTargetSelf>
      </targetParams>
    </verbProperties>
    <comps>
      <li Class="CompProperties_AbilitySmokepop">
        <smokeRadius>3.5</smokeRadius>
        <clamorType>Ability</clamorType>
        <clamorRadius>15</clamorRadius>
      </li>
    </comps>
  </AbilityDef>

  <AbilityDef>
    <defName>FirefoampopMech</defName>
    <label>firefoam pop</label>
    <description>Release a fire-retardant foam in a circular area.</description>
    <iconPath>UI/Abilities/MechFirefoamPop</iconPath>
    <cooldownTicksRange>300000</cooldownTicksRange><!-- 5 days -->
    <displayGizmoWhileUndrafted>false</displayGizmoWhileUndrafted>
    <disableGizmoWhileUndrafted>true</disableGizmoWhileUndrafted>
    <targetRequired>false</targetRequired>
    <hostile>false</hostile>
    <casterMustBeCapableOfViolence>false</casterMustBeCapableOfViolence>
    <verbProperties>
      <verbClass>Verb_CastAbility</verbClass>
      <warmupTime>0.5</warmupTime>
      <requireLineOfSight>false</requireLineOfSight>
      <drawAimPie>false</drawAimPie>      
      <range>3.9</range>
      <targetParams>
        <canTargetSelf>true</canTargetSelf>
      </targetParams>
    </verbProperties>
    <comps>
      <li Class="CompProperties_AbilityFirefoampop">
        <firefoamRadius>3.5</firefoamRadius>
        <clamorType>Ability</clamorType>
        <clamorRadius>15</clamorRadius>
      </li>
    </comps>
  </AbilityDef>

  <AbilityDef>
    <defName>ResurrectionMech</defName>
    <label>resurrect mech</label>
    <description>Resurrect a recently-killed mechanoid by externally supercharging its self-repair processes. The mechanoid will come back with some but not all of its wounds healed.</description>
    <iconPath>UI/Abilities/MechResurrection</iconPath>
    <cooldownTicksRange>120</cooldownTicksRange>
    <warmupStartSound>MechResurrect_Warmup</warmupStartSound>
    <warmupEffecter>ApocrionAoeWarmup</warmupEffecter>
    <jobDef>CastAbilityGoToThing</jobDef>
    <canUseAoeToGetTargets>true</canUseAoeToGetTargets>
    <useAverageTargetPositionForWarmupEffecter>true</useAverageTargetPositionForWarmupEffecter>
    <hostile>false</hostile>
    <casterMustBeCapableOfViolence>false</casterMustBeCapableOfViolence>
    <statBases> 
      <Ability_EffectRadius>5</Ability_EffectRadius>
    </statBases>
    <verbProperties>
      <verbClass>Verb_CastAbility</verbClass>
      <drawAimPie>true</drawAimPie>
      <range>9.9</range>
      <warmupTime>4</warmupTime>
      <soundCast>MechResurrect_Cast</soundCast>
      <requireLineOfSight>false</requireLineOfSight>
      <targetParams>
        <canTargetPawns>false</canTargetPawns>
        <canTargetBuildings>false</canTargetBuildings>
        <canTargetCorpses>true</canTargetCorpses>
        <canTargetAnimals>false</canTargetAnimals>
        <canTargetHumans>false</canTargetHumans>
        <canTargetMechs>true</canTargetMechs>
      </targetParams>
    </verbProperties>
    <comps>
      <li Class="CompProperties_ResurrectMech">
        <maxCorpseAgeTicks>3600</maxCorpseAgeTicks><!-- 60 seconds -->
        <costs>
          <li>
            <weightClass>Light</weightClass>
            <cost>1</cost>
          </li>
          <li>
            <weightClass>Medium</weightClass>
            <cost>3</cost>
          </li>
          <li>
            <weightClass>Heavy</weightClass>
            <cost>5</cost>
          </li>
        </costs>
        <appliedEffecterDef>MechResurrected</appliedEffecterDef>
      </li>
    </comps>
  </AbilityDef>

</Defs>