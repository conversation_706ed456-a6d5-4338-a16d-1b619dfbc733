﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <SoundDef>
    <defName>VoidMonolith_InspectLoop</defName>
    <context>MapOnly</context>
    <sustain>True</sustain>
    <sustainFadeoutTime>1</sustainFadeoutTime>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/VoidMonolith/Activation/L0L1/VM_Act_L0L1_Drone_Loop_D</clipPath>
          </li>
        </grains>
        <volumeRange>45</volumeRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>VoidMonolith_ActivateL0L1</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/VoidMonolith/Activation/L0L1/VM_Act_L0L1_Boing_A</clipPath>
          </li>
        </grains>
        <volumeRange>45</volumeRange>
        <tempoAffectedByGameSpeed>true</tempoAffectedByGameSpeed>
      </li>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/VoidMonolith/Activation/L0L1/VM_Act_L0L1_Build_D</clipPath>
          </li>
        </grains>
        <volumeRange>45</volumeRange>
        <tempoAffectedByGameSpeed>true</tempoAffectedByGameSpeed>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>VoidMonolith_ActivatedL0L1</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/VoidMonolith/Activation/L0L1/VM_Act_L0L1_Boom_D</clipPath>
          </li>
        </grains>
        <volumeRange>45</volumeRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>VoidMonolith_ActivateL1L2</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/VoidMonolith/Activation/L1L2/VM_Act_L1L2_Build_A</clipPath>
          </li>
        </grains>
        <volumeRange>45</volumeRange>
        <tempoAffectedByGameSpeed>true</tempoAffectedByGameSpeed>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>VoidMonolith_ActivatedL1L2</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/VoidMonolith/Activation/L1L2/VM_Act_L1L2_Boom_A</clipPath>
          </li>
        </grains>
        <volumeRange>45</volumeRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>VoidMonolith_ActivateL2L3</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/VoidMonolith/Activation/L2L3/VM_Act_L2L3_Build_A</clipPath>
          </li>
        </grains>
        <volumeRange>45</volumeRange>
        <tempoAffectedByGameSpeed>true</tempoAffectedByGameSpeed>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>VoidMonolith_ActivatedL2L3</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/VoidMonolith/Activation/L2L3/VM_Act_L2L3_Boom_A</clipPath>
          </li>
        </grains>
        <volumeRange>45</volumeRange>
        <distRange>5~50</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>VoidMonolith_Gleaming</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/VoidMonolith/Gleaming/L3-L4_Gleaming</clipPath>
          </li>
        </grains>
        <volumeRange>45</volumeRange>
        <distRange>5~30</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>VoidMonolith_GleamingActivating</defName>
    <context>MapOnly</context>
    <sustain>true</sustain>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/VoidMonolith/Gleaming/Activate_Gleaming_Monolith_Loop_A</clipPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <distRange>5~60</distRange>
      </li>
    </subSounds>
  </SoundDef>

</Defs>