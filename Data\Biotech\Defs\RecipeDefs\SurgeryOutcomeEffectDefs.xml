﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <SurgeryOutcomeEffectDef>
    <defName>ImplantXenogerm</defName>
    <outcomes>
      <li Class="SurgeryOutcome_HediffWithDuration">
        <chance>1</chance>
        <failure>false</failure>
        <hediff>XenogerminationComa</hediff>
        <applyEffectsToPart>false</applyEffectsToPart>
        <qualityToDurationDaysCurve>
          <points>
            <li>(0, 3)</li>
            <li>(1, 1)</li>
          </points>
        </qualityToDurationDaysCurve>
      </li>
    </outcomes>
    <comps>
      <li Class="SurgeryOutcomeComp_SurgeonSuccessChance"/>
      <li Class="SurgeryOutcomeComp_BedAndRoomQuality"/>
      <li Class="SurgeryOutcomeComp_Inspired">
        <inspirationDef>Inspired_Surgery</inspirationDef>
        <factor>2</factor>
      </li>
      <li Class="SurgeryOutcomeComp_MedicineQuality">
        <curve>
          <points>
            <li>(0, 0.7)</li>
            <li>(1, 1)</li>
            <li>(2, 1.3)</li>
          </points>
        </curve>
      </li>
      <li Class="SurgeryOutcomeComp_XenogermComplexity">
        <curve>
          <points>
            <li>(0, 1)</li>
            <li>(20, 0.6)</li>
          </points>
        </curve>
      </li>
      <li Class="SurgeryOutcomeComp_PatientAge">
        <curve>
          <points>
            <li>(20, 1)</li>
            <li>(60, 0.5)</li>
          </points>
        </curve>
      </li>
    </comps>
  </SurgeryOutcomeEffectDef>

</Defs>
