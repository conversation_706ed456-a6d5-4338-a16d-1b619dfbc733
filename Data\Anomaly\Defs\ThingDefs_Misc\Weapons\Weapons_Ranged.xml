﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <ThingDef ParentName="BaseHumanMakeableGun">
    <defName>Gun_HellcatRifle</defName>
    <label>hellcat rifle</label>
    <description>A versatile assault rifle with good range, decent power, and good accuracy.\n\nIt also comes equipped with a bioferrite-powered mini-burner unit which generates a blast of flame from pressurized bioferrite charges. The mini-burner unit can be used twice before refueling.</description>
    <graphicData>
      <texPath>Things/Item/Equipment/WeaponRanged/HellcatRifle</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <soundInteract>Interact_Rifle</soundInteract>
    <recipeMaker>
      <researchPrerequisite>BioferriteIgnition</researchPrerequisite>
      <skillRequirements>
        <Crafting>6</Crafting>
      </skillRequirements>
      <displayPriority>60</displayPriority>
      <recipeUsers Inherit="false">
        <li>BioferriteShaper</li>
      </recipeUsers>
    </recipeMaker>
    <thingSetMakerTags><li>RewardStandardQualitySuper</li></thingSetMakerTags>
    <statBases>
      <WorkToMake>40000</WorkToMake>
      <Mass>3.5</Mass>
      <AccuracyTouch>0.60</AccuracyTouch>
      <AccuracyShort>0.70</AccuracyShort>
      <AccuracyMedium>0.65</AccuracyMedium>
      <AccuracyLong>0.55</AccuracyLong>
      <RangedWeapon_Cooldown>1.70</RangedWeapon_Cooldown>
      <Flammability>0.7</Flammability>
    </statBases>
    <costList>
      <Steel>60</Steel>
      <Bioferrite>20</Bioferrite>
      <ComponentIndustrial>7</ComponentIndustrial>
    </costList>
    <verbs>
      <li>
        <verbClass>Verb_Shoot</verbClass>
        <hasStandardCommand>true</hasStandardCommand>
        <defaultProjectile>Bullet_HellcatRifle</defaultProjectile>
        <warmupTime>1.1</warmupTime>
        <range>26.9</range>
        <burstShotCount>3</burstShotCount>
        <ticksBetweenBurstShots>10</ticksBetweenBurstShots>
        <soundCast>Shot_AssaultRifle</soundCast>
        <soundCastTail>GunTail_Medium</soundCastTail>
        <muzzleFlashScale>9</muzzleFlashScale>
      </li>
    </verbs>
    <weaponTags>
      <li>IndustrialGunAdvanced</li>
      <li>AssaultRifle</li>
    </weaponTags>
    <tools>
      <li>
        <label>stock</label>
        <capacities>
          <li>Blunt</li>
        </capacities>
        <power>9</power>
        <cooldownTime>2</cooldownTime>
      </li>
      <li>
        <label>barrel</label>
        <capacities>
          <li>Blunt</li>
          <li>Poke</li>
        </capacities>
        <power>9</power>
        <cooldownTime>2</cooldownTime>
      </li>
    </tools>
    <comps Inherit="False">
      <li Class="CompProperties_EquippableAbilityReloadable">
        <abilityDef>HellcatBurner</abilityDef>
        <maxCharges>2</maxCharges>
        <soundReload>Standard_Reload</soundReload>
        <chargeNoun>burner charge</chargeNoun>
        <ammoDef>Bioferrite</ammoDef>
        <ammoCountPerCharge>10</ammoCountPerCharge>
        <baseReloadTicks>60</baseReloadTicks>
      </li>
      <li Class="CompProperties_Forbiddable"/>
      <li Class="CompProperties_Styleable"/>
      <li Class="CompProperties_Biocodable"/>
      <li>
        <compClass>CompQuality</compClass>
      </li>
      <li Class="CompProperties_Art">
        <nameMaker>NamerArtWeaponGun</nameMaker>
        <descriptionMaker>ArtDescription_WeaponGun</descriptionMaker>
        <minQualityForArtistic>Excellent</minQualityForArtistic>
      </li>
    </comps>
  </ThingDef>
  <ThingDef ParentName="BaseBullet">
    <defName>Bullet_HellcatRifle</defName>
    <label>hellcat rifle bullet</label>
    <graphicData>
      <texPath>Things/Projectile/Bullet_Small</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <projectile>
      <damageDef>Bullet</damageDef>
      <damageAmountBase>10</damageAmountBase>
      <speed>70</speed>
    </projectile>
  </ThingDef>
  
  <AbilityDef>
    <defName>HellcatBurner</defName>
    <label>hellcat burner</label>
    <description>Expend a pressurized bioferrite charge to create a burst of flame.</description>
    <iconPath>UI/Abilities/Burner</iconPath>
    <aiCanUse>true</aiCanUse>
    <ai_IsIncendiary>true</ai_IsIncendiary>
    <warmupStartSound>FireSpew_Warmup</warmupStartSound>
    <showOnCharacterCard>false</showOnCharacterCard>
    <verbProperties>
      <verbClass>Verb_CastAbility</verbClass>
      <requireLineOfSight>true</requireLineOfSight>
      <range>9.9</range>
      <warmupTime>0.5</warmupTime>
      <soundCast>FireSpew_Resolve</soundCast>
      <targetParams>
        <canTargetLocations>true</canTargetLocations>
      </targetParams>
      <flammabilityAttachFireChanceCurve>
        <points>
          <li>(0.2, 0)</li>
          <li>(1, 1)</li>
        </points>
      </flammabilityAttachFireChanceCurve>
    </verbProperties>
    <comps>
      <li Class="CompProperties_AbilityFireSpew">
        <range>9.9</range>
        <lineWidthEnd>3</lineWidthEnd>
        <filthDef>Filth_Ash</filthDef>
        <damAmount>12</damAmount>
        <canHitFilledCells>true</canHitFilledCells>
      </li>
      <li Class="CompProperties_AbilityBurner">
        <numStreams>15</numStreams>
        <range>11</range> <!-- go a bit further than the actual ability range, since it's meant to be a noisy effect -->
        <coneSizeDegrees>7.2</coneSizeDegrees>
        <moteDef>Mote_IncineratorBurst</moteDef>
        <barrelOffsetDistance>6</barrelOffsetDistance>
        <sizeReductionDistanceThreshold>8</sizeReductionDistanceThreshold>
        <lifespanNoise>40</lifespanNoise>
        <rangeNoise>.4</rangeNoise>
        <effecterDef>BurnerUsed</effecterDef>
      </li>
    </comps>
  </AbilityDef>

  <ThingDef ParentName="BaseHumanMakeableGun">
    <defName>Gun_Incinerator</defName>
    <label>incinerator</label>
    <description>A heavy weapon that can spray a stream of flame into an area. It can shoot in an arc over allies without harming them. The flame burns off rapidly, so it will only ignite the most flammable targets.\n\nWhile the main flamethrower can be used without limit, the weapon also comes equipped with a bioferrite-powered mega-burner unit which generates a massive blast of flame from a pressurized bioferrite charge. The mega-burner unit must be refueled after each use.</description>
    <relicChance>0</relicChance>
    <graphicData>
      <texPath>Things/Item/Equipment/WeaponRanged/Incinerator</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <soundInteract>Interact_Rifle</soundInteract>
    <generateCommonality>0.3</generateCommonality>
    <weaponClasses>
      <li>RangedHeavy</li>
    </weaponClasses>
    <statBases>
      <WorkToMake>48000</WorkToMake>
      <Mass>3.4</Mass>
      <RangedWeapon_Cooldown>3</RangedWeapon_Cooldown>
      <Flammability>0.7</Flammability>
    </statBases>
    <costList>
      <Steel>75</Steel>
      <ComponentIndustrial>6</ComponentIndustrial>
      <Bioferrite>30</Bioferrite>
    </costList>
    <recipeMaker>
      <researchPrerequisite>BioferriteIgnition</researchPrerequisite>
      <skillRequirements>
        <Crafting>6</Crafting>
      </skillRequirements>
      <displayPriority>70</displayPriority>
      <recipeUsers Inherit="false">
        <li>BioferriteShaper</li>
      </recipeUsers>
    </recipeMaker>
    <thingSetMakerTags>
      <li>RewardStandardQualitySuper</li>
    </thingSetMakerTags>
    <verbs>
      <li>
        <verbClass>Verb_ArcSprayIncinerator</verbClass>
        <hasStandardCommand>true</hasStandardCommand>
        <warmupTime>0.5</warmupTime>
        <range>15.9</range>
        <minRange>5.9</minRange>
        <beamFullWidthRange>11</beamFullWidthRange>
        <burstShotCount>20</burstShotCount>
        <showBurstShotStats>false</showBurstShotStats>
        <requireLineOfSight>true</requireLineOfSight>
        <beamWidth>6</beamWidth>
        <ticksBetweenBurstShots>2</ticksBetweenBurstShots>
        <beamDamageDef>Flame</beamDamageDef>
        <soundCastTail>GunTail_Medium</soundCastTail>
        <muzzleFlashScale>9</muzzleFlashScale>
        <soundCastBeam>Flamethrower_Firing</soundCastBeam>
        <beamGroundFleckDef>Fleck_IncineratorBeamBurn</beamGroundFleckDef>
        <beamFleckChancePerTick>0.32</beamFleckChancePerTick>
        <beamMaxDeviation>0.5</beamMaxDeviation>
        <beamCurvature>0</beamCurvature>
        <beamEndEffecterDef>IncineratorBeam_End</beamEndEffecterDef> 
        <beamChanceToStartFire>0.85</beamChanceToStartFire>
        <beamFireSizeRange>0.55~0.85</beamFireSizeRange>
        <beamStartOffset>.5</beamStartOffset>
        <beamHitsNeighborCells>true</beamHitsNeighborCells>
        <beamCantHitWithinMinRange>true</beamCantHitWithinMinRange>
        <beamHitsNeighborCellsRequiresLOS>true</beamHitsNeighborCellsRequiresLOS>
        <beamSetsGroundOnFire>true</beamSetsGroundOnFire>
        <ai_BeamIsIncendiary>true</ai_BeamIsIncendiary>
        <beamLineFleckDef>Fleck_IncineratorBeamSmoke</beamLineFleckDef>
        <highlightColor>(180, 60, 10)</highlightColor>
        <secondaryHighlightColor>(220, 210, 20)</secondaryHighlightColor>
        <beamLineFleckChanceCurve>
          <points>
            <li>(0, 0)</li>
            <li>(0.65, 0.02)</li>
            <li>(1, .5)</li>
          </points>
        </beamLineFleckChanceCurve>
        <targetParams>
          <canTargetLocations>true</canTargetLocations>
        </targetParams>
        <flammabilityAttachFireChanceCurve>
          <points>
            <li>(0.6, 0)</li>
            <li>(0.7, 0.2)</li>
            <li>(1.2, 0.8)</li>
          </points>
        </flammabilityAttachFireChanceCurve>
        <rangedFireRulepack>Combat_RangedFlamethrower</rangedFireRulepack>
      </li>
    </verbs>
    <comps Inherit="False">
      <li Class="CompProperties_EquippableAbilityReloadable">
        <abilityDef>IncineratorBurner</abilityDef>
        <maxCharges>1</maxCharges>
        <soundReload>Standard_Reload</soundReload>
        <chargeNoun>burner charge</chargeNoun>
        <ammoDef>Bioferrite</ammoDef>
        <ammoCountPerCharge>10</ammoCountPerCharge>
        <baseReloadTicks>60</baseReloadTicks>
      </li>
      <li Class="CompProperties_Forbiddable"/>
      <li Class="CompProperties_Styleable"/>
      <li Class="CompProperties_Biocodable"/>
    </comps>
    <weaponTags>
      <li>Flamethrower</li>
    </weaponTags>
    <tools>
      <li>
        <label>stock</label>
        <capacities>
          <li>Blunt</li>
        </capacities>
        <power>9</power>
        <cooldownTime>2</cooldownTime>
      </li>
      <li>
        <label>barrel</label>
        <capacities>
          <li>Blunt</li>
          <li>Poke</li>
        </capacities>
        <power>9</power>
        <cooldownTime>2</cooldownTime>
      </li>
    </tools>
  </ThingDef>
  
  <AbilityDef>
    <defName>IncineratorBurner</defName>
    <label>incinerator burner</label>
    <description>Expend a pressurized bioferrite charge to create a massive cone of flame.</description>
    <iconPath>UI/Abilities/Burner</iconPath>
    <aiCanUse>true</aiCanUse>
    <ai_IsIncendiary>true</ai_IsIncendiary>
    <warmupStartSound>FireSpew_Warmup</warmupStartSound>
    <verbProperties>
      <verbClass>Verb_CastAbility</verbClass>
      <range>11.9</range>
      <warmupTime>0.5</warmupTime>
      <soundCast>FireSpew_Resolve</soundCast>
      <targetParams>
        <canTargetLocations>true</canTargetLocations>
      </targetParams>
      <flammabilityAttachFireChanceCurve>
        <points>
          <li>(0.2, 0)</li>
          <li>(1, 1)</li>
        </points>
      </flammabilityAttachFireChanceCurve>
    </verbProperties>
    <comps>
      <li Class="CompProperties_AbilityFireSpew">
        <range>11.9</range>
        <lineWidthEnd>5</lineWidthEnd>
        <filthDef>Filth_Ash</filthDef>
        <damAmount>16</damAmount>
        <canHitFilledCells>true</canHitFilledCells>
      </li>
      <li Class="CompProperties_AbilityBurner">
        <numStreams>15</numStreams>
        <range>13</range> <!-- go a bit further than the actual ability range, since it's meant to be a noisy effect -->
        <coneSizeDegrees>12</coneSizeDegrees>
        <moteDef>Mote_IncineratorBurst</moteDef>
        <barrelOffsetDistance>6</barrelOffsetDistance>
        <sizeReductionDistanceThreshold>8</sizeReductionDistanceThreshold>
        <lifespanNoise>40</lifespanNoise>
        <rangeNoise>.4</rangeNoise>
        <effecterDef>BurnerUsed</effecterDef>
      </li>
    </comps>
  </AbilityDef>

</Defs>