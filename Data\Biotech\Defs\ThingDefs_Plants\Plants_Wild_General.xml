<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingDef Abstract="True" Name="PollutionPlantBase" ParentName="PlantBase">
    <selectable>true</selectable>
    <plant>
      <pollution>PollutedOnly</pollution>
      <fertilityMin>0.05</fertilityMin>
      <fertilitySensitivity>0.3</fertilitySensitivity>
    </plant>
  </ThingDef>

  <ThingDef Abstract="True" Name="PollutionTreeBase" ParentName="TreeBase">
    <plant>
      <pollution>PollutedOnly</pollution>
      <fertilityMin>0.50</fertilityMin>
      <fertilitySensitivity>0</fertilitySensitivity>
    </plant>
  </ThingDef>

  <ThingDef Abstract="True" Name="PollutionTreeWithStumpBase" ParentName="PollutionTreeBase">
    <plant>
      <choppedThingDef>ChoppedStump_Polluted</choppedThingDef>
      <smashedThingDef>SmashedStump_Polluted</smashedThingDef>
    </plant>
  </ThingDef>

  <!--====================================== Carpets ======================================== -->

  <ThingDef ParentName="PollutionPlantBase">
    <defName>Plant_GrayGrass</defName>
    <label>gray grass</label>
    <description>Gray grass refers to a variety of species of wild toxin-adapted grass. It grows anywhere there is minimally-fertile polluted ground and the climate isn't overwhelming hostile.</description>
    <statBases>
      <MaxHitPoints>85</MaxHitPoints>
      <Flammability>1.3</Flammability>
      <Nutrition>0.50</Nutrition>
    </statBases>
    <graphicData>
      <texPath>Things/Plant/Graygrass</texPath>
      <graphicClass>Graphic_Random</graphicClass>
    </graphicData>
    <hideAtSnowDepth>0.5</hideAtSnowDepth>
    <ingestible />
    <selectable>false</selectable>
    <plant>
      <growDays>2.5</growDays>
      <leaflessGraphicPath>Things/Plant/Graygrass_Leafless</leaflessGraphicPath>
      <harvestWork>40</harvestWork>
      <maxMeshCount>9</maxMeshCount>
      <visualSizeRange>0.4~0.6</visualSizeRange>
      <topWindExposure>0.4</topWindExposure>
      <wildOrder>1</wildOrder>
    </plant>
  </ThingDef>

  <!--====================================== Bushes ======================================== -->

  <ThingDef ParentName="PollutionPlantBase">
    <defName>Plant_Ripthorn</defName>
    <label>ripthorn</label>
    <description>A short toxin-adapted shrub that looks like a dense, tough tangle of thorny branches. Difficult to move through, it slows down anyone moving over it. It can only grow in polluted areas.</description>
    <statBases>
      <MaxHitPoints>100</MaxHitPoints>
      <Nutrition>0.50</Nutrition>
    </statBases>
    <pathCost>14</pathCost>
    <graphicData>
      <texPath>Things/Plant/Ripthorn</texPath>
      <drawSize>1.4</drawSize>
      <graphicClass>Graphic_Random</graphicClass>
    </graphicData>
    <ingestible />
    <plant>
      <growDays>3</growDays>
      <harvestWork>60</harvestWork>
      <maxMeshCount>4</maxMeshCount>
      <leaflessGraphicPath>Things/Plant/Ripthorn_Leafless</leaflessGraphicPath>
      <visualSizeRange>0.7~0.85</visualSizeRange>
      <topWindExposure>0.15</topWindExposure>
      <wildClusterRadius>4</wildClusterRadius>
      <wildClusterWeight>200</wildClusterWeight>
      <wildOrder>2</wildOrder>
      <wildEqualLocalDistribution>false</wildEqualLocalDistribution>
    </plant>
  </ThingDef>

  <ThingDef ParentName="PollutionTreeBase">
    <defName>Plant_PebbleCactus</defName>
    <label>pebble cactus</label>
    <description>A small cactus that resembles a cluster of pebbles. It can only grow on polluted terrain. It yields a meager amount of woody fiber when harvested.</description>
    <minifiedDef IsNull="True" />
    <graphicData>
      <texPath>Things/Plant/PebbleCactus</texPath>
      <graphicClass>Graphic_Random</graphicClass>
      <shadowData>
        <volume>(0.24, 0.3, 0.15)</volume>
        <offset>(-0.1,0,-0.38)</offset>
      </shadowData>
    </graphicData>
    <statBases>
      <Beauty>-5</Beauty>
      <BeautyOutdoors>0</BeautyOutdoors>
      <Nutrition>0.25</Nutrition>
    </statBases>
    <ingestible />
    <plant>
      <growDays>28</growDays>
      <visualSizeRange>0.8~1.5</visualSizeRange>
      <harvestWork>200</harvestWork>
      <harvestYield>6</harvestYield>
    </plant>
  </ThingDef>

  <!--============================ Trees ============================-->

  <ThingDef ParentName="PollutionTreeWithStumpBase">
    <defName>Plant_TreeGrayPine</defName>
    <label>gray pine tree</label>
    <description>A small toxin-adapted tree with twisted branches. It can only grow on polluted terrain, and yields a meager amount of wood when harvested.</description>
    <graphicData>
      <texPath>Things/Plant/TreeGrayPine</texPath>
      <graphicClass>Graphic_Random</graphicClass>
        <shadowData>
          <volume>(0.15, 0.3, 0.15)</volume>
          <offset>(0,0,-0.38)</offset>
        </shadowData>
    </graphicData>
    <statBases>
      <Beauty>-5</Beauty>
      <BeautyOutdoors>0</BeautyOutdoors>
    </statBases>
    <ingestible />
    <plant>
      <growDays>28</growDays>
      <visualSizeRange>1.3~3.0</visualSizeRange>
      <harvestWork>800</harvestWork>
      <harvestYield>8</harvestYield>
    </plant>
  </ThingDef>

  <ThingDef ParentName="PollutionTreeWithStumpBase">
    <defName>Plant_Witchwood</defName>
    <label>witchwood tree</label>
    <description>A small toxin-adapted tree with a twisted trunk. It can only grow on polluted terrain. When harvested, it yields a meager amount of wood.</description>
    <graphicData>
      <texPath>Things/Plant/Witchwood</texPath>
      <graphicClass>Graphic_Random</graphicClass>
      <shadowData>
        <volume>(0.15, 0.35, 0.08)</volume>
        <offset>(0,0,-0.34)</offset>
      </shadowData>
    </graphicData>
    <statBases>
      <Beauty>-5</Beauty>
      <BeautyOutdoors>0</BeautyOutdoors>
    </statBases>
    <ingestible />
    <plant>
      <growDays>28</growDays>
      <visualSizeRange>1.3~3.0</visualSizeRange>
      <harvestWork>400</harvestWork>
      <harvestYield>8</harvestYield>
    </plant>
  </ThingDef>

  <ThingDef ParentName="PollutionTreeWithStumpBase">
    <defName>Plant_RatPalm</defName>
    <label>rat palm tree</label>
    <description>A tree similar to a palm tree with dense surface roots. It can only grow on polluted terrain. When harvested, it yields a meager amount of wood.</description>
    <graphicData>
      <texPath>Things/Plant/RatPalm</texPath>
      <graphicClass>Graphic_Random</graphicClass>
      <shadowData>
        <volume>(0.1, 0.35, 0.13)</volume>
        <offset>(0,0,-0.25)</offset>
      </shadowData>
    </graphicData>
    <statBases>
      <Beauty>-5</Beauty>
      <BeautyOutdoors>0</BeautyOutdoors>
    </statBases>
    <ingestible />
    <plant>
      <growDays>28</growDays>
      <visualSizeRange>1.3~3.0</visualSizeRange>
      <harvestWork>800</harvestWork>
      <harvestYield>8</harvestYield>
    </plant>
  </ThingDef>


  <!--====================================== Stumps ======================================== -->

  <ThingDef ParentName="StumpChoppedBase">
    <defName>ChoppedStump_Polluted</defName>
    <label>chopped stump</label>
    <description>A stump left behind after a polluted tree has been felled. The stump can be extracted but yields very little usable wood. Left outdoors, the stump will deteriorate in time.</description>
    <graphicData>
      <texPath>Things/Plant/Stumps/PollutedChopped</texPath>
      <graphicClass>Graphic_Random</graphicClass>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="StumpSmashedBase">
    <defName>SmashedStump_Polluted</defName>
    <label>smashed stump</label>
    <description>The remnants of a polluted tree destroyed by damage. It's ugly. The stump can be extracted but yields very little usable wood. Left outdoors, the stump will deteriorate in time.</description>
    <graphicData>
      <texPath>Things/Plant/Stumps/PollutedSmashed</texPath>
      <graphicClass>Graphic_Random</graphicClass>
    </graphicData>
  </ThingDef>

</Defs>