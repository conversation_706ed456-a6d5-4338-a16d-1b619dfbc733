<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <QuestScriptDef>
    <defName>PollutionDump</defName>
    <rootSelectionWeight>1</rootSelectionWeight>
    <expireDaysRange>4~8</expireDaysRange>
    <questNameRules>
      <rulesStrings>

        <!-- quest name -->
        <li>questName->[waste] [dump]</li>
        <li>questName(askerIsNull==False)->[asker_faction_pawnSingular] [waste] [dump]</li>
        <li>questName(askerIsNull==False,asker_royalInCurrentFaction==False)->[asker_nameDef]'s [waste] [dump]</li>

        <!-- pieces -->
        <li>waste->waste</li>
        <li>waste->pollution</li>
        <li>waste->toxin</li>
        <li>waste->wastepack</li>
        
        <li>dump->dump</li>
        <li>dump->drop</li>
        <li>dump->clearance</li>
        <li>dump->problem</li>

      </rulesStrings>
    </questNameRules>
    <questDescriptionRules>
      <rulesStrings>
        
        <!-- Asker is null -->
        <li>questDescription(askerIsNull==True)->A machine persona in an orbiting ship has a special request. She wishes to offload an excess of toxic wastepacks and hopes that you will store them responsibly.\n\nIf you accept, she will deliver [wastepackCount] toxic wastepacks by transport pod.\n\nIf not kept frozen, toxic wastepacks will dissolve over time, polluting nearby terrain.</li>
        
        <!-- Royal asker -->
        <li>questDescription(asker_royalInCurrentFaction==True)->[asker_nameFull], a [asker_royalTitleInCurrentFaction] of [asker_faction_name] has a special request. [asker_pronoun] has produced an abundance of toxic wastepacks that [asker_pronoun] does not wish to store.\n\nIf you accept, [asker_nameDef] will deliver [wastepackCount] toxic wastepacks by transport pod.\n\nIf not kept frozen, toxic wastepacks will dissolve over time, polluting nearby terrain.</li>
        
        <!-- Normal asker -->
        <li>questDescription(asker_factionLeader==True)->[asker_faction_leaderTitle] [asker_nameFull] of [asker_faction_name] has a special request. Someone has dumped an abundance of toxic wastepacks near one of [asker_possessive] settlements and [asker_pronoun] is unable to safely store them.\n\nIf you accept, [asker_nameDef] will deliver [wastepackCount] toxic wastepacks by transport pod.\n\nIf not kept frozen, toxic wastepacks will dissolve over time, polluting nearby terrain.</li>     
      
      </rulesStrings>
    </questDescriptionRules>
    <questContentRules>
      <rulesStrings>

        <!-- Letters -->
        <li>wastepacksLetterLabel->Wastepacks arrived</li>
        <li>wastepacksLetterText->The toxic wastepacks have been delivered.</li>

      </rulesStrings>
    </questContentRules>
    <root Class="QuestNode_Root_PollutionDump" />
  </QuestScriptDef>

</Defs>