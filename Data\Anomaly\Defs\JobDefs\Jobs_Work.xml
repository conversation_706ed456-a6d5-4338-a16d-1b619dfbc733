<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <!--============= Prisoners ===============-->

  <JobDef>
    <defName>PrisonerInterrogateIdentity</defName>
    <driverClass>JobDriver_InterrogatePrisoner</driverClass>
    <reportString>interrogating TargetA.</reportString>
    <allowOpportunisticPrefix>true</allowOpportunisticPrefix>
  </JobDef>
  
  <!--============= Activity suppression ===============-->

  <JobDef>
    <defName>ActivitySuppression</defName>
    <driverClass>JobDriver_ActivitySuppression</driverClass>
    <reportString>suppressing TargetC.</reportString>
    <allowOpportunisticPrefix>true</allowOpportunisticPrefix>
  </JobDef>

  <JobDef>
    <defName>FillIn</defName>
    <driverClass>JobDriver_FillIn</driverClass>
    <reportString>filling in TargetA.</reportString>
  </JobDef>

</Defs>