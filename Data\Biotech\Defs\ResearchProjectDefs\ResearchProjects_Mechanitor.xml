<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ResearchProjectDef Abstract="True" Name="MechtechBase">
    <techLevel>Industrial</techLevel>
    <requiresMechanitor>true</requiresMechanitor>
  </ResearchProjectDef>

  <ResearchProjectDef ParentName="MechtechBase">
    <defName>BasicMechtech</defName>
    <label>basic mechtech</label>
    <description>The technology needed for your mechanitor to create and control basic-tier mechanoids.</description>
    <baseCost>200</baseCost>
    <researchViewX>9.00</researchViewX>
    <researchViewY>0.30</researchViewY>
    <prerequisites>
      <li>Electricity</li>
    </prerequisites>
    <heldByFactionCategoryTags Inherit="False" />
    <generalRules>
      <rulesStrings>
        <li>subject->mechanoids</li>

        <li>subject_story->authored a children's book covering the basic types of mechanoid</li>
        <li>subject_story->was implanted with a prototype mechlink</li>
        <li>subject_story->replicated basic biomech techniques independently</li>
        <li>subject_story->was mining an ancient exostrider wreck and found a mechlink preserved in the wreckage</li>

        <li>subject_gerund->creating basic mechanoids</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef ParentName="MechtechBase">
    <defName>StandardMechtech</defName>
    <label>standard mechtech</label>
    <description>The technology needed for your mechanitor to create and control standard-tier mechanoids.\n\nYou can get signal chips by using a mechanitor to summon a diabolus threat.</description>
    <baseCost>1000</baseCost>
    <researchViewX>13.00</researchViewX>
    <researchViewY>0.00</researchViewY>
    <techprintMarketValue>1000</techprintMarketValue>
    <prerequisites>
      <li>BasicMechtech</li>
    </prerequisites>
    <requiredAnalyzed>
      <li>SignalChip</li>
    </requiredAnalyzed>
    <generalRules>
      <rulesStrings>
        <li>subject->mechanoids</li>

        <li>subject_story->acquired a signal chip in shady circumstances</li>
        <li>subject_story->found an intact signal chip in a diabolus wreck</li>
        <li>subject_story->survived a mechanoid hive assault</li>
        <li>subject_story->collated reports of mechanoid attacks, looking for commonalities</li>

        <li>subject_gerund->creating standard mechanoids</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef ParentName="MechtechBase">
    <defName>HighMechtech</defName>
    <label>high mechtech</label>
    <description>The technology needed for your mechanitor to create and control high-tier mechanoids.\n\nYou can get powerfocus chips by using a mechanitor to summon a war queen threat.</description>
    <baseCost>3000</baseCost>
    <researchViewX>14.00</researchViewX>
    <researchViewY>0.00</researchViewY>
    <techprintMarketValue>1500</techprintMarketValue>
    <prerequisites>
      <li>StandardMechtech</li>
      <li>MultiAnalyzer</li>
    </prerequisites>
    <requiredResearchBuilding>HiTechResearchBench</requiredResearchBuilding>
    <requiredResearchFacilities>
      <li>MultiAnalyzer</li>
    </requiredResearchFacilities>
    <requiredAnalyzed>
      <li>PowerfocusChip</li>
    </requiredAnalyzed>
    <generalRules>
      <rulesStrings>
        <li>subject->high tier mechanoids</li>

        <li>subject_story->survived a War Queen assault on an Imperial world</li>
        <li>subject_story->bought a Powerfocus chip from a trader who didn't understand its worth</li>
        <li>subject_story->interrogated mechanitors captured in planetary raids</li>
        <li>subject_story->built prototype mechanoid micro-organs for an offworld client</li>

        <li>subject_gerund->creating high-tier mechanoids</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef ParentName="MechtechBase">
    <defName>UltraMechtech</defName>
    <label>ultra mechtech</label>
    <description>The advanced technology needed for your mechanitor to create and control ultra-tier mechanoids, including large mechs and ultra-tier mechanitor gear.\n\nYou can get nano structuring chips by using a mechanitor to summon an apocriton threat.</description>
    <baseCost>5000</baseCost>
    <techLevel>Ultra</techLevel>
    <researchViewX>15.00</researchViewX>
    <researchViewY>0.00</researchViewY>
    <techprintMarketValue>2000</techprintMarketValue>
    <prerequisites>
      <li>Fabrication</li>
      <li>HighMechtech</li>
    </prerequisites>
    <requiredResearchFacilities>
      <li>MultiAnalyzer</li>
    </requiredResearchFacilities>
    <requiredAnalyzed>
      <li>NanostructuringChip</li>
    </requiredAnalyzed>
    <generalRules>
      <rulesStrings>
        <li>subject->ultra-tier mechanoids</li>

        <li>subject_story->found a powered-down apocriton in the wreckage of a deadworld</li>
        <li>subject_story->observed an apocriton resurrecting mechs from a safe distance</li>
        <li>subject_story->tapped into the deep psychic hatred that the apocritons feel towards humanity</li>
        <li>subject_story->found an ascension diary next to an inert archonexus</li>

        <li>subject_gerund->creating ultra-tier mechanoids</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <!-- =========================================================== -->

  <ResearchProjectDef>
    <defName>WastepackAtomizer</defName>
    <label>wastepack atomizer</label>
    <description>Build wastepack atomizers which can safely dispose of toxic wastepacks by decomposing them at the molecular level.</description>
    <baseCost>8000</baseCost>
    <researchViewX>17.00</researchViewX>
    <researchViewY>1.50</researchViewY>
    <techLevel>Industrial</techLevel>
    <requiredResearchBuilding>HiTechResearchBench</requiredResearchBuilding>
    <requiredResearchFacilities>
      <li>MultiAnalyzer</li>
    </requiredResearchFacilities>
    <prerequisites>
      <li>AdvancedFabrication</li>
    </prerequisites>
    <requiredAnalyzed>
      <li>NanostructuringChip</li>
    </requiredAnalyzed>
    <generalRules>
      <rulesStrings>
        <li>subject->toxic waste atomization</li>

        <li>subject_story->studied safe atomic waste disposal</li>
        <li>subject_story->recruited disposable teams for solar waste disposal missions</li>
        <li>subject_story->devised the theory for atomic destabilisation from first principles</li>
        <li>subject_story->observed a toxicworld from orbit</li>

        <li>subject_gerund->constructing wastepack atomizers</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>
  
</Defs>