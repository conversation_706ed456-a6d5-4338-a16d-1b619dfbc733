﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingSetMakerDef>
    <defName>Reward_GrayBox</defName>
    <root Class="ThingSetMaker_RandomOption">
      <options>

        <!-- Tome -->
        <li>
          <weight>1.5</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Tome</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>

        <!-- Skull -->
        <li>
          <weight>1</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Skull</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>

        <!-- Shard -->
        <li>
          <weight>2</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Shard</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>

        <!-- Shard Psychic Shock Lance -->
        <li>
          <weight>0.4</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Apparel_ShardPsychicShockLance</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>

        <!-- Shard Psychic Insanity Lance -->
        <li>
          <weight>0.4</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Apparel_ShardPsychicInsanityLance</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>

        <!-- Biomutation Lance -->
        <li>
          <weight>0.3</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Apparel_BiomutationLance</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>

        <!-- Pulsers -->
        <li>
          <weight>0.25</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>PsychicSoothePulser</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.125</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>PsychicAnimalPulser</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.125</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>ShardAnimalPulser</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.25</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>BiomutationPulser</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>

        <!-- Psychic Emanator -->
        <li>
          <weight>0.25</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>PsychicEmanator</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>

        <!-- Mech Serum Healer -->
        <li>
          <weight>0.25</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>MechSerumHealer</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>

        <!-- Anti Psychic Pack -->
        <li>
          <weight>0.25</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Apparel_DisruptorFlarePack</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>

        <!-- Serums -->
        <li>
          <weight>0.6</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>JuggernautSerum</li>
                </thingDefs>
              </filter>
              <countRange>2~4</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.6</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>MetalbloodSerum</li>
                </thingDefs>
              </filter>
              <countRange>2~4</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.6</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>VoidsightSerum</li>
                </thingDefs>
              </filter>
              <countRange>1~2</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.6</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>MindNumbSerum</li>
                </thingDefs>
              </filter>
              <countRange>5~8</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>

      </options>
    </root>
  </ThingSetMakerDef>
  
  <ThingSetMakerDef>
    <defName>Reward_GrayBoxLowReward</defName>
    <root Class="ThingSetMaker_RandomOption">
      <options>
        
        <!-- Skull -->
        <li>
          <weight>10</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Skull</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        
        <!-- Human heart -->
        <li>
          <weight>1</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Heart</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>

        <!-- Human kidney -->
        <li>
          <weight>1</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Kidney</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        
        <!-- Human lung -->
        <li>
          <weight>2</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Lung</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        
      </options>
    </root>
  </ThingSetMakerDef>
  
</Defs>

