﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <ThinkTreeDef>
    <defName>Sightstealer</defName>
    <thinkRoot Class="ThinkNode_Priority">
      <subNodes>
        <!-- Despawned -->
        <li Class="ThinkNode_Subtree">
          <treeDef>Despawned</treeDef>
        </li>

        <!-- Downed -->
        <li Class="ThinkNode_Subtree">
          <treeDef>Downed</treeDef>
        </li>

        <!-- Lord directives -->
        <li Class="ThinkNode_Subtree">
          <treeDef>LordDuty</treeDef>
        </li>

        <!-- Escaping-->
        <li Class="ThinkNodeConditional_EscapingHoldingPlatform">
          <subNodes>
            <li Class="JobGiver_EscapingHoldingPlatform" />
          </subNodes>
        </li>

        <li Class="JobGiver_SightstealerAttack" />
        
        <li Class="JobGiver_WanderHerd">
          <maxDanger>Deadly</maxDanger>
          <ticksBetweenWandersRange>120~240</ticksBetweenWandersRange>
        </li>
      </subNodes>
    </thinkRoot>
  </ThinkTreeDef>

  <ThinkTreeDef>
    <defName>SightstealerConstant</defName>
    <thinkRoot Class="ThinkNode_Priority">
      <subNodes>
        <!-- Despawned -->
        <li Class="ThinkNode_Subtree">
          <treeDef>Despawned</treeDef>
        </li>

        <li Class="ThinkNode_ConditionalCanDoLordJobNow">
          <subNodes>
            <!-- Lord directives -->
            <li Class="ThinkNode_Subtree">
              <treeDef>LordDutyConstant</treeDef>
            </li>
          </subNodes>
        </li>
      </subNodes>
    </thinkRoot>
  </ThinkTreeDef>
</Defs>