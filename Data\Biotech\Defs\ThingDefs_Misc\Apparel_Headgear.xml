<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingDef Abstract="True" Name="MechanitorHeadsetBase" ParentName="HatMakeableBase">
    <graphicData>
      <drawSize>0.6</drawSize>
    </graphicData>
    <colorGenerator Class="ColorGenerator_Options">
      <options>
        <li>
          <weight>10</weight>
          <only>(0.9,0.9,0.9)</only>
        </li>
        <li>
          <weight>10</weight>
          <only>(0.33,0.33,0.33)</only>
        </li>
        <li>
          <weight>10</weight>
          <only>(0.65,0.65,0.65)</only>
        </li>
        <li>
          <weight>6</weight>
          <min>(0.3,0.3,0.3)</min>
          <max>(0.5,0.5,0.5)</max>
        </li>
      </options>
    </colorGenerator>
    <recipeMaker>
      <recipeUsers Inherit="False">
        <li>TableMachining</li>
      </recipeUsers>
      <displayPriority>700</displayPriority>
    </recipeMaker>
    <tradeability>Sellable</tradeability>
  </ThingDef>

  <ThingDef ParentName="MechanitorHeadsetBase">
    <defName>Apparel_AirwireHeadset</defName>
    <label>airwire headset</label>
    <description>A simple head-mounted comms computer that slightly enhances a mechanitor's control bandwidth. This allows them to control more mechs.</description>
    <techLevel>Spacer</techLevel>
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/AirwireHeadset/AirwireHeadset</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <costList>
      <Steel>50</Steel>
      <ComponentIndustrial>4</ComponentIndustrial>
    </costList>
    <recipeMaker>
      <researchPrerequisite>BasicMechtech</researchPrerequisite>
      <skillRequirements>
        <Crafting>4</Crafting>
      </skillRequirements>
    </recipeMaker>
    <statBases>
      <WorkToMake>2100</WorkToMake>
      <MaxHitPoints>60</MaxHitPoints>
      <Mass>0.08</Mass>
      <EquipDelay>2</EquipDelay>
    </statBases>
    <equippedStatOffsets>
      <MechBandwidth>3</MechBandwidth>
    </equippedStatOffsets>
    <apparel>
      <wornGraphicPath>Things/Pawn/Humanlike/Apparel/AirwireHeadset/AirwireHeadset</wornGraphicPath>
      <renderSkipFlags>
        <li>None</li>
      </renderSkipFlags>
      <countsAsClothingForNudity>false</countsAsClothingForNudity>
      <bodyPartGroups>
        <li>UpperHead</li>
      </bodyPartGroups>
      <layers>
        <li>Overhead</li>
      </layers>
      <scoreOffset>4</scoreOffset>
      <mechanitorApparel>true</mechanitorApparel>
    </apparel>
  </ThingDef>

  <ThingDef ParentName="MechanitorHeadsetBase">
    <defName>Apparel_ArrayHeadset</defName>
    <label>array headset</label>
    <description>A head-mounted computer and signaling array that enhances a mechanitor's control bandwidth. This allows them to control more mechs.</description>
    <techLevel>Spacer</techLevel>
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/ArrayHeadset/ArrayHeadset</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <costList>
      <Steel>50</Steel>
      <ComponentIndustrial>8</ComponentIndustrial>
    </costList>
    <recipeMaker>
      <researchPrerequisite>StandardMechtech</researchPrerequisite>
      <skillRequirements>
        <Crafting>5</Crafting>
      </skillRequirements>
    </recipeMaker>
    <statBases>
      <WorkToMake>2100</WorkToMake>
      <MaxHitPoints>60</MaxHitPoints>
      <Mass>0.08</Mass>
      <EquipDelay>2</EquipDelay>
    </statBases>
    <equippedStatOffsets>
      <MechBandwidth>6</MechBandwidth>
    </equippedStatOffsets>
    <apparel>
      <wornGraphicPath>Things/Pawn/Humanlike/Apparel/ArrayHeadset/ArrayHeadset</wornGraphicPath>
      <renderSkipFlags>
        <li>None</li>
      </renderSkipFlags>
      <countsAsClothingForNudity>false</countsAsClothingForNudity>
      <bodyPartGroups>
        <li>UpperHead</li>
      </bodyPartGroups>
      <layers>
        <li>Overhead</li>
      </layers>
      <scoreOffset>4</scoreOffset>
      <mechanitorApparel>true</mechanitorApparel>
    </apparel>
  </ThingDef>

  <ThingDef Name="ApparelArmorHelmetMechanitorBase" ParentName="ApparelArmorHelmetReconBase" Abstract="True">
    <tradeability>Sellable</tradeability>
  </ThingDef>

  <ThingDef ParentName="ApparelArmorHelmetMechanitorBase">
    <defName>Apparel_IntegratorHeadset</defName>
    <label>integrator headset</label>
    <description>An advanced head-mounted computer and signaling array that dramatically enhances a mechanitor's control bandwidth. This allows the wearer to control many more mechanoids at once.</description>
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/IntegratorHeadset/IntegratorHeadset</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>0.6</drawSize>
    </graphicData>
    <equippedStatOffsets>
      <MechBandwidth>9</MechBandwidth>
    </equippedStatOffsets>
    <statBases>
      <ArmorRating_Sharp>0</ArmorRating_Sharp>
      <ArmorRating_Blunt>0</ArmorRating_Blunt>
      <ArmorRating_Heat>0</ArmorRating_Heat>
      <Insulation_Cold>0</Insulation_Cold>
      <Insulation_Heat>0</Insulation_Heat>
    </statBases>
    <recipeMaker>
      <researchPrerequisite>HighMechtech</researchPrerequisite>
      <displayPriority>510</displayPriority>
    </recipeMaker>
    <costList>
      <ComponentIndustrial>4</ComponentIndustrial>
      <ComponentSpacer>4</ComponentSpacer>
      <Plasteel>50</Plasteel>
      <PowerfocusChip>1</PowerfocusChip>
    </costList>
    <apparel>
      <wornGraphicPath>Things/Pawn/Humanlike/Apparel/IntegratorHeadset/IntegratorHeadset</wornGraphicPath>
      <renderSkipFlags>
        <li>None</li>
      </renderSkipFlags>
      <countsAsClothingForNudity>false</countsAsClothingForNudity>
      <bodyPartGroups Inherit="False">
        <li>UpperHead</li>
      </bodyPartGroups>
    </apparel>
  </ThingDef>

  <ThingDef ParentName="ApparelArmorHelmetMechanitorBase">
    <defName>Apparel_ArmorHelmetMechCommander</defName>
    <label>mechcommander helmet</label>
    <description>A plasteel-weave combat helmet designed for battle mechanitors. It is embedded with computers and signal enhancers that moderately increase the wearer's maximum control bandwidth. This allows the mechanitor to control more mechs.</description>
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/MechcommanderHelmet/MechcommanderHelmet</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <statBases>
      <MaxHitPoints>150</MaxHitPoints>
      <ArmorRating_Sharp>0.69</ArmorRating_Sharp>
      <ArmorRating_Blunt>0.3</ArmorRating_Blunt>
      <ArmorRating_Heat>0.345</ArmorRating_Heat>
      <Insulation_Cold>3</Insulation_Cold>
      <Insulation_Heat>1.5</Insulation_Heat>
    </statBases>
    <equippedStatOffsets>
      <MechBandwidth>6</MechBandwidth>
    </equippedStatOffsets>
    <recipeMaker Inherit="False">
      <researchPrerequisite>StandardMechtech</researchPrerequisite>
      <recipeUsers>
        <li>TableMachining</li>
      </recipeUsers>
      <workSkill>Crafting</workSkill>
      <unfinishedThingDef>UnfinishedTechArmor</unfinishedThingDef>
      <skillRequirements>
        <Crafting>6</Crafting>
      </skillRequirements>
      <displayPriority>710</displayPriority>
    </recipeMaker>
    <costList Inherit="False">
      <SignalChip>1</SignalChip>
      <Plasteel>60</Plasteel>
      <ComponentIndustrial>6</ComponentIndustrial>
    </costList>
    <apparel>
      <scoreOffset>4</scoreOffset>
      <mechanitorApparel>true</mechanitorApparel>
      <wornGraphicPath>Things/Pawn/Humanlike/Apparel/MechcommanderHelmet/MechcommanderHelmet</wornGraphicPath>
    </apparel>
  </ThingDef>

  <ThingDef ParentName="ApparelArmorHelmetMechanitorBase">
    <defName>Apparel_ArmorHelmetMechlordHelmet</defName>
    <label>mechlord helmet</label>
    <description>A heavy plasteel-weave helmet packed with mechanitor-assistance gear. The mechlord helmet dramatically amplifies a mechanitor's bandwidth, but is somewhat less protective than dedicated heavy armor.</description>
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/MechlordHelmet/MechlordHelmet</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <statBases>
      <MaxHitPoints>240</MaxHitPoints>
    </statBases>
    <equippedStatOffsets>
      <MechBandwidth>12</MechBandwidth>
      <ShootingAccuracyPawn>-5</ShootingAccuracyPawn>
      <MeleeHitChance>-0.5</MeleeHitChance>
    </equippedStatOffsets>
    <recipeMaker Inherit="False">
      <researchPrerequisite>UltraMechtech</researchPrerequisite>
      <recipeUsers>
        <li>FabricationBench</li>
      </recipeUsers>
      <workSkill>Crafting</workSkill>
      <unfinishedThingDef>UnfinishedTechArmor</unfinishedThingDef>
      <skillRequirements>
        <Crafting>7</Crafting>
      </skillRequirements>
      <displayPriority>520</displayPriority>
    </recipeMaker>
    <costList Inherit="False">
      <Plasteel>120</Plasteel>
      <ComponentSpacer>6</ComponentSpacer>
      <NanostructuringChip>2</NanostructuringChip>
    </costList>
    <apparel>
      <tags>
        <li>RoyalTier7</li>
        <li>Mechlord</li>
      </tags>
      <scoreOffset>4</scoreOffset>
      <mechanitorApparel>true</mechanitorApparel>
      <wornGraphicPath>Things/Pawn/Humanlike/Apparel/MechlordHelmet/MechlordHelmet</wornGraphicPath>
      <forceEyesVisibleForRotations>
        <li>1</li>
        <li>2</li>
        <li>3</li>
      </forceEyesVisibleForRotations>
    </apparel>
  </ThingDef>

  <ThingDef ParentName="HatMakeableBase">
    <defName>Apparel_GasMask</defName>
    <label>gas mask</label>
    <description>A flexible mask worn over the face with an embedded air filter. It dramatically reduces exposure to pollution through the lungs, but does not protect the skin on the rest of the body.\n\nIt prevents tox gas irritants from entering through the eyes and mouth.</description>
    <possessionCount>1</possessionCount>
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/GasMask/GasMask</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>0.8</drawSize>
    </graphicData>
    <statBases>
      <MaxHitPoints>80</MaxHitPoints>
      <WorkToMake>6000</WorkToMake>
      <Mass>0.4</Mass>
      <ArmorRating_Sharp>0.18</ArmorRating_Sharp>
      <ArmorRating_Blunt>0.3</ArmorRating_Blunt>
      <Insulation_Cold>1.5</Insulation_Cold>
      <EquipDelay>0.8</EquipDelay>
    </statBases>
    <equippedStatOffsets>
      <ToxicEnvironmentResistance>0.8</ToxicEnvironmentResistance>
    </equippedStatOffsets>
    <recipeMaker>
      <researchPrerequisite>Machining</researchPrerequisite>
      <unfinishedThingDef>UnfinishedMask</unfinishedThingDef>
      <recipeUsers Inherit="False">
        <li>TableMachining</li>
      </recipeUsers>
      <displayPriority>215</displayPriority>
    </recipeMaker>
    <costList>
      <Steel>20</Steel>
      <Chemfuel>20</Chemfuel>
    </costList>
    <apparel>
      <countsAsClothingForNudity>false</countsAsClothingForNudity>
      <developmentalStageFilter>Child, Adult</developmentalStageFilter>
      <immuneToToxGasExposure>true</immuneToToxGasExposure>
      <bodyPartGroups>
        <li>FullHead</li>
      </bodyPartGroups>
      <wornGraphicPath>Things/Pawn/Humanlike/Apparel/GasMask/GasMask</wornGraphicPath>
      <renderSkipFlags>
        <li>None</li>
      </renderSkipFlags>
      <drawData>
        <dataNorth>
          <layer>-3</layer>
        </dataNorth>
      </drawData>
      <layers>
        <li>Overhead</li>
      </layers>
      <tags>
        <li>IndustrialBasic</li>
      </tags>
      <forceEyesVisibleForRotations>
        <li>1</li>
        <li>2</li>
        <li>3</li>
      </forceEyesVisibleForRotations>
    </apparel>
  </ThingDef>

  <ThingDef ParentName="HatMakeableBase">
    <defName>Apparel_ClothMask</defName>
    <label>face mask</label>
    <description>A piece of fabric which covers the wearer's mouth and nose with several layers of textile. It is remarkably effective in reducing the effects of environmental toxins.</description>
    <techLevel>Neolithic</techLevel>
    <possessionCount>1</possessionCount>
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/ClothMask/ClothMask</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>(0.7, 0.7)</drawSize>
    </graphicData>
    <stuffCategories>
      <li>Fabric</li>
    </stuffCategories>
    <costStuffCount>10</costStuffCount>
    <statBases>
      <WorkToMake>800</WorkToMake>
      <MaxHitPoints>40</MaxHitPoints>
      <Mass>0.04</Mass>
      <StuffEffectMultiplierArmor>0.2</StuffEffectMultiplierArmor>
      <StuffEffectMultiplierInsulation_Cold>0.02</StuffEffectMultiplierInsulation_Cold>
      <StuffEffectMultiplierInsulation_Heat>0.02</StuffEffectMultiplierInsulation_Heat>
      <EquipDelay>1</EquipDelay>
    </statBases>
    <equippedStatOffsets>
      <ToxicEnvironmentResistance>0.5</ToxicEnvironmentResistance>
    </equippedStatOffsets>
    <apparel>
      <developmentalStageFilter>Child, Adult</developmentalStageFilter>
      <bodyPartGroups>
        <li>Mouth</li>
      </bodyPartGroups>
      <wornGraphicPath>Things/Pawn/Humanlike/Apparel/ClothMask/ClothMask</wornGraphicPath>
      <renderSkipFlags>
        <li>None</li>
      </renderSkipFlags>
      <drawData>
        <dataNorth>
          <layer>-3</layer>
        </dataNorth>
      </drawData>
      <countsAsClothingForNudity>false</countsAsClothingForNudity>
      <layers>
        <li>Overhead</li>
      </layers>
    </apparel>
    <recipeMaker>
      <researchPrerequisite>ComplexClothing</researchPrerequisite>
      <recipeUsers>
        <li>ElectricTailoringBench</li>
        <li>HandTailoringBench</li>
      </recipeUsers>
      <displayPriority>165</displayPriority>
    </recipeMaker>
  </ThingDef>

</Defs>