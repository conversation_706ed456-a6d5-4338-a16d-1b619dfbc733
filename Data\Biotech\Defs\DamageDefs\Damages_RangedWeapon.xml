<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <DamageDef ParentName="Bullet">
    <defName>BulletToxic</defName>
    <label>toxic bullet</label>
    <additionalHediffs>
      <li>
        <hediff>ToxicBuildup</hediff>
        <severityPerDamageDealt>0.0065</severityPerDamageDealt>
        <victimSeverityScaling>ToxicResistance</victimSeverityScaling>
        <inverseStatScaling>true</inverseStatScaling>
        <victimSeverityScalingByInvBodySize>true</victimSeverityScalingByInvBodySize>
      </li>
    </additionalHediffs>
    <applyAdditionalHediffsIfHuntingForFood>false</applyAdditionalHediffsIfHuntingForFood>
    <impactSoundType>Bullet</impactSoundType>
  </DamageDef>

</Defs>