<?xml version="1.0" encoding="utf-8" ?>
<Defs>
  
  <ThingDef ParentName="BuildingBase">
    <defName>BandNode</defName>
    <label>band node</label>
    <description>A mechanoid-band signal amplifier. Band nodes must be tuned to a specific mechanitor. This will add 1 bandwidth to that mechanitor's total bandwidth, allowing them to control more mechanoids.\n\nOnce a band node is constructed, it can be quickly tuned to a mechanitor. However, retuning a band node to a different mechanitor takes significantly longer.</description>
    <tickerType>Normal</tickerType>
    <rotatable>false</rotatable>
    <canOverlapZones>false</canOverlapZones>
    <drawerType>MapMeshAndRealTime</drawerType>
    <fillPercent>0.4</fillPercent>
    <graphicData>
      <texPath>Things/Building/Misc/BandNode</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>(2,2)</drawSize>
      <shadowData>
        <volume>(1.5, 0.35, 1.2)</volume>
        <offset>(0, 0, -0.15)</offset>
      </shadowData>
    </graphicData>
    <altitudeLayer>Building</altitudeLayer>
    <passability>PassThroughOnly</passability>
    <building>
      <ai_chillDestination>false</ai_chillDestination>
      <buildingTags>
        <li>Biotech</li>
      </buildingTags>
    </building>
    <constructionSkillPrerequisite>4</constructionSkillPrerequisite>
    <designationCategory>Biotech</designationCategory>
    <uiOrder>2440</uiOrder>
    <statBases>
      <MaxHitPoints>150</MaxHitPoints>
      <WorkToBuild>8000</WorkToBuild>
      <Mass>25</Mass>
      <Flammability>0.5</Flammability>
    </statBases>
    <size>(2,2)</size>
    <costList>
      <Steel>200</Steel>
      <ComponentIndustrial>4</ComponentIndustrial>
    </costList>
    <researchPrerequisites>
      <li>BasicMechtech</li>
    </researchPrerequisites>
    <comps>
      <li Class="CompProperties_Power">
        <compClass>CompPowerTrader</compClass>
        <basePowerConsumption>300</basePowerConsumption>
      </li>
      <li Class="CompProperties_Flickable"/>
      <li Class="CompProperties_BandNode">
        <hediff>BandNode</hediff>
        <untunedEffect>BandNodeUntuned</untunedEffect>
        <tuningEffect>BandNodeTuning</tuningEffect>
        <tunedEffect>BandNodeTuned</tunedEffect>
        <retuningEffect>BandNodeRetuning</retuningEffect>
        <tuningCompleteSound>BandNodeTuning_Complete</tuningCompleteSound>
        <powerConsumptionIdle>100</powerConsumptionIdle>
      </li>
    </comps>
    <placeWorkers>
      <li>PlaceWorker_BandNode</li>
    </placeWorkers>
    <drawPlaceWorkersWhileSelected>true</drawPlaceWorkersWhileSelected>
  </ThingDef>

  <ThingDef ParentName="BuildingBase">
    <defName>ToxifierGenerator</defName>
    <label>toxifier generator</label>
    <description>This electrical generator very slowly pollutes the terrain in a radius around it while producing energy. If all terrain in the radius is polluted, the generator will shut down.</description>
    <tickerType>Normal</tickerType>
    <graphicData>
      <texPath>Things/Building/Power/ToxifierGenerator</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>(2,2)</drawSize>
      <shadowData>
        <volume>(1.6, 1, 1.5)</volume>
      </shadowData>
    </graphicData>
    <altitudeLayer>Building</altitudeLayer>
    <passability>Impassable</passability>
    <fillPercent>1</fillPercent>
    <rotatable>false</rotatable>
    <building>
      <ai_chillDestination>false</ai_chillDestination>
      <soundAmbient>Toxifier_Working</soundAmbient>
    </building>
    <constructionSkillPrerequisite>4</constructionSkillPrerequisite>
    <designationCategory>Power</designationCategory>
    <uiOrder>2100</uiOrder>
    <canOverlapZones>false</canOverlapZones>
    <statBases>
      <MaxHitPoints>300</MaxHitPoints>
      <WorkToBuild>8000</WorkToBuild>
      <Mass>25</Mass>
      <Flammability>0.5</Flammability>
    </statBases>
    <size>(2,2)</size>
    <costList>
      <Steel>125</Steel>
      <ComponentIndustrial>3</ComponentIndustrial>
    </costList>
    <researchPrerequisites>
      <li>ToxifierGenerator</li>
    </researchPrerequisites>
    <comps>
      <li Class="CompProperties_Power">
        <compClass>CompPowerPlant</compClass>
        <basePowerConsumption>-1400</basePowerConsumption>
        <transmitsPower>true</transmitsPower>
        <soundAmbientProducingPower>ChemfuelFiredGenerator_Ambience</soundAmbientProducingPower>
      </li>
      <li Class="CompProperties_Toxifier">
        <radius>26.9</radius>
        <cellsToPollute>6</cellsToPollute>
        <pollutionIntervalTicks>180000</pollutionIntervalTicks><!-- 3 days -->
      </li>
      <li Class="CompProperties_Stunnable">
        <useLargeEMPEffecter>true</useLargeEMPEffecter>
        <affectedDamageDefs>
          <li>EMP</li>
        </affectedDamageDefs>
      </li>
    </comps>
    <placeWorkers>
      <li>PlaceWorker_ToxifierGenerator</li>
    </placeWorkers>
    <drawPlaceWorkersWhileSelected>true</drawPlaceWorkersWhileSelected>
  </ThingDef>

  <ThingDef Name="GeneBuildingBase" ParentName="BuildingBase" Abstract="True">
    <researchPrerequisites>
      <li>Xenogermination</li>
    </researchPrerequisites>
    <altitudeLayer>Building</altitudeLayer>
    <designationCategory>Biotech</designationCategory>
    <uiOrder>2100</uiOrder>
    <pathCost>42</pathCost>
    <building>
      <ai_chillDestination>false</ai_chillDestination>
      <buildingTags>
        <li>Biotech</li>
      </buildingTags>
    </building>
    <placeWorkers>
      <li>PlaceWorker_ShowFacilitiesConnections</li>
    </placeWorkers>
    <drawPlaceWorkersWhileSelected>true</drawPlaceWorkersWhileSelected>
    <comps>
      <li Class="CompProperties_Forbiddable"/>
    </comps>
  </ThingDef>

  <ThingDef ParentName="GeneBuildingBase">
    <defName>GeneAssembler</defName>
    <label>gene assembler</label>
    <description>A workbench for creating implantable xenogerms from genepacks stored in nearby gene banks. Genepacks are not consumed in this process and can be reused.</description>
    <thingClass>Building_GeneAssembler</thingClass>
    <passability>PassThroughOnly</passability>
    <castEdgeShadows>true</castEdgeShadows>
    <fillPercent>0.4</fillPercent>
    <graphicData>
      <texPath>Things/Building/Misc/GeneAssembler/GeneAssembler</texPath>
      <graphicClass>Graphic_Multi</graphicClass>
      <drawSize>(3, 2)</drawSize>
      <shadowData>
        <volume>(2.55, 0.75, 1.7)</volume>
      </shadowData>
    </graphicData>
    <descriptionHyperlinks>
      <ThingDef>GeneBank</ThingDef>
      <ThingDef>GeneProcessor</ThingDef>
      <ThingDef>Genepack</ThingDef>
      <ThingDef>Xenogerm</ThingDef>
    </descriptionHyperlinks>
    <tickerType>Normal</tickerType>
    <size>(3, 2)</size>
    <hasInteractionCell>true</hasInteractionCell>
    <interactionCellOffset>(0, 0, 2)</interactionCellOffset>
    <defaultPlacingRot>South</defaultPlacingRot>
    <canOverlapZones>false</canOverlapZones>
    <statBases>
      <MaxHitPoints>500</MaxHitPoints>
      <WorkToBuild>12000</WorkToBuild>
      <Mass>35</Mass>
      <Flammability>0.5</Flammability>
      <AssemblySpeedFactor>1.0</AssemblySpeedFactor>
    </statBases>
    <costList>
      <Steel>200</Steel>
      <ComponentIndustrial>4</ComponentIndustrial>
    </costList>
    <constructionSkillPrerequisite>4</constructionSkillPrerequisite>
    <comps>
      <li Class="CompProperties_Power">
        <compClass>CompPowerTrader</compClass>
        <basePowerConsumption>100</basePowerConsumption>
        <idlePowerDraw>25</idlePowerDraw>
      </li>
      <li Class="CompProperties_Flickable"/>
      <li Class="CompProperties_AffectedByFacilities">
        <linkableFacilities>
          <li>GeneBank</li>
          <li>GeneProcessor</li>
        </linkableFacilities>
      </li>
    </comps>
  </ThingDef>

  <ThingDef ParentName="GeneBuildingBase">
    <defName>GeneBank</defName>
    <label>gene bank</label>
    <description>This building can store genepacks and make them usable to create new xenogerms, when placed near a gene assembler. Large gene libraries require many gene banks.\n\nWhen powered, gene banks prevent genepacks from deteriorating and will slowly repair deterioration.</description>
    <graphicData>
      <texPath>Things/Building/Misc/Genebank/Genebank</texPath>
      <graphicClass>Graphic_Multi</graphicClass>
      <drawSize>(2, 1)</drawSize>
      <shadowData>
        <volume>(1.7, 0.3, 0.85)</volume>
      </shadowData>
    </graphicData>
    <castEdgeShadows>true</castEdgeShadows>
    <minifiedDef>MinifiedThing</minifiedDef>
    <tickerType>Rare</tickerType>
    <passability>PassThroughOnly</passability>
    <canOverlapZones>false</canOverlapZones>
    <size>(2, 1)</size>
    <statBases>
      <MaxHitPoints>200</MaxHitPoints>
      <WorkToBuild>5000</WorkToBuild>
      <Mass>20</Mass>
      <Flammability>0.5</Flammability>
    </statBases>
    <thingCategories>
      <li>BuildingsProduction</li>
    </thingCategories>
    <inspectorTabs>
      <li>ITab_ContentsGenepackHolder</li>
    </inspectorTabs>
    <costList>
      <Steel>50</Steel>
      <ComponentIndustrial>1</ComponentIndustrial>
    </costList>
    <constructionSkillPrerequisite>4</constructionSkillPrerequisite>
    <descriptionHyperlinks>
      <ThingDef>Genepack</ThingDef>
      <ThingDef>GeneAssembler</ThingDef>
    </descriptionHyperlinks>
    <comps>
      <li Class="CompProperties_GenepackContainer">
        <maxCapacity>4</maxCapacity>
      </li>
      <li Class="CompProperties_Power">
        <compClass>CompPowerTrader</compClass>
        <basePowerConsumption>40</basePowerConsumption>
      </li>
      <li Class="CompProperties_Facility">
        <maxSimultaneous>100</maxSimultaneous>
        <maxDistance>12.9</maxDistance>
        <showMaxSimultaneous>false</showMaxSimultaneous>
      </li>
      <li Class="CompProperties_Flickable"/>
    </comps>
  </ThingDef>

  <ThingDef ParentName="BuildingBase">
    <defName>GeneExtractor</defName>
    <label>gene extractor</label>
    <description>An automated surgery machine that can extract a person's genes and create a genepack from them. The extracted genes will be randomly selected from the person's xenogenes and germline genes. You can force someone into the machine, but they won't like it.\n\nExtracting a person's genes while their genes are currently regrowing will kill them. Otherwise, gene extraction causes no lasting harm.\n\nGenes that require archite capsules are too complex to be extracted.</description>
    <thingClass>Building_GeneExtractor</thingClass>
    <containedPawnsSelectable>true</containedPawnsSelectable>
    <passability>PassThroughOnly</passability>
    <fillPercent>0.4</fillPercent>
    <castEdgeShadows>true</castEdgeShadows>
    <drawerType>MapMeshAndRealTime</drawerType>
    <tickerType>Normal</tickerType>
    <graphicData>
      <texPath>Things/Building/Misc/GeneExtractor/GeneExtractor</texPath>
      <graphicClass>Graphic_Multi</graphicClass>
      <drawSize>(2, 2)</drawSize>
      <shadowData>
        <volume>(1.7, 0.8, 1.7)</volume>
      </shadowData>
    </graphicData>
    <size>(2, 2)</size>
    <canOverlapZones>false</canOverlapZones>
    <hasInteractionCell>true</hasInteractionCell>
    <interactionCellOffset>(1, 0, 2)</interactionCellOffset>
    <defaultPlacingRot>South</defaultPlacingRot>
    <designationCategory>Biotech</designationCategory>
    <uiOrder>2100</uiOrder>
    <researchPrerequisites>
      <li>Xenogermination</li>
    </researchPrerequisites>
    <building>
      <buildingTags>
        <li>Biotech</li>
      </buildingTags>
    </building>
    <statBases>
      <MaxHitPoints>350</MaxHitPoints>
      <WorkToBuild>9000</WorkToBuild>
      <Mass>85</Mass>
      <Flammability>0.5</Flammability>
    </statBases>
    <costList>
      <Steel>200</Steel>
      <ComponentIndustrial>8</ComponentIndustrial>
    </costList>
    <constructionSkillPrerequisite>4</constructionSkillPrerequisite>
    <comps>
      <li Class="CompProperties_Power">
        <compClass>CompPowerTrader</compClass>
        <basePowerConsumption>200</basePowerConsumption>
        <idlePowerDraw>50</idlePowerDraw>
      </li>
      <li Class="CompProperties_Flickable"/>
    </comps>
  </ThingDef>

  <ThingDef ParentName="BuildingBase">
    <defName>GrowthVat</defName>
    <label>growth vat</label>
    <description>A vat of fluid which can gestate an embryo or accelerate a child's growth.\n\nWhile growing or gestating, the vat must be fed with a constant supply of nutrition to provide the feedstock for growth. Gestating an embryo takes twice the nutrition of accelerating a child's growth.\n\nEmbryos are gestated for 9 days before a baby is formed. There is a risk of health complications.\n\nChild growth can only be accelerated up to age 18, but not beyond. While growing, a child's skills will slowly increase. Children raised in growth vats will have lower skills and gain fewer passions than normal.\n\nThe device uses chemical injections, electrical stimulation, and simple mechanite treatments to push the body to grow. Growth vats are often used to quickly manufacture engineered workers or warriors.</description>
    <thingClass>Building_GrowthVat</thingClass>
    <containedPawnsSelectable>true</containedPawnsSelectable>
    <tickerType>Normal</tickerType>
    <graphicData>
      <texPath>Things/Building/Misc/GrowthVat/GrowthVat</texPath>
      <graphicClass>Graphic_Multi</graphicClass>
      <drawSize>(1,2)</drawSize>
      <shadowData>
        <volume>(0.85, 0.3, 1.7)</volume>
      </shadowData>
    </graphicData>
    <castEdgeShadows>true</castEdgeShadows>
    <defaultPlacingRot>East</defaultPlacingRot>
    <size>(1,2)</size>
    <statBases>
      <MaxHitPoints>500</MaxHitPoints>
      <WorkToBuild>8000</WorkToBuild>
      <Mass>30</Mass>
      <Flammability>0.5</Flammability>
    </statBases>
    <costList>
      <Steel>150</Steel>
      <ComponentIndustrial>4</ComponentIndustrial>
    </costList>
    <altitudeLayer>Building</altitudeLayer>
    <passability>PassThroughOnly</passability>
    <pathCost>42</pathCost>
    <blockWind>true</blockWind>
    <drawerType>MapMeshAndRealTime</drawerType>
    <fillPercent>0.5</fillPercent>
    <canOverlapZones>false</canOverlapZones>
    <designationCategory>Biotech</designationCategory>
    <uiOrder>2200</uiOrder>
    <hasInteractionCell>true</hasInteractionCell>
    <interactionCellOffset>(0,0,-1)</interactionCellOffset>
    <inspectorTabs>
      <li>ITab_BiosculpterNutritionStorage</li>
      <li>ITab_Genes</li>
    </inspectorTabs>
    <researchPrerequisites>
      <li>GrowthVats</li>
    </researchPrerequisites>
    <building>
      <ai_chillDestination>false</ai_chillDestination>
      <haulToContainerDuration>120</haulToContainerDuration>
      <buildingTags>
        <li>Biotech</li>
      </buildingTags>
      <fixedStorageSettings>
        <filter>
          <categories>
            <li>Foods</li>
          </categories>
          <specialFiltersToDisallow>
            <li>AllowPlantFood</li>
          </specialFiltersToDisallow>
        </filter>
      </fixedStorageSettings>
      <defaultStorageSettings>
        <filter>
          <categories>
            <li>Foods</li>
          </categories>
          <disallowedCategories>
            <li>EggsFertilized</li>
          </disallowedCategories>
          <disallowedThingDefs>
            <li>InsectJelly</li>
            <li>MealLavish</li>
            <li>MealLavish_Veg</li>
            <li>MealLavish_Meat</li>
            <li>HemogenPack</li>
            <li>Chocolate</li>
          </disallowedThingDefs>
        </filter>
      </defaultStorageSettings>
    </building>
    <constructionSkillPrerequisite>4</constructionSkillPrerequisite>
    <comps>
      <li Class="CompProperties_Power">
        <compClass>CompPowerTrader</compClass>
        <basePowerConsumption>200</basePowerConsumption>
        <idlePowerDraw>80</idlePowerDraw>
      </li>
      <li Class="CompProperties_Flickable"/>
    </comps>
  </ThingDef>

  <ThingDef ParentName="BuildingBase">
    <defName>BurnoutMechlinkBooster</defName>
    <label>mechband antenna</label>
    <description>A mech-band signaling device. When activated, it puts out a powerful mech signal pulse, which will attract an ultra-heavy feral mechanoid to attack with its escorts. If you can defeat it, you can collect special technologies from its corpse. The signal pulse burns out the transmitter, so this building can only be used once.</description>
    <graphicData>
      <texPath>Things/Building/Misc/MechbandAntenna</texPath>
      <graphicClass>Graphic_Multi</graphicClass>
      <drawSize>(2,1)</drawSize>
      <shadowData>
        <volume>(1.7, 0.75, 0.8)</volume>
        <offset>(0.15, 0, 0)</offset>
      </shadowData>
    </graphicData>
    <altitudeLayer>Building</altitudeLayer>
    <passability>PassThroughOnly</passability>
    <fillPercent>0.3</fillPercent>
    <building>
      <ai_chillDestination>false</ai_chillDestination>
      <buildingTags>
        <li>Biotech</li>
      </buildingTags>
    </building>
    <designationCategory>Biotech</designationCategory>
    <uiOrder>2450</uiOrder>
    <statBases>
      <MaxHitPoints>80</MaxHitPoints>
      <WorkToBuild>8000</WorkToBuild>
      <Mass>25</Mass>
      <Flammability>0.5</Flammability>
    </statBases>
    <size>(2,1)</size>
    <costList>
      <Steel>50</Steel>
      <ComponentIndustrial>4</ComponentIndustrial>
    </costList>
    <killedLeavings>
      <ChunkSlagSteel>3</ChunkSlagSteel>
    </killedLeavings>
    <researchPrerequisites>
      <li>StandardMechtech</li>
    </researchPrerequisites>
    <comps>
      <li Class="CompProperties_Usable">
        <useJob>TriggerObject</useJob>
        <useLabel>Summon warqueen threat</useLabel>
        <useDuration>900</useDuration>
        <floatMenuFactionIcon>Mechanoid</floatMenuFactionIcon>
      </li>
      <li Class="CompProperties_Useable_CallBossgroup">
        <bossgroupDef>Warqueen</bossgroupDef>
        <effecterDef>BurnoutMechlinkBoosterUsed</effecterDef>
        <prepareEffecterDef>MechBandAntennaPrepared</prepareEffecterDef>
        <unlockedLetterLabelKey>LetterLabelBossgroupCallerUnlocked</unlockedLetterLabelKey>
        <unlockedLetterTextKey>LetterBossgroupCallerUnlocked</unlockedLetterTextKey>
        <delayTicks>120</delayTicks>
      </li>
      <li Class="CompProperties_UseEffectDestroySelf">
        <delayTicks>120</delayTicks>
        <spawnKilledLeavings>true</spawnKilledLeavings>
        <effecterDef>MechbandBuildingDestroyed</effecterDef>
      </li>
    </comps>
    <tickerType>Normal</tickerType>
    <filthLeaving>Filth_MachineBits</filthLeaving>
  </ThingDef>

  <ThingDef ParentName="BuildingBase">
    <defName>MechbandDish</defName>
    <label>mechband dish</label>
    <description>A mech-band signaling device more powerful than the normal mechband antenna. When activated, it puts out an ultra-powerful mech signal pulse, which will attract a mechanoid commander to attack with its escorts. If you can defeat it, you can collect special technologies from its corpse. The signal pulse burns out the dish, so this building can only be used once.</description>
    <graphicData>
      <texPath>Things/Building/Misc/MechbandDish</texPath>
      <graphicClass>Graphic_Multi</graphicClass>
      <drawSize>(2,2)</drawSize>
      <shadowData>
        <volume>(1.1, 0.75, 1.1</volume>
      </shadowData>
    </graphicData>
    <altitudeLayer>Building</altitudeLayer>
    <passability>PassThroughOnly</passability>
    <fillPercent>0.3</fillPercent>
    <building>
      <ai_chillDestination>false</ai_chillDestination>
      <buildingTags>
        <li>Biotech</li>
      </buildingTags>
    </building>
    <designationCategory>Biotech</designationCategory>
    <uiOrder>2460</uiOrder>
    <statBases>
      <MaxHitPoints>150</MaxHitPoints>
      <WorkToBuild>8000</WorkToBuild>
      <Mass>25</Mass>
      <Flammability>0.5</Flammability>
    </statBases>
    <size>(2,2)</size>
    <costList>
      <Steel>75</Steel>
      <Plasteel>25</Plasteel>
      <ComponentSpacer>2</ComponentSpacer>
    </costList>
    <killedLeavings>
      <ChunkSlagSteel>5</ChunkSlagSteel>
    </killedLeavings>
    <researchPrerequisites>
      <li>HighMechtech</li>
    </researchPrerequisites>
    <comps>
      <li Class="CompProperties_Usable">
        <useJob>TriggerObject</useJob>
        <useLabel>Summon apocriton threat</useLabel>
        <useDuration>900</useDuration>
        <floatMenuFactionIcon>Mechanoid</floatMenuFactionIcon>
      </li>
      <li Class="CompProperties_Useable_CallBossgroup">
        <bossgroupDef>Apocriton</bossgroupDef>
        <effecterDef>MechbandDishUsed</effecterDef>
        <prepareEffecterDef>MechBandDishPrepared</prepareEffecterDef>
        <unlockedLetterLabelKey>LetterLabelBossgroupCallerUnlocked</unlockedLetterLabelKey>
        <unlockedLetterTextKey>LetterBossgroupCallerUnlocked</unlockedLetterTextKey>
        <delayTicks>120</delayTicks>
      </li>
      <li Class="CompProperties_UseEffectDestroySelf">
        <delayTicks>120</delayTicks>
        <spawnKilledLeavings>true</spawnKilledLeavings>
        <effecterDef>MechbandBuildingDestroyed</effecterDef>
      </li>
    </comps>
    <tickerType>Normal</tickerType>
    <filthLeaving>Filth_MachineBits</filthLeaving>
  </ThingDef>

  <ThingDef ParentName="BuildingBase">
    <defName>PollutionPump</defName>
    <label>pollution pump</label>
    <description>A groundwater-filtering pump which slowly cleans polluted terrain. The extracted pollution is formed into toxic wastepacks which must be hauled away.</description>
    <tickerType>Normal</tickerType>
    <minifiedDef>MinifiedThing</minifiedDef>
    <thingCategories>
      <li>BuildingsMisc</li>
    </thingCategories>
    <graphicData>
      <texPath>Things/Building/Production/PollutionPump</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>(1,1)</drawSize>
    </graphicData>
    <uiIconScale>0.85</uiIconScale>
    <rotatable>false</rotatable>
    <altitudeLayer>Building</altitudeLayer>
    <passability>PassThroughOnly</passability>
    <fillPercent>0.2</fillPercent>
    <building>
      <ai_chillDestination>false</ai_chillDestination>
      <buildingTags>
        <li>Biotech</li>
      </buildingTags>
    </building>
    <constructionSkillPrerequisite>3</constructionSkillPrerequisite>
    <designationCategory>Biotech</designationCategory>
    <statBases>
      <MaxHitPoints>200</MaxHitPoints>
      <WorkToBuild>4000</WorkToBuild>
      <Mass>15</Mass>
      <Flammability>0.5</Flammability>
    </statBases>
    <size>(1,1)</size>
    <costList>
      <Steel>75</Steel>
      <ComponentIndustrial>1</ComponentIndustrial>
    </costList>
    <researchPrerequisites>
      <li>Electricity</li>
    </researchPrerequisites>
    <comps>
      <li Class="CompProperties_Power">
        <compClass>CompPowerTrader</compClass>
        <basePowerConsumption>200</basePowerConsumption>
      </li>
      <li Class="CompProperties_PollutionPump">
        <radius>10.9</radius>
        <pumpEffecterDef>PollutionPumped</pumpEffecterDef>
        <pumpsPerWastepack>6</pumpsPerWastepack>
        <intervalTicks>15000</intervalTicks>
      </li>
      <li Class="CompProperties_Flickable"/>
    </comps>
    <placeWorkers>
      <li>PlaceWorker_PollutionPump</li>
    </placeWorkers>
    <drawPlaceWorkersWhileSelected>true</drawPlaceWorkersWhileSelected>
  </ThingDef>

  <ThingDef Name="ShuttleCrashedExitable" ParentName="ShuttleCrashedBase">
    <defName>ShuttleCrashed_Exitable</defName>
    <comps>
      <li Class="CompProperties_Shuttle" />
      <li Class="CompProperties_Transporter">
        <massCapacity>1000</massCapacity>
        <max1PerGroup>true</max1PerGroup>
        <showOverallStats>false</showOverallStats>
        <pawnExitSound>Shuttle_PawnExit</pawnExitSound>
      </li>
    </comps>
  </ThingDef>

  <ThingDef ParentName="ShuttleCrashedExitable">
    <defName>ShuttleCrashed_Exitable_Mechanitor</defName>
    <label>crashed mechanitor ship</label>
    <description>A short-range ship used by a mechanitor to transport mechs for low-orbit or planetary travel.</description>
    <graphicData>
      <texPath>Things/Building/Misc/MechanitorShipCrashed</texPath>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="BuildingBase">
    <defName>WastepackAtomizer</defName>
    <label>wastepack atomizer</label>
    <description>A glittertech device that deconstructs toxic wastepacks into inert gases using advanced molecular processes. The process requires considerable amounts of power.</description>
    <thingClass>Building_WastepackAtomizer</thingClass>
    <drawerType>MapMeshAndRealTime</drawerType>
    <tickerType>Normal</tickerType>
    <graphicData>
      <texPath>Things/Building/Misc/WastepackAtomizer</texPath>
      <graphicClass>Graphic_Multi</graphicClass>
      <drawSize>(3,2)</drawSize>
      <shadowData>
        <volume>(3, 0.35, 2)</volume>
      </shadowData>
    </graphicData>
    <altitudeLayer>Building</altitudeLayer>
    <passability>PassThroughOnly</passability>
    <canOverlapZones>false</canOverlapZones>
    <pathCost>42</pathCost>
    <blockWind>true</blockWind>
    <fillPercent>0.5</fillPercent>
    <building>
      <buildingTags>
        <li>Biotech</li>
      </buildingTags>
      <ai_chillDestination>false</ai_chillDestination>
      <wastepackAtomizerBottomGraphic>
        <texPath>Things/Building/Misc/WastepackAtomizer_BottomLayer</texPath>
        <graphicClass>Graphic_Multi</graphicClass>
        <drawSize>(3,2)</drawSize>
      </wastepackAtomizerBottomGraphic>
      <wastepackAtomizerWindowGraphic>
        <texPath>Things/Building/Misc/WastepackAtomizerWindow</texPath>
        <graphicClass>Graphic_Multi</graphicClass>
        <drawSize>(3,2)</drawSize>
      </wastepackAtomizerWindowGraphic>
      <wastepackAtomizerOperationEffecter>WastepackAtomizer_Operating</wastepackAtomizerOperationEffecter>
    </building>
    <designationCategory>Biotech</designationCategory>
    <hasInteractionCell>true</hasInteractionCell>
    <interactionCellOffset>(0, 0, -1)</interactionCellOffset>
    <statBases>
      <MaxHitPoints>500</MaxHitPoints>
      <WorkToBuild>30000</WorkToBuild>
      <Mass>80</Mass>
      <Flammability>0.5</Flammability>
    </statBases>
    <size>(3,2)</size>
    <costList>
      <Steel>200</Steel>
      <Plasteel>50</Plasteel>
      <NanostructuringChip>1</NanostructuringChip>
    </costList>
    <researchPrerequisites>
      <li>WastepackAtomizer</li>
    </researchPrerequisites>
    <constructionSkillPrerequisite>8</constructionSkillPrerequisite>
    <comps>
      <li Class="CompProperties_Power">
        <compClass>CompPowerTrader</compClass>
        <basePowerConsumption>400</basePowerConsumption>
      </li>
      <li Class="CompProperties_Atomizer">
        <thingDef>Wastepack</thingDef>
        <stackLimit>10</stackLimit>
        <drawContainedThing>false</drawContainedThing>
        <ticksPerAtomize>30000</ticksPerAtomize>
        <resolveEffecter>AtomizerResolve</resolveEffecter>
        <workingEffecter>WastepackAtomizer_Working</workingEffecter>
        <materialsAddedSound>WastepackAtomizer_MaterialInserted</materialsAddedSound>
        <contentsDrawOffset>(0.05, 0, 0.2)</contentsDrawOffset>
      </li>
      <li Class="CompProperties_Flickable"/>
    </comps>
  </ThingDef>

  <ThingDef ParentName="TorchLamp">
    <defName>SanguphageMeetingTorch</defName>
    <label>blood torch</label>
    <description>A specially-treated wooden torch that produces a red light over a large area. Sanguophages gather around torches like these during their ceremonial meetings. The sanguine glow is particularly pleasing to individuals with the bloodfeeder gene. It can be automatically refueled with wood, and produces a small amount of heat.</description>
    <graphicData>
      <texPath>Things/Building/Misc/Redtorch</texPath>
    </graphicData>
    <uiIconPath>Things/Building/Misc/Redtorch_MenuIcon</uiIconPath>
    <minifiedDef>MinifiedThing</minifiedDef>
    <designationCategory Inherit="False" IsNull="True"/>
    <statBases>
      <Mass>1</Mass>
    </statBases>
    <thingCategories>
      <li>BuildingsMisc</li>
    </thingCategories>
    <comps Inherit="False">
      <li Class="CompProperties_Glower">
        <glowRadius>10</glowRadius>
        <glowColor>(460, 220, 205, 0)</glowColor>
      </li>
      <li Class="CompProperties_Refuelable">
        <fuelConsumptionRate>1.5</fuelConsumptionRate>
        <fuelCapacity>20.0</fuelCapacity>
        <initialFuelPercent>0.1</initialFuelPercent>
        <fuelConsumptionPerTickInRain>0.0006</fuelConsumptionPerTickInRain>
        <fuelFilter>
          <thingDefs>
            <li>WoodLog</li>
          </thingDefs>
        </fuelFilter>
        <showAllowAutoRefuelToggle>true</showAllowAutoRefuelToggle>
      </li>
      <li Class="CompProperties_HeatPusher">
        <compClass>CompHeatPusherPowered</compClass>
        <heatPerSecond>3.5</heatPerSecond>
        <heatPushMaxTemperature>23</heatPushMaxTemperature>
      </li>
      <li Class="CompProperties_SanguophageMeetingFire">
        <fireSize>0.5</fireSize>
        <finalFireSize>0.5</finalFireSize>
        <offset>(0,0,0.1)</offset>
      </li>
      <li Class="CompProperties_MeditationFocus">
        <statDef>MeditationFocusStrength</statDef>
        <focusTypes>
          <li>Flame</li>
        </focusTypes>
        <offsets>
          <li Class="FocusStrengthOffset_Lit">
            <offset>0.1</offset>
          </li>
          <li Class="FocusStrengthOffset_BuildingDefsLit">
            <defs>
              <li>Campfire</li>
              <li>TorchLamp</li>
              <li MayRequire="Ludeon.RimWorld.Royalty">Brazier</li>
              <li MayRequire="Ludeon.RimWorld.Royalty,Ludeon.RimWorld.Ideology">DarklightBrazier</li>
              <li MayRequire="Ludeon.RimWorld.Ideology">Darktorch</li>
              <li MayRequire="Ludeon.RimWorld.Ideology">DarktorchFungus</li>
            </defs>
            <offsetPerBuilding>0.01</offsetPerBuilding>
            <radius>9.9</radius>
            <maxBuildings>8</maxBuildings>
            <explanationKey>MeditationFocusPerFlame</explanationKey>
            <explanationKeyAbstract>MeditationFocusPerFlameAbstract</explanationKeyAbstract>
            <drawRingRadius>false</drawRingRadius>
          </li>
        </offsets>
      </li>
    </comps>
    <drawPlaceWorkersWhileSelected>false</drawPlaceWorkersWhileSelected>
  </ThingDef>

  <ThingDef ParentName="BuildingBase">
    <defName>GeneProcessor</defName>
    <label>gene processor</label>
    <description>A genetic analysis and processing system. Placed near a gene assembler, it increases the maximum genetic complexity of the xenogerms you can assemble. Building multiple gene processors will increase the genetic complexity limit further.</description>
    <passability>PassThroughOnly</passability>
    <fillPercent>0.4</fillPercent>
    <pathCost>14</pathCost>
    <rotatable>false</rotatable>
    <graphicData>
      <texPath>Things/Building/Misc/GeneProcessor</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>(2, 2)</drawSize>
      <shadowData>
        <volume>(1.5, 0.4, 1.5)</volume>
      </shadowData>
    </graphicData>
    <size>(2, 2)</size>
    <canOverlapZones>false</canOverlapZones>
    <defaultPlacingRot>North</defaultPlacingRot>
    <designationCategory>Biotech</designationCategory>
    <uiOrder>2100</uiOrder>
    <researchPrerequisites>
      <li>GeneProcessor</li>
    </researchPrerequisites>
    <descriptionHyperlinks>
      <ThingDef>GeneAssembler</ThingDef>
      <ThingDef>GeneBank</ThingDef>
      <ThingDef>Genepack</ThingDef>
      <ThingDef>Xenogerm</ThingDef>
    </descriptionHyperlinks>
    <statBases>
      <MaxHitPoints>350</MaxHitPoints>
      <WorkToBuild>9000</WorkToBuild>
      <Mass>85</Mass>
      <Flammability>0.5</Flammability>
      <GeneticComplexityIncrease>2</GeneticComplexityIncrease>
    </statBases>
    <costList>
      <Steel>100</Steel>
      <Plasteel>25</Plasteel>
      <ComponentIndustrial>2</ComponentIndustrial>
    </costList>
    <constructionSkillPrerequisite>6</constructionSkillPrerequisite>
    <building>
      <buildingTags>
        <li>Biotech</li>
      </buildingTags>
    </building>
    <placeWorkers>
      <li>PlaceWorker_ShowFacilitiesConnections</li>
    </placeWorkers>
    <drawPlaceWorkersWhileSelected>true</drawPlaceWorkersWhileSelected>
    <comps>
      <li Class="CompProperties_Power">
        <compClass>CompPowerTrader</compClass>
        <basePowerConsumption>50</basePowerConsumption>
      </li>
      <li Class="CompProperties_Flickable"/>
      <li Class="CompProperties_InspectString">
        <compClass>CompInspectStringGeneProcessor</compClass>
        <inspectString>Gene complexity increase</inspectString>
      </li>
      <li Class="CompProperties_Facility">
        <maxSimultaneous>100</maxSimultaneous>
        <maxDistance>12.9</maxDistance>
        <showMaxSimultaneous>false</showMaxSimultaneous>
      </li>
    </comps>
  </ThingDef>

  <!-- Mech booster -->

  <HediffDef>
    <defName>MechBoost</defName>
    <label>mechanoid boost</label>
    <description>This mechanoid's capacities have been boosted by a nearby mechanoid booster building.</description>
    <hediffClass>HediffWithComps</hediffClass>
    <defaultLabelColor>(0.52, 1, 0.95)</defaultLabelColor>
    <comps>
      <li Class="HediffCompProperties_Disappears">
        <disappearsAfterTicks>30</disappearsAfterTicks>
        <showRemainingTime>false</showRemainingTime>
      </li>
      <li Class="HediffCompProperties_DisappearsOnDeath"/>
      <li Class="HediffCompProperties_Link">
        <requireLinkOnOtherPawn>false</requireLinkOnOtherPawn>
      </li>
    </comps>
    <stages>
      <li>
        <statFactors>
          <MoveSpeed>1.15</MoveSpeed>
          <WorkSpeedGlobal>1.50</WorkSpeedGlobal>
        </statFactors>
      </li>
    </stages>
    <isBad>false</isBad>
  </HediffDef>

  <ThingDef ParentName="BuildingBase">
    <defName>MechBooster</defName>
    <label>mech booster</label>
    <description>A remote energy and computation enhancement device. Any friendly mechanoids in its effect radius will move and work faster.</description>
    <tickerType>Normal</tickerType>
    <drawerType>RealtimeOnly</drawerType>
    <fillPercent>0.4</fillPercent>
    <graphicData>
      <texPath>Things/Building/Power/MechBooster</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>(2,2)</drawSize>
      <shadowData>
        <volume>(0.8, 0.35, 0.8)</volume>
        <offset>(0, 0, -0.2)</offset>
      </shadowData>
    </graphicData>
    <altitudeLayer>Building</altitudeLayer>
    <passability>PassThroughOnly</passability>
    <building>
      <ai_chillDestination>false</ai_chillDestination>
      <buildingTags>
        <li>Biotech</li>
      </buildingTags>
    </building>
    <constructionSkillPrerequisite>5</constructionSkillPrerequisite>
    <designationCategory>Biotech</designationCategory>
    <uiOrder>2420</uiOrder>
    <minifiedDef>MinifiedThing</minifiedDef>
    <rotatable>false</rotatable>
    <thingCategories>
      <li>BuildingsMisc</li>
    </thingCategories>
    <statBases>
      <MaxHitPoints>150</MaxHitPoints>
      <WorkToBuild>8000</WorkToBuild>
      <Mass>25</Mass>
      <Flammability>0.5</Flammability>
    </statBases>
    <size>(2,2)</size>
    <costList>
      <Steel>100</Steel>
      <ComponentIndustrial>4</ComponentIndustrial>
    </costList>
    <researchPrerequisites>
      <li>HighMechtech</li>
    </researchPrerequisites>
    <comps>
      <li Class="CompProperties_Power">
        <compClass>CompPowerTrader</compClass>
        <basePowerConsumption>200</basePowerConsumption>
      </li>
      <li Class="CompProperties_CauseHediff_AoE">
        <range>9.9</range>
        <onlyTargetMechs>true</onlyTargetMechs>
        <hediff>MechBoost</hediff>
        <activeSound>MechBooster_Working</activeSound>
      </li>
      <li Class="CompProperties_Flickable"/>
    </comps>
    <placeWorkers>
      <li>PlaceWorker_ShowCauseHediffAoE</li>
    </placeWorkers>
    <drawPlaceWorkersWhileSelected>true</drawPlaceWorkersWhileSelected>
  </ThingDef>

  <ThingDef ParentName="TrapIEDBase">
    <defName>TrapIED_ToxGas</defName>
    <label>IED tox trap</label>
    <description>A pair of tox gas shells connected to a trigger which detonates on touch or bullet impact. Since it is hidden in the surrounding terrain, it cannot be placed adjacent to other traps. Animals can sense these when calm.</description>
    <graphicData>
      <texPath>Things/Building/Security/IEDTox</texPath>
    </graphicData>
    <uiOrder>44</uiOrder>
    <costList>
      <Shell_Toxic>2</Shell_Toxic>
    </costList>
    <designationHotKey>Misc11</designationHotKey>
    <comps>
      <li Class="CompProperties_Explosive">
        <explosiveRadius>8.9</explosiveRadius>
        <explosiveDamageType>ToxGas</explosiveDamageType>
        <startWickHitPointsPercent>0.2</startWickHitPointsPercent>
        <postExplosionGasType>ToxGas</postExplosionGasType>
        <wickTicks>15</wickTicks>
        <startWickOnDamageTaken>
          <li>Bullet</li>
          <li>Arrow</li>
          <li>ArrowHighVelocity</li>
        </startWickOnDamageTaken>
      </li>
    </comps>
    <specialDisplayRadius>10.9</specialDisplayRadius>
  </ThingDef>

</Defs>