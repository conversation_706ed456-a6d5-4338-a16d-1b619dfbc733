<?xml version="1.0" encoding="utf-8" ?>

<LanguageData>

  <StatsReport_Genes>Relevant genes</StatsReport_Genes>
  <StatsReport_InSunlight>in sunlight</StatsReport_InSunlight>
  <StatsReport_Clothed>clothed</StatsReport_Clothed>
  <StatsReport_Unclothed>unclothed</StatsReport_Unclothed>

  <StatsReport_AgeRateMultiplier>Age rate multiplier</StatsReport_AgeRateMultiplier>
  <StatsReport_AgeRateMultiplier_Desc>A multiplier on how quickly this person ages biologically based on their current age.</StatsReport_AgeRateMultiplier_Desc>

  <StatsReport_RechargerWeightClass>Required weight class</StatsReport_RechargerWeightClass>
  <StatsReport_RechargerWeightClass_Desc>The weight class of mechanoid which can use this recharger. The following mechanoids are compatible</StatsReport_RechargerWeightClass_Desc>

  <StatsReport_RechargerNeeded>Recharger needed</StatsReport_RechargerNeeded>
  <StatsReport_RechargerNeeded_Desc>The type of recharger that this mechanoid can use to recover energy. This is based on the mechanoid's weight class.</StatsReport_RechargerNeeded_Desc>

  <StatsReport_DeathrestEffectiveness>Deathrest effectiveness</StatsReport_DeathrestEffectiveness>
  <StatsReport_DeathrestEffectiveness_Desc>A multiplier on how effective this is when used for deathrest. The higher this is, the less time a user needs to deathrest here.</StatsReport_DeathrestEffectiveness_Desc>

  <StatsReport_DeathrestConnectionLimit>Deathrest connection limit</StatsReport_DeathrestConnectionLimit>
  <StatsReport_DeathrestConnectionLimit_Desc>How many of these buildings can connect to a deathrester while deathresting. This number is also limited by the person's deathrest capacity.</StatsReport_DeathrestConnectionLimit_Desc>

  <StatsReport_GrowthTier>Growth tier</StatsReport_GrowthTier>
  <StatsReport_GrowthTierDesc>Children progress through growth tiers over time. The higher the learning need, the faster the progress. When a growth moment comes (at age 7, 10, and 13), the child's growth tier determines how many trait and passion options you can choose from. Growth tier resets after each growth moment.</StatsReport_GrowthTierDesc>

  <StatsReport_DevelopmentStage>Life stage</StatsReport_DevelopmentStage>
  <StatsReport_DevelopmentStageDesc_Baby>A baby. Babies cannot move on their own. Their life revolves around the simple needs of food, sleep, and play. When they giggle, nearby adults' moods are lifted, and when they cry, nearby adults are annoyed.\n\nAt age 3, human babies become children.</StatsReport_DevelopmentStageDesc_Baby>
  <StatsReport_DevelopmentStageDesc_ChildPart1>A child. In general, children are slower and less skilled than adults. However, children have a natural optimism. When their mood is high, this optimism can raise the mood of adult colonists.\n\nChildren can do various types of work, depending on their age</StatsReport_DevelopmentStageDesc_ChildPart1>
  <StatsReport_DevelopmentStageDesc_ChildPart2>As a child ages, they will gain additional passions and traits.\n\nChildren have various child-specific learning needs and require special child-sized apparel.\n\nChildren under the age of 8 won't crawl when injured.\n\nAt age 13, human children become adults.</StatsReport_DevelopmentStageDesc_ChildPart2> 
  <StatsReport_DevelopmentStageDesc_Adult>An adult. Human adults fully mature at the age of 18.</StatsReport_DevelopmentStageDesc_Adult>

  <StatsReport_Pollution>Multiplier for pollution</StatsReport_Pollution>

  <StatsReport_FertilityAgeFactor>Age factor</StatsReport_FertilityAgeFactor>
  <StatsReport_HasGene>Has {0_label} gene</StatsReport_HasGene>
  <StatsReport_NonBaselinerDescription>A person of the {0} xenotype.\n\nAbout {0}s:</StatsReport_NonBaselinerDescription>
  
  <Stat_Thing_Apparel_ValidLifestage>Lifestage</Stat_Thing_Apparel_ValidLifestage>
  <Stat_Thing_Apparel_ValidLifestage_Desc>Certain apparel requires that the wearer be within a specific age range.</Stat_Thing_Apparel_ValidLifestage_Desc>

  <StatsReport_Connected>Connected {0}</StatsReport_Connected>

  <Effects>Effects</Effects>
  <StatsReport_MissingGeneRomanceChance>A multiplier on chance of having a romantic relationship with someone who does not have this gene.</StatsReport_MissingGeneRomanceChance>
  <ImmuneTo>Immune to</ImmuneTo>
  <SocialFightChanceFactor>Social fight chance factor</SocialFightChanceFactor>
  <AggroMentalBreakSelectionChanceFactor>Chance mental break is violent</AggroMentalBreakSelectionChanceFactor>
  <PrisonBreakIntervalFactor>Prison break interval factor</PrisonBreakIntervalFactor>
  <WillNeverPrisonBreak>Will never prison break.</WillNeverPrisonBreak>
  <AlwaysAggroMentalBreak>Mental breaks are always violent.</AlwaysAggroMentalBreak>
  <NeverAggroMentalBreak>Mental breaks are never violent.</NeverAggroMentalBreak>
  <AddictionChanceFactor>{0_label} addiction chance factor</AddictionChanceFactor>
  <OverdoseChanceFactor>{0_label} overdose chance factor</OverdoseChanceFactor>
  <ToleranceBuildupFactor>{0_label} tolerance buildup factor</ToleranceBuildupFactor>
  <AddictionImmune>Immune to {0_label} addiction.</AddictionImmune>
  <FoodPoisoningImmune>Immune to food poisoning.</FoodPoisoningImmune>
  <WillNeverSocialFight>Will never do social fights.</WillNeverSocialFight>
  <AgeFactors>Aging factors</AgeFactors>
  <UnaffectedByDarkness>Work speed is unaffected by darkness.</UnaffectedByDarkness>
  <PassionModAdd>Adds one level of passion in {0_label}.</PassionModAdd>
  <PassionModDrop>Removes all passion in {0_label}.</PassionModDrop>
  <CausesNeed>Causes need</CausesNeed>
  <GivesAbility>Gives ability</GivesAbility>
  <GivesAbilities>Gives abilities</GivesAbilities>
  <ResourceLossPerDay>{RESOURCE}: {OFFSET} per day.</ResourceLossPerDay>
  <Removes>Removes</Removes>
  <DisablesNeed>Disables need</DisablesNeed>
  <DisablesNeeds>Disables needs</DisablesNeeds>
  <DevelopmentStage_Baby>Baby</DevelopmentStage_Baby>
  <DevelopmentStage_Child>Child</DevelopmentStage_Child>
  <DevelopmentStage_Adult>Adult</DevelopmentStage_Adult>

  <StatsReport_GrowthVatSpeedFactor>Growth vat speed</StatsReport_GrowthVatSpeedFactor>

  <GeneToleranceBuildupImmune>Carriers cannot build a tolerance to {0_label}.</GeneToleranceBuildupImmune>
  <GeneToleranceBuildupFactor>Carriers build a tolerance to {0_label} {1} as quickly.</GeneToleranceBuildupFactor>
  <GeneOverdoseImmune>Carriers cannot overdose on {0_label}.</GeneOverdoseImmune>
  <GeneOverdoseFactor>Carriers are {1} as likely to overdose on {0_label}.</GeneOverdoseFactor>

</LanguageData>