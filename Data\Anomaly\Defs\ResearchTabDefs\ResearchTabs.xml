﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ResearchTabDef>
    <defName>Anomaly</defName>
    <label>anomaly</label>
    <generalTitle>Anomaly research projects</generalTitle>
    <generalDescription>Unlock void-related technologies by studying unnatural entities.\n\nYou can have both a basic and an advanced project active at the same time. If no advanced project is active, advanced research will contribute to a basic project instead.</generalDescription>
    <visibleByDefault>false</visibleByDefault>
    <minMonolithLevelVisible>1</minMonolithLevelVisible>
    <tutorTag>Research-Tab-Anomaly</tutorTag>
  </ResearchTabDef>

</Defs>
