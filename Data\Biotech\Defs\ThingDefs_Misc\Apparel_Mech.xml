<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingDef Name="MechApparelBase" ParentName="ApparelNoQualityBase" Abstract="True">
    <thingClass>Apparel</thingClass>
    <generateAllowChance>0</generateAllowChance>
    <destroyOnDrop>true</destroyOnDrop>
    <tradeability>None</tradeability>
    <apparel>
      <canBeDesiredForIdeo>false</canBeDesiredForIdeo>
      <countsAsClothingForNudity>false</countsAsClothingForNudity>
      <careIfWornByCorpse>false</careIfWornByCorpse>
      <careIfDamaged>false</careIfDamaged>
      <wearPerDay>0</wearPerDay>
    </apparel>
  </ThingDef>

  <ThingDef ParentName="MechApparelBase">
    <defName>Apparel_HeavyShield</defName>
    <label>heavy shield unit</label>
    <description>A mechanoid-built projectile-repulsion device. The user can shoot out, but the shield will block shots coming in. This shield only protects the user, not those around them. It does nothing against melee attacks or heat. The shield will break instantly if hit by EMP.</description>
    <graphicData>
      <texPath>Things/Pawn/Mechanoid/Apparel/HeavyPersonalShield</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <tickerType>Normal</tickerType>
    <statBases>
      <Mass>3</Mass>
      <Flammability>0</Flammability>
      <EnergyShieldRechargeRate>0.013</EnergyShieldRechargeRate>
      <EnergyShieldEnergyMax>4.0</EnergyShieldEnergyMax>
    </statBases>
    <apparel>
      <bodyPartGroups>
        <li>Torso</li>
      </bodyPartGroups>
      <layers>
        <li>Middle</li>
      </layers>
    </apparel>
    <comps>
      <li Class="CompProperties_Shield">
        <startingTicksToReset>36000</startingTicksToReset><!-- 10 mins -->
        <minDrawSize>2.5</minDrawSize>
        <maxDrawSize>2.8</maxDrawSize>
        <energyLossPerDamage>0.01</energyLossPerDamage>
        <energyOnReset>4.0</energyOnReset>
        <blocksRangedWeapons>false</blocksRangedWeapons>
      </li>
    </comps>
  </ThingDef>

</Defs>