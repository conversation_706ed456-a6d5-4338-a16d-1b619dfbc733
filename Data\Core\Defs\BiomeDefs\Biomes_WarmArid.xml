﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <BiomeDef>
    <defName>AridShrubland</defName>
    <label>arid shrubland</label>
    <description>A dry region, but not dry enough to become a true desert. Open plains with grasses and bushes give way to scattered groves of trees. Plants are hardy and there is a moderate density of animals, but arable soil is hard to find.</description>
    <workerClass>BiomeWorker_AridShrubland</workerClass>
    <allowFarmingCamps>false</allowFarmingCamps>
    <animalDensity>1.8</animalDensity>
    <plantDensity>0.24</plantDensity>
    <settlementSelectionWeight>0.95</settlementSelectionWeight>
    <movementDifficulty>1</movementDifficulty>
    <texture>World/Biomes/AridShrubland</texture>
    <forageability>0.5</forageability>
    <foragedFood>RawAgave</foragedFood>
    <wildPlantRegrowDays>27</wildPlantRegrowDays>
    <soundsAmbient>
      <li>Ambient_NightInsects_Standard</li>
    </soundsAmbient>
    <diseaseMtbDays>65</diseaseMtbDays>
    <diseases>
      <li>
        <diseaseInc>Disease_Flu</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_Plague</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_GutWorms</diseaseInc>
        <commonality>60</commonality>
      </li>
      <li>
        <diseaseInc>Disease_MuscleParasites</diseaseInc>
        <commonality>60</commonality>
      </li>
      <li>
        <diseaseInc>Disease_FibrousMechanites</diseaseInc>
        <commonality>30</commonality>
      </li>

      <li>
        <diseaseInc>Disease_SensoryMechanites</diseaseInc>
        <commonality>30</commonality>
      </li>
      <li>
        <diseaseInc>Disease_AnimalFlu</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_AnimalPlague</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_OrganDecay</diseaseInc>
        <commonality>10</commonality>
      </li>
    </diseases>
    <terrainsByFertility>
      <li>
        <terrain>Sand</terrain>
        <min>-999</min>
        <max>0.45</max>
      </li>
      <li>
        <terrain>Soil</terrain>
        <min>0.45</min>
        <max>0.90</max>
      </li>
      <li>
        <terrain>SoilRich</terrain>
        <min>0.90</min>
        <max>999</max>
      </li>
    </terrainsByFertility>
    <baseWeatherCommonalities>
      <Clear>18</Clear>
      <Fog>1</Fog>
      <Rain>2</Rain>
      <DryThunderstorm>1</DryThunderstorm>
      <RainyThunderstorm>1</RainyThunderstorm>
      <FoggyRain>0.5</FoggyRain>
      <SnowGentle>4</SnowGentle>
      <SnowHard>4</SnowHard>
      <GrayPall MayRequire="Ludeon.RimWorld.Anomaly">1</GrayPall>
    </baseWeatherCommonalities>
    <wildPlants>
      <Plant_Grass>7.0</Plant_Grass>
      <Plant_GrayGrass MayRequire="Ludeon.RimWorld.Biotech">5</Plant_GrayGrass>
      <Plant_Dandelion>0.8</Plant_Dandelion>
      <Plant_Bush>0.7</Plant_Bush>
      <Plant_PincushionCactus>0.6</Plant_PincushionCactus>
      <Plant_Agave>0.2</Plant_Agave>
      <Plant_SaguaroCactus>0.26</Plant_SaguaroCactus>
      <Plant_TreeDrago>0.20</Plant_TreeDrago>
      <Plant_Berry>0.10</Plant_Berry>
      <Plant_PebbleCactus MayRequire="Ludeon.RimWorld.Biotech">0.6</Plant_PebbleCactus>
    </wildPlants>
    <wildAnimals>
      <Donkey>0.2</Donkey>
      <GuineaPig>0.017</GuineaPig>
      <Horse>0.1</Horse>
      <Bison>0.1</Bison>
      <Rat>1.3</Rat>
      <Hare>1.3</Hare>
      <Iguana>0.7</Iguana>
      <Gazelle>0.7</Gazelle>
      <Ostrich>0.7</Ostrich>
      <Emu>0.7</Emu>
      <Dromedary>0.7</Dromedary>
      <Boomrat>0.5</Boomrat>
      <Elephant>0.5</Elephant>
      <Rhinoceros>0.5</Rhinoceros>
      <Boomalope>0.5</Boomalope>
      <Cougar>0.15</Cougar>
      <Fox_Fennec>0.15</Fox_Fennec>
      <Megasloth>0.1</Megasloth>
    </wildAnimals>
    <pollutionWildAnimals MayRequire="Ludeon.RimWorld.Biotech">
      <Toxalope MayRequire="Ludeon.RimWorld.Biotech">0.1</Toxalope>
      <WasteRat MayRequire="Ludeon.RimWorld.Biotech">0.1</WasteRat>
      <Iguana>1</Iguana>
      <Rat>0.6</Rat>
      <Boomalope>0.4</Boomalope>
      <Boomrat>0.4</Boomrat>
      <Megascarab>0.2</Megascarab>
    </pollutionWildAnimals>
    <allowedPackAnimals>
      <li>Muffalo</li>
      <li>Dromedary</li>
      <li>Elephant</li>
      <li>Alpaca</li>
    </allowedPackAnimals>
  </BiomeDef>

  <BiomeDef>
    <defName>Desert</defName>
    <label>desert</label>
    <description>A very dry area which supports little life. There is very little arable land, and animal life is very sparse. Deserts can be hot, or quite cold.</description>
    <workerClass>BiomeWorker_Desert</workerClass>
    <allowFarmingCamps>false</allowFarmingCamps>
    <animalDensity>0.4</animalDensity>
    <plantDensity>0.05</plantDensity>
    <settlementSelectionWeight>0.65</settlementSelectionWeight>
    <campSelectionWeight>0.1</campSelectionWeight>
    <movementDifficulty>1</movementDifficulty>
    <texture>World/Biomes/Desert</texture>
    <forageability>0.25</forageability>
    <foragedFood>RawAgave</foragedFood>
    <wildPlantsCareAboutLocalFertility>false</wildPlantsCareAboutLocalFertility>
    <wildPlantRegrowDays>35</wildPlantRegrowDays>
    <diseaseMtbDays>90</diseaseMtbDays>
    <diseases>
      <li>
        <diseaseInc>Disease_Flu</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_Plague</diseaseInc>
        <commonality>80</commonality>
      </li>
      <li>
        <diseaseInc>Disease_GutWorms</diseaseInc>
        <commonality>60</commonality>
      </li>
      <li>
        <diseaseInc>Disease_MuscleParasites</diseaseInc>
        <commonality>60</commonality>
      </li>
      <li>
        <diseaseInc>Disease_FibrousMechanites</diseaseInc>
        <commonality>30</commonality>
      </li>
      <li>
        <diseaseInc>Disease_SensoryMechanites</diseaseInc>
        <commonality>30</commonality>
      </li>

      <li>
        <diseaseInc>Disease_AnimalFlu</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_AnimalPlague</diseaseInc>
        <commonality>80</commonality>
      </li>
      <li>
        <diseaseInc>Disease_OrganDecay</diseaseInc>
        <commonality>10</commonality>
      </li>
    </diseases>
    <terrainsByFertility>
      <li>
        <terrain>Sand</terrain>
        <min>-999</min>
        <max>0.80</max>
      </li>
      <li>
        <terrain>Soil</terrain>
        <min>0.80</min>
        <max>999</max>
      </li>
    </terrainsByFertility>
    <terrainPatchMakers>
      <li>
        <perlinFrequency>0.05</perlinFrequency>
        <maxFertility>0.8</maxFertility>
        <minSize>13</minSize>
        <thresholds>
          <li>
            <terrain>SoftSand</terrain>
            <min>0.65</min>
            <max>999</max>
          </li>
        </thresholds>
      </li>
    </terrainPatchMakers>
    <baseWeatherCommonalities>
      <Clear>18</Clear>
      <Rain>2</Rain>
      <DryThunderstorm>1</DryThunderstorm>
      <RainyThunderstorm>1</RainyThunderstorm>
      <SnowGentle>4</SnowGentle>
      <SnowHard>4</SnowHard>
      <GrayPall MayRequire="Ludeon.RimWorld.Anomaly">1</GrayPall>
    </baseWeatherCommonalities>
    <wildPlants>
      <Plant_Grass>6</Plant_Grass>
      <Plant_GrayGrass MayRequire="Ludeon.RimWorld.Biotech">5</Plant_GrayGrass>
      <Plant_PincushionCactus>1</Plant_PincushionCactus>
      <Plant_SaguaroCactus>1.0</Plant_SaguaroCactus>
      <Plant_Dandelion>0.5</Plant_Dandelion>
      <Plant_TreeDrago>0.45</Plant_TreeDrago>
      <Plant_Agave>0.25</Plant_Agave>
      <Plant_Bush>0.25</Plant_Bush>
      <Plant_PebbleCactus MayRequire="Ludeon.RimWorld.Biotech">1</Plant_PebbleCactus>
    </wildPlants>
    <wildAnimals>
      <Donkey>0.15</Donkey>
      <GuineaPig>0.01</GuineaPig>
      <Iguana>1.2</Iguana>
      <Dromedary>0.7</Dromedary>
      <Boomalope>0.6</Boomalope>
      <Ostrich>0.6</Ostrich>
      <Emu>0.6</Emu>
      <Gazelle>0.6</Gazelle>
      <Cougar>0.07</Cougar>
      <Fox_Fennec>0.07</Fox_Fennec>
    </wildAnimals>
    <pollutionWildAnimals MayRequire="Ludeon.RimWorld.Biotech">
      <Iguana>1</Iguana>
      <Megascarab>1</Megascarab>
    </pollutionWildAnimals>
    <allowedPackAnimals>
      <li>Muffalo</li>
      <li>Dromedary</li>
    </allowedPackAnimals>
  </BiomeDef>

  <BiomeDef>
    <defName>ExtremeDesert</defName>
    <label>extreme desert</label>
    <description>An extremely hot, dry area, devoid of almost all life. Searing heat and a near total lack of arable land make it very difficult to survive here.</description>
    <workerClass>BiomeWorker_ExtremeDesert</workerClass>
    <allowFarmingCamps>false</allowFarmingCamps>
    <animalDensity>0.1</animalDensity>
    <plantDensity>0.002</plantDensity>
    <hasVirtualPlants>false</hasVirtualPlants>
    <settlementSelectionWeight>0.42</settlementSelectionWeight>
    <campSelectionWeight>0.1</campSelectionWeight>
    <movementDifficulty>1</movementDifficulty>
    <texture>World/Biomes/ExtremeDesert</texture>
    <forageability>0</forageability>
    <wildPlantsCareAboutLocalFertility>false</wildPlantsCareAboutLocalFertility>
    <wildPlantRegrowDays>35</wildPlantRegrowDays>
    <diseaseMtbDays>90</diseaseMtbDays>
    <isExtremeBiome>true</isExtremeBiome>
    <diseases>
      <li>
        <diseaseInc>Disease_Flu</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_Plague</diseaseInc>
        <commonality>80</commonality>
      </li>
      <li>
        <diseaseInc>Disease_GutWorms</diseaseInc>
        <commonality>40</commonality>
      </li>
      <li>
        <diseaseInc>Disease_MuscleParasites</diseaseInc>
        <commonality>40</commonality>
      </li>
      <li>
        <diseaseInc>Disease_FibrousMechanites</diseaseInc>
        <commonality>30</commonality>
      </li>
      <li>
        <diseaseInc>Disease_SensoryMechanites</diseaseInc>
        <commonality>30</commonality>
      </li>

      <li>
        <diseaseInc>Disease_AnimalFlu</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_AnimalPlague</diseaseInc>
        <commonality>80</commonality>
      </li>
      <li>
        <diseaseInc>Disease_OrganDecay</diseaseInc>
        <commonality>10</commonality>
      </li>
    </diseases>
    <terrainsByFertility>
      <li>
        <terrain>Sand</terrain>
        <min>-999</min>
        <max>999</max>
      </li>
    </terrainsByFertility>
    <terrainPatchMakers>
      <li>
        <perlinFrequency>0.05</perlinFrequency>
        <minSize>13</minSize>
        <thresholds>
          <li>
            <terrain>SoftSand</terrain>
            <min>0.65</min>
            <max>999</max>
          </li>
        </thresholds>
      </li>
    </terrainPatchMakers>
    <baseWeatherCommonalities>
      <Clear>18</Clear>
      <Rain>1</Rain>
      <DryThunderstorm>1</DryThunderstorm>
      <RainyThunderstorm>1</RainyThunderstorm>
      <SnowGentle>1.5</SnowGentle>
      <SnowHard>1</SnowHard>
      <GrayPall MayRequire="Ludeon.RimWorld.Anomaly">1</GrayPall>
    </baseWeatherCommonalities>
    <wildPlants>
      <Plant_Grass>6</Plant_Grass>
      <Plant_GrayGrass MayRequire="Ludeon.RimWorld.Biotech">5</Plant_GrayGrass>
      <Plant_SaguaroCactus>1</Plant_SaguaroCactus>
      <Plant_Agave>0.25</Plant_Agave>
      <Plant_PebbleCactus MayRequire="Ludeon.RimWorld.Biotech">1</Plant_PebbleCactus>
    </wildPlants>
    <wildAnimals>
      <Donkey>0.20</Donkey>
      <GuineaPig>0.002</GuineaPig>
      <Iguana>1.5</Iguana>
      <Dromedary>0.2</Dromedary>
      <Fox_Fennec>0.1</Fox_Fennec>
    </wildAnimals>
    <pollutionWildAnimals MayRequire="Ludeon.RimWorld.Biotech">
      <Iguana>1</Iguana>
      <Megascarab>1</Megascarab>
    </pollutionWildAnimals>
    <allowedPackAnimals>
      <li>Muffalo</li>
      <li>Dromedary</li>
    </allowedPackAnimals>
  </BiomeDef>

</Defs>