﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <GameConditionDef>
    <defName>DeathPall</defName>
    <conditionClass>GameCondition_DeathPall</conditionClass>
    <label>death pall resurrection</label>
    <description>The dead will rise! Burn, bury, destroy, or move corpses indoors to prevent them from resurrecting.</description>
    <letterText>A death-gray cloud has descended on the area.\n\nThe cloud is made of microscopic self-powered archites created by some unknown superintelligence. Where they land on dead flesh, they will enter the body and reanimate it to create a twisted imitation of life.\n\nThe dead will rise! Burn, bury, destroy, or move corpses indoors to prevent them from resurrecting.</letterText>
    <letterDef>NegativeEvent</letterDef>
    <weatherDef>UnnaturalFog</weatherDef>
    <canBePermanent>true</canBePermanent>
    <natural>false</natural>
    <allowUnderground>false</allowUnderground>
  </GameConditionDef>
  
  <GameConditionDef>
    <defName>GrayPall</defName>
    <conditionClass>GameCondition_GrayPall</conditionClass>
    <label>gray pall stench</label>
    <description>A putrid stench fills the air, making people unhappy. The smell is worse outside.</description>
    <letterText>A blanket of gritty fog has descended on this area. It smells somehow ancient and stings the skin. The unnatural grayness of it invokes a sense of dread.</letterText>
    <letterDef>NeutralEvent</letterDef>
    <weatherDef>GrayPall</weatherDef>
    <canBePermanent>true</canBePermanent>
    <allowUnderground>false</allowUnderground>
  </GameConditionDef>
  
  <GameConditionDef>
    <defName>HateChantDrone</defName>
    <conditionClass>GameCondition_HateChantDrone</conditionClass>
    <label>hate chanting</label>
    <description>Cultists of Horax are performing a psychically tuned chant. The ominous chanting conveys a dark and dreamlike hatred. It makes your head pound and your skin crawl. So long as the drone continues, colonists will be unhappy.\n\nThe chant will continue to grow more powerful as long as the cultists are not stopped.</description>
    <canBePermanent>true</canBePermanent>
  </GameConditionDef>

  <GameConditionDef>
    <defName>UnnaturalDarkness</defName>
    <conditionClass>GameCondition_UnnaturalDarkness</conditionClass>
    <label>unnatural darkness</label>
    <description>The world is blanketed by a layer of unnatural darkness. From the inky blackness, one can hear the inhuman groaning of deadly creatures. Stay in the light.</description>
    <weatherDef>UnnaturalDarkness_Stage1</weatherDef>
    <preventNeutralVisitors>true</preventNeutralVisitors>
    <preventIncidents>true</preventIncidents>
    <canBePermanent>true</canBePermanent>
    <displayOnUI>false</displayOnUI> <!--  Displayed by weather instead -->
    <natural>false</natural>
    <allowUnderground>false</allowUnderground>
  </GameConditionDef>

  <GameConditionDef>
    <defName>BloodRain</defName>
    <conditionClass>GameCondition_BloodRain</conditionClass>
    <label>blood rain frenzy</label>
    <description>Blood-like fluid falls from the sky. Anyone outside will be driven into a berserk frenzy.</description>
    <weatherDef>BloodRain</weatherDef>
    <preventNeutralVisitors>true</preventNeutralVisitors>
    <canBePermanent>true</canBePermanent>
    <natural>false</natural>
    <letterText>Drops of thick blood-like fluid have begun falling from the sky.\n\nAnyone exposed feels a growing anger which strengthens melee attacks but will eventually become a berserk rage. Exposed animals will eventually become manhunters. The intensity of the effect varies between individuals depending on their traits, psychic sensitivity, and random individual factors.</letterText>
    <endMessage>The blood rain is ending.</endMessage>
    <allowUnderground>false</allowUnderground>
    <pennedAnimalsSeekShelter>true</pennedAnimalsSeekShelter>
  </GameConditionDef>

  <GameConditionDef>
    <defName>UnnaturalHeat</defName>
    <conditionClass>GameCondition_UnnaturalHeat</conditionClass>
    <label>unnatural heat</label>
    <description>A thermal projection device is agitating gas molecules in the atmosphere, raising outdoor temperatures.</description>
    <displayOnUI>false</displayOnUI>
  </GameConditionDef>
</Defs>