<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <ThingDef ParentName="BaseWeapon">
    <techLevel>Medieval</techLevel>
    <defName>NerveSpiker</defName>
    <label>nerve spiker</label>
    <description>A crossbow-like device that throws rough spikes embedded with a paralytic biotoxin. Low damage, but it stuns non-mechanoid targets. Large targets are more resistant to the biotoxin and will be stunned for less time.</description>
    <graphicData>
      <texPath>Things/Item/Equipment/WeaponRanged/NerveSpiker</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <soundInteract>Bow_Recurve</soundInteract>
    <smeltable>true</smeltable>
    <costList>
      <Bioferrite>40</Bioferrite>
    </costList>
    <statBases>
      <WorkToMake>9000</WorkToMake>
      <Mass>1.3</Mass>
      <AccuracyTouch>0.70</AccuracyTouch>
      <AccuracyShort>0.78</AccuracyShort>
      <AccuracyMedium>0.65</AccuracyMedium>
      <AccuracyLong>0.35</AccuracyLong>
      <RangedWeapon_Cooldown>1.65</RangedWeapon_Cooldown>
    </statBases>
    <weaponTags>
      <li>NerveSpiker</li>
    </weaponTags>
    <tradeTags>
     <!--  <li>WeaponRanged</li> --> <!-- Removed as we don't want horaxian weapons to appear in normal trader inventories -->
      <li>HoraxWeapon</li>
    </tradeTags>
    <weaponClasses>
      <li>Ranged</li>
      <li>RangedLight</li>
    </weaponClasses>
    <thingCategories>
      <li>WeaponsRanged</li>
    </thingCategories>
    <burnableByRecipe>true</burnableByRecipe>
    <comps>
      <li>
        <compClass>CompQuality</compClass>
      </li>
    </comps>
    <relicChance>1</relicChance>
    <recipeMaker>
      <researchPrerequisite>BioferriteExtraction</researchPrerequisite>
      <recipeUsers>
        <li>CraftingSpot</li>
        <li>BioferriteShaper</li>
      </recipeUsers>
      <skillRequirements>
        <Crafting>3</Crafting>
      </skillRequirements>
      <displayPriority>50</displayPriority>
      <workSpeedStat>GeneralLaborSpeed</workSpeedStat>
      <workSkill>Crafting</workSkill>
      <effectWorking>Smith</effectWorking>
      <soundWorking>Recipe_Machining</soundWorking>
      <unfinishedThingDef>UnfinishedWeapon</unfinishedThingDef>
    </recipeMaker>
    <thingSetMakerTags><li>RewardStandardQualitySuper</li></thingSetMakerTags>
    <verbs>
      <li>
        <verbClass>Verb_Shoot</verbClass>
        <hasStandardCommand>true</hasStandardCommand>
        <defaultProjectile>NerveSpiker_Spike</defaultProjectile>
        <warmupTime>1.45</warmupTime>
        <range>29.9</range>
        <soundCast>Bow_Recurve</soundCast>
      </li>
    </verbs>
    <tools>
      <li>
        <label>limb</label>
        <capacities>
          <li>Blunt</li>
          <li>Poke</li>
        </capacities>
        <power>9</power>
        <cooldownTime>2</cooldownTime>
      </li>
    </tools>
    <rotateInShelves>false</rotateInShelves>
  </ThingDef>

  <ThingDef ParentName="BaseProjectileNeolithic">
    <defName>NerveSpiker_Spike</defName>
    <label>nerve spike</label>
    <graphicData>
      <texPath>Things/Projectile/NerveSpikerShot</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <projectile>
      <speed>44</speed>
      <damageDef>Nerve</damageDef>
      <damageAmountBase>11</damageAmountBase>
    </projectile>
  </ThingDef>
</Defs>