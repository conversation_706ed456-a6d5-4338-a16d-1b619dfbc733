<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <DamageDef Name="Flame">
    <defName>Flame</defName>
    <workerClass>DamageWorker_Flame</workerClass>
    <label>flame</label>
    <hasForcefulImpact>false</hasForcefulImpact>
    <makesBlood>false</makesBlood>
    <canInterruptJobs>false</canInterruptJobs>
    <externalViolence>true</externalViolence>
    <deathMessage>{0} has burned to death.</deathMessage>
    <hediff>Burn</hediff>
    <armorCategory>Heat</armorCategory>
    <minDamageToFragment>15</minDamageToFragment>
    <defaultDamage>10</defaultDamage>
    <defaultArmorPenetration>0</defaultArmorPenetration>
    <explosionHeatEnergyPerCell>15</explosionHeatEnergyPerCell>
    <explosionCellFleck>BlastFlame</explosionCellFleck>
    <explosionColorCenter>(1, 0.7, 0.7)</explosionColorCenter>
    <explosionColorEdge>(1, 1, 0.7)</explosionColorEdge>
    <soundExplosion>Explosion_Flame</soundExplosion>
    <combatLogRules>Damage_Flame</combatLogRules>
    <canUseDeflectMetalEffect>false</canUseDeflectMetalEffect>
    <scaleDamageToBuildingsBasedOnFlammability>true</scaleDamageToBuildingsBasedOnFlammability>
  </DamageDef>
  
  <!-- Burn is like Flame, but doesn't ignite anything -->
  <DamageDef ParentName="Flame">
    <defName>Burn</defName>
    <label>burn</label>
    <workerClass>DamageWorker_AddInjury</workerClass>
  </DamageDef>
  
  <DamageDef>
    <defName>Frostbite</defName>
    <label>frostbite</label>
    <workerClass>DamageWorker_Frostbite</workerClass>
    <externalViolence>false</externalViolence>
    <deathMessage>{0} has succumbed to frostbite.</deathMessage>
    <hediff>Frostbite</hediff>
    <harmAllLayersUntilOutside>true</harmAllLayersUntilOutside>
    <canUseDeflectMetalEffect>false</canUseDeflectMetalEffect>
  </DamageDef>

  <DamageDef>
    <defName>TornadoScratch</defName>
    <label>scratch</label>
    <workerClass>DamageWorker_AddInjury</workerClass>
    <externalViolence>true</externalViolence>
    <deathMessage>{0} has been killed by a tornado.</deathMessage>
    <hediff>Scratch</hediff>
    <hediffSolid>Crack</hediffSolid>
    <harmAllLayersUntilOutside>true</harmAllLayersUntilOutside>
    <impactSoundType>Tornado</impactSoundType>
    <armorCategory>Sharp</armorCategory>
    <overkillPctToDestroyPart>0~0.7</overkillPctToDestroyPart>
  </DamageDef>

</Defs>
