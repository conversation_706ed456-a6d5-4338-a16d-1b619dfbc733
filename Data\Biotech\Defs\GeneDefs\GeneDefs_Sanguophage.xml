<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <GeneDef>
    <defName>Hemogenic</defName>
    <label>hemogenic</label>
    <description>Carriers of this gene have a reserve of biological strength powered by a resource called hemogen. The resource can be gained and spent in various ways, all of which are unlocked by other genes.\n\nCarriers lose 2 hemogen per day from biological entropy.</description>
    <geneClass>Gene_Hemogen</geneClass>
    <resourceGizmoType>GeneGizmo_ResourceHemogen</resourceGizmoType>
    <resourceLabel>hemogen</resourceLabel>
    <resourceGizmoThresholds>
      <li>0.25</li>
      <li>0.5</li>
      <li>0.75</li>
    </resourceGizmoThresholds>
    <showGizmoOnWorldView>true</showGizmoOnWorldView>
    <showGizmoWhenDrafted>true</showGizmoWhenDrafted>
    <resourceDescription>A reserve of biological strength which can be gained and spent in a variety of ways.\n\nHemogen can be increased by bloodfeeding from a human or consuming hemogen packs. You can obtain hemogen packs using the 'extract hemogen pack' medical operation on a non-hemogenic human.\n\nIf hemogen reaches zero, {PAWN_nameDef} will become very unhappy.</resourceDescription>
    <iconPath>UI/Icons/Genes/Gene_Hemogenic</iconPath>
    <selectionWeight>0</selectionWeight>
    <displayCategory>Hemogen</displayCategory>
    <displayOrderInCategory>-2</displayOrderInCategory>
    <customEffectDescriptions>
      <li>Gives hemogen supply.</li>
    </customEffectDescriptions>
    <symbolPack>
      <prefixSymbols>
        <li><symbol>hemo</symbol></li>
      </prefixSymbols>
    </symbolPack>
    <minAgeActive>3</minAgeActive>
    <resourceLossPerDay>0.02</resourceLossPerDay>
    <biostatCpx>1</biostatCpx>
    <biostatMet>1</biostatMet>
  </GeneDef>

  <GeneDef>
    <defName>HemogenDrain</defName>
    <label>hemogen drain</label>
    <labelShortAdj>draining</labelShortAdj>
    <description>Carriers lose an additional 8 hemogen per day from biological entropy.</description>
    <resourceLabel>hemogen</resourceLabel>
    <geneClass>Gene_HemogenDrain</geneClass>
    <iconPath>UI/Icons/Genes/Gene_HemogenDrain</iconPath>
    <prerequisite>Hemogenic</prerequisite>
    <resourceLossPerDay>0.08</resourceLossPerDay>
    <displayCategory>Hemogen</displayCategory>
    <displayOrderInCategory>-1</displayOrderInCategory>
    <minAgeActive>3</minAgeActive>
    <biostatCpx>1</biostatCpx>
    <biostatMet>6</biostatMet>
  </GeneDef>

  <GeneDef>
    <defName>FireWeakness</defName>
    <label>tinderskin</label>
    <labelShortAdj>tinderskin</labelShortAdj>
    <description>Carriers have dry, thin skin which burns easily from fire, and their immune systems react very poorly to this kind of threat. Damage from fire is multiplied by 4.</description>
    <iconPath>UI/Icons/Genes/Gene_FireWeakness</iconPath>
    <displayCategory>ResistanceAndWeakness</displayCategory>
    <displayOrderInCategory>70</displayOrderInCategory>
    <damageFactors>
      <Flame>4</Flame>
    </damageFactors>
    <biostatMet>2</biostatMet>
    <symbolPack>
      <prefixSymbols>
        <li><symbol>dry</symbol></li>
        <li><symbol>tinder</symbol></li>
        <li><symbol>kindle</symbol></li>
      </prefixSymbols>
    </symbolPack>
    <exclusionTags>
      <li>FireDamage</li>
    </exclusionTags>
  </GeneDef>

  <GeneDef>
    <defName>FireTerror</defName>
    <label>pyrophobia</label>
    <labelShortAdj>pyrophobic</labelShortAdj>
    <description>Carriers of this gene have an intense fear of fire. When fires are close, there is a chance they will have a mental breakdown at any moment.</description>
    <customEffectDescriptions>
      <li>May have a mental breakdown when near fires.</li>
    </customEffectDescriptions>
    <suppressedTraits>
      <li>
        <def>Pyromaniac</def>
      </li>
    </suppressedTraits>
    <displayCategory>ResistanceAndWeakness</displayCategory>
    <displayOrderInCategory>80</displayOrderInCategory>
    <iconPath>UI/Icons/Genes/Gene_FireTerror</iconPath>
    <mentalBreakMtbDays>0.1</mentalBreakMtbDays>
    <mentalBreakDef>FireTerror</mentalBreakDef>
    <biostatMet>4</biostatMet>
  </GeneDef>
  <ThoughtDef>
    <defName>Pyrophobia</defName>
    <workerClass>ThoughtWorker_Pyrophobia</workerClass>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
    <stages>
      <li>
        <label>afraid of fire</label>
        <description>The flames make me so nervous. Such searing heat - I feel like it will leap at me at any moment!</description>
        <baseMoodEffect>-10</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <GeneDef>
    <defName>PerfectImmunity</defName>
    <label>perfect immunity</label>
    <labelShortAdj>perfect-immune</labelShortAdj>
    <description>Carriers of this gene have archite-enhanced immune systems which intelligently destroy invaders. They are totally immune to most normal illnesses.</description>
    <iconPath>UI/Icons/Genes/Gene_PerfectImmunity</iconPath>
    <displayCategory>Archite</displayCategory>
    <makeImmuneTo>
      <li>Flu</li>
      <li>Malaria</li>
      <li>SleepingSickness</li>
      <li>Plague</li>
      <li>WoundInfection</li>
      <li>LungRot</li>
      <li>GutWorms</li>
      <li>MuscleParasites</li>
      <li>OrganDecay</li>
    </makeImmuneTo>
    <biostatCpx>3</biostatCpx>
    <biostatArc>1</biostatArc>
    <exclusionTags>
      <li>Immunity</li>
    </exclusionTags>
  </GeneDef>

  <GeneDef>
    <defName>DiseaseFree</defName>
    <label>non-senescent</label>
    <description>Carriers of this gene do not go through senescence in the normal way. They never get chronic age-related diseases like cancer, bad back, cataracts, or dementia.</description>
    <iconPath>UI/Icons/Genes/Gene_NonSenescent</iconPath>
    <displayCategory>Archite</displayCategory>
    <hediffGiversCannotGive>
      <li>Carcinoma</li>
      <li>HeartAttack</li>
      <li>BadBack</li>
      <li>Frail</li>
      <li>Cataract</li>
      <li>Dementia</li>
      <li>Alzheimers</li>
      <li>Asthma</li>
      <li>HeartArteryBlockage</li>
    </hediffGiversCannotGive>
    <biostatCpx>3</biostatCpx>
    <biostatArc>1</biostatArc>
  </GeneDef>

  <GeneDef>
    <defName>TotalHealing</defName>
    <label>scarless</label>
    <labelShortAdj>scarless</labelShortAdj>
    <description>Carriers of this gene have a special type of regenerator cell which can heal old wounds and chronic illnesses like bad back.</description>
    <customEffectDescriptions>
      <li>Every (*DateTime)15-30 days(/DateTime), one old wound or chronic illness is healed.</li>
    </customEffectDescriptions>
    <iconPath>UI/Icons/Genes/Gene_TotalHealing</iconPath>
    <geneClass>Gene_Healing</geneClass>
    <preventPermanentWounds>true</preventPermanentWounds>
    <biostatCpx>4</biostatCpx>
    <biostatArc>1</biostatArc>
    <displayCategory>Archite</displayCategory>
  </GeneDef>

  <GeneDef>
    <defName>Deathrest</defName>
    <label>deathrest</label>
    <description>Carriers of this gene must periodically regenerate themselves in a special coma called deathrest. Deathrest takes days, but can confer substantial bonuses. Deathrest can be accelerated and its effects enhanced by the use of a variety of special buildings and technologies.\n\nThose who put off deathresting will suffer from deathrest exhaustion.</description>
    <geneClass>Gene_Deathrest</geneClass>
    <iconPath>UI/Icons/Genes/Gene_Deathrest</iconPath>
    <prerequisite>Hemogenic</prerequisite>
    <displayCategory>Hemogen</displayCategory>
    <causesNeed>Deathrest</causesNeed>
    <biostatMet>6</biostatMet>
    <minAgeActive>3</minAgeActive>
    <symbolPack>
      <prefixSymbols>
        <li><symbol>death</symbol></li>
        <li><symbol>dead</symbol></li>
        <li><symbol>still</symbol></li>
      </prefixSymbols>
      <suffixSymbols>
        <li><symbol>rester</symbol></li>
      </suffixSymbols>
    </symbolPack>
  </GeneDef>

  <GeneDef>
    <defName>Ageless</defName>
    <label>ageless</label>
    <description>Carriers of this gene have archites in the bloodstream which continuously reverse the process of aging. Starting at the age of 13, carriers begin to biologically age slower. By 18, the aging process stops completely.</description>
    <iconPath>UI/Icons/Genes/Gene_Ageless</iconPath>
    <displayCategory>Archite</displayCategory>
    <biologicalAgeTickFactorFromAgeCurve>
      <points>
        <li>(13, 1)</li>
        <li>(18.5, 0)</li>
      </points>
    </biologicalAgeTickFactorFromAgeCurve>
    <biostatCpx>3</biostatCpx>
    <biostatArc>1</biostatArc>
    <symbolPack>
      <wholeNameSymbols>
        <li><symbol>immortal</symbol></li>
        <li><symbol>methuselah</symbol></li>
      </wholeNameSymbols>
      <prefixSymbols>
        <li><symbol>god</symbol></li>
        <li><symbol>deo</symbol></li>
        <li><symbol>deu</symbol></li>
        <li><symbol>methu</symbol></li>
      </prefixSymbols>
      <suffixSymbols>
        <li><symbol>god</symbol></li>
        <li><symbol>deus</symbol></li>
      </suffixSymbols>
    </symbolPack>
  </GeneDef>

  <GeneDef>
    <defName>Deathless</defName>
    <label>deathless</label>
    <description>Carriers of this gene have archites in the blood which will sustain their life processes no matter what. As long as the brain remains intact, a carrier of this gene will never die.</description>
    <geneClass>Gene_Deathless</geneClass>
    <iconPath>UI/Icons/Genes/Gene_Deathless</iconPath>
    <displayCategory>Archite</displayCategory>
    <marketValueFactor>2</marketValueFactor>
    <biostatCpx>7</biostatCpx>
    <biostatArc>1</biostatArc>
    <customEffectDescriptions>
      <li>Can only die from destruction of brain.</li>
    </customEffectDescriptions>
    <symbolPack>
      <wholeNameSymbols>
        <li><symbol>immortal</symbol></li>
      </wholeNameSymbols>
      <prefixSymbols>
        <li><symbol>god</symbol></li>
        <li><symbol>deo</symbol></li>
        <li><symbol>deu</symbol></li>
      </prefixSymbols>
      <suffixSymbols>
        <li><symbol>god</symbol></li>
        <li><symbol>deus</symbol></li>
      </suffixSymbols>
    </symbolPack>
  </GeneDef>

  <GeneDef>
    <defName>ArchiteMetabolism</defName>
    <label>archite metabolism</label>
    <labelShortAdj>archite-metabolic</labelShortAdj>
    <description>Carriers of this gene have special archites in their cells that facilitate and optimize metabolism. This improves overall genetic and metabolic quality.</description>
    <iconPath>UI/Icons/Genes/Gene_ArchiteMetabolism</iconPath>
    <displayCategory>Archite</displayCategory>
    <marketValueFactor>1.5</marketValueFactor>
    <biostatCpx>6</biostatCpx>
    <biostatMet>6</biostatMet>
    <biostatArc>2</biostatArc>
  </GeneDef>

</Defs>