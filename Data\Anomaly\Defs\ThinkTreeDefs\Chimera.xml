<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <ThinkTreeDef>
    <defName>Chimera</defName>
    <thinkRoot Class="ThinkNode_Priority">
      <subNodes>
        
        <li Class="ThinkNode_Subtree">
          <treeDef>Downed</treeDef>
        </li>
        <li Class="ThinkNode_Subtree">
          <treeDef>BurningResponse</treeDef>
        </li>

        <!-- Lord directives -->
        <li Class="ThinkNode_Subtree">
          <treeDef>LordDuty</treeDef>
        </li>

        <!-- Escaping-->
        <li Class="ThinkNodeConditional_EscapingHoldingPlatform">
          <subNodes>
            <li Class="JobGiver_EscapingHoldingPlatform" />
          </subNodes>
        </li>

        <li Class="JobGiver_WanderHerd">
          <maxDanger>Deadly</maxDanger>
          <ticksBetweenWandersRange>120~240</ticksBetweenWandersRange>
        </li>
        
        <li Class="JobGiver_IdleError"/>
      </subNodes>
    </thinkRoot>
  </ThinkTreeDef>

  <ThinkTreeDef>
    <defName>ChimeraConstant</defName>
    <thinkRoot Class="ThinkNode_Priority">
      <subNodes>
        <!-- Despawned -->
        <li Class="ThinkNode_Subtree">
          <treeDef>Despawned</treeDef>
        </li>
      </subNodes>
    </thinkRoot>
  </ThinkTreeDef>
  
</Defs>