<?xml version="1.0" encoding="utf-8" ?>
<Defs>
  
  <DamageDef ParentName="Flame">
    <defName>ElectricalBurn</defName>
    <label>electrical burn</label>
    <workerClass>DamageWorker_AddInjury</workerClass>
    <deathMessage>{0} has died from electrical burns.</deathMessage>
    <hediff>ElectricalBurn</hediff>
    <minDamageToFragment>1</minDamageToFragment>
    <explosionColorCenter>(0.26, 0.51, 0.97)</explosionColorCenter>
    <explosionColorEdge>(0.26, 0.51, 0.97)</explosionColorEdge>
    <scaleDamageToBuildingsBasedOnFlammability>false</scaleDamageToBuildingsBasedOnFlammability>
  </DamageDef>

</Defs>
