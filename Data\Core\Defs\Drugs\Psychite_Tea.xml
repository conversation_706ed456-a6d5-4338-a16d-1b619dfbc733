﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingDef ParentName="MakeableDrugBase">
    <defName>PsychiteTea</defName>
    <label>psychite tea</label>
    <description>A fragrant tea infused with leaves of the psychoid plant. Drinking it induces a subtle psychite euphoria. This tea is easy to produce at cooking facilities, but can produce psychite addiction if consumed too often.\n\nMany tribes use psychoid tea, both as a daily energizer and as part of social and religious rituals.</description>
    <descriptionHyperlinks>
      <HediffDef>PsychiteTeaHigh</HediffDef>
      <HediffDef>PsychiteTolerance</HediffDef>
      <HediffDef>PsychiteAddiction</HediffDef>
      <HediffDef>ChemicalDamageSevere</HediffDef>
    </descriptionHyperlinks>
    <graphicData>
      <texPath>Things/Item/Drug/Tea</texPath>
      <graphicClass>Graphic_StackCount</graphicClass>
      <drawSize>0.88</drawSize>
    </graphicData>
    <rotatable>false</rotatable>
    <statBases>
      <WorkToMake>400</WorkToMake>
      <MarketValue>10</MarketValue>
      <Mass>0.05</Mass>
      <DeteriorationRate>6</DeteriorationRate>
    </statBases>
    <techLevel>Neolithic</techLevel>
    <ingestible>
      <foodType>Fluid, Processed</foodType>
      <joyKind>Chemical</joyKind>
      <joy>0.40</joy>
      <baseIngestTicks>210</baseIngestTicks>
      <nurseable>true</nurseable>
      <drugCategory>Social</drugCategory>
      <ingestSound>Ingest_Drink</ingestSound>
      <ingestHoldOffsetStanding>
        <northDefault>
          <offset>(0.18,0,0)</offset>
        </northDefault>
      </ingestHoldOffsetStanding>
      <ingestHoldUsesTable>false</ingestHoldUsesTable>
      <ingestCommandString>Drink {0}</ingestCommandString>
      <ingestReportString>Drinking {0}.</ingestReportString>
      <outcomeDoers>
        <li Class="IngestionOutcomeDoer_GiveHediff">
          <hediffDef>PsychiteTeaHigh</hediffDef>
          <severity>0.75</severity>
          <toleranceChemical>Psychite</toleranceChemical>
        </li>
        <li Class="IngestionOutcomeDoer_OffsetNeed">
          <need>Rest</need>
          <offset>0.1</offset>
          <toleranceChemical>Psychite</toleranceChemical>
        </li>
        <li Class="IngestionOutcomeDoer_GiveHediff">
          <hediffDef>PsychiteTolerance</hediffDef>
          <toleranceChemical>Psychite</toleranceChemical>
          <severity>0.03</severity>
          <divideByBodySize>true</divideByBodySize>
          <multiplyByGeneToleranceFactors>true</multiplyByGeneToleranceFactors>
        </li>
      </outcomeDoers>
    </ingestible>
    <recipeMaker>
      <researchPrerequisite>PsychoidBrewing</researchPrerequisite>
      <recipeUsers>
        <li>Campfire</li>
        <li>ElectricStove</li>
        <li>FueledStove</li>
      </recipeUsers>
      <workSpeedStat>DrugCookingSpeed</workSpeedStat>
      <workSkill>Cooking</workSkill>
      <skillRequirements>
        <Cooking>2</Cooking>
      </skillRequirements>
      <requiredGiverWorkType>Cooking</requiredGiverWorkType>
      <displayPriority>1460</displayPriority>
    </recipeMaker>
    <costList>
      <PsychoidLeaves>4</PsychoidLeaves>
    </costList>
    <comps>
      <li Class="CompProperties_Drug">
        <chemical>Psychite</chemical>
        <addictiveness>0.02</addictiveness>
        <minToleranceToAddict>0.10</minToleranceToAddict>
        <existingAddictionSeverityOffset>0.15</existingAddictionSeverityOffset>
        <needLevelOffset>0.9</needLevelOffset>
        <listOrder>110</listOrder>
      </li>
    </comps>
  </ThingDef>
  
  <HediffDef>
    <defName>PsychiteTeaHigh</defName>
    <label>high on psychite tea</label>
    <labelNoun>a psychite tea high</labelNoun>
    <description>Active psychite tea in the bloodstream. Generates a mild euphoric effect.</description>
    <hediffClass>Hediff_High</hediffClass>
    <defaultLabelColor>(1,0,0.5)</defaultLabelColor>
    <scenarioCanAdd>true</scenarioCanAdd>
    <maxSeverity>1.0</maxSeverity>
    <isBad>false</isBad>
    <comps>
      <li Class="HediffCompProperties_SeverityPerDay">
        <severityPerDay>-3.0</severityPerDay>
        <showHoursToRecover>true</showHoursToRecover>
      </li>
    </comps>
      <stages>
        <li>
          <painFactor>0.9</painFactor>
          <statFactors>
            <RestFallRateFactor>0.8</RestFallRateFactor>
          </statFactors>
        </li>
      </stages>
  </HediffDef>
  
  <ThoughtDef>
    <defName>PsychiteTeaHigh</defName>
    <workerClass>ThoughtWorker_Hediff</workerClass>
    <hediff>PsychiteTeaHigh</hediff>
    <validWhileDespawned>true</validWhileDespawned>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
    <stages>
      <li>
        <label>psychite tea</label>
        <description>Drinking that tea made me feel great. I love having this energy!</description>
        <baseMoodEffect>12</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

</Defs>
