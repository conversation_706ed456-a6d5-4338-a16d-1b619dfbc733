<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <!-- Kid clothing -->
  <ThingDef Abstract="True" Name="ChildApparelMakeableBase" ParentName="ApparelMakeableBase">
    <recipeMaker>
      <researchPrerequisite>ComplexClothing</researchPrerequisite>
    </recipeMaker>
    <stuffCategories>
      <li>Fabric</li>
      <li>Leathery</li>
    </stuffCategories>
    <thingCategories>
      <li>ApparelMisc</li>
    </thingCategories>
    <apparel>
      <canBeDesiredForIdeo>false</canBeDesiredForIdeo>
      <developmentalStageFilter>Child</developmentalStageFilter>
      <layers>
        <li>OnSkin</li>
      </layers>
      <tags>
        <li>IndustrialBasic</li>
      </tags>
    </apparel>
  </ThingDef>

  <ThingDef ParentName="ChildApparelMakeableBase">
    <defName>Apparel_KidRomper</defName>
    <label>kid romper</label>
    <description>A combined t-shirt and pants sized for kids, with a snuggly built-in undergarment. Little ones love it!</description>
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/KidRomper/KidRomper</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>(0.7, 0.7)</drawSize>
    </graphicData>
    <costStuffCount>30</costStuffCount>
    <statBases>
      <MaxHitPoints>50</MaxHitPoints>
      <WorkToMake>2000</WorkToMake>
      <Mass>0.18</Mass>
      <StuffEffectMultiplierArmor>0.2</StuffEffectMultiplierArmor>
      <StuffEffectMultiplierInsulation_Cold>0.22</StuffEffectMultiplierInsulation_Cold>
      <StuffEffectMultiplierInsulation_Heat>0.10</StuffEffectMultiplierInsulation_Heat>
      <EquipDelay>1.5</EquipDelay>
    </statBases>
    <apparel>
      <bodyPartGroups>
        <li>Torso</li>
        <li>Neck</li>
        <li>Shoulders</li>
        <li>Arms</li>
        <li>Legs</li>
      </bodyPartGroups>
      <wornGraphicPath>Things/Pawn/Humanlike/Apparel/KidRomper/KidRomper</wornGraphicPath>
    </apparel>
    <colorGenerator Class="ColorGenerator_StandardApparel" />
    <tradeTags>
      <li>BasicClothing</li>
    </tradeTags>
    <recipeMaker>
      <displayPriority>270</displayPriority>
    </recipeMaker>
  </ThingDef>

  <ThingDef ParentName="ChildApparelMakeableBase">
    <defName>Apparel_KidShirt</defName>
    <label>kid shirt</label>
    <description>A small, simple t-shirt sized to fit children.</description>
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/KidShirt/KidShirt</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>(0.7, 0.7)</drawSize>
    </graphicData>
    <costStuffCount>20</costStuffCount>
    <statBases>
      <MaxHitPoints>50</MaxHitPoints>
      <WorkToMake>1280</WorkToMake>
      <Mass>0.15</Mass>
      <StuffEffectMultiplierArmor>0.2</StuffEffectMultiplierArmor>
      <StuffEffectMultiplierInsulation_Cold>0.22</StuffEffectMultiplierInsulation_Cold>
      <StuffEffectMultiplierInsulation_Heat>0.10</StuffEffectMultiplierInsulation_Heat>
      <EquipDelay>1.5</EquipDelay>
    </statBases>
    <apparel>
      <bodyPartGroups>
        <li>Torso</li>
        <li>Shoulders</li>
      </bodyPartGroups>
      <wornGraphicPath>Things/Pawn/Humanlike/Apparel/KidShirt/KidShirt</wornGraphicPath>
    </apparel>
    <colorGenerator Class="ColorGenerator_StandardApparel" />
    <tradeTags>
      <li>BasicClothing</li>
    </tradeTags>
    <recipeMaker>
      <displayPriority>275</displayPriority>
    </recipeMaker>
  </ThingDef>

  <ThingDef ParentName="ChildApparelMakeableBase">
    <defName>Apparel_KidPants</defName>
    <label>kid pants</label>
    <description>A small set of pants sized to fit children.</description>
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/KidPants/KidPants</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>(0.7, 0.7)</drawSize>
    </graphicData>
    <costStuffCount>20</costStuffCount>
    <statBases>
      <MaxHitPoints>50</MaxHitPoints>
      <WorkToMake>1280</WorkToMake>
      <Mass>0.25</Mass>
      <StuffEffectMultiplierArmor>0.2</StuffEffectMultiplierArmor>
      <StuffEffectMultiplierInsulation_Cold>0.20</StuffEffectMultiplierInsulation_Cold>
      <StuffEffectMultiplierInsulation_Heat>0.08</StuffEffectMultiplierInsulation_Heat>
      <EquipDelay>2</EquipDelay>
    </statBases>
    <apparel>
      <bodyPartGroups>
        <li>Legs</li>
      </bodyPartGroups>
    </apparel>
    <colorGenerator Class="ColorGenerator_StandardApparel" />
    <tradeTags>
      <li>BasicClothing</li>
    </tradeTags>
    <recipeMaker>
      <displayPriority>285</displayPriority>
    </recipeMaker>
  </ThingDef>

  <ThingDef ParentName="ChildApparelMakeableBase">
    <defName>Apparel_KidParka</defName>
    <label>kid parka</label>
    <description>A heavy jacket for keeping a child warm in cold temperatures.</description>
    <techLevel>Neolithic</techLevel>
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/KidParka/KidParka</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>(0.7, 0.7)</drawSize>
    </graphicData>
    <costStuffCount>40</costStuffCount>
    <statBases>
      <MaxHitPoints>100</MaxHitPoints>
      <WorkToMake>6400</WorkToMake>
      <Mass>1</Mass>
      <StuffEffectMultiplierArmor>0.2</StuffEffectMultiplierArmor>
      <StuffEffectMultiplierInsulation_Cold>1.5</StuffEffectMultiplierInsulation_Cold>
      <StuffEffectMultiplierInsulation_Heat>0.00</StuffEffectMultiplierInsulation_Heat>
      <EquipDelay>3</EquipDelay>
    </statBases>
    <apparel>
      <bodyPartGroups>
        <li>Torso</li>
        <li>Neck</li>
        <li>Shoulders</li>
        <li>Arms</li>
      </bodyPartGroups>
      <layers Inherit="False">
        <li>Shell</li>
      </layers>
      <wornGraphicPath>Things/Pawn/Humanlike/Apparel/KidParka/KidParka</wornGraphicPath>
      <anyTechLevelCanUseForWarmth>true</anyTechLevelCanUseForWarmth>
      <tags>
        <li>Neolithic</li>
      </tags>
    </apparel>
    <colorGenerator Class="ColorGenerator_StandardApparel" />
    <tradeTags>
      <li>BasicClothing</li>
    </tradeTags>
    <recipeMaker>
      <displayPriority>280</displayPriority>
      <researchPrerequisite Inherit="False" IsNull="True"/>
    </recipeMaker>
  </ThingDef>

  <ThingDef ParentName="ChildApparelMakeableBase">
    <defName>Apparel_KidTribal</defName>
    <label>kid tribalwear</label>
    <description>A one-piece garment sized for kids, crafted using neolithic tools. It provides effective insulation against the elements.</description>
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/KidTribal/KidTribalwear</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>(0.7, 0.7)</drawSize>
    </graphicData>
    <costStuffCount>30</costStuffCount>
    <statBases>
      <MaxHitPoints>60</MaxHitPoints>
      <WorkToMake>1400</WorkToMake>
      <Mass>0.5</Mass>
      <StuffEffectMultiplierArmor>0.2</StuffEffectMultiplierArmor>
      <StuffEffectMultiplierInsulation_Cold>0.5</StuffEffectMultiplierInsulation_Cold>
      <StuffEffectMultiplierInsulation_Heat>0.5</StuffEffectMultiplierInsulation_Heat>
      <EquipDelay>1.5</EquipDelay>
    </statBases>
    <apparel>
      <bodyPartGroups>
        <li>Torso</li>
        <li>Legs</li>
      </bodyPartGroups>
      <wornGraphicPath>Things/Pawn/Humanlike/Apparel/KidTribal/KidTribalwear</wornGraphicPath>
      <tags Inherit="False">
        <li>Neolithic</li>
      </tags>
      <canBeDesiredForIdeo>false</canBeDesiredForIdeo>
      <developmentalStageFilter>Child</developmentalStageFilter>
    </apparel>
    <techLevel>Neolithic</techLevel>
    <recipeMaker>
      <researchPrerequisite Inherit="False" IsNull="True" />
      <recipeUsers>
        <li>ElectricTailoringBench</li>
        <li>HandTailoringBench</li>
        <li>CraftingSpot</li>
      </recipeUsers>
      <displayPriority>290</displayPriority>
    </recipeMaker>
    <colorGenerator Class="ColorGenerator_Options">
      <options>
        <li>
          <weight>10</weight>
          <only>(0.4,0.3,0.15)</only>
        </li>
        <li>
          <weight>15</weight>
          <only>(0.6,0.45,0.18)</only>
        </li>
        <li>
          <weight>20</weight>
          <only>(0.8,0.6,0.23)</only>
        </li>
      </options>
    </colorGenerator>
    <tradeTags>
      <li>BasicClothing</li>
    </tradeTags>
  </ThingDef>

  <!-- Kid armor -->
  <ThingDef ParentName="ArmorHelmetMakeableBase">
    <defName>Apparel_KidHelmet</defName>
    <label>kid helmet</label>
    <description>A simple helmet sized for children. It gives moderate protection against sharp attacks. Not effective against blunt weapons. That said, if this is being used in combat something has already gone very wrong.</description>
    <techLevel>Industrial</techLevel> 
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/KidHelmet/KidHelmet</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>0.55</drawSize>
    </graphicData>
    <stuffCategories>
      <li>Metallic</li>
    </stuffCategories>
    <costStuffCount>20</costStuffCount>
    <statBases>
      <WorkToMake>2000</WorkToMake>
      <MaxHitPoints>60</MaxHitPoints>
      <Mass>1</Mass>
      <StuffEffectMultiplierArmor>0.5</StuffEffectMultiplierArmor>
      <StuffEffectMultiplierInsulation_Cold>0.15</StuffEffectMultiplierInsulation_Cold>
      <StuffEffectMultiplierInsulation_Heat>0</StuffEffectMultiplierInsulation_Heat>
      <EquipDelay>1.5</EquipDelay>
    </statBases>
    <recipeMaker>
      <researchPrerequisite>Smithing</researchPrerequisite>
      <recipeUsers>
        <li>ElectricSmithy</li>
        <li>FueledSmithy</li>
      </recipeUsers>
      <displayPriority>210</displayPriority>
    </recipeMaker>
    <apparel>
      <countsAsClothingForNudity>false</countsAsClothingForNudity>
      <bodyPartGroups>
        <li>UpperHead</li>
      </bodyPartGroups>
      <parentTagDef>ApparelHead</parentTagDef>
      <wornGraphicPath>Things/Pawn/Humanlike/Apparel/KidHelmet/KidHelmet</wornGraphicPath>
      <layers>
        <li>Overhead</li>
      </layers>
      <tags>
        <li>IndustrialMilitaryBasic</li>
      </tags>
      <defaultOutfitTags>
        <li>Soldier</li>
      </defaultOutfitTags>
      <canBeDesiredForIdeo>false</canBeDesiredForIdeo>
      <developmentalStageFilter>Child</developmentalStageFilter>
    </apparel>
    <tradeTags>
      <li>Clothing</li>
      <li>Armor</li>
    </tradeTags>
  </ThingDef>

</Defs>