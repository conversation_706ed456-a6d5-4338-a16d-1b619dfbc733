﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <SoundDef>
    <defName>Pawn_Revenant_Call</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Revenant/Call</clipFolderPath>
          </li>
        </grains>
        <volumeRange>20~28</volumeRange>
        <pitchRange>0.9770588~1.137647</pitchRange>
        <distRange>0~50.40025</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>
  
  <SoundDef>
    <defName>Pawn_Revenant_Wounded</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Revenant/Pain</clipFolderPath>
          </li>
        </grains>
        <volumeRange>25~35</volumeRange>
        <pitchRange>0.9311764~1.068824</pitchRange>
        <distRange>0~70</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Revenant_Death</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Revenant/Death</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30~35</volumeRange>
        <pitchRange>0.9311764~1.068824</pitchRange>
        <distRange>0~70</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>
  
  <SoundDef>
    <defName>Pawn_Revenant_Revealed</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <onCamera>True</onCamera>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Revenant/Revealed</clipFolderPath>
          </li>
        </grains>
        <volumeRange>25~35</volumeRange>
        <pitchRange>0.9311764~1.068824</pitchRange>
        <distRange>0~70</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Revenant_Stealth</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Revenant/Stealth</clipFolderPath>
          </li>
        </grains>
        <volumeRange>25~35</volumeRange>
        <pitchRange>0.9311764~1.068824</pitchRange>
        <distRange>0~70</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Revenant_Hypnotize</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Pawn/Revenant/Revenant_Hypnotize_Loop_A_01</clipPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <sustainAttack>2.5</sustainAttack>
        <sustainRelease>2.5</sustainRelease>
        <distRange>20~100</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Revenant_StartledScream</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Revenant/StartledScream</clipFolderPath>
          </li>
        </grains>
        <volumeRange>25~30</volumeRange>
        <pitchRange>0.9311764~1.068824</pitchRange>
        <distRange>0~70</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>
</Defs>