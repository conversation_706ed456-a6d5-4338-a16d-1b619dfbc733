<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <SoundDef>
    <defName>StartRecombining</defName>
    <maxVoices>1</maxVoices>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <onCamera>True</onCamera>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/GeneAssembler/Xenogerm_Creation_Started</clipPath>
          </li>
        </grains>
        <volumeRange>40</volumeRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>LetterArrive_Bossgroup</defName>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <onCamera>True</onCamera>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>UI/LetterArriveBossgroup</clipPath>
          </li>
        </grains>
        <volumeRange>50</volumeRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>DatacoreRead</defName>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <onCamera>True</onCamera>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>UI/DatacoreRead</clipPath>
          </li>
        </grains>
        <volumeRange>40</volumeRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Designate_Adopt</defName>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <onCamera>True</onCamera>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>UI/Adopt</clipPath>
          </li>
        </grains>
        <volumeRange>50</volumeRange>
      </li>
    </subSounds>
  </SoundDef>

</Defs>