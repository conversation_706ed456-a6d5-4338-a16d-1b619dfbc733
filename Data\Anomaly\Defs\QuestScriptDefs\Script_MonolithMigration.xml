﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <QuestScriptDef>
    <defName>MonolithMigration</defName>
    <isRootSpecial>true</isRootSpecial>
    <defaultChallengeRating>1</defaultChallengeRating>
    <questNameRules>
      <rulesStrings>
        <li>questName->Strange signal</li>
      </rulesStrings>
    </questNameRules>
    <questDescriptionRules>
      <rulesStrings>
        <li>questDescription->Your colonists all feel a psychic message echoing. It's a signal from an inhuman, twisted mind that was left behind somewhere far away. You can focus on the signal and echo it back, but you can't tell what will happen if you do.</li>
      </rulesStrings>
    </questDescriptionRules>
    <root Class="QuestNode_Root_MonolithMigration" />
  </QuestScriptDef>
</Defs>