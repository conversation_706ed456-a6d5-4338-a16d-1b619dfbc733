﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <MutantDef Name="BaseShambler">
    <defName>Shambler</defName>
    <label>shambler</label>
    <description>A creature who has been raised from the dead by corrupted nano-scale archites.\n\nAbout shamblers: Shamblers are corpses which have been reanimated by archotechnology. They attack living creatures relentlessly and their immunity to pain makes them difficult to kill. However, the archites which animate them cannot sustain the rotting body for long, so shamblers expire after a few days of movement.\n\nThere are ways to raise your own shamblers and make yourself invisible to them, so they attack only outsiders.</description>
    <hediff>Shambler</hediff>
    <thinkTree>Shambler</thinkTree>
    <thinkTreeConstant>ShamblerConstant</thinkTreeConstant>
    <namePrefix>shambler </namePrefix>
    <useCorpseGraphics>true</useCorpseGraphics>
    <isConsideredCorpse>true</isConsideredCorpse>
    <canBleed>false</canBleed>
    <bloodDef>Filth_DarkBlood</bloodDef>
    <bloodSmearDef>Filth_DarkBloodSmear</bloodSmearDef>
    <entitledToMedicalCare>false</entitledToMedicalCare>
    <isImmuneToInfections>true</isImmuneToInfections>
    <removeChronicIllnesses>true</removeChronicIllnesses>
    <removeAddictions>true</removeAddictions>
    <removeAllInjuries>true</removeAllInjuries>
    <restoreLegs>true</restoreLegs>
    <defaultFaction>Entities</defaultFaction>
    <standingAnimation>ShamblerSway</standingAnimation>
    <canOpenDoors>false</canOpenDoors>
    <makesFootprints>false</makesFootprints>
    <clearMutantStatusOnDeath>true</clearMutantStatusOnDeath>
    <canAttackWhileCrawling>true</canAttackWhileCrawling>
    <deathOnDownedChance>0.25</deathOnDownedChance>
    <woundColor>(0.3, 0.3, 0.0, 1.0)</woundColor>
    <anomalyKnowledgeOffset>0</anomalyKnowledgeOffset>
    <knowledgeCategory>Basic</knowledgeCategory>
    <codexEntry>Shambler</codexEntry>
    <removesHediffs>
      <li>CryptosleepSickness</li>
      <li>LuciferiumHigh</li>
      <li>LuciferiumAddiction</li>
      <li>Scaria</li>
      <li>CorpseTorment</li>
      <li>CubeInterest</li>
      <li>CubeWithdrawal</li>
      <li>CubeComa</li>
      <li>CubeRage</li>
      <li>HeartAttack</li>
      <li>Scaria</li>
      <li MayRequire="Ludeon.RimWorld.Royalty">PsychicAmplifier</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">MechlinkImplant</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">PregnantHuman</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">PregnancyLabor</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">HemogenAmplified</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">DeathrestExhaustion</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">InterruptedDeathrest</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">HemogenCraving</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">Deathrest</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">RegenerationComa</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">PostpartumExhaustion</li>
    </removesHediffs>
    <disablesGenes>
      <li MayRequire="Ludeon.RimWorld.Biotech">Hemogenic</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">Deathrest</li>
    </disablesGenes>
    <producesBioferrite>true</producesBioferrite>
    <!-- Sounds -->
    <soundCall>Pawn_Shambler_Call</soundCall>
    <soundAttack>Pawn_Shambler_Attack</soundAttack>
    <soundWounded>Pawn_Shambler_Wounded</soundWounded>
    <soundDeath>Pawn_Shambler_Killed</soundDeath>
    <!-- Rendering -->
    <renderNodeProperties>
      <li Class="PawnRenderNodeProperties_Overlay">
        <debugLabel>Shambler wounds</debugLabel>
        <workerClass>PawnRenderNodeWorker_OverlayShambler</workerClass>
        <overlayLayer>Body</overlayLayer>
        <baseLayer>20</baseLayer>
        <pawnType>HumanlikeOnly</pawnType>
      </li>
      <li>
        <debugLabel>Shambler wounds</debugLabel>
        <nodeClass>PawnRenderNode_AnimalPart</nodeClass>
        <workerClass>PawnRenderNodeWorker_OverlayShambler</workerClass>
        <overlayLayer>Body</overlayLayer>
        <baseLayer>20</baseLayer>
        <pawnType>NonHumanlikeOnly</pawnType>
      </li>
    </renderNodeProperties>

    <tools>
      <li>
        <label>teeth</label>
        <capacities>
          <li>Bite</li>
        </capacities>
        <power>8.2</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>Teeth</linkedBodyPartsGroup>
        <chanceFactor>1</chanceFactor>
        <soundMeleeHit>Pawn_Melee_HumanBite_Hit</soundMeleeHit>
        <soundMeleeMiss>Pawn_Melee_HumanBite_Miss</soundMeleeMiss>
      </li>
      <li>
        <label>left hand</label>
        <labelNoLocation>hand</labelNoLocation>
        <capacities>
          <li>Scratch</li>
        </capacities>
        <power>7.0</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>LeftHand</linkedBodyPartsGroup>
        <chanceFactor>1.5</chanceFactor>
        <alwaysTreatAsWeapon>true</alwaysTreatAsWeapon>
      </li>
      <li>
        <label>right hand</label>
        <labelNoLocation>hand</labelNoLocation>
        <capacities>
          <li>Scratch</li>
        </capacities>
        <power>7.0</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>RightHand</linkedBodyPartsGroup>
        <chanceFactor>1.5</chanceFactor>
        <alwaysTreatAsWeapon>true</alwaysTreatAsWeapon>
      </li>
    </tools>
  </MutantDef>

  <MutantDef>
    <defName>Ghoul</defName>
    <label>ghoul</label>
    <description>A person who has been turned into a ghoul.\n\nAbout ghouls: Ghouls are engineered murder machines, twisted by dark psychic influences.\n\nGhouls are very dangerous in melee combat. However, the process of their creation leaves them incapable of higher levels of thought, preventing them from holding weapons or tools. They cannot work and outside combat they wander in a half-conscious stupor.\n\nMany people find the constant twitching of ghouls disturbing. More disturbing is their dependence on raw meat. If they go hungry they will turn hostile, even attacking their creators to sate their hunger.</description>
    <hediff>Ghoul</hediff>
    <thinkTree>Ghoul</thinkTree>
    <thinkTreeConstant>GhoulConstant</thinkTreeConstant>
    <showLabel>true</showLabel>
    <canBleed>false</canBleed>
    <isImmuneToInfections>true</isImmuneToInfections>
    <showJobReport>true</showJobReport>
    <canWearApparel>false</canWearApparel>
    <canBeDrafted>true</canBeDrafted>
    <canLearn>true</canLearn>
    <relativeTurnedThought>BecameGhoul</relativeTurnedThought>
    <canTravelInCaravan>true</canTravelInCaravan>
    <respectsAllowedArea>true</respectsAllowedArea>
    <hasHostilityResponse>true</hasHostilityResponse>
    <allowedDevelopmentalStages>Adult</allowedDevelopmentalStages>
    <enabledNeeds>
      <li>Food</li>
    </enabledNeeds>
    <foodType>CarnivoreAnimalStrict</foodType>
    <allowEatingCorpses>true</allowEatingCorpses>
    <deathOnDownedChance>0.25</deathOnDownedChance>
    <removeChronicIllnesses>true</removeChronicIllnesses>
    <removePermanentInjuries>true</removePermanentInjuries>
    <removeAddictions>true</removeAddictions>
    <canUseDrugs>true</canUseDrugs>
    <drugWhitelist>
      <li>VoidsightSerum</li>
      <li>MetalbloodSerum</li>
      <li>JuggernautSerum</li>
      <li>MindNumbSerum</li>
      <li>GhoulResurrectionSerum</li>
    </drugWhitelist>
    <anomalyKnowledgeOffset>0.5</anomalyKnowledgeOffset>
    <knowledgeCategory>Basic</knowledgeCategory>
    <codexEntry>Ghoul</codexEntry>
    <givesHediffs>
      <li>
        <def>Regeneration</def>
        <severity>0.1</severity>
      </li>
    </givesHediffs>
    <removesHediffs>
      <li>LuciferiumHigh</li>
      <li>LuciferiumAddiction</li>
      <li>CorpseTorment</li>
      <li>CubeInterest</li>
      <li>CubeWithdrawal</li>
      <li>CubeComa</li>
      <li>CubeRage</li>
      <li>HeartAttack</li>
      <li MayRequire="Ludeon.RimWorld.Royalty">PsychicAmplifier</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">MechlinkImplant</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">PregnantHuman</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">PregnancyLabor</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">HemogenAmplified</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">DeathrestExhaustion</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">InterruptedDeathrest</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">HemogenCraving</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">Deathrest</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">RegenerationComa</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">PostpartumExhaustion</li>
      <li>Inhumanized</li>
    </removesHediffs>
    <disablesGenes>
      <li MayRequire="Ludeon.RimWorld.Biotech">Hemogenic</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">Deathrest</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">Furskin</li>
    </disablesGenes>
    <abilityWhitelist>
      <li>GhoulFrenzy</li>
      <li>CorrosiveSpray</li>
      <li>MetalbloodInjection</li>
    </abilityWhitelist>
    <producesBioferrite>true</producesBioferrite>
    <deathLetter>One of your ghouls has died.</deathLetter>
    <deathLetterExtra>Unlike regular colonists, ghouls do not need to be buried and their death will not upset others. You may choose to bury them should you wish.</deathLetterExtra>
    <!-- Sounds -->
    <soundCall>Pawn_Ghoul_Call</soundCall>
    <soundAttack>Pawn_Ghoul_Attack</soundAttack>
    <soundWounded>Pawn_Ghoul_Pain</soundWounded>
    <soundDeath>Pawn_Ghoul_Killed</soundDeath>
    <!-- Rendering -->
    <skinColorOverride>(0.58, 0.62, 0.55)</skinColorOverride>
    <hairColorOverride>(0.65, 0.65, 0.65)</hairColorOverride>
    <bodyTypeGraphicPaths>
      <Male>Things/Pawn/Ghoul/Bodies/Ghoulskin_Male</Male>
      <Female>Things/Pawn/Ghoul/Bodies/Ghoulskin_Female</Female>
      <Hulk>Things/Pawn/Ghoul/Bodies/Ghoulskin_Hulk</Hulk>
      <Fat>Things/Pawn/Ghoul/Bodies/Ghoulskin_Fat</Fat>
      <Thin>Things/Pawn/Ghoul/Bodies/Ghoulskin_Thin</Thin>
    </bodyTypeGraphicPaths>
    <forcedHeadTypes>
      <li>Ghoul_Normal</li>
      <li>Ghoul_Heavy</li>
      <li>Ghoul_Narrow</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">Ghoul_Wide</li>
    </forcedHeadTypes>
    <hairTagFilter>
      <whitelist>true</whitelist>
      <tags>
        <li>Ghoul</li>
      </tags>
    </hairTagFilter>
    <beardTagFilter>
      <whitelist>true</whitelist>
      <tags>
        <li>NoBeard</li>
      </tags>
    </beardTagFilter>

    <tools>
      <li>
        <label>teeth</label>
        <capacities>
          <li>Bite</li>
        </capacities>
        <power>8.2</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>Teeth</linkedBodyPartsGroup>
        <chanceFactor>0.5</chanceFactor>
        <soundMeleeHit>Pawn_Melee_HumanBite_Hit</soundMeleeHit>
        <soundMeleeMiss>Pawn_Melee_HumanBite_Miss</soundMeleeMiss>
      </li>
      <li>
        <label>left claw</label>
        <labelNoLocation>claw</labelNoLocation>
        <capacities>
          <li>Scratch</li>
        </capacities>
        <power>7.0</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>LeftHand</linkedBodyPartsGroup>
        <chanceFactor>1.5</chanceFactor>
        <alwaysTreatAsWeapon>true</alwaysTreatAsWeapon>
        <soundMeleeHit>Pawn_Ghoul_Scratch</soundMeleeHit>
        <soundMeleeMiss>Pawn_Melee_SmallScratch_Miss</soundMeleeMiss>
      </li>
      <li>
        <label>right claw</label>
        <labelNoLocation>claw</labelNoLocation>
        <capacities>
          <li>Scratch</li>
        </capacities>
        <power>7.0</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>RightHand</linkedBodyPartsGroup>
        <chanceFactor>1.5</chanceFactor>
        <alwaysTreatAsWeapon>true</alwaysTreatAsWeapon>
        <soundMeleeHit>Pawn_Ghoul_Scratch</soundMeleeHit>
        <soundMeleeMiss>Pawn_Melee_SmallScratch_Miss</soundMeleeMiss>
      </li>
    </tools>

    <hediffGivers>
      <li Class="HediffGiver_MeatHunger">
        <hediff>MeatHunger</hediff>
      </li>
    </hediffGivers>
  </MutantDef>

  <MutantDef>
    <defName>AwokenCorpse</defName>
    <label>awoken corpse</label>
    <hediff>AwokenCorpse</hediff>
    <thinkTree>AwokenCorpse</thinkTree>
    <thinkTreeConstant>AwokenCorpseConstant</thinkTreeConstant>
    <useCorpseGraphics>true</useCorpseGraphics>
    <deathOnDownedChance>0</deathOnDownedChance>
    <canBeCaptured>false</canBeCaptured>
    <canBleed>false</canBleed>
    <entitledToMedicalCare>false</entitledToMedicalCare>
    <isImmuneToInfections>true</isImmuneToInfections>
    <showInScenarioEditor>false</showInScenarioEditor>
    <restoreLegs>true</restoreLegs>
    <defaultFaction>Entities</defaultFaction>
    <woundColor>(0.3, 0.3, 0.0, 1.0)</woundColor>
    <anomalyKnowledgeOffset>0.4</anomalyKnowledgeOffset>
    <knowledgeCategory>Basic</knowledgeCategory>
    <producesBioferrite>true</producesBioferrite>
    <showLabel>true</showLabel>
    <removeIdeo>false</removeIdeo>
    <psychicShockUntargetable>true</psychicShockUntargetable>
    <canOpenAnyDoor>true</canOpenAnyDoor>
    <givesHediffs>
      <li>
        <def>RapidRegeneration</def>
      </li>
    </givesHediffs>
    <removesHediffs>
      <li>CryptosleepSickness</li>
      <li>LuciferiumHigh</li>
      <li>LuciferiumAddiction</li>
      <li>Scaria</li>
      <li>CorpseTorment</li>
      <li>CubeInterest</li>
      <li>CubeWithdrawal</li>
      <li>CubeComa</li>
      <li>CubeRage</li>
      <li>HeartAttack</li>
      <li MayRequire="Ludeon.RimWorld.Royalty">PsychicAmplifier</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">MechlinkImplant</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">PregnantHuman</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">PregnancyLabor</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">HemogenAmplified</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">DeathrestExhaustion</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">InterruptedDeathrest</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">HemogenCraving</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">Deathrest</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">RegenerationComa</li>
      <li MayRequire="Ludeon.RimWorld.Biotech">PostpartumExhaustion</li>
    </removesHediffs>
    <abilities>
      <li>UnnaturalCorpseSkip</li>
    </abilities>
  </MutantDef>

</Defs>