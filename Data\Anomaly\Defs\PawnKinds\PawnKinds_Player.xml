﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <PawnKindDef ParentName="BasePlayerPawnKind">
    <defName>Researcher</defName>
    <label>researcher</label>
    <defaultFactionType>ResearchExpedition</defaultFactionType>
    <chemicalAddictionChance>0.06</chemicalAddictionChance>
    <apparelRequired>
      <li>Apparel_LabCoat</li>
    </apparelRequired>
    <apparelColor>(250,250,250)</apparelColor>
    <apparelTags>
      <li>IndustrialBasic</li>
    </apparelTags>
    <apparelAllowHeadgearChance>0</apparelAllowHeadgearChance>
    <apparelMoney>750~1200</apparelMoney>
    <backstoryCryptosleepCommonality>1</backstoryCryptosleepCommonality>
    <techHediffsChance>0.03</techHediffsChance>
    <techHediffsMoney>50~800</techHediffsMoney>
    <techHediffsTags>
      <li>Poor</li>
      <li>Simple</li>
      <li>ImplantEmpireCommon</li>
    </techHediffsTags>
    <techHediffsDisallowTags>
      <li>PainCauser</li>
    </techHediffsDisallowTags>
    <initialResistanceRange>13~21</initialResistanceRange>
  </PawnKindDef>
</Defs>