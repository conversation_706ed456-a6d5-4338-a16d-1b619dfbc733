<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <GeneDef>
    <defName>FireSpew</defName>
    <label>fire spew</label>
    <description>Carriers are able to spew flammable bile generated by a special organ in their neck. The bile sticks to anything in a small area and can ignite people, objects, and the ground.</description>
    <iconPath>UI/Icons/Genes/Gene_Firespew</iconPath>
    <displayCategory>Ability</displayCategory>
    <marketValueFactor>1.5</marketValueFactor>
    <abilities>
      <li>FireSpew</li>
    </abilities>
    <descriptionHyperlinks>
      <AbilityDef>FireSpew</AbilityDef>
    </descriptionHyperlinks>
    <biostatMet>-2</biostatMet>
    <symbolPack>
      <wholeNameSymbols>
        <li><symbol>burner</symbol></li>
        <li><symbol>igniter</symbol></li>
      </wholeNameSymbols>
      <prefixSymbols>
        <li><symbol>fire</symbol></li>
        <li><symbol>flame</symbol></li>
        <li><symbol>pyre</symbol></li>
        <li><symbol>pyro</symbol></li>
        <li><symbol>spark</symbol></li>
        <li><symbol>ember</symbol></li>
        <li><symbol>spew</symbol></li>
        <li><symbol>burn</symbol></li>
        <li><symbol>incendi</symbol></li>
        <li><symbol>cinder</symbol></li>
        <li><symbol>ash</symbol></li>
      </prefixSymbols>
      <suffixSymbols>
        <li><symbol>light</symbol></li>
        <li><symbol>burner</symbol></li>
        <li><symbol>pyro</symbol></li>
        <li><symbol>ash</symbol></li>
      </suffixSymbols>
    </symbolPack>
  </GeneDef>

  <GeneDef>
    <defName>FoamSpray</defName>
    <label>foam spray</label>
    <description>Carriers grow glands in the neck that generate and store a fire-retardant foam. They can spew this foam over an area to extinguish fires.</description>
    <iconPath>UI/Icons/Genes/Gene_FoamSpray</iconPath>
    <displayCategory>Ability</displayCategory>
    <abilities>
      <li>FoamSpray</li>
    </abilities>
    <descriptionHyperlinks>
      <AbilityDef>FoamSpray</AbilityDef>
    </descriptionHyperlinks>
    <biostatMet>-2</biostatMet>
    <symbolPack>
      <wholeNameSymbols>
        <li><symbol>foamer</symbol></li>
      </wholeNameSymbols>
      <prefixSymbols>
        <li><symbol>foam</symbol></li>
        <li><symbol>spit</symbol></li>
        <li><symbol>spray</symbol></li>
        <li><symbol>suds</symbol></li>
        <li><symbol>cream</symbol></li>
        <li><symbol>froth</symbol></li>
      </prefixSymbols>
      <suffixSymbols>
        <li><symbol>foamer</symbol></li>
        <li><symbol>sud</symbol></li>
      </suffixSymbols>
    </symbolPack>
  </GeneDef>

  <GeneDef>
    <defName>LongjumpLegs</defName>
    <label>longjump legs</label>
    <description>Carriers have special hemogen-powered muscle fibers in their legs which allow them to jump great distances.</description>
    <iconPath>UI/Icons/Genes/Gene_LongJumpLegs</iconPath>
    <prerequisite>Hemogenic</prerequisite>
    <displayCategory>Hemogen</displayCategory>
    <abilities>
      <li>Longjump</li>
    </abilities>
    <descriptionHyperlinks>
      <AbilityDef>Longjump</AbilityDef>
    </descriptionHyperlinks>
    <biostatMet>-2</biostatMet>
    <symbolPack>
      <wholeNameSymbols>
        <li><symbol>jumper</symbol></li>
        <li><symbol>leaper</symbol></li>
        <li><symbol>bouncer</symbol></li>
      </wholeNameSymbols>
      <prefixSymbols>
        <li><symbol>jump</symbol></li>
        <li><symbol>leap</symbol></li>
        <li><symbol>vault</symbol></li>
        <li><symbol>long</symbol></li>
        <li><symbol>fly</symbol></li>
        <li><symbol>hop</symbol></li>
      </prefixSymbols>
    </symbolPack>
  </GeneDef>

  <GeneDef>
    <defName>AnimalWarcall</defName>
    <label>animal warcall</label>
    <description>Carriers of this gene can perform an animal warcall, using a powerful bellow and psychic connection to call an animal to fight for them.</description>
    <iconPath>UI/Icons/Genes/Gene_AnimalWarcall</iconPath>
    <displayCategory>Ability</displayCategory>
    <abilities>
      <li>AnimalWarcall</li>
    </abilities>
    <descriptionHyperlinks>
      <AbilityDef>AnimalWarcall</AbilityDef>
    </descriptionHyperlinks>
    <biostatMet>-3</biostatMet>
    <biostatCpx>1</biostatCpx>
    <symbolPack>
      <wholeNameSymbols>
        <li><symbol>warcaller</symbol></li>
      </wholeNameSymbols>
      <prefixSymbols>
        <li><symbol>war</symbol></li>
        <li><symbol>animal</symbol></li>
        <li><symbol>cry</symbol></li>
      </prefixSymbols>
      <suffixSymbols>
        <li><symbol>caller</symbol></li>
        <li><symbol>crier</symbol></li>
        <li><symbol>hailer</symbol></li>
      </suffixSymbols>
    </symbolPack>
  </GeneDef>

  <GeneDef>
    <defName>Bloodfeeder</defName>
    <label>bloodfeeder</label>
    <description>Carriers of this gene have small retractable fangs and an organ on the roof of the mouth which can extract hemogen from fresh warm blood. They can bite an unresisting person, suck the blood, and gain hemogen directly.</description>
    <iconPath>UI/Icons/Genes/Gene_Bloodfeeder</iconPath>
    <geneClass>Gene_Bloodfeeder</geneClass>
    <prerequisite>Hemogenic</prerequisite>
    <displayCategory>Hemogen</displayCategory>
    <deathHistoryEvent>BloodfeederDied</deathHistoryEvent>
    <abilities>
      <li>Bloodfeed</li>
    </abilities>
    <descriptionHyperlinks>
      <AbilityDef>Bloodfeed</AbilityDef>
    </descriptionHyperlinks>
    <biostatMet>-1</biostatMet>
    <minAgeActive>3</minAgeActive>
    <symbolPack>
      <wholeNameSymbols>
        <li><symbol>bloodfeeder</symbol></li>
      </wholeNameSymbols>
      <prefixSymbols>
        <li><symbol>hemo</symbol></li>
        <li><symbol>blood</symbol></li>
        <li><symbol>vamp</symbol></li>
        <li><symbol>fang</symbol></li>
      </prefixSymbols>
      <suffixSymbols>
        <li><symbol>fang</symbol></li>
        <li><symbol>eater</symbol></li>
        <li><symbol>ubus</symbol></li>
      </suffixSymbols>
    </symbolPack>
  </GeneDef>

  <GeneDef>
    <defName>Coagulate</defName>
    <label>coagulate</label>
    <labelShortAdj>coagulator</labelShortAdj>
    <description>Carriers of this gene have special glands on their hands and wrists, as well as a unique salivary compound that they can use to rapidly tend wounds.</description>
    <iconPath>UI/Icons/Genes/Gene_Coagulate</iconPath>
    <prerequisite>Hemogenic</prerequisite>
    <displayCategory>Hemogen</displayCategory>
    <abilities>
      <li>Coagulate</li>
    </abilities>
    <descriptionHyperlinks>
      <AbilityDef>Coagulate</AbilityDef>
    </descriptionHyperlinks>
    <biostatMet>-1</biostatMet>
    <minAgeActive>3</minAgeActive>
    <symbolPack>
      <suffixSymbols>
        <li><symbol>life</symbol></li>
        <li><symbol>clotter</symbol></li>
        <li><symbol>tender</symbol></li>
      </suffixSymbols>
    </symbolPack>
  </GeneDef>

  <GeneDef>
    <defName>XenogermReimplanter</defName>
    <label>gene implanter</label>
    <labelShortAdj>reimplanter</labelShortAdj>
    <description>Carriers of this gene can implant a copy of their xenogerm into another person through a somewhat gross-looking injector organ. Their own genetic material will then regrow very slowly. If they implant while their genes are regrowing, they will die. Germline genes will be unaffected.</description>
    <iconPath>UI/Icons/Genes/Gene_XenogermReimplanter</iconPath>
    <displayCategory>Archite</displayCategory>
    <abilities>
      <li>ReimplantXenogerm</li>
    </abilities>
    <descriptionHyperlinks>
      <AbilityDef>ReimplantXenogerm</AbilityDef>
    </descriptionHyperlinks>
    <biostatCpx>3</biostatCpx>
    <biostatArc>1</biostatArc>
  </GeneDef>

  <GeneDef>
    <defName>PiercingSpine</defName>
    <label>piercing spine</label>
    <description>Carriers grow an opening in their upper chest along with a quiver of keratin spines. Using a hemogen-powered chemical reaction, they can fire these spines at high speed at nearby targets with surprising accuracy.</description>
    <iconPath>UI/Icons/Genes/Gene_PiercingSpine</iconPath>
    <prerequisite>Hemogenic</prerequisite>
    <displayCategory>Hemogen</displayCategory>
    <marketValueFactor>1.5</marketValueFactor>
    <abilities>
      <li>PiercingSpine</li>
    </abilities>
    <descriptionHyperlinks>
      <AbilityDef>PiercingSpine</AbilityDef>
    </descriptionHyperlinks>
    <biostatMet>-1</biostatMet>
    <symbolPack>
      <wholeNameSymbols>
        <li><symbol>spiner</symbol></li>
      </wholeNameSymbols>
      <prefixSymbols>
        <li><symbol>spine</symbol></li>
        <li><symbol>thorn</symbol></li>
        <li><symbol>bolt</symbol></li>
      </prefixSymbols>
    </symbolPack>
  </GeneDef>

  <GeneDef>
    <defName>AcidSpray</defName>
    <label>acid spray</label>
    <description>Carriers grow glands in the neck that generate and store a sticky acid substance, along with acid-tolerant tissues in the mouth. They can spew this acid over an area, where it will stick to enemies and burn them.</description>
    <iconPath>UI/Icons/Genes/Gene_AcidSpray</iconPath>
    <displayCategory>Ability</displayCategory>
    <marketValueFactor>1.5</marketValueFactor>
    <abilities>
      <li>AcidSpray</li>
    </abilities>
    <descriptionHyperlinks>
      <AbilityDef>AcidSpray</AbilityDef>
    </descriptionHyperlinks>
    <biostatMet>-2</biostatMet>
    <symbolPack>
      <prefixSymbols>
        <li><symbol>acid</symbol></li>
        <li><symbol>acer</symbol></li>
        <li><symbol>acri</symbol></li>
        <li><symbol>oxo</symbol></li>
      </prefixSymbols>
      <suffixSymbols>
        <li><symbol>sprayer</symbol></li>
        <li><symbol>spitter</symbol></li>
      </suffixSymbols>
    </symbolPack>
  </GeneDef>

 <!--
  <GeneDef>
    <defName>Resurrect</defName>
    <label>resurrect</label>
    <description>Carriers have a reserve of unique resurrector archites which can infuse a corpse, rebuild degenerated tissue and kickstart the vital processes. This brings the dead back to life - though possibly with health issues. This can only be done very rarely, and comes at a steep hemogen cost.</description>
    <iconPath>UI/Icons/Genes/Gene_Resurrect</iconPath>
    <prerequisite>Hemogenic</prerequisite>
    <displayCategory>Archite</displayCategory>
    <marketValueFactor>1.5</marketValueFactor>
    <abilities>
      <li>Resurrect</li>
    </abilities>
    <descriptionHyperlinks>
      <AbilityDef>Resurrect</AbilityDef>
    </descriptionHyperlinks>
    <biostatCpx>4</biostatCpx>
    <biostatArc>1</biostatArc>
    <minAgeActive>3</minAgeActive>
    <symbolPack>
      <wholeNameSymbols>
        <li><symbol>resurrector</symbol></li>
      </wholeNameSymbols>
      <prefixSymbols>
        <li><symbol>life</symbol></li>
      </prefixSymbols>
    </symbolPack>
  </GeneDef>
-->

</Defs>