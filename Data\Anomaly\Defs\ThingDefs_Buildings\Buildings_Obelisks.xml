﻿<?xml version="1.0" encoding="utf-8"?>

<Defs>
  
  <ThingDef Name="WarpedObelisk" ParentName="BuildingBase" Abstract="True">
    <description>A large metallic pillar that emanates a putrid psychic energy. The pillar hums ominously and appears to be increasing in activity. It's not clear what will happen when it reaches its full capacity.\n\nYou can send colonists to suppress the obelisk to prevent it from activating. You can also mark the obelisk for study to try to learn its purpose and perhaps make use of it.\n\nYou can attack the obelisk to destroy it, but doing so may unleash unnatural and dangerous phenomena.</description>
    <tickerType>Normal</tickerType>
    <size>(3,3)</size>
    <drawerType>RealtimeOnly</drawerType>
    <fillPercent>1</fillPercent>
    <graphicData>
      <graphicClass>Graphic_ActivityMask</graphicClass>
      <shaderType>CutoutComplexBlend</shaderType>
      <drawSize>(5,5)</drawSize>
      <addTopAltitudeBias>true</addTopAltitudeBias>
      <drawOffset>(0,0,0.3)</drawOffset>
      <colorTwo>(226,53,33)</colorTwo>
      <shadowData>
        <volume>(2.5, 5, 2.5)</volume>
        <offset>(0, 0, -0.3)</offset>
      </shadowData>
    </graphicData>
    <altitudeLayer>Building</altitudeLayer>
    <passability>Impassable</passability>
    <building>
      <paintable>false</paintable>
      <deconstructible>false</deconstructible>
      <claimable>false</claimable>
      <soundMeleeHitOverride>ObeliskDamaged</soundMeleeHitOverride>
    </building>
    <killedLeavings>
      <Shard>1</Shard>
    </killedLeavings>
    <statBases>
      <MaxHitPoints>3000</MaxHitPoints>
      <Flammability>0</Flammability>
      <MeditationFocusStrength>0.24</MeditationFocusStrength>
    </statBases>
    <comps>
      <li Class="CompProperties_Activity">
        <startingRange>0.2~0.4</startingRange>
        <changePerDayBase>0.2</changePerDayBase>
        <changePerDamage>0.001</changePerDamage>
        <warning>0.9</warning>
        <workerClass>ObeliskActivityWorker</workerClass>
        <activityResearchFactorCurve>
          <points>
            <li>0, 0.5</li>
            <li>0.5, 1</li>
            <li>0.99, 2</li>
          </points>
      </activityResearchFactorCurve>
      </li>
      <li Class="CompProperties_Studiable">
        <frequencyTicks>120000</frequencyTicks>
        <knowledgeCategory>Advanced</knowledgeCategory>
        <anomalyKnowledge>2</anomalyKnowledge>
        <minMonolithLevelForStudy>1</minMonolithLevelForStudy>
        <studyEnabledByDefault>false</studyEnabledByDefault>
        <showToggleGizmo>true</showToggleGizmo>
        <canBeActivityDeactivated>true</canBeActivityDeactivated>
      </li>
      <li Class="CompProperties_MeditationFocus">
        <statDef>MeditationFocusStrength</statDef>
        <focusTypes>
          <li>Void</li>
        </focusTypes>
      </li>
    </comps>
    <inspectorTabs>
      <li>ITab_StudyNotes</li>
    </inspectorTabs>
  </ThingDef>
  
  <ThingDef ParentName="WarpedObelisk">
    <defName>WarpedObelisk_Mutator</defName>
    <label>twisted obelisk</label>
    <graphicData>
      <texPath>Things/Building/Obelisk_Twisted/TwistedObelisk</texPath>
    </graphicData>
    <comps>
      <li Class="CompProperties_Obelisk">
        <compClass>CompObelisk_Mutator</compClass>
        <interactionEffectCooldownDays>5</interactionEffectCooldownDays>
      </li>
      <li Class="CompProperties_StudyUnlocks">
        <studyNotes>
          <li>
            <threshold>2</threshold>
            <label>Obelisk study progress</label> <!-- text in this is the same as other obelisk so player can't tell which it is -->
            <text>Investigation of the obelisk has revealed some information. It is an archotechnological device that seems to interact with organic creatures at a distance. It is probably a piece of a much larger archotech structure that was destroyed or broken apart long ago. The machinery at its core is inhumanly complex and frighteningly powerful. Its purpose and method of action remain totally mysterious.\n\nIf not suppressed, its activity level will increase over time. You can't tell what will happen when the device fully activates.</text>
          </li>
          <li>
            <threshold>10</threshold>
            <label>Obelisk study progress</label>
            <text>You've learned more about the obelisk. It is an archotech machine that transforms flesh creatures into different forms. The device is damaged, so the new forms would be chaotic and unstable - a person or animal so transformed would become something totally unrecognizable.\n\nThe obelisk's energy level rises unless suppressed. If it fully activates, it could transform an endless number of creatures, creating a nightmarish horde of flesh mutants.\n\n{PAWN_nameDef} thinks that it may be possible to safely shut down the obelisk but this will need significantly more study.\n\nYou can now intentionally provoke a response from the twisted obelisk.</text>
          </li>
          <li>
            <threshold>30</threshold>
            <label>Obelisk study complete</label>
            <text>{PAWN_nameDef} has completed {PAWN_possessive} investigation of the twisted obelisk. {PAWN_pronoun} has discovered a method for safely deactivating the obelisk using shards of archotechnology.\n\nYou can no longer study the obelisk after deactivating it.</text>
          </li>
        </studyNotes>
      </li>
      <li Class="CompProperties_ObeliskTriggerInteractor">
        <activeTicks>1</activeTicks>
        <ticksToActivate>180</ticksToActivate>
        <cooldownTicks>300000</cooldownTicks> <!-- 5 days -->
        <onCooldownString>Mutation cooldown</onCooldownString>
        <activateTexPath>UI/Commands/TriggerMutatorObelisk</activateTexPath>
        <activateLabelString>Mutate...</activateLabelString>
        <activateDescString>Induce rapid cellular growth in a person causing them to grow a tentacle or fleshmass organ.</activateDescString>
        <guiLabelString>Choose who should do this</guiLabelString>
        <jobString>Trigger mutation</jobString>
        <activatingStringPending>triggering obelisk mutation</activatingStringPending>
        <activatingString>triggering obelisk mutation: {1}s</activatingString>
        <messageCompletedString>{PAWN_nameDef} has successfully triggered obelisk mutation.</messageCompletedString>
        <messageCooldownEnded>The twisted obelisk is ready to use again.</messageCooldownEnded>
        <showMustBeActivatedByColonist>false</showMustBeActivatedByColonist>
        <targetingParameters>
          <canTargetBuildings>false</canTargetBuildings>
          <canTargetAnimals>false</canTargetAnimals>
          <canTargetMechs>false</canTargetMechs>
          <onlyTargetControlledPawns>true</onlyTargetControlledPawns>
        </targetingParameters>
      </li>
      <li Class="CompProperties_ObeliskDeactivationInteractor">
        <shardsRequired>2</shardsRequired>
        <activeTicks>1</activeTicks>
        <ticksToActivate>180</ticksToActivate>
        <activateTexPath>UI/Commands/DisableObelisk</activateTexPath>
        <activateLabelString>Deactivate...</activateLabelString>
        <activateDescString>Permanently and safely disable the twisted obelisk. This will prevent it from activating and being studied further.\n\nRequires 2 shards.</activateDescString>
        <guiLabelString>Choose who should do this</guiLabelString>
        <jobString>Deactivate obelisk</jobString>
        <activatingStringPending>deactivating obelisk</activatingStringPending>
        <activatingString>deactivating obelisk: {1}s</activatingString>
        <messageCompletedString>{PAWN_nameDef} has successfully deactivated the obelisk.</messageCompletedString>
        <showMustBeActivatedByColonist>false</showMustBeActivatedByColonist>
        <targetingParameters>
          <canTargetBuildings>false</canTargetBuildings>
          <canTargetAnimals>false</canTargetAnimals>
          <canTargetMechs>false</canTargetMechs>
          <onlyTargetControlledPawns>true</onlyTargetControlledPawns>
        </targetingParameters>
      </li>
    </comps>
  </ThingDef>
  
  <ThingDef ParentName="WarpedObelisk">
    <defName>WarpedObelisk_Duplicator</defName>
    <label>corrupted obelisk</label>
    <graphicData>
      <texPath>Things/Building/Obelisk_Corrupted/CorruptedObelisk</texPath>
    </graphicData>
    <comps>
      <li Class="CompProperties_Obelisk">
        <compClass>CompObelisk_Duplicator</compClass>
        <interactionEffectCooldownDays>40</interactionEffectCooldownDays>
      </li>
      <li Class="CompProperties_StudyUnlocks">
        <studyNotes>
          <li>
            <threshold>2</threshold>
            <label>Obelisk study progress</label> <!-- text in this is the same as other obelisk -->
            <text>Investigation of the obelisk has revealed some information. It is an archotechnological device that seems to interact with organic creatures at a distance. It is probably a piece of a much larger archotech structure that was destroyed or broken apart long ago. The machinery at its core is inhumanly complex and frighteningly powerful. Its purpose and method of action remain totally mysterious.\n\nIf not suppressed, its activity level will increase over time. You can't tell what will happen when the device fully activates.</text>
          </li>
          <li>
            <threshold>10</threshold>
            <label>Obelisk study progress</label>
            <text>You've learned more about the obelisk. The device has the power to duplicate intelligent creatures. In theory, it can produce duplicates without limit.\n\nThe obelisk appears damaged and unstable, so its energy level rises unless suppressed. If it were to fully activate, it would go into a duplication loop, chaotically pumping out a large number of flawed human duplicates.\n\n{PAWN_nameDef} thinks that it may be possible to safely shut down the obelisk but this will need significantly more study.\n\nYou can now intentionally provoke a response from the corrupted obelisk.</text>
          </li>
          <li>
            <threshold>30</threshold>
            <label>Obelisk study complete</label>
            <text>{PAWN_nameDef} has completed {PAWN_possessive} investigation of the corrupted obelisk. {PAWN_pronoun} has discovered a method for safely deactivating the obelisk using shards of archotechnology.\n\nYou can no longer study the obelisk after deactivating it.</text>
          </li>
        </studyNotes>
      </li>
      <li Class="CompProperties_ObeliskTriggerInteractor">
        <activeTicks>1</activeTicks>
        <ticksToActivate>180</ticksToActivate>
        <cooldownTicks>1800000</cooldownTicks> <!-- 30 days -->
        <onCooldownString>Duplication cooldown</onCooldownString>
        <activateTexPath>UI/Commands/TriggerDuplicatorObelisk</activateTexPath>
        <activateLabelString>Duplicate...</activateLabelString>
        <activateDescString>Provoke the obelisk into duplicating a willing person.</activateDescString>
        <guiLabelString>Choose who should do this</guiLabelString>
        <jobString>Trigger duplication</jobString>
        <activatingStringPending>triggering obelisk duplication</activatingStringPending>
        <activatingString>triggering obelisk duplication: {1}s</activatingString>
        <messageCooldownEnded>The corrupted obelisk is ready to use again.</messageCooldownEnded>
        <showMustBeActivatedByColonist>false</showMustBeActivatedByColonist>
        <targetingParameters>
          <canTargetBuildings>false</canTargetBuildings>
          <canTargetAnimals>false</canTargetAnimals>
          <canTargetMechs>false</canTargetMechs>
          <onlyTargetControlledPawns>true</onlyTargetControlledPawns>
        </targetingParameters>
      </li>
      <li Class="CompProperties_ObeliskDeactivationInteractor">
        <shardsRequired>2</shardsRequired>
        <activeTicks>1</activeTicks>
        <ticksToActivate>180</ticksToActivate>
        <activateTexPath>UI/Commands/DisableObelisk</activateTexPath>
        <activateLabelString>Deactivate...</activateLabelString>
        <activateDescString>Permanently and safely disable the corrupted obelisk. This will prevent it from activating, and from being studied further.\n\nRequires 2 shards.</activateDescString>
        <guiLabelString>Choose who should do this</guiLabelString>
        <jobString>Deactivate obelisk</jobString>
        <activatingStringPending>deactivating obelisk</activatingStringPending>
        <activatingString>deactivating obelisk: {1}s</activatingString>
        <messageCompletedString>{PAWN_nameDef} has successfully deactivated the obelisk.</messageCompletedString>
        <showMustBeActivatedByColonist>false</showMustBeActivatedByColonist>
        <targetingParameters>
          <canTargetBuildings>false</canTargetBuildings>
          <canTargetAnimals>false</canTargetAnimals>
          <canTargetMechs>false</canTargetMechs>
          <onlyTargetControlledPawns>true</onlyTargetControlledPawns>
        </targetingParameters>
      </li>
    </comps>
  </ThingDef>
  
  <ThingDef ParentName="WarpedObelisk">
    <defName>WarpedObelisk_Abductor</defName>
    <label>warped obelisk</label>
    <graphicData>
      <texPath>Things/Building/Obelisk_Warped/WarpedObelisk</texPath>
    </graphicData>
    <comps>
      <li Class="CompProperties_Obelisk">
        <compClass>CompObelisk_Abductor</compClass>
        <interactionEffectCooldownDays>0</interactionEffectCooldownDays>
      </li>
      <li Class="CompProperties_StudyUnlocks">
        <studyNotes>
          <li>
            <threshold>2</threshold>
            <label>Obelisk study progress</label> <!-- text in this is the same as other obelisk -->
            <text>Investigation of the obelisk has revealed some information. It is an archotechnological device that seems to interact with organic creatures at a distance. It is probably a piece of a much larger archotech structure that was destroyed or broken apart long ago. The machinery at its core is inhumanly complex and frighteningly powerful. Its purpose and method of action remain totally mysterious.\n\nIf not suppressed, its activity level will increase over time. You can't tell what will happen when the device fully activates.</text>
          </li>
          <li>
            <threshold>10</threshold>
            <label>Obelisk study complete</label>
            <text>You've completed your investigation of the obelisk. It creates a bridge to a point in a spacetime void separated from ours along usually inaccessible spatial dimensions.\n\nThe void plane is filled with a gray labyrinthine structure of astronomical scale. Someone trapped there could return by finding the obelisk's mirror version inside the gray rooms and hallways.\n\nThe gray structures seem to have been created by an archotech superintelligence, but there's no way to know their purpose, age, or spatial limits.\n\nThe obelisk is damaged and unstable, so its energy level rises unless suppressed. If it were to fully activate, it could begin to abduct individuals at random.\n\nYou can now intentionally provoke a response from the warped obelisk.</text>
          </li>
        </studyNotes>
      </li>
      <li Class="CompProperties_ObeliskTriggerInteractor">
        <activeTicks>1</activeTicks>
        <ticksToActivate>180</ticksToActivate>
        <cooldownTicks>900000</cooldownTicks> <!-- 15 days -->
        <onCooldownString>Teleportation cooldown</onCooldownString>
        <activateTexPath>UI/Commands/TriggerAbductorObelisk</activateTexPath>
        <activateLabelString>Teleport...</activateLabelString>
        <activateDescString>Transport a willing person to a distant spatial dimension. It may be difficult for them to return.</activateDescString>
        <guiLabelString>Choose who should do this</guiLabelString>
        <jobString>Trigger teleportation</jobString>
        <activatingStringPending>triggering obelisk teleportation</activatingStringPending>
        <activatingString>triggering obelisk teleportation: {1}s</activatingString>
        <messageCompletedString>{PAWN_nameDef} has successfully triggered obelisk teleportation.</messageCompletedString>
        <messageCooldownEnded>The warped obelisk is ready to use again.</messageCooldownEnded>
        <showMustBeActivatedByColonist>false</showMustBeActivatedByColonist>
        <targetingParameters>
          <canTargetBuildings>false</canTargetBuildings>
          <canTargetAnimals>false</canTargetAnimals>
          <canTargetMechs>false</canTargetMechs>
          <onlyTargetControlledPawns>true</onlyTargetControlledPawns>
        </targetingParameters>
      </li>
    </comps>
  </ThingDef>
  
  <ThingDef ParentName="WarpedObelisk">
    <defName>WarpedObelisk_Labyrinth</defName>
    <label>warped obelisk</label>
    <description>A large metallic pillar that emanates a psychic energy. It can be activated to return home.</description>
    <destroyable>false</destroyable>
    <useHitPoints>false</useHitPoints>
    <drawerType>MapMeshOnly</drawerType>
    <graphicData>
      <texPath>Things/Building/Obelisk_Warped/WarpedObelisk</texPath>
    </graphicData>
    <statBases>
      <Flammability>0</Flammability>
    </statBases>
    <comps Inherit="False">
      <li Class="CompProperties_ObeliskLabyrinth">
        <messageActivating>The obelisk has begun to activate.</messageActivating>
        
        <activeTicks>1</activeTicks>
        <ticksToActivate>600</ticksToActivate>
        <activateTexPath>UI/Commands/TriggerAbductorObelisk</activateTexPath>
        
        <activateLabelString>Activate...</activateLabelString>
        <activateDescString>Activate the obelisk.</activateDescString>
        <guiLabelString>Choose who should do this</guiLabelString>
        <jobString>Activate obelisk</jobString>
        <activatingStringPending>activating obelisk</activatingStringPending>
        <activatingString>activating obelisk: {1}s</activatingString>
        
        <targetingParameters>
          <canTargetBuildings>false</canTargetBuildings>
          <canTargetAnimals>false</canTargetAnimals>
          <canTargetMechs>false</canTargetMechs>
          <onlyTargetControlledPawns>true</onlyTargetControlledPawns>
        </targetingParameters>
      </li>
    </comps>
  </ThingDef>
  
</Defs>