﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingDef ParentName="MakeableDrugBase">
    <defName>Yayo</defName>
    <label>yayo</label>
    <description>A refined powdery preparation of the psychite drug. When snorted, it produces a rapid euphoric high, dramatically reduces the user's need for rest, and suppresses pain. Like all forms of psychite, it is addictive, though it is not as addictive as the cruder flake.\n\nBecause of its high cost and refined appearance, many cultures associate yayo with degenerate wealth. Whether in the throneroom or the boardroom, many hare-brained policy schemes have been developed during yayo-fueled binge parties.</description>
    <descriptionHyperlinks>
      <HediffDef>YayoHigh</HediffDef>
      <HediffDef>PsychiteTolerance</HediffDef>
      <HediffDef>PsychiteAddiction</HediffDef>
      <HediffDef>ChemicalDamageSevere</HediffDef>
    </descriptionHyperlinks>
    <graphicData>
      <texPath>Things/Item/Drug/Yayo</texPath>
      <graphicClass>Graphic_StackCount</graphicClass>
      <drawSize>0.75</drawSize>
    </graphicData>
    <rotatable>false</rotatable>
    <statBases>
      <WorkToMake>350</WorkToMake>
      <MarketValue>21</MarketValue>
      <Mass>0.05</Mass>
    </statBases>
    <techLevel>Industrial</techLevel>
    <minRewardCount>10</minRewardCount>
    <ingestible>
      <foodType>Processed</foodType>
      <joyKind>Chemical</joyKind>
      <joy>0.80</joy>
      <drugCategory>Hard</drugCategory>
      <baseIngestTicks>150</baseIngestTicks>
      <ingestSound>Ingest_Snort</ingestSound>
      <ingestHoldOffsetStanding>
        <northDefault>
          <offset>(0.21,0,0.05)</offset>
        </northDefault>
      </ingestHoldOffsetStanding>
      <ingestHoldUsesTable>false</ingestHoldUsesTable>
      <ingestCommandString>Snort {0}</ingestCommandString>
      <ingestReportString>Snorting {0}.</ingestReportString>
      <useEatingSpeedStat>false</useEatingSpeedStat>
      <outcomeDoers>
        <li Class="IngestionOutcomeDoer_GiveHediff">
          <hediffDef>YayoHigh</hediffDef>
          <severity>0.75</severity>
          <toleranceChemical>Psychite</toleranceChemical>
        </li>
        <li Class="IngestionOutcomeDoer_OffsetNeed">
          <need>Rest</need>
          <offset>0.4</offset>
          <toleranceChemical>Psychite</toleranceChemical>
        </li>
        <li Class="IngestionOutcomeDoer_GiveHediff">
          <hediffDef>PsychiteTolerance</hediffDef>
          <toleranceChemical>Psychite</toleranceChemical>
          <severity>0.040</severity>
          <divideByBodySize>true</divideByBodySize>
          <multiplyByGeneToleranceFactors>true</multiplyByGeneToleranceFactors>
        </li>
      </outcomeDoers>
    </ingestible>
    <recipeMaker>
      <researchPrerequisite>PsychiteRefining</researchPrerequisite>
      <recipeUsers>
        <li>DrugLab</li>
      </recipeUsers>
      <soundWorking>Recipe_Drug</soundWorking>
      <displayPriority>1700</displayPriority>
    </recipeMaker>
    <costList>
      <PsychoidLeaves>8</PsychoidLeaves>
    </costList>
    <comps>
      <li Class="CompProperties_Drug">
        <chemical>Psychite</chemical>
        <addictiveness>0.01</addictiveness>
        <existingAddictionSeverityOffset>0.20</existingAddictionSeverityOffset>
        <needLevelOffset>0.9</needLevelOffset>
        <isCombatEnhancingDrug>true</isCombatEnhancingDrug>
        <listOrder>100</listOrder>
        <overdoseSeverityOffset>0.18~0.35</overdoseSeverityOffset>
        <largeOverdoseChance>0.01</largeOverdoseChance>
      </li>
    </comps>
    <allowedArchonexusCount>200</allowedArchonexusCount>
  </ThingDef>

  <HediffDef>
    <defName>YayoHigh</defName>
    <label>high on yayo</label>
    <labelNoun>a yayo high</labelNoun>
    <description>Active yayo in the bloodstream. Generates an intense euphoric high.</description>
    <hediffClass>Hediff_High</hediffClass>
    <defaultLabelColor>(1,0,0.5)</defaultLabelColor>
    <scenarioCanAdd>true</scenarioCanAdd>
    <maxSeverity>1.0</maxSeverity>
    <isBad>false</isBad>
    <comps>
      <li Class="HediffCompProperties_SeverityPerDay">
        <severityPerDay>-1.5</severityPerDay>
        <showHoursToRecover>true</showHoursToRecover>
      </li>
    </comps>
      <stages>
        <li>
          <painFactor>0.5</painFactor>
          <statFactors>
            <RestFallRateFactor>0.33</RestFallRateFactor>
          </statFactors>
          <capMods>
            <li>
              <capacity>Moving</capacity>
              <offset>0.15</offset>
            </li>
          </capMods>
        </li>
      </stages>
  </HediffDef>

  <ThoughtDef>
    <defName>YayoHigh</defName>
    <workerClass>ThoughtWorker_Hediff</workerClass>
    <hediff>YayoHigh</hediff>
    <validWhileDespawned>true</validWhileDespawned>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
    <stages>
      <li>
        <label>high on yayo</label>
        <description>Feeling pumped! Let's do this!</description>
        <baseMoodEffect>35</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>
  
  <!-- Psychite addiction -->
  
  <ChemicalDef>
    <defName>Psychite</defName>
    <label>psychite</label>
    <addictionHediff>PsychiteAddiction</addictionHediff>
    <toleranceHediff>PsychiteTolerance</toleranceHediff>
    <onGeneratedAddictedToleranceChance>0.8</onGeneratedAddictedToleranceChance>
    <onGeneratedAddictedEvents>
      <li>
        <hediff>ChemicalDamageSevere</hediff>
        <chance>0.15</chance>
        <partsToAffect>
          <li>Kidney</li>
        </partsToAffect>
      </li>
    </onGeneratedAddictedEvents>
    <geneToleranceBuildupFactorResist>0.5</geneToleranceBuildupFactorResist>
    <geneToleranceBuildupFactorImmune>0</geneToleranceBuildupFactorImmune>
    <geneOverdoseChanceFactorResist>0.5</geneOverdoseChanceFactorResist>
    <geneOverdoseChanceFactorImmune>0</geneOverdoseChanceFactorImmune>
  </ChemicalDef>
  
  <NeedDef ParentName="DrugAddictionNeedBase">
    <defName>Chemical_Psychite</defName>
    <needClass>Need_Chemical</needClass>
    <label>psychite</label>
    <description>Because of a psychite addiction, this person needs to regularly consume the drug to avoid withdrawal symptoms.</description>
    <listPriority>50</listPriority>
  </NeedDef>

  <HediffDef ParentName="DrugToleranceBase">
    <defName>PsychiteTolerance</defName>
    <label>psychite tolerance</label>
    <description>A built-up tolerance to psychite. The more severe this tolerance is, the more psychite-based drugs like yayo or flake it takes to get the same effect.</description>
    <comps>
      <li Class="HediffCompProperties_SeverityPerDay">
        <severityPerDay>-0.015</severityPerDay>
      </li>
      <li Class="HediffCompProperties_DrugEffectFactor">
        <chemical>Psychite</chemical>
      </li>
    </comps>
    <hediffGivers>
      <li Class="HediffGiver_RandomDrugEffect">
        <hediff>ChemicalDamageSevere</hediff>
        <severityToMtbDaysCurve>
          <points>
            <li>(0.45, 99999)</li>
            <li>(0.5, 180)</li>
            <li>(1, 135)</li>
          </points>
        </severityToMtbDaysCurve>
        <partsToAffect>
          <li>Kidney</li>
        </partsToAffect>
      </li>
    </hediffGivers>
  </HediffDef>
  
  <HediffDef ParentName="AddictionBase">
    <defName>PsychiteAddiction</defName>
    <label>psychite addiction</label>
    <description>A chemical addiction to psychite. Long-term presence of psychite has caused neurological adaptations at the cellular level, so the brain can no longer function properly without the drug.
\nWithout regular doses of psychite from drugs like flake or yayo, withdrawal symptoms will begin. However, extended abstinence will force the brain to adapt back to its normal state, resolving the addiction.</description>
    <hediffClass>Hediff_Addiction</hediffClass>
    <causesNeed>Chemical_Psychite</causesNeed>
    <comps>
      <li Class="HediffCompProperties_SeverityPerDay">
        <severityPerDay>-0.0333</severityPerDay>
        <showDaysToRecover>true</showDaysToRecover>
      </li>
    </comps>
    <stages>
      <li>
      </li>
      <li>
        <label>withdrawal</label>
        <socialFightChanceFactor>3.0</socialFightChanceFactor>
        <statOffsets>
          <RestFallRateFactor>0.3</RestFallRateFactor>
        </statOffsets>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.20</offset>
          </li>
          <li>
            <capacity>Moving</capacity>
            <offset>-0.20</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.20</offset>
          </li>
        </capMods>
        <mentalStateGivers>
          <li>
            <mentalState>Binging_DrugExtreme</mentalState>
            <mtbDays>40</mtbDays>
          </li>
          <li>
            <mentalState>Wander_Psychotic</mentalState>
            <mtbDays>10</mtbDays>
          </li>
        </mentalStateGivers>
      </li>
    </stages>
  </HediffDef>

  <ThoughtDef>
    <defName>PsychiteWithdrawal</defName>
    <workerClass>ThoughtWorker_Hediff</workerClass>
    <hediff>PsychiteAddiction</hediff>
    <validWhileDespawned>true</validWhileDespawned>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
    <stages>
      <li>
        <visible>false</visible>
      </li>
      <li>
        <label>psychite withdrawal</label>
        <description>God I'm tired. Everything's so slow and boring. Especially me.</description>
        <baseMoodEffect>-35</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>
  
</Defs>