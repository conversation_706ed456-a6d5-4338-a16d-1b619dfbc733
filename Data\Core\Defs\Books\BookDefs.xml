<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingDef Name="BookBase" Abstract="True">
    <thingClass>Book</thingClass>
    <category>Item</category>
    <selectable>true</selectable>
    <rotatable>True</rotatable>
    <altitudeLayer>Item</altitudeLayer>
    <tickerType>Rare</tickerType>
    <alwaysHaulable>true</alwaysHaulable>
    <burnableByRecipe>true</burnableByRecipe>
    <useHitPoints>true</useHitPoints>
    <healthAffectsPrice>true</healthAffectsPrice>
    <drawGUIOverlay>true</drawGUIOverlay>
    <tradeNeverStack>true</tradeNeverStack>
    <neverMultiSelect>true</neverMultiSelect>
    <stackLimit>1</stackLimit>
    <tradeNeverGenerateStacked>true</tradeNeverGenerateStacked>
    <comps>
      <li Class="CompProperties_Forbiddable"/>
      <li>
        <compClass>CompQuality</compClass>
      </li>
    </comps>
    <thingCategories>
      <li>Books</li>
    </thingCategories>
    <inspectorTabs>
      <li>ITab_Book</li>
    </inspectorTabs>
    <statBases>
      <MaxHitPoints>60</MaxHitPoints>
      <DeteriorationRate>5</DeteriorationRate>
      <Mass>0.50</Mass>
      <Flammability>1</Flammability>
      <Beauty>1</Beauty>
    </statBases>
    <thingSetMakerTags>
      <li>RewardStandardMidFreq</li>
    </thingSetMakerTags>
    <possessionCount>1</possessionCount>
  </ThingDef>

  <ThingDef ParentName="BookBase">
    <defName>TextBook</defName>
    <label>textbook</label>
    <description>A book which focuses on teaching skills.</description>
    <graphicData>
      <texPath>Things/Item/Book/Textbook/Textbook</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>0.8</drawSize>
    </graphicData>
    <uiIconPath>Things/Item/Book/Textbook/Textbook</uiIconPath>
    <statBases>
      <MarketValue>160</MarketValue>
    </statBases>
    <comps>
      <li Class="CompProperties_Book">
        <nameMaker>Namer_TextBook</nameMaker>
        <descriptionMaker>Description_SkillBook</descriptionMaker>
        <ageYearsRange>100~200</ageYearsRange>
        <openGraphic>
          <texPath>Things/Item/Book/Textbook/Textbook_Open</texPath>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>0.7</drawSize>
        </openGraphic>
        <verticalGraphic>
          <texPath>Things/Item/Book/Textbook/Textbook_Vertical</texPath>
          <graphicClass>Graphic_Multi</graphicClass>
          <addTopAltitudeBias>true</addTopAltitudeBias>
        </verticalGraphic>
        <doers>
          <li Class="BookOutcomeProperties_GainSkillExp" />
        </doers>
      </li>
    </comps>
  </ThingDef>
  
  <ThingDef ParentName="BookBase">
    <defName>Schematic</defName>
    <label>schematic</label>
    <description>A book which describes technology and methods for using it. Reading schematics can help unlock new technologies.</description>
    <graphicData>
      <texPath>Things/Item/Book/Schematic/Schematic</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>0.8</drawSize>
    </graphicData>
    <uiIconPath>Things/Item/Book/Schematic/Schematic</uiIconPath>
    <statBases>
      <MarketValue>150</MarketValue>
    </statBases>
    <comps>
      <li Class="CompProperties_Book">
        <nameMaker>Namer_Schematic</nameMaker>
        <descriptionMaker>Description_Schematic</descriptionMaker>
        <ageYearsRange>100~200</ageYearsRange>
        <openGraphic>
          <texPath>Things/Item/Book/Schematic/Schematic_Open</texPath>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>0.7</drawSize>
        </openGraphic>
        <verticalGraphic>
          <texPath>Things/Item/Book/Schematic/Schematic_Vertical</texPath>
          <graphicClass>Graphic_Multi</graphicClass>
          <addTopAltitudeBias>true</addTopAltitudeBias>
        </verticalGraphic>
        <doers>
          <li Class="BookOutcomeProperties_GainResearch">
            <tabs>
              <Main />
            </tabs>
            <exclude>
              <BasicMechtech MayRequire="Ludeon.RimWorld.Biotech"/>
              <StandardMechtech MayRequire="Ludeon.RimWorld.Biotech"/>
              <HighMechtech MayRequire="Ludeon.RimWorld.Biotech"/>
            </exclude>
          </li>
        </doers>
      </li>
    </comps>
  </ThingDef>
  
  <ThingDef ParentName="BookBase">
    <defName>Novel</defName>
    <label>novel</label>
    <description>A book containing fictional or true stories for the pleasure and edification of the reader.</description>
    <graphicData>
      <texPath>Things/Item/Book/Novel/Novel</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>0.8</drawSize>
    </graphicData>
    <uiIconPath>Things/Item/Book/Novel/Novel</uiIconPath>
    <statBases>
      <MarketValue>160</MarketValue>
    </statBases>
    <comps>
      <li Class="CompProperties_Book">
        <nameMaker>Namer_Novel</nameMaker>
        <descriptionMaker>Description_Novel</descriptionMaker>
        <ageYearsRange>100~200</ageYearsRange>
        <openGraphic>
          <texPath>Things/Item/Book/Novel/Novel_Open</texPath>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>0.7</drawSize>
        </openGraphic>
        <verticalGraphic>
          <texPath>Things/Item/Book/Novel/Novel_Vertical</texPath>
          <graphicClass>Graphic_Multi</graphicClass>
          <addTopAltitudeBias>true</addTopAltitudeBias>
        </verticalGraphic>
        <doers>
          <li Class="BookOutcomeProperties_JoyFactorModifier" />
        </doers>
      </li>
    </comps>
  </ThingDef>
  
</Defs>