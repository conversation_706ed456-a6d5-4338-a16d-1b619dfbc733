<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <StyleCategoryDef>
    <defName>Horaxian</defName>
    <label>horaxian</label>
    <iconPath>UI/StyleCategories/Horaxian</iconPath>
    <fixedIdeoOnly>true</fixedIdeoOnly>
    <thingDefStyles>
      <li MayRequire="Ludeon.RimWorld.Royalty">
        <thingDef MayRequire="Ludeon.RimWorld.Royalty">MeleeWeapon_Axe</thingDef>
        <styleDef>Horaxian_Axe</styleDef>
      </li>
      <li>
        <thingDef><PERSON><PERSON><PERSON><PERSON>pon_Knife</thingDef>
        <styleDef><PERSON><PERSON><PERSON>_Knife</styleDef>
      </li>
      <li>
        <thingDef>MeleeWeapon_Gladius</thingDef>
        <styleDef>Horaxian_Gladius</styleDef>
      </li>
      <li>
        <thingDef><PERSON><PERSON><PERSON><PERSON>pon_<PERSON></thingDef>
        <styleDef><PERSON>rax<PERSON>_<PERSON></styleDef>
      </li>
      <li>
        <thingDef>MeleeWeapon_Club</thingDef>
        <styleDef>Horaxian_Club</styleDef>
      </li>
    </thingDefStyles>
  </StyleCategoryDef>

</Defs>