﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingDef ParentName="BasePawn" Name="BaseFleshbeast" Abstract="True">
    <statBases>
      <ComfyTemperatureMin>-40</ComfyTemperatureMin>
      <ComfyTemperatureMax>60</ComfyTemperatureMax>
      <Flammability>1.25</Flammability>
      <MeatAmount>70</MeatAmount>
    </statBases>
    <tradeability>None</tradeability>
    <hiddenWhileUndiscovered>true</hiddenWhileUndiscovered>
    <race>
      <thinkTreeMain>Fleshbeast</thinkTreeMain>
      <thinkTreeConstant>FleshbeastConstant</thinkTreeConstant>
      <intelligence>ToolUser</intelligence>
      <specificMeatDef>Meat_Twisted</specificMeatDef>
      <overrideShouldHaveAbilityTracker>true</overrideShouldHaveAbilityTracker>
      <disableIgniteVerb>true</disableIgniteVerb>
      <canOpenFactionlessDoors>false</canOpenFactionlessDoors>
      <needsRest>false</needsRest>
      <hasGenders>false</hasGenders>
      <foodType>None</foodType>
      <bloodDef>Filth_Blood</bloodDef>
      <bloodSmearDef>Filth_BloodSmear</bloodSmearDef>
      <soundMeleeDodge>Pawn_MeleeDodge</soundMeleeDodge>
      <trainability>None</trainability>
      <fleshType>Fleshbeast</fleshType>
      <isImmuneToInfections>true</isImmuneToInfections>
      <bleedRateFactor>0.5</bleedRateFactor>
      <hediffGiverSets>
        <li>Fleshbeast</li>
      </hediffGiverSets>
      <corpseHiddenWhileUndiscovered>true</corpseHiddenWhileUndiscovered>
    </race>
    <comps>
      <li Class="CompProperties_HoldingPlatformTarget">
        <baseEscapeIntervalMtbDays>60</baseEscapeIntervalMtbDays>
        <getsColdContainmentBonus>true</getsColdContainmentBonus>
      </li>
      <li Class="CompProperties_InspectString">
        <compClass>CompInspectStringEmergence</compClass>
        <inspectString>Emerged from {SOURCEPAWN_labelShort}.</inspectString>
      </li>
    </comps>
  </ThingDef>

  <ThingDef ParentName="BaseFleshbeast">
    <defName>Bulbfreak</defName>
    <label>bulbfreak</label>
    <description>A horrid oozing amalgamation of many smaller fleshbeasts, all controlled by a linked nervous system. It has two large claws which it uses to tear through its victims. Bulbfreaks are often smelled before they are seen, giving off an overwhelming odor of rancid meat. They are relatively slow and easy to kill, but once they die, they explode into a horde of smaller fleshbeasts.</description>
    <statBases>
      <MoveSpeed>3.8</MoveSpeed>
      <MinimumContainmentStrength>75</MinimumContainmentStrength>
      <ToxicResistance>0.5</ToxicResistance>
    </statBases>
    <uiIconPath>Things/Pawn/Fleshbeast/Bulbfreak/Bulbfreak_MenuIcon</uiIconPath>
    <race>
      <body>Bulbfreak</body>
      <baseBodySize>3.5</baseBodySize>
      <baseHealthScale>0.3</baseHealthScale>
      <lifeExpectancy>15</lifeExpectancy>
      <soundMoving>Pawn_Fleshbeast_Bulbfreak_Moving</soundMoving>
      <deathAction Class="DeathActionProperties_Divide">
        <divideBloodFilthCountRange>3~4</divideBloodFilthCountRange>
        <dividePawnCount>4</dividePawnCount>
        <dividePawnKindOptions>
          <li>Toughspike</li>
          <li>Trispike</li>
        </dividePawnKindOptions>
      </deathAction>
      <renderTree>Bulbfreak</renderTree>
      <lifeStageAges>
        <li>
          <def>EntityFullyFormed</def>
          <soundWounded>Pawn_Fleshbeast_Bulbfreak_Wounded</soundWounded>
          <soundDeath>Pawn_Fleshbeast_Bulbfreak_Death</soundDeath>
          <soundCall>Pawn_Fleshbeast_Bulbfreak_Call</soundCall>
        </li>
      </lifeStageAges>
    </race>
    <comps>
      <li Class="CompProperties_AttachPoints">
        <points>
          <li>
            <offset>(-.71, 0, .71)</offset>
            <type>PlatformRestraint0</type>
          </li>
          <li>
            <offset>(.71, 0, .71)</offset>
            <type>PlatformRestraint1</type>
          </li>
          <li>
            <offset>(.71, 0, -.71)</offset>
            <type>PlatformRestraint2</type>
          </li>
          <li>
            <offset>(-.71, 0, -.71)</offset>
            <type>PlatformRestraint3</type>
          </li>
        </points>
      </li>
      <li Class="CompProperties_Studiable">
        <frequencyTicks>120000</frequencyTicks>
        <knowledgeCategory>Advanced</knowledgeCategory>
        <anomalyKnowledge>2.5</anomalyKnowledge>
        <minMonolithLevelForStudy>1</minMonolithLevelForStudy>
        <requiresHoldingPlatform>true</requiresHoldingPlatform>
      </li>
      <li Class="CompProperties_ProducesBioferrite">
        <bioferriteDensity>2</bioferriteDensity>
      </li>
    </comps>
    <tools>
      <li>
        <label>tentacle</label>
        <capacities>
          <li>Blunt</li>
        </capacities>
        <power>7</power>
        <cooldownTime>2</cooldownTime>
        <alwaysTreatAsWeapon>true</alwaysTreatAsWeapon>
        <linkedBodyPartsGroup>FirstTentacle</linkedBodyPartsGroup>
        <soundMeleeHit>Pawn_Fleshbeast_Attack_Blunt</soundMeleeHit>
      </li>
      <li>
        <label>tentacle</label>
        <capacities>
          <li>Blunt</li>
        </capacities>
        <power>7</power>
        <cooldownTime>2</cooldownTime>
        <alwaysTreatAsWeapon>true</alwaysTreatAsWeapon>
        <linkedBodyPartsGroup>SecondTentacle</linkedBodyPartsGroup>
        <soundMeleeHit>Pawn_Fleshbeast_Attack_Blunt</soundMeleeHit>
      </li>
      <li>
        <label>tentacle</label>
        <capacities>
          <li>Blunt</li>
        </capacities>
        <power>7</power>
        <cooldownTime>2</cooldownTime>
        <alwaysTreatAsWeapon>true</alwaysTreatAsWeapon>
        <linkedBodyPartsGroup>ThirdTentacle</linkedBodyPartsGroup>
        <soundMeleeHit>Pawn_Fleshbeast_Attack_Blunt</soundMeleeHit>
      </li>
      <li>
        <label>tentacle</label>
        <capacities>
          <li>Blunt</li>
        </capacities>
        <power>7</power>
        <cooldownTime>2</cooldownTime>
        <alwaysTreatAsWeapon>true</alwaysTreatAsWeapon>
        <linkedBodyPartsGroup>FourthTentacle</linkedBodyPartsGroup>
        <soundMeleeHit>Pawn_Fleshbeast_Attack_Blunt</soundMeleeHit>
      </li>
      <li>
        <label>tentacle</label>
        <capacities>
          <li>Blunt</li>
        </capacities>
        <power>7</power>
        <cooldownTime>2</cooldownTime>
        <alwaysTreatAsWeapon>true</alwaysTreatAsWeapon>
        <linkedBodyPartsGroup>FifthTentacle</linkedBodyPartsGroup>
        <soundMeleeHit>Pawn_Fleshbeast_Attack_Blunt</soundMeleeHit>
      </li>
      <li>
        <label>head</label>
        <capacities>
          <li>Blunt</li>
        </capacities>
        <power>3</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>HeadAttackTool</linkedBodyPartsGroup>
        <ensureLinkedBodyPartsGroupAlwaysUsable>true</ensureLinkedBodyPartsGroupAlwaysUsable>
        <chanceFactor>0.2</chanceFactor>
        <soundMeleeHit>Pawn_Fleshbeast_Attack_Blunt</soundMeleeHit>
      </li>
    </tools>
  </ThingDef>

  <ThingDef ParentName="BaseFleshbeast">
    <defName>Toughspike</defName>
    <label>toughspike</label>
    <description>A fleshy man-sized creature with two spiked limbs and an armored carapace. Toughspikes throw hardened keratin spikes to take down victims from a distance.\n\nToughspike corpses will dissolve into puddles of liquefied flesh if they're not butchered quickly.</description>
    <statBases>
      <MoveSpeed>4.3</MoveSpeed>
      <MinimumContainmentStrength>50</MinimumContainmentStrength>
      <ArmorRating_Sharp>0.2</ArmorRating_Sharp>
      <ArmorRating_Blunt>0.16</ArmorRating_Blunt>
      <ToxicResistance>0.5</ToxicResistance>
    </statBases>
    <uiIconPath>Things/Pawn/Fleshbeast/Toughspike/Toughspike_MenuIcon</uiIconPath>
    <race>
      <body>Toughspike</body>
      <baseBodySize>1</baseBodySize>
      <baseHealthScale>1</baseHealthScale>
      <lifeExpectancy>15</lifeExpectancy>
      <renderTree>Toughspike</renderTree>
      <canBecomeShambler>true</canBecomeShambler>
      <lifeStageAges>
        <li>
          <def>FullyFormed_Toughspike</def>
          <soundWounded>Pawn_Fleshbeast_Toughspike_Wounded</soundWounded>
          <soundDeath>Pawn_Fleshbeast_Toughspike_Death</soundDeath>
          <soundCall>Pawn_Fleshbeast_Toughspike_Call</soundCall>
        </li>
      </lifeStageAges>
    </race>
    <comps>
      <li Class="CompProperties_AttachPoints">
        <points>
          <li>
            <offset>(-.35, 0, .5)</offset>
            <type>PlatformRestraint0</type>
          </li>
          <li>
            <offset>(.35, 0, .5)</offset>
            <type>PlatformRestraint1</type>
          </li>
          <li>
            <offset>(.3, 0, -.45)</offset>
            <type>PlatformRestraint2</type>
          </li>
          <li>
            <offset>(-.3, 0, -.45)</offset>
            <type>PlatformRestraint3</type>
          </li>
        </points>
      </li>
      <li Class="CompProperties_Studiable">
        <frequencyTicks>120000</frequencyTicks>
        <knowledgeCategory>Basic</knowledgeCategory>
        <minMonolithLevelForStudy>1</minMonolithLevelForStudy>
        <anomalyKnowledge>2</anomalyKnowledge>
        <requiresHoldingPlatform>true</requiresHoldingPlatform>
      </li>
      <li Class="CompProperties_ProducesBioferrite">
        <bioferriteDensity>2</bioferriteDensity>
      </li>
    </comps>
    <tools>
      <li>
        <label>left spike</label>
        <labelNoLocation>spike</labelNoLocation>
        <capacities>
          <li>Stab</li>
        </capacities>
        <power>7</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>LeftSpike</linkedBodyPartsGroup>
        <soundMeleeHit>Pawn_Fleshbeast_Attack_Spike</soundMeleeHit>
      </li>
      <li>
        <label>right spike</label>
        <labelNoLocation>spike</labelNoLocation>
        <capacities>
          <li>Stab</li>
        </capacities>
        <power>7</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>RightSpike</linkedBodyPartsGroup>
        <soundMeleeHit>Pawn_Fleshbeast_Attack_Spike</soundMeleeHit>
      </li>
      <li>
        <label>head</label>
        <capacities>
          <li>Blunt</li>
        </capacities>
        <power>3</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>HeadAttackTool</linkedBodyPartsGroup>
        <ensureLinkedBodyPartsGroupAlwaysUsable>true</ensureLinkedBodyPartsGroupAlwaysUsable>
        <chanceFactor>0.2</chanceFactor>
        <soundMeleeHit>Pawn_Fleshbeast_Attack_Blunt</soundMeleeHit>
      </li>
    </tools>
  </ThingDef>

  <ThingDef ParentName="BaseFleshbeast">
    <defName>Trispike</defName>
    <label>trispike</label>
    <description>A man-sized amalgamation of several smaller fleshbeasts. Each of the creature's limbs is capable of surviving independently. On death, trispikes may split into separate fingerspikes as each limb rips free and attacks.</description>
    <statBases>
      <MoveSpeed>4.3</MoveSpeed>
      <MinimumContainmentStrength>40</MinimumContainmentStrength>
      <ToxicResistance>0.5</ToxicResistance>
    </statBases>
    <uiIconPath>Things/Pawn/Fleshbeast/Trispike/Trispike_MenuIcon</uiIconPath>
    <race>
      <body>Trispike</body>
      <baseBodySize>1</baseBodySize>
      <baseHealthScale>0.3</baseHealthScale>
      <lifeExpectancy>10</lifeExpectancy>
      <renderTree>Trispike</renderTree>
      <deathAction Class="DeathActionProperties_Divide">
        <divideBloodFilthCountRange>1~2</divideBloodFilthCountRange>
        <dividePawnCount>3</dividePawnCount>
        <dividePawnKindOptions>
          <li>Fingerspike</li>
        </dividePawnKindOptions>
      </deathAction>
      <lifeStageAges>
        <li>
          <def>EntityFullyFormed</def>
          <soundWounded>Pawn_Fleshbeast_Trispike_Wounded</soundWounded>
          <soundDeath>Pawn_Fleshbeast_Trispike_Death</soundDeath>
          <soundCall>Pawn_Fleshbeast_Trispike_Call</soundCall>
        </li>
      </lifeStageAges>
    </race>
    <comps>
      <li Class="CompProperties_AttachPoints">
        <points>
          <li>
            <offset>(-.35, 0, .35)</offset>
            <type>PlatformRestraint0</type>
          </li>
          <li>
            <offset>(.35, 0, .35)</offset>
            <type>PlatformRestraint1</type>
          </li>
          <li>
            <offset>(.35, 0, -.35)</offset>
            <type>PlatformRestraint2</type>
          </li>
          <li>
            <offset>(-.35, 0, -.35)</offset>
            <type>PlatformRestraint3</type>
          </li>
        </points>
      </li>
      <li Class="CompProperties_Studiable">
        <frequencyTicks>120000</frequencyTicks>
        <knowledgeCategory>Basic</knowledgeCategory>
        <minMonolithLevelForStudy>1</minMonolithLevelForStudy>
        <anomalyKnowledge>2</anomalyKnowledge>
        <requiresHoldingPlatform>true</requiresHoldingPlatform>
      </li>
      <li Class="CompProperties_ProducesBioferrite">
        <bioferriteDensity>2</bioferriteDensity>
      </li>
    </comps>
    <tools>
      <li>
        <label>left spike</label>
        <labelNoLocation>spike</labelNoLocation>
        <capacities>
          <li>Stab</li>
        </capacities>
        <power>7</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>LeftSpike</linkedBodyPartsGroup>
        <alwaysTreatAsWeapon>true</alwaysTreatAsWeapon>
        <soundMeleeHit>Pawn_Fleshbeast_Attack_Spike</soundMeleeHit>
      </li>
      <li>
        <label>right spike</label>
        <labelNoLocation>spike</labelNoLocation>
        <capacities>
          <li>Stab</li>
        </capacities>
        <power>7</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>RightSpike</linkedBodyPartsGroup>
        <alwaysTreatAsWeapon>true</alwaysTreatAsWeapon>
        <soundMeleeHit>Pawn_Fleshbeast_Attack_Spike</soundMeleeHit>
      </li>
      <li>
        <label>top spike</label>
        <labelNoLocation>spike</labelNoLocation>
        <capacities>
          <li>Stab</li>
        </capacities>
        <power>7</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>MiddleSpike</linkedBodyPartsGroup>
        <alwaysTreatAsWeapon>true</alwaysTreatAsWeapon>
        <chanceFactor>0.5</chanceFactor>
        <soundMeleeHit>Pawn_Fleshbeast_Attack_Spike</soundMeleeHit>
      </li>
      <li>
        <label>head</label>
        <capacities>
          <li>Blunt</li>
        </capacities>
        <power>3</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>HeadAttackTool</linkedBodyPartsGroup>
        <ensureLinkedBodyPartsGroupAlwaysUsable>true</ensureLinkedBodyPartsGroupAlwaysUsable>
        <chanceFactor>0.2</chanceFactor>
        <soundMeleeHit>Pawn_Fleshbeast_Attack_Blunt</soundMeleeHit>
      </li>
    </tools>
  </ThingDef>

  <ThingDef ParentName="BaseFleshbeast">
    <defName>Fingerspike</defName>
    <label>fingerspike</label>
    <description>A fleshy dog-sized creature with a single spiked limb. Despite their short legs, fingerspikes can move remarkably fast.</description>
    <statBases>
      <MoveSpeed>5.1</MoveSpeed>
      <MinimumContainmentStrength>20</MinimumContainmentStrength>
      <ToxicResistance>0.5</ToxicResistance>
    </statBases>
    <uiIconPath>Things/Pawn/Fleshbeast/Fingerspike/Fingerspike_MenuIcon</uiIconPath>
    <race>
      <body>Fingerspike</body>
      <baseBodySize>0.6</baseBodySize>
      <baseHealthScale>0.5</baseHealthScale>
      <lifeExpectancy>10</lifeExpectancy>
      <renderTree>Fingerspike</renderTree>
      <deathAction Class="DeathActionProperties_Vanish">
        <workerClass>DeathActionWorker_Vanish</workerClass>
        <filth>Filth_TwistedFlesh</filth>
        <filthCountRange>1~3</filthCountRange>
        <meatExplosionSize>Small</meatExplosionSize>
      </deathAction>
      <lifeStageAges>
        <li>
          <def>EntityFullyFormed</def>
          <soundWounded>Pawn_Fleshbeast_Fingerspike_Wounded</soundWounded>
          <soundDeath>Pawn_Fleshbeast_Fingerspike_Death</soundDeath>
          <soundCall>Pawn_Fleshbeast_Fingerspike_Call</soundCall>
        </li>
      </lifeStageAges>
    </race>
    <comps>
      <li Class="CompProperties_AttachPoints">
        <points>
          <li>
            <offset>(-.21, 0, .21)</offset>
            <type>PlatformRestraint0</type>
          </li>
          <li>
            <offset>(.21, 0, .21)</offset>
            <type>PlatformRestraint1</type>
          </li>
          <li>
            <offset>(.21, 0, -.21)</offset>
            <type>PlatformRestraint2</type>
          </li>
          <li>
            <offset>(-.21, 0, -.21)</offset>
            <type>PlatformRestraint3</type>
          </li>
        </points>
      </li>
      <li Class="CompProperties_CanBeDormant">
        <maxDistAwakenByOther>3</maxDistAwakenByOther>
        <wakeUpDelayRange>100~200</wakeUpDelayRange>
        <jobDormancy>true</jobDormancy>
      </li>
      <li Class="CompProperties_WakeUpDormant">
        <wakeUpOnDamage>true</wakeUpOnDamage>
        <wakeUpCheckRadius>4.9</wakeUpCheckRadius>
        <wakeUpIfAnyTargetClose>true</wakeUpIfAnyTargetClose>
        <wakeUpWithDelay>true</wakeUpWithDelay>
        <activateMessageKey>MessageFingerspikeDisturbed</activateMessageKey>
        <activatePluralMessageKey>MessageFingerspikeDisturbedPlural</activatePluralMessageKey>
        <wakeUpTargetingParams>
          <canTargetBuildings>false</canTargetBuildings>
          <canTargetAnimals>false</canTargetAnimals>
          <canTargetMechs>false</canTargetMechs>
          <canTargetHumans>true</canTargetHumans>
        </wakeUpTargetingParams>
      </li>
      <li Class="CompProperties_Studiable">
        <frequencyTicks>120000</frequencyTicks>
        <knowledgeCategory>Basic</knowledgeCategory>
        <minMonolithLevelForStudy>1</minMonolithLevelForStudy>
        <anomalyKnowledge>1</anomalyKnowledge>
        <requiresHoldingPlatform>true</requiresHoldingPlatform>
      </li>
      <li Class="CompProperties_ProducesBioferrite">
        <bioferriteDensity>2</bioferriteDensity>
      </li>
    </comps>
    <tools>
      <li>
        <label>spike</label>
        <labelNoLocation>spike</labelNoLocation>
        <capacities>
          <li>Stab</li>
        </capacities>
        <power>7</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>MiddleSpike</linkedBodyPartsGroup>
        <alwaysTreatAsWeapon>true</alwaysTreatAsWeapon>
        <chanceFactor>0.5</chanceFactor>
        <soundMeleeHit>Pawn_Fleshbeast_Attack_Spike</soundMeleeHit>
      </li>
      <li>
        <label>head</label>
        <capacities>
          <li>Blunt</li>
        </capacities>
        <power>3</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>HeadAttackTool</linkedBodyPartsGroup>
        <ensureLinkedBodyPartsGroupAlwaysUsable>true</ensureLinkedBodyPartsGroupAlwaysUsable>
        <chanceFactor>0.2</chanceFactor>
        <soundMeleeHit>Pawn_Fleshbeast_Attack_Blunt</soundMeleeHit>
      </li>
    </tools>
  </ThingDef>

  <ThingDef ParentName="BaseFleshbeast">
    <defName>Dreadmeld</defName>
    <label>dreadmeld</label>
    <description>A gargantuan amalgamation of dozens of smaller fleshbeasts linked together by a loosely shared central nervous system. It uses its massive claws to dig tunnels through solid rock. Fleshbeast dreadmelds are rarely seen above ground, instead sheltering in caverns built from living flesh. They share a psychic connection with the flesh used to create their burrow.</description>
    <statBases>
      <MoveSpeed>1.5</MoveSpeed>
      <PsychicSensitivity>0.5</PsychicSensitivity>
      <ToxicResistance>0.8</ToxicResistance>
    </statBases>
    <uiIconPath>Things/Pawn/Fleshbeast/Dreadmeld/Dreadmeld_MenuIcon</uiIconPath>
    <race>
      <body>Dreadmeld</body>
      <baseBodySize>5</baseBodySize>
      <baseHealthScale>10</baseHealthScale>
      <lifeExpectancy>50</lifeExpectancy>
      <soundMoving>Pawn_Fleshbeast_Dreadmeld_Moving</soundMoving>
      <deathAction Class="DeathActionProperties_Divide">
        <divideBloodFilthCountRange>3~4</divideBloodFilthCountRange>
        <dividePawnKindAdditionalForced>
          <li>Toughspike</li>
          <li>Trispike</li>
          <li>Bulbfreak</li>
        </dividePawnKindAdditionalForced>
      </deathAction>
      <renderTree>Dreadmeld</renderTree>
      <lifeStageAges>
        <li>
          <def>EntityFullyFormed</def>
          <soundWounded>Pawn_Fleshbeast_Dreadmeld_Wounded</soundWounded>
          <soundDeath>Pawn_Fleshbeast_Dreadmeld_Death</soundDeath>
          <soundCall>Pawn_Fleshbeast_Dreadmeld_Call</soundCall>
        </li>
      </lifeStageAges>
    </race>
    <tools>
      <li>
        <label>left spike</label>
        <labelNoLocation>spike</labelNoLocation>
        <capacities>
          <li>Stab</li>
        </capacities>
        <power>14</power>
        <cooldownTime>2</cooldownTime>
        <alwaysTreatAsWeapon>true</alwaysTreatAsWeapon>
        <linkedBodyPartsGroup>LeftSpike</linkedBodyPartsGroup>
        <soundMeleeHit>Pawn_Dreadmeld_Attack_Spike</soundMeleeHit>
      </li>
      <li>
        <label>right spike</label>
        <labelNoLocation>spike</labelNoLocation>
        <capacities>
          <li>Stab</li>
        </capacities>
        <power>14</power>
        <cooldownTime>2</cooldownTime>
        <alwaysTreatAsWeapon>true</alwaysTreatAsWeapon>
        <linkedBodyPartsGroup>RightSpike</linkedBodyPartsGroup>
        <soundMeleeHit>Pawn_Dreadmeld_Attack_Spike</soundMeleeHit>
      </li>
      <li>
        <label>head</label>
        <capacities>
          <li>Blunt</li>
        </capacities>
        <power>3</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>HeadAttackTool</linkedBodyPartsGroup>
        <ensureLinkedBodyPartsGroupAlwaysUsable>true</ensureLinkedBodyPartsGroupAlwaysUsable>
        <chanceFactor>0.2</chanceFactor>
        <soundMeleeHit>Pawn_Fleshbeast_Attack_Blunt</soundMeleeHit>
      </li>
    </tools>
    <comps>
      <li>
        <compClass>CompDreadmeld</compClass>
      </li>
      <li Class="CompProperties_LetterOnRevealed">
        <label>Dreadmeld released</label>
        <text>You've discovered a fleshbeast dreadmeld! This mammoth creature seems to be made up of dozens of smaller fleshbeasts.\n\nIt appears to have some psychic connection to the fleshmass that supports this cavern. Killing it may destabilize the entire cave system.</text>
        <letterDef>ThreatBig</letterDef>
      </li>
    </comps>
    <killedLeavings>
      <Shard>3</Shard>
      <Bioferrite>30</Bioferrite>
    </killedLeavings>
  </ThingDef>

</Defs>