﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <FactionDef ParentName="FactionBase" Abstract="True" Name="AncientsBase">
    <pawnSingular>ancient</pawnSingular>
    <pawnsPlural>ancients</pawnsPlural>
    <categoryTag>Ancient</categoryTag>
    <requiredCountAtGameStart>1</requiredCountAtGameStart>
    <maxConfigurableAtWorldCreation>1</maxConfigurableAtWorldCreation>
    <configurationListOrderPriority>2000</configurationListOrderPriority>
    <displayInFactionSelection>false</displayInFactionSelection>
    <fixedName>Ancients</fixedName>
    <factionIconPath>World/WorldObjects/Expanding/Sites/DownedRefugee</factionIconPath>
    <techLevel>Spacer</techLevel>
    <backstoryFilters>
      <li>
        <categories>
          <li>Offworld</li>
        </categories>
      </li>
    </backstoryFilters>
    <hidden>true</hidden>
    <rescueesCanJoin>true</rescueesCanJoin>
    <autoFlee>false</autoFlee>
    <apparelStuffFilter>
      <thingDefs>
        <li>Synthread</li>
        <li>Hyperweave</li>
        <li>Plasteel</li>
      </thingDefs>
    </apparelStuffFilter>
    <allowedMemes>
      <li MayRequire="Ludeon.RimWorld.Ideology">Structure_Archist</li>
      <li MayRequire="Ludeon.RimWorld.Ideology">Transhumanist</li>
      <li MayRequire="Ludeon.RimWorld.Ideology">HumanPrimacy</li>
      <li MayRequire="Ludeon.RimWorld.Ideology">Loyalist</li>
    </allowedMemes>
    <requiredMemes>
      <li MayRequire="Ludeon.RimWorld.Ideology">Structure_Archist</li>
      <li MayRequire="Ludeon.RimWorld.Ideology">Transhumanist</li>
      <li MayRequire="Ludeon.RimWorld.Ideology">HumanPrimacy</li>
      <li MayRequire="Ludeon.RimWorld.Ideology">Loyalist</li>
    </requiredMemes>
  </FactionDef>

  <FactionDef ParentName="AncientsBase">
    <defName>Ancients</defName>
    <label>neutral ancients</label>
  </FactionDef>

  <FactionDef ParentName="AncientsBase">
    <defName>AncientsHostile</defName>
    <label>hostile ancients</label>
    <permanentEnemy>true</permanentEnemy>
  </FactionDef>

  <FactionDef ParentName="FactionBase">
    <defName>Mechanoid</defName>
    <label>mechanoid hive</label>
    <description>Killer machines of unknown origin. Hidden in ancient structures, under mounds of dust, or at the bottom of the ocean, mechanoids can self-maintain for thousands of years. This group of mechs seems to be unified in purpose, but not well-coordinated in action. While local scholars believe they're autonomous weapons left over from an ancient war, tribal legends describe them as the demonic servants of a sleeping god.</description>
    <pawnSingular>mechanoid</pawnSingular>
    <pawnsPlural>mechanoids</pawnsPlural>
    <requiredCountAtGameStart>1</requiredCountAtGameStart>
    <factionNameMaker>NamerFactionMechanoid</factionNameMaker>
    <factionIconPath>World/WorldObjects/Expanding/Mechanoids</factionIconPath>
    <canStageAttacks>true</canStageAttacks>
    <colorSpectrum>
      <li>(0.78, 0.79, 0.71)</li>
    </colorSpectrum>
    <raidCommonalityFromPointsCurve>
      <points>
        <li>(300,  0)</li>
        <li>(700,  1)</li>
        <li>(1400, 1.8)</li>
        <li>(2800, 2.2)</li>
        <li>(4000, 2.6)</li>
      </points>
    </raidCommonalityFromPointsCurve>
    <raidLootMaker>MechanoidRaidLootMaker</raidLootMaker>
    <raidLootValueFromPointsCurve>
      <points>
        <li>(35,     8)</li>
        <li>(100,   60)</li>
        <li>(1000, 250)</li>
        <li>(2000, 400)</li>
        <li>(4000, 500)</li>
      </points>
    </raidLootValueFromPointsCurve>
    <humanlikeFaction>false</humanlikeFaction>
    <hidden>true</hidden>
    <autoFlee>false</autoFlee>
    <canUseAvoidGrid>false</canUseAvoidGrid>
    <techLevel>Ultra</techLevel>
    <earliestRaidDays>45</earliestRaidDays>
    <permanentEnemy>true</permanentEnemy>
    <hostileToFactionlessHumanlikes>true</hostileToFactionlessHumanlikes>
    <maxPawnCostPerTotalPointsCurve>
      <points>
        <li>(400,200)</li>
        <li>(900,300)</li>
        <li>(100000,10000)</li>
      </points>
    </maxPawnCostPerTotalPointsCurve>
    <pawnGroupMakers>
      <li>
        <!-- All types-->
        <kindDef>Combat</kindDef>
        <commonality>100</commonality>
        <options>
          <Mech_Scyther>10</Mech_Scyther>
          <Mech_Pikeman>10</Mech_Pikeman>
          <Mech_Lancer>10</Mech_Lancer>
          <Mech_CentipedeBlaster>10</Mech_CentipedeBlaster>
          <Mech_Militor MayRequire="Ludeon.RimWorld.Biotech">20</Mech_Militor>
          <Mech_Centurion MayRequire="Ludeon.RimWorld.Biotech">2</Mech_Centurion>
          <Mech_Warqueen MayRequire="Ludeon.RimWorld.Biotech">1</Mech_Warqueen>
          <Mech_Apocriton MayRequire="Ludeon.RimWorld.Biotech">1</Mech_Apocriton>
        </options>
      </li>
      <li>
        <!-- Ranged only -->
        <kindDef>Combat</kindDef>
        <commonality>80</commonality>
        <options>
          <Mech_Pikeman>10</Mech_Pikeman>
          <Mech_Lancer>10</Mech_Lancer>
          <Mech_Scorcher MayRequire="Ludeon.RimWorld.Biotech">5</Mech_Scorcher>
          <Mech_Tesseron MayRequire="Ludeon.RimWorld.Biotech">5</Mech_Tesseron>
          <Mech_Legionary MayRequire="Ludeon.RimWorld.Biotech">2</Mech_Legionary>
          <Mech_Diabolus MayRequire="Ludeon.RimWorld.Biotech">1</Mech_Diabolus>
        </options>
      </li>
      <li>
        <!-- Melee only-->
        <kindDef>Combat</kindDef>
        <commonality>70</commonality>
        <options>
          <Mech_Scyther>10</Mech_Scyther>
        </options>
      </li>
      <li>
        <!-- Centipede only -->
        <kindDef>Combat</kindDef>
        <commonality>30</commonality>
        <options>
          <Mech_CentipedeBlaster>10</Mech_CentipedeBlaster>
          <Mech_CentipedeGunner>5</Mech_CentipedeGunner>
          <Mech_CentipedeBurner>5</Mech_CentipedeBurner>
        </options>
      </li>
      <li MayRequire="Ludeon.RimWorld.Biotech">
        <!-- militor only -->
        <kindDef>Combat</kindDef>
        <commonality>30</commonality>
        <options>
          <Mech_Militor>10</Mech_Militor>
        </options>
      </li>
      <li>
        <!-- breach raids -->
        <kindDef>Combat</kindDef>
        <commonality>1</commonality>
        <options>
          <Mech_Pikeman>10</Mech_Pikeman>
          <Mech_Scyther>10</Mech_Scyther>
          <Mech_Lancer>10</Mech_Lancer>
          <Mech_CentipedeBlaster>10</Mech_CentipedeBlaster>
          <Mech_Termite_Breach>1</Mech_Termite_Breach>
          <Mech_Militor MayRequire="Ludeon.RimWorld.Biotech">20</Mech_Militor>
        </options>
      </li>
    </pawnGroupMakers>
    <settlementTexturePath>World/WorldObjects/DefaultSettlement</settlementTexturePath>
    <allowedArrivalTemperatureRange>-100~200</allowedArrivalTemperatureRange>
    <maxConfigurableAtWorldCreation>1</maxConfigurableAtWorldCreation>
    <configurationListOrderPriority>900</configurationListOrderPriority>
    <dropPodActive>ActiveDropPodMechanoid</dropPodActive>
    <dropPodIncoming>DropPodIncomingMechanoid</dropPodIncoming>
  </FactionDef>

  <FactionDef ParentName="FactionBase">
    <defName>Insect</defName>
    <label>insect geneline</label>
    <description>These giant insect-like creatures live underground and burrow up to attack when attracted by noise or pheromone signals. Originally from the planet Sorne, interstellar entrepreneurs managed to capture, genetically-modify, and vat-grow the insect colonies for use as weapons. It's not clear who placed Sorne insects on this planet, but they are here and as dangerous as ever.</description>
    <pawnSingular>insect</pawnSingular>
    <pawnsPlural>insects</pawnsPlural>
    <requiredCountAtGameStart>1</requiredCountAtGameStart>
    <fixedName>Sorne Geneline</fixedName>
    <factionIconPath>World/WorldObjects/Expanding/Insects</factionIconPath>
    <colorSpectrum>
      <li>(0.44, 0.41, 0.32)</li>
      <li>(0.61, 0.58, 0.49)</li>
      <li>(0.60, 0.49, 0.36)</li>
    </colorSpectrum>
    <raidCommonalityFromPointsCurve>
      <points>
        <li>(0, 0)</li>
      </points>
    </raidCommonalityFromPointsCurve>
    <humanlikeFaction>false</humanlikeFaction>
    <hidden>true</hidden>
    <autoFlee>false</autoFlee>
    <canUseAvoidGrid>false</canUseAvoidGrid>
    <techLevel>Animal</techLevel>
    <permanentEnemyToEveryoneExcept>
      <li MayRequire="Ludeon.RimWorld.Anomaly">Entities</li>
    </permanentEnemyToEveryoneExcept>
    <settlementTexturePath>World/WorldObjects/DefaultSettlement</settlementTexturePath>
    <allowedArrivalTemperatureRange>0~45</allowedArrivalTemperatureRange>
    <maxConfigurableAtWorldCreation>1</maxConfigurableAtWorldCreation>
    <configurationListOrderPriority>1000</configurationListOrderPriority>
  </FactionDef>
  
  <ThingSetMakerDef>
    <defName>MechanoidRaidLootMaker</defName>
    <root Class="ThingSetMaker_MarketValue">
      <fixedParams>
        <filter>
          <thingDefs>
            <li>Plasteel</li>
            <li>ComponentIndustrial</li>
          </thingDefs>
        </filter>
      </fixedParams>
    </root>
  </ThingSetMakerDef>

</Defs>
