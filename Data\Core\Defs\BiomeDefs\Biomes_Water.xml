﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <BiomeDef>
    <defName>Ocean</defName>
    <label>ocean</label>
    <description>Open ocean. Great for fish. Not so great for you.</description>
    <workerClass>BiomeWorker_Ocean</workerClass>
    <canBuildBase>false</canBuildBase>
    <allowRoads>false</allowRoads>
    <allowRivers>false</allowRivers>
    <hasVirtualPlants>false</hasVirtualPlants>
    <texture>World/Biomes/Ocean</texture>
    <impassable>true</impassable>
  </BiomeDef>

  <BiomeDef>
    <defName>Lake</defName>
    <label>lake</label>
    <description>A large lake. Beautiful to live next to. Not so beautiful to live in.</description>
    <workerClass>BiomeWorker_Ocean</workerClass>
    <canBuildBase>false</canBuildBase>
    <allowRoads>false</allowRoads>
    <allowRivers>false</allowRivers>
    <hasVirtualPlants>false</hasVirtualPlants>
    <texture>World/Biomes/Ocean</texture>
    <impassable>true</impassable>
  </BiomeDef>
  
</Defs>