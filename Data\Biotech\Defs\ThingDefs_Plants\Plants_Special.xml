<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingDef ParentName="TreeBase">
    <defName>Plant_TreePolux</defName>
    <label>polux tree</label>
    <description>After generations of exposure, these trees have evolved to metabolize pollutants. By drawing pollutants from the ground through wide root networks, they slowly clean polluted terrain in their vicinity. However, they cannot do this if buildings are constructed over their roots.\n\nUnlike most methods of cleaning polluted terrain, polux trees do not create toxic wastepacks.</description>
    <graphicData>
      <texPath>Things/Plant/TreePolux</texPath>
      <graphicClass>Graphic_Random</graphicClass>
      <shadowData>
        <volume>(0.35, 0.3, 0.35)</volume>
        <offset>(0,0,0.05)</offset>
      </shadowData>
    </graphicData>
    <tickerType>Normal</tickerType>
    <minifiedDef IsNull="True" />
    <statBases>
      <Beauty>6</Beauty>
      <BeautyOutdoors>6</BeautyOutdoors>
      <Flammability>1</Flammability>
      <MaxHitPoints>300</MaxHitPoints>
    </statBases>
    <ingestible />
    <preventSkyfallersLandingOn>true</preventSkyfallersLandingOn>
    <canScatterOver>false</canScatterOver>
    <plant>
      <pollution>Any</pollution>
      <growDays>10</growDays>
      <visualSizeRange>1.5~2.1</visualSizeRange>
      <sowMinSkill>0</sowMinSkill>
      <sowTags Inherit="False"/>
      <lifespanDaysPerGrowDays>0</lifespanDaysPerGrowDays>
      <fertilityMin>0.05</fertilityMin>
      <fertilitySensitivity>0</fertilitySensitivity>
      <wildClusterWeight>0</wildClusterWeight>
      <sowWork>400</sowWork>
      <harvestYield>30</harvestYield>
      <treeCategory>Super</treeCategory>
      <dieIfNoSunlight>false</dieIfNoSunlight>
      <growMinGlow>0</growMinGlow>
      <sowResearchPrerequisites Inherit="False" />
      <dieFromToxicFallout>False</dieFromToxicFallout>
      <showGrowthInInspectPane>false</showGrowthInInspectPane>
      <minSpacingBetweenSamePlant>4.9</minSpacingBetweenSamePlant>
      <warnIfMarkedForCut>true</warnIfMarkedForCut>
      <harvestWork>1000</harvestWork>
      <choppedThingDef>ChoppedStump_Polux</choppedThingDef>
      <smashedThingDef>SmashedStump_Polux</smashedThingDef>
      <immatureGraphicPath>Things/Plant/TreePolux_Immature</immatureGraphicPath>
    </plant>
    <comps>
      <li Class="CompProperties_SelfhealHitpoints">
        <ticksPerHeal>2000</ticksPerHeal> <!-- 30 hp per day, must be a multiple of 2000, since plants have Long ticker -->
      </li>
      <li Class="CompProperties_PollutionPump">
        <radius>7.9</radius>
        <pumpsPerWastepack>0</pumpsPerWastepack>
        <intervalTicks>25000</intervalTicks>
        <disabledByArtificialBuildings>true</disabledByArtificialBuildings>
        <pumpEffecterDef>PollutionExtractedPoluxTree</pumpEffecterDef>
      </li>
    </comps>
    <drawPlaceWorkersWhileSelected>true</drawPlaceWorkersWhileSelected>
    <placeWorkers>
      <li>PlaceWorker_PollutionPumpArtificialBuildings</li>
    </placeWorkers>
  </ThingDef>
  
  <ThingDef ParentName="StumpChoppedBase">
    <defName>ChoppedStump_Polux</defName>
    <label>chopped polux stump</label>
    <description>A stump left behind after a polux tree has been felled. The stump can be extracted but yields very little usable wood. Left outdoors, the stump will deteriorate in time.</description>
    <graphicData>
      <texPath>Things/Plant/Stumps/TreePolux/Chopped</texPath>
      <graphicClass>Graphic_Random</graphicClass>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="StumpSmashedBase">
    <defName>SmashedStump_Polux</defName>
    <label>smashed polux stump</label>
    <description>The remnants of a polux tree destroyed by damage. It's ugly. The stump can be extracted but yields very little usable wood. Left outdoors, the stump will deteriorate in time.</description>
    <graphicData>
      <texPath>Things/Plant/Stumps/TreePolux/Smashed</texPath>
      <graphicClass>Graphic_Random</graphicClass>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="ResourceBase">
    <defName>PoluxSeed</defName>
    <label>polux seed</label>
    <description>The seed of a polux tree. It can be planted to create a new polux tree which will absorb pollution from nearby terrain.</description>
    <descriptionHyperlinks>
      <ThingDef>Plant_TreePolux</ThingDef>
    </descriptionHyperlinks>
    <stackLimit>5</stackLimit>
    <statBases>
      <MaxHitPoints>50</MaxHitPoints>
      <Mass>0.3</Mass>
      <Beauty>0</Beauty>
      <MarketValue>1200</MarketValue>
    </statBases>
    <graphicData>
      <texPath>Things/Item/PoluxSeed</texPath>
      <graphicClass>Graphic_StackCount</graphicClass>
      <drawSize>(0.6, 0.6)</drawSize>
    </graphicData>
    <thingCategories>
      <li>ItemsMisc</li>
    </thingCategories>
    <comps>
      <li Class="CompProperties_Plantable">
        <plantDefToSpawn>Plant_TreePolux</plantDefToSpawn>
      </li>
    </comps>
  </ThingDef>
</Defs>