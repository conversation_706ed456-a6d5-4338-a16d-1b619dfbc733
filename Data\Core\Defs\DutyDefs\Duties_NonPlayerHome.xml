<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <DutyDef>
    <defName>DefendBase</defName>
    <alwaysShowWeapon>true</alwaysShowWeapon>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_TakeCombatEnhancingDrug">
          <onlyIfInDanger>true</onlyIfInDanger>
        </li>
        <li Class="JobGiver_AIDefendPoint">
          <targetAcquireRadius>25</targetAcquireRadius>
          <targetKeepRadius>30</targetKeepRadius>
        </li>
        <li Class="ThinkNode_ForbidOutsideFlagRadius">
          <maxDistToSquadFlag>16</maxDistToSquadFlag>
          <subNodes>
            <li Class="ThinkNode_Subtree">
              <treeDef>SatisfyBasicNeeds</treeDef>
            </li>
          </subNodes>
        </li>
        <li Class="JobGiver_SeekSafeTemperature" />
        <li Class="ThinkNode_ConditionalCloseToDutyTarget">
          <maxDistToDutyTarget>12</maxDistToDutyTarget>
          <subNodes>
            <li Class="JobGiver_WanderAnywhere">
              <wanderRadius>12</wanderRadius>
            </li>
          </subNodes>
        </li>
        <li Class="JobGiver_WanderNearDutyLocation">
          <locomotionUrgencyOutsideRadius>Sprint</locomotionUrgencyOutsideRadius>
        </li>
      </subNodes>
    </thinkNode>
  </DutyDef>

</Defs>
