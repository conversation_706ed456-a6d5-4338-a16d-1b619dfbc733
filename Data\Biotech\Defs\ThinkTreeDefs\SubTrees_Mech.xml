<?xml version="1.0" encoding="utf-8" ?>
<Defs>
  

  <ThinkTreeDef>
    <defName>MechConstant</defName>
    <thinkRoot Class="ThinkNode_Priority">
      <subNodes>

        <!-- Despawned -->
        <li Class="ThinkNode_Subtree">
          <treeDef>Despawned</treeDef>
        </li>

        <!-- Player mechs -->
        <li Class="ThinkNode_ConditionalPlayerMech">
          <subNodes>
            
            <li Class="ThinkNode_ConditionalHasAbility">
              <ability>LongjumpMechLauncher</ability>
              <subNodes>
                
                <!-- Jump towards fire to beat -->
                <li Class="ThinkNode_ConditionalDoingJob">
                  <jobDef>BeatFire</jobDef>
                  <subNodes>
                    <li Class="JobGiver_AIJumpToJobTarget">
                      <ability>LongjumpMechLauncher</ability>
                    </li>
                  </subNodes>
                </li>

                <!-- Jump to rescue pawn -->
                <li Class="ThinkNode_ConditionalDoingJob">
                  <jobDef>Rescue</jobDef>
                  <subNodes>
                    <!-- Downed pawn -->
                    <li Class="JobGiver_AIJumpToJobRescueTarget">
                      <ability>LongjumpMechLauncher</ability>
                    </li>
                    <!-- Jump to bed -->
                    <li Class="JobGiver_AIJumpToJobRescueTarget">
                      <ability>LongjumpMechLauncher</ability>
                      <targetIndex>B</targetIndex>
                    </li>
                  </subNodes>
                </li>

              </subNodes>
            </li>

            

          </subNodes>
        </li>
        
        <!-- Non-player mechs -->
        <li Class="ThinkNode_ConditionalPlayerMech">
          <invert>true</invert>
          <subNodes>
            <li Class="ThinkNode_ConditionalCanDoConstantThinkTreeJobNow">
              <subNodes>
                <!-- Smokepop -->
                <li Class="ThinkNode_ConditionalHasAbility">
                  <ability>SmokepopMech</ability>
                  <subNodes>
                    <li Class="ThinkNode_ConditionalShotRecently">
                      <thresholdTicks>600</thresholdTicks>
                      <subNodes>
                        <li Class="ThinkNode_ConditionalTotalDamage">
                          <thresholdPercent>0.25</thresholdPercent>
                          <subNodes>
                            <li Class="JobGiver_AICastAbilityOnSelf">
                              <ability>SmokepopMech</ability>
                            </li>
                          </subNodes>
                        </li>
                      </subNodes>
                    </li>
                  </subNodes>
                </li>
                
                <!-- Resurrect -->
                <li Class="ThinkNode_ConditionalHasAbility">
                  <ability>ResurrectionMech</ability>
                  <subNodes>
                    <li Class="JobGiver_AIResurrectTarget">
                      <ability>ResurrectionMech</ability>
                    </li>
                    <li Class="ThinkNode_ConditionalHasAbility">
                      <ability>LongjumpMech</ability>
                        <subNodes>
                          <li Class="ThinkNode_ConditionalAbilityCastLastTick">
                            <ability>ResurrectionMech</ability>
                            <maxTicksAgo>120</maxTicksAgo>
                            <subNodes>
                              <li Class="ThinkNode_HarmedRecently">
                                <thresholdTicks>120</thresholdTicks>
                                <subNodes>
                                  <li Class="JobGiver_AIJumpEscapeEnemies">
                                    <ability>LongjumpMech</ability>
                                  </li>
                                </subNodes>
                              </li>
                            </subNodes>
                          </li>
                        </subNodes>
                    </li>
                  </subNodes>
                </li>                

                <!-- Spawn mecs -->
                <li Class="ThinkNode_ConditionalHasTarget">
                  <subNodes>
                    <li Class="JobGiver_AIReleaseMechs" />
                  </subNodes>
                </li>
              
              </subNodes>
            </li>
          </subNodes>
        </li>
      </subNodes>
    </thinkRoot>
  </ThinkTreeDef>

  <ThinkTreeDef>
    <defName>WarUrchinConstant</defName>
    <thinkRoot Class="ThinkNode_Priority">
      <subNodes>
        
        <!-- Despawned -->
        <li Class="ThinkNode_Subtree">
          <treeDef>Despawned</treeDef>
        </li>

        <li Class="ThinkNode_ConditionalCanDoConstantThinkTreeJobNow">
          <subNodes>
            <li Class="JobGiver_AIFightEnemies">
              <targetAcquireRadius>65</targetAcquireRadius>
              <targetKeepRadius>72</targetKeepRadius>
            </li>
            <li Class="JobGiver_AITrashColonyClose" />
            <li Class="JobGiver_AITrashBuildingsDistant" />
            <li Class="JobGiver_AIGotoNearestHostile" />
            <li Class="JobGiver_AITrashBuildingsDistant">
              <attackAllInert>true</attackAllInert>
            </li>
            <li Class="ThinkNode_ConditionalPlayerMech">
              <subNodes>
                <li Class="JobGiver_WanderColony" />
              </subNodes>
            </li>
            <li Class="JobGiver_WanderAnywhere" />
          </subNodes>
        </li>
      </subNodes>
    </thinkRoot>
  </ThinkTreeDef>


</Defs>