<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThoughtDef>
    <defName>FedOn</defName>
    <thoughtClass>Thought_Memory</thoughtClass>
    <durationDays>5</durationDays>
    <nullifyingTraits>
      <li>Masochist</li>
    </nullifyingTraits>
    <nullifyingPrecepts>
      <li>Bloodfeeders_Revered</li>
    </nullifyingPrecepts>
    <stages>
      <li>
        <label>fed on by {0}</label>
        <description>I was violated by a bloodfeeder like some kind of livestock.</description>
        <baseMoodEffect>-5</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>IngestedHemogenPack</defName>
    <thoughtClass>Thought_Memory</thoughtClass>
    <durationDays>5</durationDays>
    <stackLimit>5</stackLimit>
    <nullifyingTraits>
      <li>Cannibal</li>
    </nullifyingTraits>
    <nullifyingGenes>
      <li>Hemogenic</li>
    </nullifyingGenes>
    <nullifyingPrecepts>
      <li MayRequire="Ludeon.RimWorld.Ideology">Cannibalism_Acceptable</li>
      <li MayRequire="Ludeon.RimWorld.Ideology">Cannibalism_Preferred</li>
      <li MayRequire="Ludeon.RimWorld.Ideology">Cannibalism_RequiredStrong</li>
      <li MayRequire="Ludeon.RimWorld.Ideology">Cannibalism_RequiredRavenous</li>
    </nullifyingPrecepts>
    <stages>
      <li>
        <label>ingested hemogen pack</label>
        <description>I ingested a hemogen pack. It might keep me alive, but it was absolutely disgusting.</description>
        <baseMoodEffect>-5</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>PregnancyTerminated</defName>
    <thoughtClass>Thought_Memory</thoughtClass>
    <durationDays>10</durationDays>
    <stackLimit>3</stackLimit>
    <stages>
      <li>
        <label>pregnancy terminated</label>
        <description>My pregnancy was terminated.</description>
        <baseMoodEffect>-5</baseMoodEffect>
      </li>
      <li>
        <label>pregnancy terminated</label>
        <description>My pregnancy was terminated.</description>
        <baseMoodEffect>-10</baseMoodEffect>
      </li>
      <li>
        <label>pregnancy terminated</label>
        <description>My pregnancy was terminated.</description>
        <baseMoodEffect>-20</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>PregnancyEnded</defName>
    <thoughtClass>Thought_Memory</thoughtClass>
    <durationDays>5</durationDays>
    <stackLimit>3</stackLimit>
    <stages>
      <li>
        <label>pregnancy ended</label>
        <description>I'm relieved I'm not pregnant any more.</description>
        <baseMoodEffect>5</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>Stillbirth</defName>
    <thoughtClass>Thought_Memory</thoughtClass>
    <durationDays>10</durationDays>
    <nullifyingHediffs>
      <li MayRequire="Ludeon.RimWorld.Anomaly">Inhumanized</li>
    </nullifyingHediffs>
    <stages>
      <li>
        <label>stillbirth</label>
        <description>My child died during labor.</description>
        <baseMoodEffect>-15</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>Miscarried</defName>
    <thoughtClass>Thought_Memory</thoughtClass>
    <durationDays>10</durationDays>
    <nullifyingHediffs>
      <li MayRequire="Ludeon.RimWorld.Anomaly">Inhumanized</li>
    </nullifyingHediffs>
    <stages>
      <li>
        <label>miscarried</label>
        <description>I lost my child.</description>
        <baseMoodEffect>-12</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>PartnerMiscarried</defName>
    <thoughtClass>Thought_Memory</thoughtClass>
    <durationDays>10</durationDays>
    <nullifyingHediffs>
      <li MayRequire="Ludeon.RimWorld.Anomaly">Inhumanized</li>
    </nullifyingHediffs>
    <stages>
      <li>
        <label>{0} miscarried</label>
        <description>My child's story ended before it started.</description>
        <baseMoodEffect>-12</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>BabyBorn</defName>
    <thoughtClass>Thought_Memory</thoughtClass>
    <durationDays>15</durationDays>
    <nullifyingHediffs>
      <li MayRequire="Ludeon.RimWorld.Anomaly">Inhumanized</li>
    </nullifyingHediffs>
    <stages>
      <li>
        <label>{0} was born healthy</label>
        <description>My baby brings me so much joy. I would move earth and sky for my little one.</description>
        <baseMoodEffect>8</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>PsychicBondTorn</defName>
    <thoughtClass>Thought_Memory</thoughtClass>
    <durationDays>30</durationDays>
    <validWhileDespawned>true</validWhileDespawned>
    <lerpMoodToZero>true</lerpMoodToZero>
    <stages>
      <li>
        <label>psychic bond torn ({0})</label>
        <description>My psychic bond with {OTHERPAWN} was torn apart. I feel like part of my mind and emotions are missing.</description>
        <baseMoodEffect>-25</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>XenogermHarvested_Prisoner</defName>
    <thoughtClass>Thought_Memory</thoughtClass>
    <durationDays>25</durationDays>
    <stackLimit>1</stackLimit>
    <stages>
      <li>
        <label>genes harvested</label>
        <description>They extracted my genes like I'm some sort of lab creature.</description>
        <baseMoodEffect>-8</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

</Defs>