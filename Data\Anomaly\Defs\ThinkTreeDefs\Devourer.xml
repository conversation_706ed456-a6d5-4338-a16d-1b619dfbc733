﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>
  
  <ThinkTreeDef>
    <defName>Devourer</defName>
    <thinkRoot Class="ThinkNode_Priority">
      <subNodes>
        <!-- Despawned -->
        <li Class="ThinkNode_Subtree">
          <treeDef>Despawned</treeDef>
        </li>
        
        <!-- Downed -->
        <li Class="ThinkNode_Subtree">
          <treeDef>Downed</treeDef>
        </li>
        
        <!-- Lord directives -->
        <li Class="ThinkNode_Subtree">
          <treeDef>LordDuty</treeDef>
        </li>
        
        <!-- Escaping-->
        <li Class="ThinkNodeConditional_EscapingHoldingPlatform">
          <subNodes>
            <li Class="JobGiver_EscapingHoldingPlatform" />
          </subNodes>
        </li>
        
        <!-- Insertion hook for modders -->
        <li Class="ThinkNode_SubtreesByTag">
          <insertTag>Devourer_PreMain</insertTag>
        </li>

        <!-- Consume leap a target -->
        <li Class="JobGiver_AIAbilityFight">
          <targetAcquireRadius>65</targetAcquireRadius>
          <targetKeepRadius>72</targetKeepRadius>
          <ability>ConsumeLeap_Devourer</ability>
        </li>

        <!-- React to close melee threat -->
        <li Class="JobGiver_ReactToCloseMeleeThreat">
          <maxDistance>2.9</maxDistance>
        </li>

        <!-- Regular fight -->
        <li Class="JobGiver_AIFightEnemies">
          <targetAcquireRadius>65</targetAcquireRadius>
          <targetKeepRadius>72</targetKeepRadius>
          <allowTurrets>true</allowTurrets>
        </li>
        
        <!-- Insertion hook for modders -->
        <li Class="ThinkNode_SubtreesByTag">
          <insertTag>Devourer_PreWander</insertTag>
        </li>
        
        <!-- Wander -->
        <li Class="JobGiver_WanderAnywhere">
          <maxDanger>Deadly</maxDanger>
          <ticksBetweenWandersRange>120~240</ticksBetweenWandersRange>
        </li>
      </subNodes>
    </thinkRoot>
  </ThinkTreeDef>
  
  <ThinkTreeDef>
    <defName>DevourerConstant</defName>
    <thinkRoot Class="ThinkNode_Priority">
      <subNodes>
        <!-- Despawned -->
        <li Class="ThinkNode_Subtree">
          <treeDef>Despawned</treeDef>
        </li>
        
        <li Class="ThinkNode_ConditionalCanDoLordJobNow">
          <subNodes>
            <!-- Lord directives -->
            <li Class="ThinkNode_Subtree">
              <treeDef>LordDutyConstant</treeDef>
            </li>
          </subNodes>
        </li>
      </subNodes>
    </thinkRoot>
  </ThinkTreeDef>
  
</Defs>