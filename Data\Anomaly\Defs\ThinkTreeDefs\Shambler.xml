﻿<?xml version="1.0" encoding="utf-8"?>

<Defs>
  <ThinkTreeDef>
    <defName>Shambler</defName>
    <thinkRoot Class="ThinkNode_Priority">
      <subNodes>
        <!-- Despawned -->
        <li Class="ThinkNode_Subtree">
          <treeDef>Despawned</treeDef>
        </li>

        <!-- Downed -->
        <li Class="ThinkNode_ConditionalDowned">
          <subNodes>
            <!-- Downed and can't crawl -->
            <li Class="ThinkNode_ConditionalCanCrawl">
              <invert>true</invert>
              <subNodes>
                <li Class="ThinkNode_Subtree">
                  <treeDef>Downed</treeDef>
                </li>
              </subNodes>
            </li>
          </subNodes>
        </li>

        <!-- Do a queued job -->
        <li Class="ThinkNode_QueuedJob" />

        <!-- Escaping-->
        <li Class="ThinkNodeConditional_EscapingHoldingPlatform">
          <subNodes>
            <li Class="JobGiver_EscapingHoldingPlatform" />
          </subNodes>
        </li>

        <!-- React to close melee threat -->
        <li Class="JobGiver_ReactToCloseMeleeThreat" />

        <!-- Lord directives -->
        <li Class="ThinkNode_Subtree">
          <treeDef>LordDuty</treeDef>
        </li>

        <!-- Insertion hook for modders -->
        <li Class="ThinkNode_SubtreesByTag">
          <insertTag>Shambler_PreMain</insertTag>
        </li>

        <li Class="JobGiver_ShamblerFight">
          <targetAcquireRadius>20</targetAcquireRadius>
          <targetKeepRadius>30</targetKeepRadius>
        </li>

        <!-- Insertion hook for modders -->
        <li Class="ThinkNode_SubtreesByTag">
          <insertTag>Shambler_PreWander</insertTag>
        </li>

        <li Class="JobGiver_ShamblerWander">
          <wanderRadius>5</wanderRadius>
        </li>
        
      </subNodes>
    </thinkRoot>
  </ThinkTreeDef>

  <ThinkTreeDef>
    <defName>ShamblerConstant</defName>
    <thinkRoot Class="ThinkNode_Priority">
      <subNodes>
        <!-- Despawned -->
        <li Class="ThinkNode_Subtree">
          <treeDef>Despawned</treeDef>
        </li>

        <li Class="ThinkNode_ConditionalCanDoLordJobNow">
          <subNodes>
            <!-- Lord directives -->
            <li Class="ThinkNode_Subtree">
              <treeDef>LordDutyConstant</treeDef>
            </li>
          </subNodes>
        </li>
      </subNodes>
    </thinkRoot>
  </ThinkTreeDef>
</Defs>
