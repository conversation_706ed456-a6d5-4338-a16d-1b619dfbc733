﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingDef ParentName="MakeableDrugBase">
    <defName>SmokeleafJoint</defName>
    <label>smokeleaf joint</label>
    <description>Smokeleaf leaves prepared in small rolls for smoking. The drug improves mood, but also increases appetite, reduces focus and slows movement. Smokeleaf use can produce a dependency.\n\nJoints can be produced at a crafting spot without equipment, and are a fixture in many traditional low-industriousness cultures.</description>
    <possessionCount>5</possessionCount>
    <descriptionHyperlinks>
      <HediffDef>SmokeleafHigh</HediffDef>
      <HediffDef>SmokeleafTolerance</HediffDef>
      <HediffDef>SmokeleafAddiction</HediffDef>
      <HediffDef>Carcinoma</HediffDef>
      <HediffDef>Asthma</HediffDef>
    </descriptionHyperlinks>
    <graphicData>
      <texPath>Things/Item/Drug/Joint</texPath>
      <graphicClass>Graphic_StackCount</graphicClass>
    </graphicData>
    <rotatable>false</rotatable>
    <statBases>
      <WorkToMake>450</WorkToMake>
      <MarketValue>11</MarketValue>
      <Mass>0.05</Mass>
      <DeteriorationRate>6</DeteriorationRate>
      <Flammability>1.3</Flammability>
    </statBases>
    <techLevel>Neolithic</techLevel>
    <ingestible>
      <foodType>Plant, Processed</foodType>
      <joyKind>Chemical</joyKind>
      <joy>0.80</joy>
      <baseIngestTicks>720</baseIngestTicks>
      <nurseable>true</nurseable>
      <drugCategory>Social</drugCategory>
      <ingestSound>Ingest_Smoke</ingestSound>
      <ingestEffect>Smoke_Joint</ingestEffect>
      <ingestEffectEat>EatVegetarian</ingestEffectEat>
      <ingestHoldOffsetStanding>
        <northDefault>
          <offset>(0.27,0,0.08)</offset>
          <behind>true</behind>
        </northDefault>
        <east>
          <offset>(0.45,0,0.08)</offset>
        </east>
        <south>
          <offset>(0.27,0,0.08)</offset>
        </south>
        <west>
          <offset>(-0.50,0,0.08)</offset>
          <flip>true</flip>
        </west>
      </ingestHoldOffsetStanding>
      <ingestHoldUsesTable>false</ingestHoldUsesTable>
      <ingestCommandString>Smoke {0}</ingestCommandString>
      <ingestReportString>Smoking {0}.</ingestReportString>
      <ingestReportStringEat>Consuming {0}.</ingestReportStringEat>
      <useEatingSpeedStat>false</useEatingSpeedStat>
      <outcomeDoers>
        <li Class="IngestionOutcomeDoer_GiveHediff">
          <hediffDef>SmokeleafHigh</hediffDef>
          <severity>0.5</severity>
          <toleranceChemical>Smokeleaf</toleranceChemical>
        </li>
        <li Class="IngestionOutcomeDoer_OffsetNeed">
          <need>Rest</need>
          <offset>-0.1</offset>
        </li>
        <li Class="IngestionOutcomeDoer_GiveHediff">
          <hediffDef>SmokeleafTolerance</hediffDef>
          <toleranceChemical>Smokeleaf</toleranceChemical>
          <severity>0.030</severity>
          <divideByBodySize>true</divideByBodySize>
          <multiplyByGeneToleranceFactors>true</multiplyByGeneToleranceFactors>
        </li>
      </outcomeDoers>
    </ingestible>
    <recipeMaker>
      <recipeUsers>
        <li>CraftingSpot</li>
        <li>DrugLab</li>
      </recipeUsers>
      <workSpeedStat>DrugCookingSpeed</workSpeedStat>
      <workSkill>Cooking</workSkill>
      <displayPriority>1400</displayPriority>
    </recipeMaker>
    <costList>
      <SmokeleafLeaves>4</SmokeleafLeaves>
    </costList>
    <comps>
      <li Class="CompProperties_Drug">
        <chemical>Smokeleaf</chemical>
        <addictiveness>0.020</addictiveness>
        <minToleranceToAddict>0.15</minToleranceToAddict>
        <existingAddictionSeverityOffset>0.06</existingAddictionSeverityOffset>
        <needLevelOffset>0.9</needLevelOffset>
        <listOrder>20</listOrder>
      </li>
    </comps>
    <allowedArchonexusCount>50</allowedArchonexusCount>
  </ThingDef>

  <HediffDef>
    <defName>SmokeleafHigh</defName>
    <label>stoned on smokeleaf</label>
    <labelNoun>a smokeleaf high</labelNoun>
    <description>Smokeleaf's active chemical in the bloodstream. Generates a soft feeling of fuzzy well-being.</description>
    <hediffClass>Hediff_High</hediffClass>
    <defaultLabelColor>(1,0,0.5)</defaultLabelColor>
    <scenarioCanAdd>true</scenarioCanAdd>
    <maxSeverity>1.0</maxSeverity>
    <isBad>false</isBad>
    <comps>
      <li Class="HediffCompProperties_SeverityPerDay">
        <severityPerDay>-1.0</severityPerDay>
        <showHoursToRecover>true</showHoursToRecover>
      </li>
    </comps>
      <stages>
        <li>
          <hungerRateFactorOffset>0.3</hungerRateFactorOffset>
          <painOffset>-0.2</painOffset>
          <capMods>
            <li>
              <capacity>Consciousness</capacity>
              <offset>-0.3</offset>
            </li>
            <li>
              <capacity>Moving</capacity>
              <offset>-0.1</offset>
            </li>
          </capMods>
        </li>
      </stages>
  </HediffDef>
  
  <ThoughtDef>
    <defName>SmokeleafHigh</defName>
    <workerClass>ThoughtWorker_Hediff</workerClass>
    <hediff>SmokeleafHigh</hediff>
    <validWhileDespawned>true</validWhileDespawned>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
    <stages>
      <li>
        <label>high on smokeleaf</label>
        <description>I'm, like, stoned, man.</description>
        <baseMoodEffect>13</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <!-- Smokeleaf addiction -->

  <ChemicalDef>
    <defName>Smokeleaf</defName>
    <label>Smokeleaf</label>
    <addictionHediff>SmokeleafAddiction</addictionHediff>
    <toleranceHediff>SmokeleafTolerance</toleranceHediff>
    <onGeneratedAddictedToleranceChance>0.8</onGeneratedAddictedToleranceChance>
    <geneToleranceBuildupFactorResist>0.5</geneToleranceBuildupFactorResist>
    <geneToleranceBuildupFactorImmune>0</geneToleranceBuildupFactorImmune>
  </ChemicalDef>

  <NeedDef ParentName="DrugAddictionNeedBase">
    <defName>Chemical_Smokeleaf</defName>
    <needClass>Need_Chemical</needClass>
    <label>smokeleaf</label>
    <description>Because of a smokeleaf dependence, this person needs to regularly consume the drug to avoid withdrawal symptoms.</description>
    <listPriority>25</listPriority>
  </NeedDef>
  
  <HediffDef ParentName="DrugToleranceBase">
    <defName>SmokeleafTolerance</defName>
    <label>smokeleaf tolerance</label>
    <description>A built-up tolerance to smokeleaf. The more severe this tolerance is, the more smokeleaf it takes to get the same effect.</description>
    <comps>
      <li Class="HediffCompProperties_SeverityPerDay">
        <severityPerDay>-0.015</severityPerDay>
      </li>
      <li Class="HediffCompProperties_DrugEffectFactor">
        <chemical>Smokeleaf</chemical>
      </li>
    </comps>
    <hediffGivers>
      <li Class="HediffGiver_RandomDrugEffect">
        <hediff>Asthma</hediff>
        <severityToMtbDaysCurve>
          <points>
            <li>(0.45, 99999)</li>
            <li>(0.5, 180)</li>
            <li>(1, 135)</li>
          </points>
        </severityToMtbDaysCurve>
        <partsToAffect>
          <li>Lung</li>
        </partsToAffect>
        <countToAffect>2</countToAffect>
      </li>
      <li Class="HediffGiver_RandomDrugEffect">
        <hediff>Carcinoma</hediff>
        <severityToMtbDaysCurve>
          <points>
            <li>(0.36, 99999)</li>
            <li>(0.4, 180)</li>
            <li>(1, 135)</li>
          </points>
        </severityToMtbDaysCurve>
        <partsToAffect>
          <li>Lung</li>
        </partsToAffect>
        <countToAffect>1</countToAffect>
      </li>
    </hediffGivers>
  </HediffDef>

  <HediffDef ParentName="AddictionBase">
    <defName>SmokeleafAddiction</defName>
    <label>smokeleaf dependence</label>
    <description>A chemical addiction to smokeleaf. Long-term use of smokeleaf has caused neurological adaptations at the cellular level, so the brain can no longer function properly without the drug.
\nWithout regular doses of smokeleaf, withdrawal symptoms will begin. However, extended abstinence will force the brain to adapt back to its normal state, resolving the addiction.</description>
    <hediffClass>Hediff_Addiction</hediffClass>
    <causesNeed>Chemical_Smokeleaf</causesNeed>
    <comps>
      <li Class="HediffCompProperties_SeverityPerDay">
        <severityPerDay>-0.0333</severityPerDay>
        <showDaysToRecover>true</showDaysToRecover>
      </li>
    </comps>
    <stages>
      <li>
      </li>
      <li>
        <label>withdrawal</label>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.20</offset>
          </li>
          <li>
            <capacity>Moving</capacity>
            <offset>-0.15</offset>
          </li>
        </capMods>
        <mentalStateGivers>
          <li>
            <mentalState>Binging_Food</mentalState>
            <mtbDays>30</mtbDays>
          </li>
          <li>
            <mentalState>Binging_DrugMajor</mentalState>
            <mtbDays>50</mtbDays>
          </li>
        </mentalStateGivers>
      </li>
    </stages>
  </HediffDef>

  <ThoughtDef>
    <defName>SmokeleafWithdrawal</defName>
    <workerClass>ThoughtWorker_Hediff</workerClass>
    <hediff>SmokeleafAddiction</hediff>
    <validWhileDespawned>true</validWhileDespawned>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
    <stages>
      <li>
        <visible>false</visible>
      </li>
      <li>
        <label>smokeleaf withdrawal</label>
        <description>I really wish I could smoke. I feel jittery, and my gut has that anxious sensation all the time.</description>
        <baseMoodEffect>-20</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

</Defs>