﻿<?xml version="1.0" encoding="utf-8"?>

<Defs>

  <PawnGroupKindDef>
    <defName>Shamblers</defName>
    <workerClass>PawnGroupKindWorker_Shamblers</workerClass>
  </PawnGroupKindDef>
  
  <PawnGroupKindDef>
    <defName>Fleshbeasts</defName>
    <workerClass>PawnGroupKindWorker_Normal</workerClass>
  </PawnGroupKindDef>
  
  <PawnGroupKindDef>
    <defName>FleshbeastsWithDreadmeld</defName>
    <workerClass>PawnGroupKindWorker_Normal</workerClass>
  </PawnGroupKindDef>
  
  <PawnGroupKindDef>
    <defName>Sightstealers</defName>
    <workerClass>PawnGroupKindWorker_Normal</workerClass>
  </PawnGroupKindDef>
  
  <PawnGroupKindDef>
    <defName>Metalhorrors</defName>
    <workerClass>PawnGroupKindWorker_Normal</workerClass>
  </PawnGroupKindDef>

  <PawnGroupKindDef>
    <defName>PsychicRitualSiege</defName>
    <workerClass>PawnGroupKindWorker_Normal</workerClass>
  </PawnGroupKindDef>

  <PawnGroupKindDef>
    <defName>Gorehulks</defName>
    <workerClass>PawnGroupKindWorker_Normal</workerClass>
  </PawnGroupKindDef>
  
  <PawnGroupKindDef>
    <defName>Devourers</defName>
    <workerClass>PawnGroupKindWorker_Normal</workerClass>
  </PawnGroupKindDef>
  
  <PawnGroupKindDef>
    <defName>Noctols</defName>
    <workerClass>PawnGroupKindWorker_Normal</workerClass>
  </PawnGroupKindDef>

  <PawnGroupKindDef>
    <defName>SightstealersNoctols</defName>
    <workerClass>PawnGroupKindWorker_Normal</workerClass>
  </PawnGroupKindDef>

  <PawnGroupKindDef>
    <defName>Chimeras</defName>
    <workerClass>PawnGroupKindWorker_Normal</workerClass>
  </PawnGroupKindDef>
</Defs>