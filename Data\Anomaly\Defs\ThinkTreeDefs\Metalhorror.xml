﻿<?xml version="1.0" encoding="utf-8"?>

<Defs>
  <ThinkTreeDef>
    <defName>Metalhorror</defName>
    <thinkRoot Class="ThinkNode_Priority">
      <subNodes>
        
        <!-- Keep lying down if we have to -->
        <li Class="ThinkNode_ConditionalMustKeepLyingDown">
          <subNodes>
            <!-- Do a queued job if possible -->
            <li Class="ThinkNode_QueuedJob">
              <inBedOnly>true</inBedOnly>
            </li>

            <!-- Keep lying down -->
            <li Class="JobGiver_KeepLyingDown" />
          </subNodes>
        </li>
        
        <!-- Downed -->
        <li Class="ThinkNode_Subtree">
          <treeDef>Downed</treeDef>
        </li>

        <!-- Burning -->
        <li Class="ThinkNode_Subtree">
          <treeDef>BurningResponse</treeDef>
        </li>
        
        <!-- Escaping -->
        <li Class="ThinkNodeConditional_EscapingHoldingPlatform">
          <subNodes>
            <li Class="JobGiver_EscapingHoldingPlatform" />
          </subNodes>
        </li>
        
        <!-- React to close melee threat -->
        <li Class="JobGiver_ReactToCloseMeleeThreat" />

        <!-- Mental state non critical -->
        <li Class="ThinkNode_Subtree">
          <treeDef>MentalStateNonCritical</treeDef>
        </li>
        
        <!-- Lord directives -->
        <li Class="ThinkNode_Subtree">
          <treeDef>LordDuty</treeDef>
        </li>
        
        <!-- Fight -->
        <li Class="JobGiver_MetalhorrorFight" />
        
        <!-- Insertion hook for modders -->
        <li Class="ThinkNode_SubtreesByTag">
          <insertTag>Metalhorror_PreWander</insertTag>
        </li>
        
        <!-- Wander -->
        <li Class="ThinkNode_Tagger">
          <tagToGive>Idle</tagToGive>
          <subNodes>
            <li Class="JobGiver_WanderAnywhere">
              <maxDanger>Deadly</maxDanger>
              <ticksBetweenWandersRange>180~720</ticksBetweenWandersRange>
              <wanderRadius>6</wanderRadius>
              <canBashDoors>true</canBashDoors>
              <expireOnNearbyEnemy>true</expireOnNearbyEnemy>
            </li>
          </subNodes>
        </li>
        
        <li Class="JobGiver_IdleError"/>
      </subNodes>
    </thinkRoot>
  </ThinkTreeDef>

  <ThinkTreeDef>
    <defName>MetalhorrorConstant</defName>
    <thinkRoot Class="ThinkNode_Priority">
      <subNodes>
        
        <!-- Despawned -->
        <li Class="ThinkNode_Subtree">
          <treeDef>Despawned</treeDef>
        </li>

        <li Class="ThinkNode_ConditionalCanDoLordJobNow">
          <subNodes>
            <!-- Lord directives -->
            <li Class="ThinkNode_Subtree">
              <treeDef>LordDutyConstant</treeDef>
            </li>
          </subNodes>
        </li>
        
      </subNodes>
    </thinkRoot>
  </ThinkTreeDef>
</Defs>
