<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <!-- Temporary hack; ranged weapons need to use the old pre-flavorful-combat system to avoid a rebalance cascade. -->
  <DamageDef>
    <defName>RangedStab</defName>
    <label>stab</label>
    <workerClass>DamageWorker_Stab</workerClass>
    <externalViolence>true</externalViolence>
    <deathMessage>{0} has been stabbed to death.</deathMessage>
    <hediff>Stab</hediff>
    <hediffSolid>Crack</hediffSolid>
    <impactSoundType>Slice</impactSoundType>
    <armorCategory>Sharp</armorCategory>
    <overkillPctToDestroyPart>0~0.7</overkillPctToDestroyPart>
    <stabChanceOfForcedInternal>0.6</stabChanceOfForcedInternal>
    <isRanged>true</isRanged>
    <makesAnimalsFlee>true</makesAnimalsFlee>
  </DamageDef>

  <DamageDef Name="Bullet">
    <defName>Bullet</defName>
    <label>bullet</label>
    <workerClass>DamageWorker_AddInjury</workerClass>
    <externalViolence>true</externalViolence>
    <deathMessage>{0} has been shot to death.</deathMessage>
    <hediff>Gunshot</hediff>
    <harmAllLayersUntilOutside>true</harmAllLayersUntilOutside>
    <impactSoundType>Bullet</impactSoundType>
    <armorCategory>Sharp</armorCategory>
    <overkillPctToDestroyPart>0~0.7</overkillPctToDestroyPart>
    <isRanged>true</isRanged>
    <makesAnimalsFlee>true</makesAnimalsFlee>
  </DamageDef>

  <DamageDef Name="Arrow">
    <defName>Arrow</defName>
    <label>arrow</label>
    <workerClass>DamageWorker_AddInjury</workerClass>
    <externalViolence>true</externalViolence>
    <deathMessage>{0} has been shot to death by an arrow.</deathMessage>
    <hediff>Cut</hediff>
    <hediffSolid>Crack</hediffSolid>
    <harmAllLayersUntilOutside>true</harmAllLayersUntilOutside>
    <impactSoundType>Bullet</impactSoundType>
    <armorCategory>Sharp</armorCategory>
    <overkillPctToDestroyPart>0~0.7</overkillPctToDestroyPart>
    <isRanged>true</isRanged>
    <makesAnimalsFlee>true</makesAnimalsFlee>
  </DamageDef>

  <DamageDef ParentName="Arrow">
    <defName>ArrowHighVelocity</defName>
    <hediff>Stab</hediff>
  </DamageDef>

</Defs>
