<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <SoundDef>
    <defName>BabyBecomesChild</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <onCamera>true</onCamera>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Birthdays/BabyBecomesChild</clipFolderPath>
          </li>
        </grains>
        <distRange>10~40</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>ChildBecomesAdult</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <onCamera>true</onCamera>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Birthdays/ChildBecomesAdult</clipFolderPath>
          </li>
        </grains>
        <distRange>10~40</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>ChildBirthday</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <onCamera>true</onCamera>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Birthdays/ChildBirthday</clipFolderPath>
          </li>
        </grains>
        <distRange>10~40</distRange>
      </li>
    </subSounds>
  </SoundDef>

</Defs>