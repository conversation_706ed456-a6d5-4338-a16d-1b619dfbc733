<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <RecipeDef Name="MediumMechanoidRecipe" ParentName="BaseMechanoidRecipe" Abstract="True">
    <gestationCycles>2</gestationCycles>
    <researchPrerequisite>StandardMechtech</researchPrerequisite>
  </RecipeDef>

  <RecipeDef ParentName="MediumMechanoidRecipe">
    <defName>Scyther</defName>
    <label>gestate scyther</label>
    <description>Gestate a scyther mechanoid.</description>
    <ingredients Inherit="False">
      <li>
        <filter>
          <thingDefs>
            <li>Steel</li>
          </thingDefs>
        </filter>
        <count>75</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>Plasteel</li>
          </thingDefs>
        </filter>
        <count>75</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>ComponentIndustrial</li>
          </thingDefs>
        </filter>
        <count>4</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>SubcoreRegular</li>
          </thingDefs>
        </filter>
        <count>1</count>
      </li>
    </ingredients>
    <products>
      <Mech_Scyther>1</Mech_Scyther>
    </products>
    <descriptionHyperlinks>
      <ThingDef>Mech_Scyther</ThingDef>
    </descriptionHyperlinks>
  </RecipeDef>

  <RecipeDef ParentName="MediumMechanoidRecipe">
    <defName>Pikeman</defName>
    <label>gestate pikeman</label>
    <description>Gestate a pikeman mechanoid.</description>
    <ingredients Inherit="False">
      <li>
        <filter>
          <thingDefs>
            <li>Steel</li>
          </thingDefs>
        </filter>
        <count>100</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>Plasteel</li>
          </thingDefs>
        </filter>
        <count>40</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>ComponentIndustrial</li>
          </thingDefs>
        </filter>
        <count>4</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>SubcoreRegular</li>
          </thingDefs>
        </filter>
        <count>1</count>
      </li>
    </ingredients>
    <products>
      <Mech_Pikeman>1</Mech_Pikeman>
    </products>
    <descriptionHyperlinks>
      <ThingDef>Mech_Pikeman</ThingDef>
    </descriptionHyperlinks>
  </RecipeDef>

  <RecipeDef ParentName="MediumMechanoidRecipe">
    <defName>Lancer</defName>
    <label>gestate lancer</label>
    <description>Gestate a lancer mechanoid.</description>
    <ingredients Inherit="False">
      <li>
        <filter>
          <thingDefs>
            <li>Steel</li>
          </thingDefs>
        </filter>
        <count>75</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>Plasteel</li>
          </thingDefs>
        </filter>
        <count>75</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>ComponentIndustrial</li>
          </thingDefs>
        </filter>
        <count>4</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>SubcoreRegular</li>
          </thingDefs>
        </filter>
        <count>1</count>
      </li>
    </ingredients>
    <products>
      <Mech_Lancer>1</Mech_Lancer>
    </products>
    <descriptionHyperlinks>
      <ThingDef>Mech_Lancer</ThingDef>
    </descriptionHyperlinks>
    <researchPrerequisite>HighMechtech</researchPrerequisite>
  </RecipeDef>

  <RecipeDef ParentName="CentipedeMechanoidRecipe">
    <defName>CentipedeBlaster</defName>
    <label>gestate centipede blaster</label>
    <description>Gestate a centipede mechanoid armed with a heavy charge blaster.</description>
    <researchPrerequisite>UltraMechtech</researchPrerequisite>
    <ingredients Inherit="False">
      <li>
        <filter>
          <thingDefs>
            <li>Steel</li>
          </thingDefs>
        </filter>
        <count>255</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>Plasteel</li>
          </thingDefs>
        </filter>
        <count>355</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>ComponentIndustrial</li>
          </thingDefs>
        </filter>
        <count>8</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>SubcoreHigh</li>
          </thingDefs>
        </filter>
        <count>1</count>
      </li>
    </ingredients>
    <products>
      <Mech_CentipedeBlaster>1</Mech_CentipedeBlaster>
    </products>
    <descriptionHyperlinks>
      <ThingDef>Mech_CentipedeBlaster</ThingDef>
    </descriptionHyperlinks>
  </RecipeDef>

  <!-- Legionary & Tesseron mechs -->
  <RecipeDef Name="WalkerMediumMechanoidRecipe" ParentName="MediumMechanoidRecipe" Abstract="True">
    <ingredients Inherit="False">
      <li>
        <filter>
          <thingDefs>
            <li>Plasteel</li>
          </thingDefs>
        </filter>
        <count>100</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>ComponentIndustrial</li>
          </thingDefs>
        </filter>
        <count>6</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>SubcoreHigh</li>
          </thingDefs>
        </filter>
        <count>1</count>
      </li>
    </ingredients>
    <researchPrerequisite>UltraMechtech</researchPrerequisite>
    <gestationCycles>4</gestationCycles>
  </RecipeDef>

  <RecipeDef ParentName="WalkerMediumMechanoidRecipe">
    <defName>Legionary</defName>
    <label>gestate legionary</label>
    <description>Gestate a legionary mechanoid.</description>
    <products>
      <Mech_Legionary>1</Mech_Legionary>
    </products>
    <descriptionHyperlinks>
      <ThingDef>Mech_Legionary</ThingDef>
    </descriptionHyperlinks>
  </RecipeDef>

  <RecipeDef ParentName="WalkerMediumMechanoidRecipe">
    <defName>Tesseron</defName>
    <label>gestate tesseron</label>
    <description>Gestate a tesseron mechanoid.</description>
    <ingredients Inherit="False">
      <li>
        <filter>
          <thingDefs>
            <li>Plasteel</li>
          </thingDefs>
        </filter>
        <count>110</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>ComponentIndustrial</li>
          </thingDefs>
        </filter>
        <count>7</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>SubcoreHigh</li>
          </thingDefs>
        </filter>
        <count>1</count>
      </li>
    </ingredients>
    <products>
      <Mech_Tesseron>1</Mech_Tesseron>
    </products>
    <descriptionHyperlinks>
      <ThingDef>Mech_Tesseron</ThingDef>
    </descriptionHyperlinks>
  </RecipeDef>

  <RecipeDef ParentName="MediumMechanoidRecipe">
    <defName>Scorcher</defName>
    <label>gestate scorcher</label>
    <description>Gestate a scorcher mechanoid.</description>
    <ingredients Inherit="False">
      <li>
        <filter>
          <thingDefs>
            <li>Steel</li>
          </thingDefs>
        </filter>
        <count>80</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>Plasteel</li>
          </thingDefs>
        </filter>
        <count>32</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>ComponentIndustrial</li>
          </thingDefs>
        </filter>
        <count>3</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>SubcoreRegular</li>
          </thingDefs>
        </filter>
        <count>1</count>
      </li>
    </ingredients>
    <products>
      <Mech_Scorcher>1</Mech_Scorcher>
    </products>
    <descriptionHyperlinks>
      <ThingDef>Mech_Scorcher</ThingDef>
    </descriptionHyperlinks>
  </RecipeDef>

</Defs>