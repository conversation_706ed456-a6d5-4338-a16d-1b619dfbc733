<?xml version="1.0" encoding="utf-8" ?>
<Defs>
  
  <ThingDef ParentName="MoteBase" Name="MechGestatorFormingBase" Abstract="True">
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.35</fadeInTime>
      <fadeOutTime>0.35</fadeOutTime>
      <solidTime>999999</solidTime>
      <needsMaintenance>True</needsMaintenance>
      <fadeOutUnmaintained>True</fadeOutUnmaintained>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <color>(1.0, 0.2, 0.15)</color>
      <shaderType>MoteMechGestatorGlow</shaderType>
      <shaderParameters>
        <_MistTex>/Things/Mote/SmokeTiled</_MistTex>
        <_MistScrollSpeed>0.2</_MistScrollSpeed>
      </shaderParameters>
    </graphicData>
  </ThingDef>

  <!-- Mech gestator - Forming -->
  <ThingDef Name="MechGestatorFormingSouth" ParentName="MechGestatorFormingBase">
    <defName>MechGestatorForming_South</defName>
    <graphicData>
      <texPath>Things/Building/Production/MechGestatorStandardGlassGlow_south</texPath>
      <shaderParameters>
        <_MistOpacityTex>/Things/Building/Production/MechGestatorStandardGlassGlowSoft_south</_MistOpacityTex>
        <_ScanOffset>-0.11</_ScanOffset>
        <_ScanScale>12.5</_ScanScale>
      </shaderParameters>
      <drawSize>(6, 4)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef Name="MechGestatorFormingEast" ParentName="MechGestatorFormingBase">
    <defName>MechGestatorForming_East</defName>
    <graphicData>
      <texPath>Things/Building/Production/MechGestatorStandardGlassGlow_east</texPath>
      <shaderParameters>
        <_MistOpacityTex>/Things/Building/Production/MechGestatorStandardGlassGlowSoft_east</_MistOpacityTex>
        <_ScanOffset>0.16</_ScanOffset>
        <_ScanScale>7</_ScanScale>
      </shaderParameters>
      <drawSize>(4, 6)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef Name="MechGestatorFormingNorth" ParentName="MechGestatorFormingBase">
    <defName>MechGestatorForming_North</defName>
    <graphicData>
      <texPath>Things/Building/Production/MechGestatorStandardGlassGlow_north</texPath>
      <shaderParameters>
        <_MistOpacityTex>/Things/Building/Production/MechGestatorStandardGlassGlowSoft_north</_MistOpacityTex>
        <_ScanOffset>-0.11</_ScanOffset>
        <_ScanScale>12.5</_ScanScale>
      </shaderParameters>
      <drawSize>(6, 4)</drawSize>
    </graphicData>
  </ThingDef>

  <!-- Mech gestator - Cycle complete -->
  <ThingDef ParentName="MechGestatorFormingSouth">
    <defName>MechGestatorCycleComplete_South</defName>
    <graphicData>
      <color>(1, 0.36, 0)</color>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MechGestatorFormingEast">
    <defName>MechGestatorCycleComplete_East</defName>
    <graphicData>
      <color>(1, 0.36, 0)</color>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MechGestatorFormingNorth">
    <defName>MechGestatorCycleComplete_North</defName>
    <graphicData>
      <color>(1, 0.36, 0)</color>
    </graphicData>
  </ThingDef>

  <!-- Mech gestator - Formed -->
  <ThingDef ParentName="MechGestatorFormingSouth">
    <defName>MechGestatorFormed_South</defName>
    <graphicData>
      <color>(0.2, 1, 0.15)</color>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MechGestatorFormingEast">
    <defName>MechGestatorFormed_East</defName>
    <graphicData>
      <color>(0.2, 1, 0.15)</color>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MechGestatorFormingNorth">
    <defName>MechGestatorFormed_North</defName>
    <graphicData>
      <color>(0.2, 1, 0.15)</color>
    </graphicData>
  </ThingDef>

  <!-- Large mech gestator - Forming -->
  <ThingDef Name="MechGestatorFormingLargeSouth" ParentName="MechGestatorFormingBase">
    <defName>MechGestatorFormingLarge_South</defName>
    <graphicData>
      <texPath>Things/Building/Production/MechGestatorLargeGlassGlow_south</texPath>
      <shaderParameters>
        <_MistOpacityTex>/Things/Building/Production/MechGestatorLargeGlassGlowSoft_south</_MistOpacityTex>
        <_ScanOffset>-0.09</_ScanOffset>
        <_ScanScale>12</_ScanScale>
      </shaderParameters>
      <drawSize>(5.7, 4.275)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef Name="MechGestatorFormingLargeEast" ParentName="MechGestatorFormingBase">
    <defName>MechGestatorFormingLarge_East</defName>
    <graphicData>
      <texPath>Things/Building/Production/MechGestatorLargeGlassGlow_east</texPath>
      <shaderParameters>
        <_MistOpacityTex>/Things/Building/Production/MechGestatorLargeGlassGlowSoft_east</_MistOpacityTex>
        <_ScanOffset>-0.05</_ScanOffset>
        <_ScanScale>11.25</_ScanScale>
      </shaderParameters>
      <drawSize>(4.275, 5.7)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef Name="MechGestatorFormingLargeWest" ParentName="MechGestatorFormingLargeEast">
    <defName>MechGestatorFormingLarge_West</defName>
    <graphicData>
      <texPath>Things/Building/Production/MechGestatorLargeGlassGlow_west</texPath>
      <shaderParameters>
        <_MistOpacityTex>/Things/Building/Production/MechGestatorLargeGlassGlowSoft_west</_MistOpacityTex>
      </shaderParameters>
    </graphicData>
  </ThingDef>

  <ThingDef Name="MechGestatorFormingLargeNorth" ParentName="MechGestatorFormingBase">
    <defName>MechGestatorFormingLarge_North</defName>
    <graphicData>
      <texPath>Things/Building/Production/MechGestatorLargeGlassGlow_north</texPath>
      <shaderParameters>
        <_MistOpacityTex>/Things/Building/Production/MechGestatorLargeGlassGlowSoft_north</_MistOpacityTex>
        <_ScanOffset>-0.09</_ScanOffset>
        <_ScanScale>12</_ScanScale>
      </shaderParameters>
      <drawSize>(5.7, 4.275)</drawSize>
    </graphicData>
  </ThingDef>

  <!-- Large mech gestator - Cycle complete -->
  <ThingDef ParentName="MechGestatorFormingLargeSouth">
    <defName>MechGestatorCycleCompleteLarge_South</defName>
    <graphicData>
      <color>(1, 0.36, 0)</color>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MechGestatorFormingLargeEast">
    <defName>MechGestatorCycleCompleteLarge_East</defName>
    <graphicData>
      <color>(1, 0.36, 0)</color>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MechGestatorFormingLargeWest">
    <defName>MechGestatorCycleCompleteLarge_West</defName>
    <graphicData>
      <color>(1, 0.36, 0)</color>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MechGestatorFormingLargeNorth">
    <defName>MechGestatorCycleCompleteLarge_North</defName>
    <graphicData>
      <color>(1, 0.36, 0)</color>
    </graphicData>
  </ThingDef>

  <!-- Large mech gestator - Formed -->
  <ThingDef ParentName="MechGestatorFormingLargeSouth">
    <defName>MechGestatorFormedLarge_South</defName>
    <graphicData>
      <color>(0.2, 1, 0.15)</color>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MechGestatorFormingLargeEast">
    <defName>MechGestatorFormedLarge_East</defName>
    <graphicData>
      <color>(0.2, 1, 0.15)</color>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MechGestatorFormingLargeWest">
    <defName>MechGestatorFormedLarge_West</defName>
    <graphicData>
      <color>(0.2, 1, 0.15)</color>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MechGestatorFormingLargeNorth">
    <defName>MechGestatorFormedLarge_North</defName>
    <graphicData>
      <color>(0.2, 1, 0.15)</color>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_PollutionPump</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.2</fadeInTime>
      <fadeOutTime>0.5</fadeOutTime>
      <solidTime>2.5</solidTime>
      <growthRate>1.9</growthRate>
    </mote>
    <graphicData>
      <texPath>Things/Mote/Pollution</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <color>(0.18, 0.13, 0.20, 1.0)</color>
      <shaderType>MotePollutionPump</shaderType>
      <shaderParameters>
        <_MultiplyTex>/Things/Mote/RadiationDistortion_A</_MultiplyTex>
        <_FadeTex>/Things/Mote/PollutionWave</_FadeTex>
        <_texAScale>0.4</_texAScale>
        <_texBScale>0.4</_texBScale>
        <_texAScrollSpeed>0.15</_texAScrollSpeed>
        <_texBScrollSpeed>-0.4</_texBScrollSpeed>
        <_Intensity>32</_Intensity>
      </shaderParameters>
      <drawSize>(2.8, 2.8)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_GraserBeamBase</defName>
    <thingClass>MoteDualAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.2</fadeInTime>
      <fadeOutTime>0.3</fadeOutTime>
      <solidTime>999999</solidTime>
      <needsMaintenance>True</needsMaintenance>
      <rotateTowardsTarget>True</rotateTowardsTarget>
      <scaleToConnectTargets>True</scaleToConnectTargets>
      <fadeOutUnmaintained>True</fadeOutUnmaintained>
    </mote>
    <drawOffscreen>true</drawOffscreen>
    <graphicData>
      <texPath>Things/Mote/GraserBeam</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>MoteBeam</shaderType>
      <shaderParameters>
        <_ExtraTexA>/Things/Mote/BeamSecondaryNoise_A</_ExtraTexA>
        <_ExtraTexB>/Things/Mote/BeamSecondaryNoise_B</_ExtraTexB>
        <_ScrollSpeedA>-4 </_ScrollSpeedA>
        <_ScrollSpeedB>3</_ScrollSpeedB>
        <_Intensity>2</_Intensity>
      </shaderParameters>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_MechCharging</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.35</fadeInTime>
      <fadeOutTime>0.25</fadeOutTime>
      <solidTime>999999</solidTime>
      <needsMaintenance>True</needsMaintenance>
    </mote>
    <graphicData>
      <graphicClass>Graphic_PawnBodySilhouette</graphicClass>
      <color>(0.35, 0.52, 0.85, 1.0)</color>
      <shaderType>MoteMultiplyAddScroll</shaderType>
      <texPath>Things/Mote/Transparent</texPath>
      <shaderParameters>
        <_MultiplyTexA>/Things/Mote/Cloudy_A</_MultiplyTexA>
        <_MultiplyTexB>/Things/Mote/Cloudy_C</_MultiplyTexB>
        <_DetailTex>/Things/Mote/Electicity</_DetailTex>
        <_detailIntensity>1.3</_detailIntensity>
        <_texAScale>0.3</_texAScale>
        <_texBScale>0.35</_texBScale>
        <_DetailScale>0.75</_DetailScale>
        <_texAScrollSpeed>(0, 0.15, 0)</_texAScrollSpeed>
        <_texBScrollSpeed>(0.1, -0.15, 0)</_texBScrollSpeed>
        <_detailScrollSpeed>(0.2, 0.0, 0)</_detailScrollSpeed>
        <_DetailOffset>(0.345, 0.0)</_DetailOffset>
        <_DetailDistortion>0.1</_DetailDistortion>
      </shaderParameters>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_ChargingCablesPulse</defName>
    <thingClass>MoteDualAttached</thingClass>
    <altitudeLayer>BuildingOnTop</altitudeLayer>
    <mote>
      <fadeInTime>0.5</fadeInTime>
      <fadeOutTime>0.3</fadeOutTime>
      <solidTime>999999</solidTime>
      <needsMaintenance>True</needsMaintenance>
      <rotateTowardsTarget>True</rotateTowardsTarget>
      <scaleToConnectTargets>True</scaleToConnectTargets>
    </mote>
    <graphicData>
      <texPath>Things/Mote/ElectricPulse</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>MoteChargingPulse</shaderType>
      <shaderParameters>
        <_DistortionTex>/Things/Mote/Cloudy_C</_DistortionTex>
        <_DistortionIntensity>0.12</_DistortionIntensity>
        <_Delay>0.8</_Delay>
      </shaderParameters>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_HellsphereCannon_Aim</defName>
    <thingClass>MoteDualAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.15</fadeInTime>
      <fadeOutTime>0.3</fadeOutTime>
      <solidTime>999999</solidTime>
      <needsMaintenance>True</needsMaintenance>
      <rotateTowardsTarget>True</rotateTowardsTarget>
      <scaleToConnectTargets>True</scaleToConnectTargets>
      <fadeOutUnmaintained>True</fadeOutUnmaintained>
    </mote>
    <graphicData>
      <texPath>Things/Mote/HellsphereCannon_Aim</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>MoteHellfireCannon_Aim</shaderType>
      <shaderParameters>
        <_DistortionTex>/Things/Mote/Cloudy_C</_DistortionTex>
        <_NumFrames>10</_NumFrames>
        <_FramesPerSec>1.35</_FramesPerSec>
        <_DistortionScrollSpeed>1.35</_DistortionScrollSpeed>
        <_Intensity>3</_Intensity>
        <_DistortionScale>0.082</_DistortionScale>
      </shaderParameters>
      <drawSize>(3, 0.9)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_HellsphereCannon_Charge</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.15</fadeInTime>
      <fadeOutTime>0.3</fadeOutTime>
      <solidTime>999999</solidTime>
      <needsMaintenance>True</needsMaintenance>
    </mote>
    <graphicData>
      <texPath>Things/Mote/HellsphereCannon_ChargeGlow</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>MoteHellfireCannon_Charge</shaderType>
      <shaderParameters>
        <_RandomTex>/Things/Mote/RandomFlicker</_RandomTex>
        <_FlickerFrequency>0.25</_FlickerFrequency>
        <_FlickerAmount>0.6</_FlickerAmount>
        <_InnerCircleIntensity>0.1</_InnerCircleIntensity>
        <_InnerCircleSize>0.4</_InnerCircleSize>
        <_InnerCircleShimmerAmount>0.14</_InnerCircleShimmerAmount>
      </shaderParameters>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_HellsphereCannon_Target</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.5</fadeInTime>
      <fadeOutTime>0.3</fadeOutTime>
      <solidTime>999999</solidTime>
      <needsMaintenance>True</needsMaintenance>
    </mote>
    <graphicData>
      <texPath>Things/Mote/MoteHellfireCannon_Target</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>MoteHellfireCannon_Target</shaderType>
      <shaderParameters>
        <_ScanTex>/Things/Mote/MoteHellfireCannon_Target_Scan</_ScanTex>
        <_ScanMask>/Things/Mote/MoteHellfireCannon_Target_Scan_Mask</_ScanMask>
      </shaderParameters>
      <drawSize>(8, 8)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase" Name="SubcoreScannerMoteBase" Abstract="True">
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.35</fadeInTime>
      <fadeOutTime>0.35</fadeOutTime>
      <solidTime>999999</solidTime>
      <needsMaintenance>True</needsMaintenance>
      <fadeOutUnmaintained>True</fadeOutUnmaintained>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <color>(1.0, 0.3, 0.2, 0.85)</color>
      <shaderType>MoteSoftScannerGlow</shaderType>
      <shaderParameters>
        <_ScanSpeed>1.0</_ScanSpeed>
        <_ScanPow>7</_ScanPow>
      </shaderParameters>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="SubcoreScannerMoteBase">
    <defName>SoftScannerGlow_North</defName>
    <graphicData>
      <texPath>Things/Mote/SoftScannerGlow_north</texPath>
      <drawSize>(3, 2)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="SubcoreScannerMoteBase">
    <defName>SoftScannerGlow_South</defName>
    <graphicData>
      <texPath>Things/Mote/SoftScannerGlow_south</texPath>
      <drawSize>(3, 2)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="SubcoreScannerMoteBase">
    <defName>SoftScannerGlow_East</defName>
    <graphicData>
      <texPath>Things/Mote/SoftScannerGlow_east</texPath>
      <drawSize>(2, 3)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="SubcoreScannerMoteBase">
    <defName>SoftScannerGlow_West</defName>
    <graphicData>
      <texPath>Things/Mote/SoftScannerGlow_east</texPath>
      <drawSize>(2, 3)</drawSize>
      <shaderParameters>
        <_FlipHorizontally>1</_FlipHorizontally>
      </shaderParameters>
    </graphicData>
  </ThingDef>

  <ThingDef Name="SubcoreRipscannerMoteBase" ParentName="SubcoreScannerMoteBase" Abstract="True">
    <graphicData>
      <color>(1.0, 0.26, 0.1, 1.2)</color>
      <shaderParameters>
        <_ScanSpeed>1.5</_ScanSpeed>
        <_ScanPow>9</_ScanPow>
      </shaderParameters>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="SubcoreRipscannerMoteBase">
    <defName>RipScannerGlow_North</defName>
    <graphicData>
      <texPath>Things/Mote/RipScannerGlow_north</texPath>
      <drawSize>(3, 2)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="SubcoreRipscannerMoteBase">
    <defName>RipScannerGlow_South</defName>
    <graphicData>
      <texPath>Things/Mote/RipScannerGlow_south</texPath>
      <drawSize>(3, 2)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="SubcoreRipscannerMoteBase">
    <defName>RipScannerGlow_East</defName>
    <graphicData>
      <texPath>Things/Mote/RipScannerGlow_east</texPath>
      <drawSize>(2, 3)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="SubcoreRipscannerMoteBase">
    <defName>RipScannerGlow_West</defName>
    <graphicData>
      <texPath>Things/Mote/RipScannerGlow_east</texPath>
      <drawSize>(2, 3)</drawSize>
      <shaderParameters>
        <_FlipHorizontally>1</_FlipHorizontally>
      </shaderParameters>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_RipscannerHeadGlow</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>BuildingBelowTop</altitudeLayer>
    <mote>
      <fadeInTime>0.2</fadeInTime>
      <fadeOutTime>0.2</fadeOutTime>
      <solidTime>0.1</solidTime>
      <growthRate>0.2</growthRate>
    </mote>
    <graphicData>
      <shaderType>MoteGlow</shaderType>
      <texPath>Things/Mote/RipscannerHeadGlow</texPath>
      <drawSize>(1.2, 1.2)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef Abstract="True" Name="SubcoreEncoderMoteBase" ParentName="MoteBase">
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>BuildingBelowTop</altitudeLayer>
    <mote>
      <fadeInTime>0.2</fadeInTime>
      <fadeOutTime>0.2</fadeOutTime>
      <solidTime>99999</solidTime>
      <needsMaintenance>true</needsMaintenance>
    </mote>
    <graphicData>
      <shaderType>MoteSubcoreEncoder</shaderType>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderParameters>
        <_DetailTex>/Things/Mote/SubcoreEncoder_Detail</_DetailTex>
        <_ScanIntensity>4</_ScanIntensity>
      </shaderParameters>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="SubcoreEncoderMoteBase">
    <defName>Mote_SubcoreEncoderNorth</defName>
    <graphicData>
      <texPath>Things/Mote/SubcoreEncoder_North</texPath>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="SubcoreEncoderMoteBase">
    <defName>Mote_SubcoreEncoderSouth</defName>
    <graphicData>
      <texPath>Things/Mote/SubcoreEncoder_South</texPath>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="SubcoreEncoderMoteBase">
    <defName>Mote_SubcoreEncoderEast</defName>
    <graphicData>
      <texPath>Things/Mote/SubcoreEncoder_East</texPath>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_MechbandAntennaWaves</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.2</fadeInTime>
      <fadeOutTime>0.4</fadeOutTime>
      <solidTime>1.5</solidTime>
    </mote>
    <graphicData>
      <texPath>Things/Mote/MechbandDishWaves</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <color>(0.65, 0.8, 0.65)</color>
      <shaderType>MoteGlowCircularScrolling</shaderType>
      <shaderParameters>
        <_MaskTex>/Things/Mote/PollutionWave</_MaskTex>
        <_DistortionTex>/Things/Mote/MechBandDistortion</_DistortionTex>
        <_ScrollSpeed>-0.9</_ScrollSpeed>
        <_ScrollScale>0.22</_ScrollScale>
        <_DistortionIntensity>0.3</_DistortionIntensity>
      </shaderParameters>
      <drawSize>(8, 8)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_MechbandDishWaves</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.2</fadeInTime>
      <fadeOutTime>0.4</fadeOutTime>
      <solidTime>1.6</solidTime>
    </mote>
    <graphicData>
      <texPath>Things/Mote/MechbandDishWaves</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <color>(0.8, 0.45, 0.45)</color>
      <shaderType>MoteGlowCircularScrolling</shaderType>
      <shaderParameters>
        <_MaskTex>/Things/Mote/PollutionWave</_MaskTex>
        <_ScrollSpeed>-1.2</_ScrollSpeed>
        <_ScrollScale>0.22</_ScrollScale>
      </shaderParameters>
      <drawSize>(10, 10)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_MechbandPreparing</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.1</fadeInTime>
      <solidTime>0.15</solidTime>
      <fadeOutTime>0.8</fadeOutTime>
    </mote>
    <graphicData>
      <texPath>Things/Mote/AntennaPreparingFlash</texPath>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_WarhUrchinSpawnElectricity</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.05</fadeInTime>
      <fadeOutTime>0.1</fadeOutTime>
      <solidTime>0.8</solidTime>
    </mote>
    <graphicData>
      <texPath>Things/Mote/RadialElectricityPulseAnimation</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>GlowAnimated</shaderType>
      <shaderParameters>
        <_NumFrames>5</_NumFrames>
        <_FramesPerSec>7</_FramesPerSec>
      </shaderParameters>
      <drawSize>(1.1, 1.1)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_WarUrchinSpawnFlash</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.1</fadeInTime>
      <solidTime>0.15</solidTime>
      <fadeOutTime>0.5</fadeOutTime>
    </mote>
    <graphicData>
      <texPath>Things/Mote/BrightFlash</texPath>
      <shaderType>MoteGlow</shaderType>
      <color>(1, 0.34, 0.2)</color>
      <drawSize>(1.2, 1.2)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_WarQueenSpineGlow</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.1</fadeInTime>
      <solidTime>0.15</solidTime>
      <fadeOutTime>0.7</fadeOutTime>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithParentRotation</graphicClass>
      <texPath>Things/Mote/Warqueen_SpineGlow</texPath>
      <shaderType>MoteGlowParentRotation</shaderType>
      <drawSize>(3, 3)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_MechTunnelerSlowedDown</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.2</fadeInTime>
      <solidTime>0.0</solidTime>
      <fadeOutTime>0.35</fadeOutTime>
    </mote>
    <graphicData>
      <graphicClass>Graphic_PawnBodySilhouette</graphicClass>
      <color>(0.65, 0.7, 0.75, 0.47)</color>
      <shaderType>MotePawnBodyColor</shaderType>
      <texPath>Things/Mote/Transparent</texPath>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_NewbornBecomeChildGlow</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.1</fadeInTime>
      <solidTime>0.2</solidTime>
      <fadeOutTime>1</fadeOutTime>
    </mote>
    <graphicData>
      <graphicClass>Graphic_PawnBodySilhouette</graphicClass>
      <color>(1, 0.9, 0.6, 1)</color>
      <shaderType>MotePawnBodyGlow</shaderType>
      <texPath>Things/Mote/Transparent</texPath>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_MechUncontrolled</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>2</fadeInTime>
      <solidTime>0</solidTime>
      <fadeOutTime>1</fadeOutTime>
    </mote>
    <graphicData>
      <graphicClass>Graphic_PawnBodySilhouette</graphicClass>
      <color>(0.8, 0, 0, 0.35)</color>
      <shaderType>MotePawnBodyColor</shaderType>
      <texPath>Things/Mote/Transparent</texPath>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_MechResurrectWarmupOnTarget</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.25</fadeInTime>
      <fadeOutTime>0.25</fadeOutTime>
      <solidTime>999999</solidTime>
      <needsMaintenance>True</needsMaintenance>
      <fadeOutUnmaintained>True</fadeOutUnmaintained>
    </mote>
    <graphicData>
      <graphicClass>Graphic_PawnBodySilhouette</graphicClass>
      <color>(0.85, 0.32, 0.25, 1.0)</color>
      <shaderType>MoteMultiplyAddScroll</shaderType>
      <texPath>Things/Mote/Transparent</texPath>
      <shaderParameters>
        <_MultiplyTexA>/Things/Mote/Cloudy_A</_MultiplyTexA>
        <_MultiplyTexB>/Things/Mote/Cloudy_C</_MultiplyTexB>
        <_DistortionTex>/Things/Mote/Cloudy_C</_DistortionTex>
        <_DetailTex>/Things/Mote/Electicity</_DetailTex>
        <_detailIntensity>1.3</_detailIntensity>
        <_texAScale>0.3</_texAScale>
        <_texBScale>0.35</_texBScale>
        <_DetailScale>0.9</_DetailScale>
        <_texAScrollSpeed>(0, 0.15, 0)</_texAScrollSpeed>
        <_texBScrollSpeed>(0.1, -0.15, 0)</_texBScrollSpeed>
        <_DetailScrollSpeed>(0.0, 0.25, 0)</_DetailScrollSpeed>
        <_DistortionScrollSpeed>(0.3, 0.75, 0)</_DistortionScrollSpeed>
        <_DetailOffset>(0.1, 0.0)</_DetailOffset>
        <_DetailDistortion>0.3</_DetailDistortion>
        <_OutlineMultiplier>0.42</_OutlineMultiplier>
      </shaderParameters>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteGlowDistorted">
    <defName>Mote_ApocritonWarmupArea</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.2</fadeInTime>
      <fadeOutTime>0.3</fadeOutTime>
      <solidTime>99999</solidTime>
      <needsMaintenance>true</needsMaintenance>
      <fadeOutUnmaintained>true</fadeOutUnmaintained>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>MoteApocritonPulse</shaderType>
      <texPath>Things/Mote/PsychicDistortionApocriton</texPath>
      <shaderParameters>
        <_PulseTex>/Things/Mote/ApocritonWaves</_PulseTex>
        <_distortionIntensity>0</_distortionIntensity>
        <_ScrollSpeed>-0.9</_ScrollSpeed>
        <_ScrollScale>0.22</_ScrollScale>
        <_distortionTint>(0.23, 0, 0, 0)</_distortionTint>
        <_pulseSpeed>2</_pulseSpeed>
      </shaderParameters>
      <drawSize>10</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_MechResurrectGlow</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.1</fadeInTime>
      <solidTime>0.1</solidTime>
      <fadeOutTime>0.6</fadeOutTime>
    </mote>
    <graphicData>
      <graphicClass>Graphic_PawnBodySilhouette</graphicClass>
      <color>(1, 0.35, 0.25, 1)</color>
      <shaderType>MotePawnBodyGlow</shaderType>
      <texPath>Things/Mote/Transparent</texPath>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_MechResurrectFlash</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.05</fadeInTime>
      <fadeOutTime>0.25</fadeOutTime>
      <solidTime>0.15</solidTime> 
      <growthRate>2.5</growthRate>
    </mote>
    <graphicData>
      <texPath>Things/Mote/RadiationDistortion_A</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <color>(0.85, 0.35, 0.25, 1.0)</color>
      <shaderType>MoteMultiplyAddCircularGrayscale</shaderType>
      <shaderParameters>
        <_MultiplyTex>/Things/Mote/RadiationDistortion_B</_MultiplyTex>
        <_texAScale>0.3</_texAScale>
        <_texBScale>0.3</_texBScale>
        <_texAScrollSpeed>1.5</_texAScrollSpeed>
        <_texBScrollSpeed>-0.8</_texBScrollSpeed>
        <_Intensity>11</_Intensity>
      </shaderParameters>
      <drawSize>(1.4, 1.4)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_XenogermImplantation</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.2</fadeInTime>
      <solidTime>99999</solidTime>
      <fadeOutTime>0.05</fadeOutTime>
      <needsMaintenance>True</needsMaintenance>
    </mote>
    <graphicData>
      <shaderType>Transparent</shaderType>
      <texPath>Things/Mote/Xenogerm_Implantation</texPath>
      <drawSize>0.8</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_MechControlTakingSparkArch</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <drawOffscreen>true</drawOffscreen>
    <mote>
      <fadeInTime>0.05</fadeInTime>
      <fadeOutTime>0.1</fadeOutTime>
      <solidTime>1</solidTime>
    </mote>
    <graphicData>
      <texPath>Things/Mote/MechSparkArch</texPath>
      <graphicClass>GraphicMote_RandomWithAgeSecs</graphicClass>
      <shaderType>GlowAnimated</shaderType>
      <shaderParameters>
        <_NumFrames>7</_NumFrames>
        <_FramesPerSec>6</_FramesPerSec>
      </shaderParameters>
    </graphicData>
  </ThingDef>
  
  <ThingDef ParentName="MoteBase">
    <defName>Mote_RedFlash</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.03</fadeInTime>
      <fadeOutTime>0.06</fadeOutTime>
      <solidTime>0</solidTime>
    </mote>
    <graphicData>
      <texPath>Things/Mote/BrightFlash</texPath>
      <shaderType>MoteGlow</shaderType>
      <color>(1, 0.34, 0.2, 0.35)</color>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_RedFlashStrong</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.1</fadeInTime>
      <fadeOutTime>0.13</fadeOutTime>
      <solidTime>0.1</solidTime>
    </mote>
    <graphicData>
      <texPath>Things/Mote/BrightFlash</texPath>
      <shaderType>MoteGlow</shaderType>
      <color>(1, 0.34, 0.2, 0.75)</color>
    </graphicData>
  </ThingDef>
  
  <ThingDef ParentName="MoteBase">
    <defName>Mote_CoagulateStencil</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>Terrain</altitudeLayer>
    <mote>
      <fadeInTime>0</fadeInTime>
      <fadeOutTime>0</fadeOutTime>
      <solidTime>999999</solidTime>
      <needsMaintenance>True</needsMaintenance>
    </mote>
    <graphicData>
      <graphicClass>Graphic_PawnBodySilhouette</graphicClass>
      <shaderType>PawnSilhouetteStencil</shaderType>
      <texPath>Things/Mote/Transparent</texPath>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase" Name="MoteCoagulateBase" Abstract="True">
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <solidTime>0.45</solidTime>
      <fadeInTime>0.1</fadeInTime>
      <fadeOutTime>0.2</fadeOutTime>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>PawnSilhouetteStencilColorAnimated</shaderType>
      <drawSize>(0.65, 0.65)</drawSize>
      <shaderParameters>
        <_NumFrames>5</_NumFrames>
        <_FramesPerSec>7.5</_FramesPerSec>
      </shaderParameters>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteCoagulateBase">
    <defName>Mote_CoagulateA</defName>
    <graphicData>
      <texPath>Things/Mote/Coagulate/CoagulateA</texPath>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteCoagulateBase">
    <defName>Mote_CoagulateB</defName>
    <graphicData>
      <texPath>Things/Mote/Coagulate/CoagulateA</texPath>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteCocoonBreakingBase">
    <defName>Mote_CocoonBreaking</defName>
    <graphicData>
      <color>(140, 142, 100, 150)</color>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteCocoonBreakingBase">
    <defName>Mote_CocoonDestroyedMist</defName>
    <mote>
      <solidTime>0.2</solidTime>
      <fadeOutTime>1.3</fadeOutTime>
    </mote>
    <graphicData>
      <color>(140, 152, 130, 120)</color>
      <shaderType>Transparent</shaderType>
      <drawSize>(1.4, 1.4)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_CocoonDestroyedFlyingSlime</defName>
    <thingClass>MoteThrown</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.03</fadeInTime>
      <fadeOutTime>0.2</fadeOutTime>
      <solidTime>0.4</solidTime>
      <growthRate>2</growthRate>
      <archHeight>0.5</archHeight>
      <archDuration>0.5</archDuration>
      <speedPerTime>-3</speedPerTime>
    </mote>
    <graphicData>
      <texPath>Things/Filth/Spatter</texPath>
      <graphicClass>Graphic_MoteRandom</graphicClass>
      <color>(180, 182, 125, 170)</color>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_CocoonDestroyedFlyingPiece</defName>
    <thingClass>MoteThrown</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.03</fadeInTime>
      <fadeOutTime>0.1</fadeOutTime>
      <solidTime>0.87</solidTime>
      <archHeight>0.5</archHeight>
      <archDuration>0.5</archDuration>
      <speedPerTime>-3</speedPerTime>
    </mote>
    <graphicData>
      <texPath>Things/Mote/CocoonPieces</texPath>
      <graphicClass>Graphic_MoteRandom</graphicClass>
      <drawSize>0.4</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_SanguophageMeetingSmoke</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>Weather</altitudeLayer>
    <mote>
      <solidTime>99999</solidTime>
      <fadeInTime>0.4</fadeInTime>
      <fadeOutTime>1</fadeOutTime>
      <needsMaintenance>True</needsMaintenance>
      <fadeOutUnmaintained>True</fadeOutUnmaintained>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>RitualSmokeNoMask</shaderType>
      <texPath>Things/Mote/RitualEffects/Smoke2_Thick</texPath>
      <color>(35, 14, 18)</color>
      <shaderParameters>
        <_SmokeTex1>/Things/Mote/RitualEffects/Smoke1_Thick</_SmokeTex1>
        <_SmokeTex2>/Things/Mote/RitualEffects/Smoke2_Thick</_SmokeTex2>
        <_MaskTex>/Things/Mote/RitualEffects/GlowRayMask</_MaskTex>
        <_DistortionTex>/Things/Mote/RitualEffects/DistortionSoft</_DistortionTex>
        <_ScrollSpeed>0.016</_ScrollSpeed>
        <_Thickness>19</_Thickness>
        <_Distortion>0.6</_Distortion>
      </shaderParameters>
      <drawSize>(23.5, 23.5)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_Deathresting</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.35</fadeInTime>
      <fadeOutTime>1.8</fadeOutTime>
      <solidTime>0.1</solidTime>
    </mote>
    <graphicData>
      <graphicClass>Graphic_PawnBodySilhouette</graphicClass>
      <color>(1, 0.4, 0.4, 0.35)</color>
      <shaderType>MotePawnBodyColor</shaderType>
      <texPath>Things/Mote/Transparent</texPath>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_VatGlowHorizontal</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>1</fadeInTime>
      <fadeOutTime>1</fadeOutTime>
      <solidTime>0.2</solidTime>
    </mote>
    <graphicData>
      <shaderType>MoteGlow</shaderType>
      <texPath>Things/Building/Misc/GrowthVat/GrowthVatGlow_horizontal</texPath>
      <drawSize>(2, 1)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_VatGlowVertical</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>1</fadeInTime>
      <fadeOutTime>1</fadeOutTime>
      <solidTime>0.2</solidTime>
    </mote>
    <graphicData>
      <shaderType>MoteGlow</shaderType>
      <texPath>Things/Building/Misc/GrowthVat/GrowthVatGlow_vertical</texPath>
      <drawSize>(1, 2)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_MechbandElectricity</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.05</fadeInTime>
      <fadeOutTime>0.1</fadeOutTime>
      <solidTime>0.8</solidTime>
    </mote>
    <graphicData>
      <texPath>Things/Mote/RadialElectricityPulseAnimation</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>GlowAnimated</shaderType>
      <shaderParameters>
        <_NumFrames>5</_NumFrames>
        <_FramesPerSec>10</_FramesPerSec>
      </shaderParameters>
      <drawSize>(1.1, 1.1)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteGlowDistorted">
    <defName>Mote_ApocritonPulse</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.2</fadeInTime>
      <fadeOutTime>0.3</fadeOutTime>
      <solidTime>0.5</solidTime>
      <growthRate>4</growthRate>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>MoteApocritonPulse</shaderType>
      <texPath>Things/Mote/PsychicDistortionApocriton</texPath>
      <shaderParameters>
        <_PulseTex>/Things/Mote/ApocritonWaves</_PulseTex>
        <_distortionIntensity>0.18</_distortionIntensity>
        <_ScrollSpeed>-0.9</_ScrollSpeed>
        <_ScrollScale>0.14</_ScrollScale>
        <_distortionTint>(0.23, 0, 0, 0)</_distortionTint>
        <_pulseSpeed>2</_pulseSpeed>
      </shaderParameters>
      <drawSize>1.5</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_MechSparkSimple</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.05</fadeInTime>
      <fadeOutTime>0.1</fadeOutTime>
      <solidTime>0.35</solidTime>
    </mote>
    <graphicData>
      <texPath>Things/Mote/MechSparkSimple</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>GlowAnimated</shaderType>
      <shaderParameters>
        <_NumFrames>4</_NumFrames>
        <_FramesPerSec>8</_FramesPerSec>
      </shaderParameters>
      <drawSize>(1.1, 1.1)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_AnimalWarcallFlash</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverheadLow</altitudeLayer>
    <mote>
      <fadeInTime>0.05</fadeInTime>
      <fadeOutTime>0.3</fadeOutTime>
      <solidTime>0.15</solidTime>
      <growthRate>4</growthRate>
    </mote>
    <graphicData>
      <texPath>Things/Mote/RadiationDistortion_A</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <color>(0.85, 0.32, 0.25, 1)</color>
      <shaderType>MoteMultiplyAddCircularGrayscale</shaderType>
      <shaderParameters>
        <_MultiplyTex>/Things/Mote/RadiationDistortion_B</_MultiplyTex>
        <_texAScale>0.3</_texAScale>
        <_texBScale>0.3</_texBScale>
        <_texAScrollSpeed>0.8</_texAScrollSpeed>
        <_texBScrollSpeed>-0.4</_texBScrollSpeed>
        <_texAScaleDist>0.5</_texAScaleDist>
        <_DistortionScale>0.6</_DistortionScale>
        <_Intensity>10</_Intensity>
      </shaderParameters>
      <drawSize>(2.5, 2.5)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_AnimalWarcallDistortion</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.1</fadeInTime>
      <fadeOutTime>0.3</fadeOutTime>
      <solidTime>0.3</solidTime>
      <growthRate>12</growthRate>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>MotePsychicWarmupNeuroquake</shaderType>
      <texPath>Things/Mote/MoteHellfireCannon_HeatWaveDistortion</texPath>
      <shaderParameters>
        <_distortionIntensity>0.3</_distortionIntensity>
        <_brightnessMultiplier>1.3</_brightnessMultiplier>
        <_distortionTint>(0.15, 0, 0, 0)</_distortionTint>
        <_pulseSpeed>1</_pulseSpeed>
      </shaderParameters>
      <drawSize>2</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_AnimalWarcallTarget</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.1</fadeInTime>
      <fadeOutTime>0.3</fadeOutTime>
      <solidTime>0.3</solidTime>
      <growthRate>2</growthRate>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>MotePsychicWarmupNeuroquake</shaderType>
      <texPath>Things/Mote/MoteHellfireCannon_HeatWaveDistortion</texPath>
      <shaderParameters>
        <_distortionIntensity>0.5</_distortionIntensity>
        <_brightnessMultiplier>1.3</_brightnessMultiplier>
        <_distortionTint>(0.35, 0, 0, 0)</_distortionTint>
        <_pulseSpeed>1</_pulseSpeed>
      </shaderParameters>
      <drawSize>2</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_AnimalWarcallWarmup</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.1</fadeInTime>
      <fadeOutTime>0.2</fadeOutTime>
      <solidTime>1.25</solidTime>
      <growthRate>-0.4</growthRate>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>MotePsychicWarmupNeuroquake</shaderType>
      <texPath>Things/Mote/MoteHellfireCannon_HeatWaveDistortion</texPath>
      <shaderParameters>
        <_distortionIntensity>0.2</_distortionIntensity>
        <_brightnessMultiplier>1.3</_brightnessMultiplier>
        <_distortionTint>(0.23, 0, 0, 0)</_distortionTint>
        <_pulseSpeed>-0.5</_pulseSpeed>
      </shaderParameters>
      <drawSize>5</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_AnimalWarcallTargetFlash</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.1</fadeInTime>
      <solidTime>0.15</solidTime>
      <fadeOutTime>0.5</fadeOutTime>
    </mote>
    <graphicData>
      <texPath>Things/Mote/PsycastDistortionMask</texPath>
      <shaderType>MoteGlow</shaderType>
      <color>(1, 0.34, 0.2, 0.25)</color>
      <drawSize>(2, 2)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_AnimalWarcallMentalState</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.1</fadeInTime>
      <fadeOutTime>0.4</fadeOutTime>
      <solidTime>99999</solidTime>
      <needsMaintenance>true</needsMaintenance>
      <attachedToHead>true</attachedToHead>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>MoteGlowCircularScrolling</shaderType>
      <texPath>Things/Mote/RadiationDistortion_D</texPath>
      <color>(1, 0.34, 0.2)</color>
      <shaderParameters>
        <_MaskTex>/Things/Mote/NorthEastCornerMask</_MaskTex>
        <_BrightnessMultiplier>1.1</_BrightnessMultiplier>
        <_ScrollSpeed>0.15</_ScrollSpeed>
        <_ScrollScalse>(0.1, 0.1, 0, 0)</_ScrollScalse>
      </shaderParameters>
      <drawSize>2</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_BabyCryingDots</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <thingClass>MoteAttached</thingClass>
    <mote>
      <fadeInTime>0.1</fadeInTime>
      <solidTime>0.4</solidTime>
      <fadeOutTime>0.1</fadeOutTime></mote>
    <graphicData>
      <texPath>Things/Mote/Childcare/BabyCrying/Dots</texPath>
      <shaderType>Mote</shaderType>
      <drawSize>0.3</drawSize>
    </graphicData>
  </ThingDef>
  
</Defs>