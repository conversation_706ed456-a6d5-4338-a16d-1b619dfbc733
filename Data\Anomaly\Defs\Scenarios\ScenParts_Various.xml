﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <ScenPartDef>
    <defName>AutoActivateMonolith</defName>
    <label>automatically activate monolith</label>
    <category>Misc</category>
    <scenPartClass>ScenPart_AutoActivateMonolith</scenPartClass>
    <maxUses>1</maxUses>
  </ScenPartDef>

  <ScenPartDef>
    <defName>ScenPart_MonolithGeneration</defName>
    <label>void monolith</label>
    <scenPartClass>ScenPart_MonolithGeneration</scenPartClass>
    <genStep>VoidMonolith</genStep>
    <category>Rule</category>
    <selectionWeight>0</selectionWeight>
    <summaryPriority>500</summaryPriority>
    <maxUses>1</maxUses>
  </ScenPartDef>

  <ScenPartDef>
    <defName>ConfigurePawnsMutants</defName>
    <label>starting people</label>
    <scenPartClass>ScenPart_ConfigPage_ConfigureStartingPawns_Mutants</scenPartClass>
    <selectionWeight>0</selectionWeight>
    <pageClass>Page_ConfigureStartingPawns</pageClass>
    <summaryPriority>500</summaryPriority>
    <category>Fixed</category>
  </ScenPartDef>
</Defs>