<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <DutyDef>
    <defName>Warqueen</defName>
    <alwaysShowWeapon>true</alwaysShowWeapon>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_AIFightEnemies">
          <targetAcquireRadius>65</targetAcquireRadius>
          <targetKeepRadius>72</targetKeepRadius>
          <allowTurrets>true</allowTurrets>
        </li>
        <li Class="JobGiver_AITrashColonyClose" />
        <li Class="JobGiver_AITrashBuildingsDistant" />
        <li Class="JobGiver_AIGotoNearestHostile" />
        <li Class="JobGiver_AITrashBuildingsDistant">
          <attackAllInert>true</attackAllInert>
        </li>
      </subNodes>
    </thinkNode>
  </DutyDef>
  
</Defs>