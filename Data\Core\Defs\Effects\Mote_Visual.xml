﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <!--=============== Bases ==============-->

  <ThingDef Name="MoteBase" Abstract="True">
    <thingClass>MoteThrown</thingClass>
    <label>Mote</label>
    <category>Mote</category>
    <graphicData>
      <graphicClass>Graphic_Mote</graphicClass>
      <shaderType>Mote</shaderType>
    </graphicData>
    <altitudeLayer>MoteLow</altitudeLayer>
    <tickerType>Normal</tickerType>
    <useHitPoints>false</useHitPoints>
    <isSaveable>false</isSaveable>
    <rotatable>false</rotatable>
  </ThingDef>

  <ThingDef Abstract="True" ParentName="MoteBase" Name="MoteCocoonBreakingBase">
    <thingClass>MoteThrown</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.03</fadeInTime>
      <fadeOutTime>1</fadeOutTime>
      <solidTime>0</solidTime>
      <growthRate>1.0</growthRate>
    </mote>
    <graphicData>
      <texPath>Things/Filth/PoolSoft</texPath>
      <graphicClass>Graphic_MoteRandom</graphicClass>
      <color>(140, 142, 100, 150)</color>
    </graphicData>
  </ThingDef>

  <!--=============== Flashes ================-->

  <ThingDef ParentName="MoteBase">
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <defName>Mote_ActivatorCountdownGlow</defName>
    <mote>
      <fadeInTime>0.1</fadeInTime>
      <solidTime>999999999</solidTime>
      <fadeOutTime>0.5</fadeOutTime>
      <needsMaintenance>True</needsMaintenance>
    </mote>
    <graphicData>
      <drawSize>1.3</drawSize>
      <drawOffset>(0,0,1)</drawOffset>
      <color>(135, 215, 40, 140)</color>
      <texPath>Things/Mote/BrightFlash</texPath>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <defName>Mote_ActivatorProximityGlow</defName>
    <mote>
      <fadeInTime>0.1</fadeInTime>
      <solidTime>999999999</solidTime>
      <fadeOutTime>0.5</fadeOutTime>
      <needsMaintenance>True</needsMaintenance>
    </mote>
    <graphicData>
      <drawSize>1.1</drawSize>
      <drawOffset>(0,0,0.65)</drawOffset>
      <color>(235, 75, 40, 150)</color>
      <texPath>Things/Mote/BrightFlash</texPath>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
  </ThingDef>

  <!--=============== Misc visuals ================-->

  <ThingDef ParentName="MoteBase">
    <defName>Mote_Leaf</defName>
    <thingClass>MoteLeaf</thingClass>
    <mote>
      <fadeInTime>1</fadeInTime>
      <solidTime>4</solidTime>
      <fadeOutTime>2</fadeOutTime>
    </mote>
    <altitudeLayer>Building</altitudeLayer>
    <graphicData>
      <texPath>Things/Mote/FoodBitVegetarian</texPath>
      <drawSize>0.40</drawSize>
    </graphicData>
  </ThingDef>


  <ThingDef Abstract="True" Name="MoteGlowDistorted" ParentName="MoteBase">
    <graphicData>
      <shaderType>MoteGlowDistorted</shaderType>
      <shaderParameters>
        <_DistortionTex>/Other/Ripples</_DistortionTex>
      </shaderParameters>
    </graphicData>
  </ThingDef>

  <!--=================== Eating ==================-->

  <ThingDef ParentName="MoteBase" Name="FoodBitBase" Abstract="True">
    <graphicData>
      <shaderType>Cutout</shaderType>
    </graphicData>
    <altitudeLayer>Projectile</altitudeLayer>
    <mote>
      <fadeInTime>0.08</fadeInTime>
      <solidTime>0.4</solidTime>
      <fadeOutTime>0.25</fadeOutTime>
    </mote>
  </ThingDef>
  
  <ThingDef ParentName="FoodBitBase">
    <defName>Mote_FoodBitVegetarian</defName>
    <graphicData>
      <texPath>Things/Mote/FoodBitVegetarian</texPath>
      <shaderType>Mote</shaderType>
    </graphicData>
  </ThingDef>
  
  <ThingDef ParentName="FoodBitBase">
    <defName>Mote_FoodBitMeat</defName>
    <graphicData>
      <texPath>Things/Mote/FoodBitMeat</texPath>
      <shaderType>Mote</shaderType>
    </graphicData>
  </ThingDef>
  
  <ThingDef ParentName="FoodBitBase">
    <defName>Mote_StoneBit</defName>
    <graphicData>
      <texPath>Things/Mote/StoneBit</texPath>
      <shaderType>Mote</shaderType>
    </graphicData>
  </ThingDef>
  
  <ThingDef ParentName="FoodBitBase">
    <defName>Mote_CookBit</defName>
    <graphicData>
      <texPath>Things/Mote/CookBit</texPath>
      <shaderType>Mote</shaderType>
    </graphicData>
  </ThingDef>
  
  <ThingDef ParentName="FoodBitBase">
    <defName>Mote_VomitBit</defName>
    <graphicData>
      <texPath>Things/Mote/VomitBit</texPath>
      <shaderType>Mote</shaderType>
    </graphicData>
  </ThingDef>

  <!-- Bombardment -->

  <ThingDef ParentName="MoteBase">
    <defName>Mote_Bombardment</defName>
    <graphicData>
      <texPath>Things/Mote/Bombardment</texPath>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <drawOffscreen>true</drawOffscreen>
    <mote>
      <fadeInTime>0.5</fadeInTime>
      <solidTime>6.8</solidTime>
      <fadeOutTime>1.0</fadeOutTime>
    </mote>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_PowerBeam</defName>
    <graphicData>
      <texPath>Things/Mote/PowerBeam</texPath>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <drawOffscreen>true</drawOffscreen>
    <mote>
      <fadeInTime>0.5</fadeInTime>
      <solidTime>9.3</solidTime>
      <fadeOutTime>1.0</fadeOutTime>
    </mote>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_GiantExplosion</defName>
    <graphicData>
      <texPath>Things/Mote/PowerBeam</texPath>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <drawOffscreen>true</drawOffscreen>
    <mote>
      <fadeInTime>0.01</fadeInTime>
      <solidTime>0</solidTime>
      <fadeOutTime>0.4</fadeOutTime>
      <growthRate>325</growthRate>
    </mote>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_GiantExplosionInner</defName>
    <graphicData>
      <texPath>Things/Mote/PowerBeam</texPath>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <drawOffscreen>true</drawOffscreen>
    <mote>
      <fadeInTime>0.01</fadeInTime>
      <solidTime>0</solidTime>
      <fadeOutTime>0.4</fadeOutTime>
      <growthRate>200</growthRate>
    </mote>
  </ThingDef>

  <ThingDef Name="Mote_PsychicConditionCauserEffect" ParentName="MoteBase">
    <defName>Mote_PsychicConditionCauserEffect</defName>
    <mote>
      <fadeInTime>0.1</fadeInTime>
      <solidTime>0.62</solidTime>
      <fadeOutTime>0.5</fadeOutTime>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <texPath>Things/Mote/PsychicConditionCauserMask</texPath>
      <shaderType>MotePsychicConditionCauser</shaderType>
      <shaderParameters>
        <_distortionIntensity>0.015</_distortionIntensity>
        <_brightnessMultiplier>1.1</_brightnessMultiplier>
        <_distortionScale>40</_distortionScale>
      </shaderParameters>
      <drawSize>30</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="Mote_PsychicConditionCauserEffect">
    <defName>Mote_PsychicEmanatorEffect</defName>
    <graphicData>
      <shaderParameters>
        <_distortionIntensity>0.006</_distortionIntensity>
        <_brightnessMultiplier>1.075</_brightnessMultiplier>
        <_distortionScale>25</_distortionScale>
      </shaderParameters>
      <drawSize>8</drawSize>
    </graphicData>
  </ThingDef>
  
  <ThingDef Name="ForceJob" ParentName="MoteBase">
    <defName>Mote_ForceJob</defName>
    <thingClass>MoteBubble</thingClass>
    <altitudeLayer>MetaOverlays</altitudeLayer>
    <mote>
      <attachedDrawOffset>(0.85, 0, 0.85)</attachedDrawOffset>
      <fadeInTime>0.1</fadeInTime>
      <fadeOutTime>0.1</fadeOutTime>
      <solidTime>0.7</solidTime>
    </mote>
    <graphicData>
      <texPath>Things/Mote/Thought</texPath>
      <shaderType>TransparentPostLight</shaderType>
      <drawSize>1.25</drawSize>
    </graphicData>
  </ThingDef>
  
  <ThingDef ParentName="ForceJob">
    <defName>Mote_ForceJobMaintained</defName>
    <mote>
      <fadeInTime>0.1</fadeInTime>
      <fadeOutTime>0.1</fadeOutTime>
      <solidTime>999999</solidTime>
      <needsMaintenance>True</needsMaintenance>
    </mote>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>FlashShieldBreak</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <thingClass>MoteAttached</thingClass>
    <mote>
      <fadeInTime>0.06</fadeInTime>
      <solidTime>0.1</solidTime>
      <fadeOutTime>0.5</fadeOutTime>
    </mote>
    <graphicData>
      <texPath>Things/Mote/ShieldBubble_BrokenFlash</texPath>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>CracksShieldBreak</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <thingClass>MoteAttached</thingClass>
    <mote>
      <fadeInTime>0.01</fadeInTime>
      <solidTime>0.2</solidTime>
      <fadeOutTime>0.03</fadeOutTime>
    </mote>
    <graphicData>
      <texPath>Things/Mote/ShieldBubble_BrokenCracks</texPath>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_SparkThrownFastBright</defName>
    <graphicData>
      <texPath>Things/Mote/SparkThrown</texPath>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
    <altitudeLayer>Projectile</altitudeLayer>
    <mote>
      <fadeInTime>0.03</fadeInTime>
      <solidTime>0.04</solidTime>
      <fadeOutTime>0.13</fadeOutTime>
      <collide>true</collide>
    </mote>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_ResurrectAbility</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.35</fadeInTime>
      <fadeOutTime>0.25</fadeOutTime>
      <solidTime>999999</solidTime>
      <needsMaintenance>True</needsMaintenance>
    </mote>
    <graphicData>
      <graphicClass>Graphic_PawnBodySilhouette</graphicClass>
      <color>(0.45, 0.85, 0.52, 1.0)</color>
      <shaderType>MoteMultiplyAddScroll</shaderType>
      <texPath>Things/Mote/Transparent</texPath>
      <shaderParameters>
        <_MultiplyTexA>/Things/Mote/Cloudy_B</_MultiplyTexA>
        <_MultiplyTexB>/Things/Mote/Cloudy_C</_MultiplyTexB>
        <_DetailTex>/Things/Mote/Speckles</_DetailTex>
        <_detailIntensity>1.3</_detailIntensity>
        <_texAScale>0.4</_texAScale>
        <_texBScale>0.3</_texBScale>
        <_DetailScale>1.25</_DetailScale>
        <_texAScrollSpeed>(0, 0.15, 0)</_texAScrollSpeed>
        <_texBScrollSpeed>(0.12, 0, 0)</_texBScrollSpeed>
        <_detailScrollSpeed>(0.15, 0.15, 0)</_detailScrollSpeed>
      </shaderParameters>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_ResurrectFlash</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.05</fadeInTime>
      <fadeOutTime>0.25</fadeOutTime>
      <solidTime>0.15</solidTime>
      <growthRate>2.5</growthRate>
    </mote>
    <graphicData>
      <texPath>Things/Mote/RadiationDistortion_A</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <color>(0.45, 0.85, 0.52, 1.0)</color>
      <shaderType>MoteMultiplyAddCircularGrayscale</shaderType>
      <shaderParameters>
        <_MultiplyTex>/Things/Mote/RadiationDistortion_B</_MultiplyTex>
        <_texAScale>0.3</_texAScale>
        <_texBScale>0.3</_texBScale>
        <_texAScrollSpeed>1.5</_texAScrollSpeed>
        <_texBScrollSpeed>-0.8</_texBScrollSpeed>
        <_Intensity>11</_Intensity>
      </shaderParameters>
      <drawSize>(1.4, 1.4)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_PowerCellBurning</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.0</fadeInTime>
      <fadeOutTime>0.0</fadeOutTime>
      <solidTime>1</solidTime>
    </mote>
    <graphicData>
      <texPath>Things/Mote/PowerCellBurning</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>GlowAnimated</shaderType>
      <shaderParameters>
        <_NumFrames>5</_NumFrames>
        <_FramesPerSec>10</_FramesPerSec>
      </shaderParameters>
    </graphicData>
  </ThingDef>
  
  <ThingDef ParentName="MoteBase">
    <defName>Mote_ForcedVisible</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteLow</altitudeLayer>
    <mote>
      <fadeInTime>.05</fadeInTime>
      <solidTime>0</solidTime>
      <fadeOutTime>.5</fadeOutTime>
      <growthRate>1</growthRate>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <texPath>Things/Mote/Black</texPath>
      <shaderType>MoteLargeDistortionWave</shaderType>
      <shaderParameters>
        <_DistortionTex>/Things/Mote/PsychicDistortionCurrents</_DistortionTex>
        <_NoiseTex>/Things/Mote/PsycastNoise</_NoiseTex>
        <_distortionIntensity>0.2</_distortionIntensity>
        <_brightnessMultiplier>-1</_brightnessMultiplier>
        <_noiseIntensity>0</_noiseIntensity>
      </shaderParameters>
      <drawSize>10.0</drawSize>
    </graphicData>
  </ThingDef>


  <ThingDef ParentName="MoteBase">
    <defName>GroundCrackHuge</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>Filth</altitudeLayer>
    <drawOffscreen>true</drawOffscreen>
    <mote>
      <fadeInTime>10</fadeInTime>
      <solidTime>99999</solidTime>
      <fadeOutTime>0</fadeOutTime>
      <growthRate>0</growthRate>
    </mote>
    <graphicData>
      <texPath>Things/Mote/GroundCracks/GroundCrackHuge</texPath>
      <shaderType>Mote</shaderType>
      <renderInstanced>false</renderInstanced>
      <graphicClass>Graphic_Mote</graphicClass>
    </graphicData>
  </ThingDef>
  
</Defs>
