<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <RulePackDef>
    <defName>NamerMech</defName>
    <rulePack>
      <rulesStrings>
        <li>r_name(p=6)->[namefile]</li>
        <li>r_name->[exoticname]</li>

        <li>exoticname(p=2)->[AdjectiveBadass]</li>
        <li>exoticname(p=2)->[PersonBadass]</li>
        <li>exoticname->[Enemy]</li>
        <li>exoticname->[ConceptAny]</li>
        <li>exoticname->[Color]</li>
      </rulesStrings>
      <rulesFiles>
        <li>namefile->Names/Mech_Unisex</li>
      </rulesFiles>
    </rulePack>
  </RulePackDef>


</Defs>
