<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <QuestScriptDef>
    <defName>Bossgroup</defName>
    <defaultHidden>true</defaultHidden>
    <autoAccept>true</autoAccept>
    <questNameRules>
      <rulesStrings>
        <li>questName->bossgroup</li><!-- hidden -->
      </rulesStrings>
    </questNameRules>
    <questDescriptionRules>
      <rulesStrings>
        <li>questDescription->bossgroup</li><!-- hidden -->
      </rulesStrings>
    </questDescriptionRules>
    <questContentRules>
      <rulesStrings>

        <li>bossDefeatedLetterLabel->[escortees0_label] defeated: [reward_label]</li>

        <li>bossDefeatedStudyChipLetterText->[defeated][study][manufacture]</li>
        <li>bossDefeatedLetterText->[defeated][manufacture]</li>

        <li>defeated->The hostile [escortees0_label] has been defeated and has dropped a [reward_label]!</li>
        <li>manufacture->\n\nThe chip can also be used to create more advanced mechanoids.</li>
        <li>study->\n\nYou can study the [reward_label] by selecting a mechanitor and right-clicking the chip. Studying a [reward_label] helps unlock higher tiers of mechtech research.</li>

      </rulesStrings>
    </questContentRules>
    <root Class="QuestNode_Root_Bossgroup" />
  </QuestScriptDef>

</Defs>