<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <PsychicRitualDef_VoidProvocation>
    <defName>VoidProvocation</defName>
    <label>void provocation</label>
    <description>Psychically commune with the void, attracting an entity which can be captured and studied. There’s a chance that the invoker will fall into a coma for several hours.</description>
    <iconPath>UI/PsychicRituals/PsychicRitual_VoidProvocation</iconPath>
    <outcomeDescription>With the current quality, there's a {0} chance the invoker will receive dark psychic shock.</outcomeDescription>
    <hoursUntilOutcome>2</hoursUntilOutcome>
    <cooldownHours>120</cooldownHours>
    <psychicShockChanceFromQualityCurve>
      <points>
        <li>(0, 0.9)</li>
        <li>(0.5, 0.25)</li>
        <li>(0.9, 0.05)</li>
        <li>(1, 0.01)</li>
      </points>
    </psychicShockChanceFromQualityCurve>
    <darkPsychicShockDurarionHoursRange>2~4</darkPsychicShockDurarionHoursRange>
    <incidentDelayHoursRange>2~4</incidentDelayHoursRange>
    <researchPrerequisite>BasicPsychicRituals</researchPrerequisite>
  </PsychicRitualDef_VoidProvocation>

  <PsychicRitualDef_ImbueDeathRefusal>
    <defName>ImbueDeathRefusal</defName>
    <label>imbue death refusal</label>
    <description>Link a target with a distant archotechnological engine that can bring them back from the dead in seconds. This process is not without side-effects.</description>
    <researchPrerequisite>DeathRefusal</researchPrerequisite>
    <outcomeDescription>The target will gain death refusal.\n\nWith the current quality, all the target's skills will also be reduced by {0}.</outcomeDescription>
    <hoursUntilOutcome>6</hoursUntilOutcome>
    <targetRole>DeathRefusalTarget</targetRole>
    <requiredOffering>
      <filter>
        <thingDefs>
          <li>Shard</li>
        </thingDefs>
      </filter>
      <count>1</count>
    </requiredOffering>
    <skillOffsetPercentFromQualityCurve>
      <points>
        <li>(0, -0.5)</li>
        <li>(0.5, -0.15)</li>
        <li>(0.75, -0.05)</li>
        <li>(1, -0.01)</li>
      </points>
    </skillOffsetPercentFromQualityCurve>
    <cooldownHours>360</cooldownHours>
    <chanterRole>ChanterAdvanced</chanterRole>
    <iconPath>UI/PsychicRituals/PsychicRitual_ImbueDeathRefusal</iconPath>
  </PsychicRitualDef_ImbueDeathRefusal>

  <PsychicRitualDef_Philophagy>
    <defName>Philophagy</defName>
    <label>philophagy</label>
    <description>Psychically draw experiences from the victim's mind and transfer them to the invoker, targeting the victim's highest skill. The target will suffer brain damage, causing them to fall into a coma for several days.</description>
    <researchPrerequisite>Philophagy</researchPrerequisite>
    <outcomeDescription>With the current ritual quality, the invoker will steal {0} of the target's XP in their best skill.\n\nThe target will suffer brain damage and fall into a coma.</outcomeDescription>
    <targetRole>PhilophagyTarget</targetRole>
    <hoursUntilOutcome>4</hoursUntilOutcome>
    <requiredOffering>
      <filter>
        <thingDefs>
          <li>Shard</li>
        </thingDefs>
      </filter>
      <count>1</count>
    </requiredOffering>
    <brainDamageRange>3~5</brainDamageRange>
    <xpTransferFromQualityCurve>
      <points>
        <li>(0, 0.1)</li>
        <li>(0.5, 0.3)</li>
        <li>(0.9, 0.5)</li>
        <li>(1, 0.8)</li>
      </points>
    </xpTransferFromQualityCurve>
    <cooldownHours>120</cooldownHours>
    <iconPath>UI/PsychicRituals/PsychicRitual_Philophagy</iconPath>
  </PsychicRitualDef_Philophagy>

  <PsychicRitualDef_SummonAnimals>
    <defName>SummonAnimals</defName>
    <label>draw animals</label>
    <description>Create a psychic pulse which will draw a distant herd of animals into the local area. You can then hunt or make use of them as you choose. If not done correctly, this may attract scaria-infected manhunters.</description>
    <researchPrerequisite>SummonAnimals</researchPrerequisite>
    <outcomeDescription>With the current ritual quality, there is a {0} chance that the animals will arrive as manhunters.</outcomeDescription>
    <hoursUntilOutcome>4</hoursUntilOutcome>
    <manhunterSpawnChanceFromQualityCurve>
      <points>
        <li>(0, 0.75)</li>
        <li>(0.75, 0.1)</li>
        <li>(0.9, 0.05)</li>
        <li>(1, 0.01)</li>
      </points>
    </manhunterSpawnChanceFromQualityCurve>
    <requiredOffering>
      <filter>
        <thingDefs>
          <li>Bioferrite</li>
        </thingDefs>
      </filter>
      <count>10</count>
    </requiredOffering>
    <cooldownHours>240</cooldownHours>
    <iconPath>UI/PsychicRituals/PsychicRitual_SummonAnimals</iconPath>
  </PsychicRitualDef_SummonAnimals>

  <PsychicRitualDef_SummonShamblers>
    <defName>SummonShamblers</defName>
    <label>draw shamblers</label>
    <description>Create a psychic pulse which will draw a distant horde of animated corpses towards your local area. While they attack any humans that they see, they won't attack your colony directly. You can capture them for study.</description>
    <researchPrerequisite>SummonShamblers</researchPrerequisite>
    <outcomeDescription>The higher the ritual quality, the more shamblers that will be summoned.</outcomeDescription>
    <hoursUntilOutcome>2</hoursUntilOutcome>
    <combatPointsFromQualityRange>200~2500</combatPointsFromQualityRange>
    <requiredOffering>
      <filter>
        <thingDefs>
          <li>Bioferrite</li>
        </thingDefs>
      </filter>
      <count>20</count>
    </requiredOffering>
    <cooldownHours>120</cooldownHours>
    <iconPath>UI/PsychicRituals/PsychicRitual_SummonShamblers</iconPath>
  </PsychicRitualDef_SummonShamblers>

  <PsychicRitualDef_SummonPitGate>
    <defName>SummonPitGate</defName>
    <label>provoke pit gate</label>
    <description>Use a psychic pulse to provoke underground fleshbeasts into opening a massive pit gate in the ground somewhere in the local area. The massive sinkhole will lead down to a fleshbeast-infested cavern which can be explored for resources.</description>
    <iconPath>UI/PsychicRituals/PsychicRitual_SummonPitGate</iconPath>
    <researchPrerequisite>SummonPitGate</researchPrerequisite>
    <outcomeDescription>A higher-quality ritual will attract more fleshbeasts. The fleshbeasts can be captured and studied, or butchered for twisted meat.</outcomeDescription>
    <hoursUntilOutcome>6</hoursUntilOutcome>
    <castableOnPocketMaps>false</castableOnPocketMaps>
    <combatPointMultiplierFromQualityRange>0.5~1.25</combatPointMultiplierFromQualityRange>
    <requiredOffering>
      <filter>
        <thingDefs>
          <li>Bioferrite</li>
        </thingDefs>
      </filter>
      <count>75</count>
    </requiredOffering>
    <cooldownHours>1080</cooldownHours>
    <chanterRole>ChanterAdvanced</chanterRole>
  </PsychicRitualDef_SummonPitGate>

  <PsychicRitualDef_Chronophagy>
    <defName>Chronophagy</defName>
    <label>chronophagy</label>
    <description>Induce a distant archotech to transfer entropy from one person to another. This causes the target to age rapidly while the invoker becomes younger, potentially healing scarring or age-related illnesses. The target will suffer brain damage, causing them to fall into a coma for several days.</description>
    <researchPrerequisite>Chronophagy</researchPrerequisite>
    <outcomeDescription>At the current ritual quality, the invoker will become {0} years younger and the target will become {1} years older.\n\nThe target will suffer brain damage and fall into a coma.</outcomeDescription>
    <targetRole>ChronophagyTarget</targetRole>
    <hoursUntilOutcome>4</hoursUntilOutcome>
    <requiredOffering>
      <filter>
        <thingDefs>
          <li>Bioferrite</li>
        </thingDefs>
      </filter>
      <count>20</count>
    </requiredOffering>
    <yearsTransferredFromQualityRange>1~25</yearsTransferredFromQualityRange>
    <cooldownHours>120</cooldownHours>
    <iconPath>UI/PsychicRituals/PsychicRitual_Chronophagy</iconPath>
    <brainDamageFromAgeCurve>
      <points>
        <li>(0, 1)</li>
        <li>(70, 1)</li>
        <li>(90, 5)</li>
        <li>(100, 8)</li>
        <li>(140, 10)</li>
      </points>
    </brainDamageFromAgeCurve>
  </PsychicRitualDef_Chronophagy>

  <PsychicRitualDef_PleasurePulse>
    <defName>PleasurePulse</defName>
    <label>pleasure pulse</label>
    <description>Emit an intense psychic pulse that makes any psychically sensitive individual in the region happier but reduces their desire to work. This will replace the effect of other active pulses.</description>
    <researchPrerequisite>PleasurePulse</researchPrerequisite>
    <outcomeDescription>At the current ritual quality, the effect will last for {0}.</outcomeDescription>
    <hoursUntilOutcome>4</hoursUntilOutcome>
    <requiredOffering>
      <filter>
        <thingDefs>
          <li>Bioferrite</li>
        </thingDefs>
      </filter>
      <count>30</count>
    </requiredOffering>
    <durationDaysFromQualityCurve>
      <points>
        <li>(0, 1)</li>
        <li>(0.5, 5)</li>
        <li>(0.75, 10)</li>
        <li>(1, 25)</li>
      </points>
    </durationDaysFromQualityCurve>
    <cooldownHours>120</cooldownHours>
    <chanterRole>ChanterAdvanced</chanterRole>
    <iconPath>UI/PsychicRituals/PsychicRitual_PleasurePulse</iconPath>
  </PsychicRitualDef_PleasurePulse>

  <PsychicRitualDef_NeurosisPulse>
    <defName>NeurosisPulse</defName>
    <label>neurosis pulse</label>
    <description>Emit an intense psychic pulse that makes any psychically sensitive individual in the region work faster but become more irritable. Those affected will feel less need for recreation but will be unhappier and more likely to have a mental break. This will replace the effect of other active pulses.</description>
    <researchPrerequisite>NeurosisPulse</researchPrerequisite>
    <outcomeDescription>At the current ritual quality, the effect will last for {0}.</outcomeDescription>
    <hoursUntilOutcome>4</hoursUntilOutcome>
    <requiredOffering>
      <filter>
        <thingDefs>
          <li>Bioferrite</li>
        </thingDefs>
      </filter>
      <count>30</count>
    </requiredOffering>
    <durationDaysFromQualityCurve>
      <points>
        <li>(0, 1)</li>
        <li>(0.5, 5)</li>
        <li>(0.75, 10)</li>
        <li>(1, 25)</li>
      </points>
    </durationDaysFromQualityCurve>
    <cooldownHours>120</cooldownHours>
    <chanterRole>ChanterAdvanced</chanterRole>
    <iconPath>UI/PsychicRituals/PsychicRitual_NeurosisPulse</iconPath>
  </PsychicRitualDef_NeurosisPulse>

  <PsychicRitualDef_BloodRain>
    <defName>BloodRain</defName>
    <label>blood rain</label>
    <description>Cast a ritual which causes blood-like psychofluid to fall from the sky. The fluid will slowly drive exposed humans and animals into a berserk frenzy. Traits and psychic sensitivity modulate the effect.</description>
    <researchPrerequisite>BloodRain</researchPrerequisite>
    <outcomeDescription>At the current ritual quality, the blood rain will last for {0}.</outcomeDescription>
    <hoursUntilOutcome>2</hoursUntilOutcome>
    <durationHoursFromQualityRange>3~16</durationHoursFromQualityRange>
    <castableOnPocketMaps>false</castableOnPocketMaps>
    <requiredOffering>
      <filter>
        <thingDefs>
          <li>Bioferrite</li>
        </thingDefs>
      </filter>
      <count>70</count>
    </requiredOffering>
    <cooldownHours>600</cooldownHours>
    <chanterRole>ChanterAdvanced</chanterRole>
    <iconPath>UI/PsychicRituals/PsychicRitual_BloodRain</iconPath>
  </PsychicRitualDef_BloodRain>
  
  <PsychicRitualDef_Brainwipe>
    <defName>Brainwipe</defName>
    <label>brainwipe</label>
    <description>Erase a person's memories. Traumatic events will be forgotten. Prisoners will forget their allegiance and become easier to recruit, even if they were unwavering before. Inhumanized individuals will forget their connection to the void. Unfortunately, the disruptive effect of the ritual will induce a coma that could last a long time, especially if the ritual is of poor quality.</description>
    <outcomeDescription>The target will be brainwiped.\n\nWith the current ritual quality, the target will also enter a coma for {0}.</outcomeDescription>
    <researchPrerequisite>Brainwipe</researchPrerequisite>
    <hoursUntilOutcome>6</hoursUntilOutcome>
    <targetRole>BrainwipeTarget</targetRole>
    <requiredOffering>
      <filter>
        <thingDefs>
          <li>Shard</li>
        </thingDefs>
      </filter>
      <count>1</count>
    </requiredOffering>
    <comaDurationDaysFromQualityCurve>
      <points>
        <li>(0, 60)</li>
        <li>(0.5, 14)</li>
        <li>(1, 1)</li>
      </points>
    </comaDurationDaysFromQualityCurve>
    <cooldownHours>480</cooldownHours>
    <chanterRole>ChanterAdvanced</chanterRole>
    <iconPath>UI/PsychicRituals/PsychicRitual_Brainwipe</iconPath>
  </PsychicRitualDef_Brainwipe>

  <PsychicRitualDef_Psychophagy>
    <defName>Psychophagy</defName>
    <label>psychophagy</label>
    <description>Consume the psychic sensitivity of a target. The invoker will increase their psychic sensitivity for a period of time. The effect does not stack. The target will suffer brain damage, causing them to fall into a coma for several days.</description>
    <researchPrerequisite>Psychophagy</researchPrerequisite>
    <outcomeDescription>At the current ritual quality, the invoker will increase their psychic sensitivity for {0}.\n\nThe target will suffer brain damage and fall into a coma.</outcomeDescription>
    <targetRole>PsychophagyTarget</targetRole>
    <hoursUntilOutcome>4</hoursUntilOutcome>
    <requiredOffering>
      <filter>
        <thingDefs>
          <li>Bioferrite</li>
        </thingDefs>
      </filter>
      <count>20</count>
    </requiredOffering>
    <cooldownHours>120</cooldownHours>
    <effectDurationDaysFromQualityCurve>
      <points>
        <li>(0, 10)</li>
        <li>(0.5, 30)</li>
        <li>(1, 120)</li>
      </points>
    </effectDurationDaysFromQualityCurve>
    <brainDamageRange>3~5</brainDamageRange>
    <iconPath>UI/PsychicRituals/PsychicRitual_Psychophagy</iconPath>
  </PsychicRitualDef_Psychophagy>

  <!-- Skip abduction (NPC) -->
  <PsychicRitualDef_SkipAbduction>
    <defName>SkipAbduction</defName>
    <label>skip abduction</label>
    <description>Teleport a random hostile person to the ritual location and place them in a coma.</description>
    <outcomeDescription>The target will be teleported to the invoker's location and fall into a coma.</outcomeDescription>
    <letterAICompleteLabel>Skip abduction</letterAICompleteLabel>
    <letterAICompleteText>{PAWN_nameDef} has been abducted by the cultists!</letterAICompleteText>
    <aiCastable>true</aiCastable>
    <playerCastable>false</playerCastable>
    <hoursUntilOutcome>4</hoursUntilOutcome>
    <nonRequiredPawnsMayLeave>true</nonRequiredPawnsMayLeave>
    <defenderRole>Defender</defenderRole>
    <letterAIArrivedText>They intend to use a psychic ritual to abduct a colonist via teleportation.</letterAIArrivedText>
  </PsychicRitualDef_SkipAbduction>

  <!-- Skip abduction (player)-->
  <PsychicRitualDef_SkipAbductionPlayer>
    <defName>SkipAbductionPlayer</defName>
    <label>skip abduction</label>
    <description>Abduct a random hostile person from anywhere in the world, prioritizing those nearby. The ritual induces a dark archotech to warp space, teleporting a distant person to the center of the ritual circle. The process causes the target to fall into a short coma.</description>
    <researchPrerequisite>SkipAbductionPlayer</researchPrerequisite>
    <outcomeDescription>At the current ritual quality, the target will remain in a coma for {0}.</outcomeDescription>
    <hoursUntilOutcome>2</hoursUntilOutcome>
    <requiredOffering>
      <filter>
        <thingDefs>
          <li>Bioferrite</li>
        </thingDefs>
      </filter>
      <count>60</count>
    </requiredOffering>
    <cooldownHours>240</cooldownHours>
    <comaDurationDaysFromQualityCurve>
      <points>
        <li>(0, 30)</li>
        <li>(0.5, 10)</li>
        <li>(0.75, 5)</li>
        <li>(1, 0.4166)</li>
      </points>
    </comaDurationDaysFromQualityCurve>
    <iconPath>UI/PsychicRituals/PsychicRitual_SkipAbduction</iconPath>
  </PsychicRitualDef_SkipAbductionPlayer>

  <!-- Summon fleshbeasts (AI) -->
  <PsychicRitualDef_SummonFleshbeasts>
    <defName>SummonFleshbeasts</defName>
    <label>draw fleshbeasts</label>
    <description>Create a psychic pulse which will draw fleshbeasts to dig upwards and emerge near your enemies. They will attack anyone they find, including your enemies and allies. The fleshbeasts can be captured and studied, or butchered for twisted meat.</description>
    <outcomeDescription>Create a psychic pulse which will draw a horde of fleshbeasts.</outcomeDescription>
    <letterAICompleteLabel>Fleshbeasts emerging</letterAICompleteLabel>
    <letterAICompleteText>Fleshbeasts are clawing their way out of the ground. Prepare for their attack!</letterAICompleteText>
    <aiCastable>true</aiCastable>
    <playerCastable>false</playerCastable>
    <hoursUntilOutcome>4</hoursUntilOutcome>
    <nonRequiredPawnsMayLeave>true</nonRequiredPawnsMayLeave>
    <defenderRole>Defender</defenderRole>
    <letterAIArrivedText>They intend to use a psychic ritual to summon hideous fleshbeasts to attack you.</letterAIArrivedText>
    <fleshbeastPointsFromThreatPointsCurve>
      <points>
        <li>(250, 100)</li>
        <li>(1000, 300)</li>
        <li>(5000, 600)</li>
      </points>
    </fleshbeastPointsFromThreatPointsCurve>
  </PsychicRitualDef_SummonFleshbeasts>

  <!-- Summon fleshbeasts (player) -->
  <PsychicRitualDef_SummonFleshbeastsPlayer>
    <defName>SummonFleshbeastsPlayer</defName>
    <label>draw fleshbeasts</label>
    <description>Create a psychic pulse which will draw fleshbeasts to dig upwards and emerge near your enemies. They will attack anyone they find, including your enemies and allies. The fleshbeasts can be captured and studied, or butchered for twisted meat.</description>
    <researchPrerequisite>SummonFleshbeastsPlayer</researchPrerequisite>
    <outcomeDescription>A higher quality ritual will attract more fleshbeasts.</outcomeDescription>
    <hoursUntilOutcome>2</hoursUntilOutcome>
    <castableOnPocketMaps>false</castableOnPocketMaps>
    <requiredOffering>
      <filter>
        <thingDefs>
          <li>Bioferrite</li>
        </thingDefs>
      </filter>
      <count>50</count>
    </requiredOffering>
    <cooldownHours>120</cooldownHours>
    <chanterRole>ChanterAdvanced</chanterRole>
    <fleshbeastCombatPointsFromQualityCurve>
      <points>
        <li>(0, 200)</li>
        <li>(0.7, 600)</li>
        <li>(0.9, 1200)</li>
        <li>(1, 2400)</li>
      </points>
    </fleshbeastCombatPointsFromQualityCurve>
    <iconPath>UI/PsychicRituals/PsychicRitual_SummonFleshbeasts</iconPath>
  </PsychicRitualDef_SummonFleshbeastsPlayer>

</Defs>