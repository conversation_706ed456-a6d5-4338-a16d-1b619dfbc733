﻿<?xml version="1.0" encoding="utf-8" ?>

<Defs>

  <!-- Neanderthal -->
  
  <RulePackDef>
    <defName>NeanderthalUtility</defName>
    <rulePack>
      <rulesStrings>
        <li>maybeApo(p=8)-></li>
        <li>maybeApo->'</li>

        <li>placeNameCore->[SylN][maybeApo][SylN]</li>
        <li>placeNameCore->[SylN][maybeApo][SylN][maybeApo][SylN]</li>
        <li>placeNameCore->[SylN][maybeApo][placeEnd]</li>
        <li>placeNameCore->[SylN][maybeApo][SylN][maybeApo][placeEnd]</li>

        <li>placeEnd->ils</li>
        <li>placeEnd->ilt</li>
        <li>placeEnd->ig</li>
        <li>placeEnd->fweg</li>
        <li>placeEnd->then</li>
        <li>placeEnd->thor</li>
        <li>placeEnd->bog</li>
        <li>placeEnd->ton</li>
        <li>placeEnd->ten</li>
        <li>placeEnd->moor</li>
        <li>placeEnd->mouk</li>
        <li>placeEnd->mun</li>
        <li>placeEnd->mursh</li>
        <li>placeEnd->man</li>
        <li>placeEnd->bel</li>
        <li>placeEnd->sil</li>
        <li>placeEnd->sonk</li>
        <li>placeEnd->soud</li>
      </rulesStrings>
      <rulesFiles>
        <li>SylN->WordParts/Syllables_Neanderthal</li>
      </rulesFiles>
    </rulePack>
  </RulePackDef>

  <RulePackDef>
    <defName>NamerFactionTribalNeanderthal</defName>
    <include>
      <li>NeanderthalUtility</li>
    </include>
    <rulePack>
      <rulesStrings>
        <li>r_name(p=2)->[placeNameCore]</li>
        <li>r_name->[placeNameCore] [political_union_tribal]</li>
        <li>r_name->[political_union_tribal] of [placeNameCore]</li>
      </rulesStrings>
      <rulesFiles>
        <li>political_union_tribal->Words/Nouns/PoliticalUnions_Tribal</li>
      </rulesFiles>
    </rulePack>
  </RulePackDef>

  <RulePackDef>
    <defName>NamerSettlementTribalNeaderthal</defName>
    <include>
      <li>NeanderthalUtility</li>
    </include>
    <rulePack>
      <rulesStrings>
        <li>r_name->[placeNameCore]</li>
      </rulesStrings>
    </rulePack>
  </RulePackDef>

  <!-- Impid -->
  
  <RulePackDef>
    <defName>ImpidUtility</defName>
    <rulePack>
      <rulesStrings>
        <li>placeNameCore->[SylI][SylI]</li>
        <li>placeNameCore->[SylI][SylI][SylI]</li>
        <li>placeNameCore->[SylI][placeEnd]</li>
        <li>placeNameCore->[SylI][SylI][placeEnd]</li>

        <li>placeEnd->ach</li>
        <li>placeEnd->az</li>
        <li>placeEnd->eth</li>
        <li>placeEnd->zoz</li>
        <li>placeEnd->och</li>
        <li>placeEnd->oz</li>
        <li>placeEnd->echz</li>
        <li>placeEnd->ess</li>
        <li>placeEnd->onss</li>
        <li>placeEnd->in</li>
        <li>placeEnd->ichs</li>
        <li>placeEnd->izz</li>
        <li>placeEnd->sezz</li>
        <li>placeEnd->tch</li>
        <li>placeEnd->tchz</li>
        <li>placeEnd->ken</li>
        <li>placeEnd->kev</li>
        <li>placeEnd->vel</li>
      </rulesStrings>
      <rulesFiles>
        <li>SylI->WordParts/Syllables_Impid</li>
      </rulesFiles>
    </rulePack>
  </RulePackDef>

  <RulePackDef>
    <defName>NamerFactionTribalImpid</defName>
    <include>
      <li>ImpidUtility</li>
    </include>
    <rulePack>
      <rulesStrings>
        <li>r_name(p=2)->[placeNameCore]</li>
        <li>r_name->[placeNameCore] [faction]</li>
        <li>r_name->[faction] of [placeNameCore]</li>

        <li>faction->clade</li>
        <li>faction->league</li>
        <li>faction->runship</li>
        <li>faction->sprintship</li>
        <li>faction->flamehome</li>
        <li>faction->firehome</li>
      </rulesStrings>
    </rulePack>
  </RulePackDef>

  <RulePackDef>
    <defName>NamerSettlementTribalImpid</defName>
    <include>
      <li>ImpidUtility</li>
    </include>
    <rulePack>
      <rulesStrings>
        <li>r_name->[placeNameCore]</li>
      </rulesStrings>
    </rulePack>
  </RulePackDef>

  <!-- Yttakin -->

  <RulePackDef>
    <defName>YttakinUtility</defName>
    <rulePack>
      <rulesStrings>
        <li>placeNameCore->[SylY][SylY]</li>
        <li>placeNameCore->[SylY][SylY][SylY]</li>
        <li>placeNameCore->[SylY][placeEnd]</li>
        <li>placeNameCore->[SylY][SylY][placeEnd]</li>

        <li>placeEnd->esh</li>
        <li>placeEnd->sytt</li>
        <li>placeEnd->wytt</li>
        <li>placeEnd->wott</li>
        <li>placeEnd->wosh</li>
        <li>placeEnd->barg</li>
        <li>placeEnd->berg</li>
        <li>placeEnd->bersh</li>
        <li>placeEnd->bug</li>
        <li>placeEnd->bytt</li>
        <li>placeEnd->byon</li>
        <li>placeEnd->den</li>
        <li>placeEnd->gen</li>
        <li>placeEnd->yul</li>
        <li>placeEnd->ya</li>
        <li>placeEnd->ysh</li>
        <li>placeEnd->ytt</li>
        <li>placeEnd->ys</li>
        <li>placeEnd->yn</li>
      </rulesStrings>
      <rulesFiles>
        <li>SylY->WordParts/Syllables_Yttakin</li>
      </rulesFiles>
    </rulePack>
  </RulePackDef>

  <RulePackDef>
    <defName>NamerFactionPirateYttakin</defName>
    <include>
      <li>YttakinUtility</li>
    </include>
    <rulePack>
      <rulesStrings>
        <li>r_name(p=2)->[placeNameCore]</li>
        <li>r_name->[placeNameCore] [faction]</li>
        <li>r_name->[faction] of [placeNameCore]</li>

        <li>faction->roarclan</li>
        <li>faction->tribe</li>
        <li>faction->clan</li>
        <li>faction->roar</li>
        <li>faction->pack</li>
        <li>faction->family</li>
        <li>faction->fur</li>
        <li>faction->kinroar</li>
      </rulesStrings>
    </rulePack>
  </RulePackDef>

  <RulePackDef>
    <defName>NamerSettlementPirateYttakin</defName>
    <include>
      <li>YttakinUtility</li>
    </include>
    <rulePack>
      <rulesStrings>
        <li>r_name->[placeNameCore]</li>
      </rulesStrings>
    </rulePack>
  </RulePackDef>

  <!-- Pigskin -->

  <RulePackDef>
    <defName>NamerFactionOutlanderPig</defName>
    <include>
      <li>OutlanderPlaceNameUtility</li>
    </include>
    <rulePack>
      <rulesStrings>
        <li>r_name(p=2)->[nameCore]</li>
        <li>r_name->[nameCore] [political_union_outlander]</li>
        <li>r_name->[political_union_outlander] of [nameCore]</li>

        <li>maybeHyphen(p=3)-></li>
        <li>maybeHyphen->-</li>

        <li>nameCore->[SylP][maybeHyphen][SylP]</li>
        <li>nameCore->[SylP][maybeHyphen][SylP][maybeHyphen][SylP]</li>
        <li>nameCore->[SylP][maybeHyphen][end]</li>
        <li>nameCore->[SylP][maybeHyphen][SylP][maybeHyphen][end]</li>

        <li>end->oo</li>
        <li>end->lok</li>
        <li>end->lom</li>
        <li>end->oink</li>
        <li>end->gug</li>
        <li>end->und</li>
        <li>end->oog</li>
        <li>end->pig</li>
        <li>end->pog</li>
        <li>end->mok</li>
        <li>end->mm</li>

      </rulesStrings>
      <rulesFiles>
        <li>political_union_outlander->Words/Nouns/PoliticalUnions_Outlander</li>
      </rulesFiles>
    </rulePack>
  </RulePackDef>

  <RulePackDef>
    <defName>NamerSettlementOutlanderPig</defName>
    <rulePack>
      <rulesStrings>
        <li>r_name->[townname_wordgen]</li>

        <li>maybeHyphen(p=3)-></li>
        <li>maybeHyphen->-</li>

        <li>townname_wordgen->[SylP][maybeHyphen][SylP]</li>
        <li>townname_wordgen->[SylP][maybeHyphen][SylP][maybeHyphen][SylP]</li>
        <li>townname_wordgen->[SylP][maybeHyphen][townend]</li>
        <li>townname_wordgen->[SylP][maybeHyphen][SylP][maybeHyphen][townend]</li>

        <li>townend->bille</li>
        <li>townend->wille</li>
        <li>townend->bwug</li>
        <li>townend->born</li>
        <li>townend->grab</li>
        <li>townend->bal</li>
        <li>townend->wope</li>
        <li>townend->ig</li>
        <li>townend->kess</li>
        <li>townend->mur</li>
        <li>townend->wig</li>
        <li>townend->gon</li>
        <li>townend->og</li>
        <li>townend->wen</li>
        <li>townend->wag</li>
        <li>townend->woog</li>
        <li>townend->worg</li>
        <li>townend->roog</li>
        <li>townend->rog</li>
        <li>townend->welg</li>
        <li>townend->op</li>
        <li>townend->pig</li>
      </rulesStrings>
    </rulePack>
  </RulePackDef>

  <!-- Waster pirates -->

  <RulePackDef>
    <defName>WasterUtility</defName>
    <rulePack>
      <rulesStrings>
        <li>poison->poison</li>
        <li>poison->toxin</li>
        <li>poison->tox</li>
        <li>poison->choke</li>
        <li>poison->gas</li>
        <li>poison->pill</li>
        <li>poison->psyck</li>
        <li>poison->virus</li>
        <li>poison->venom</li>
        <li>poison->cancer</li>
        <li>poison->toxoid</li>
        <li>poison->blight</li>
        <li>poison->miasma</li>
        <li>poison->filth</li>
        <li>poison->garbage</li>
        <li>poison->pollution</li>
        <li>poison->decay</li>
        <li>poison->trash</li>
        <li>poison->pest</li>

      </rulesStrings>
    </rulePack>
  </RulePackDef>

  <RulePackDef>
    <defName>NamerFactionPirateWaster</defName>
    <include>
      <li>WasterUtility</li>
    </include>
    <rulePack>
      <rulesStrings>
        <li>r_name->[poison] [groupname]</li>
        <li>r_name->[poison] [men]</li>

        <li>men->men</li>
        <li>men->crew</li>
        <li>men->gang</li>
        <li>men->chokers</li>
        <li>men->pillers</li>
        <li>men->psychers</li>
        <li>men->toxers</li>
        <li>men->blighters</li>
        <li>men->wasters</li>
        <li>men->packers</li>
        <li>men->slickers</li>
        <li>men->slickers</li>
        <li>men->pests</li>
        <li>men->trash</li>
      </rulesStrings>
      <rulesFiles>
        <li>groupname->Words/Nouns/GroupNames</li>
      </rulesFiles>
    </rulePack>
  </RulePackDef>

  <RulePackDef>
    <defName>NamerSettlementPirateWaster</defName>
    <include>
      <li>WasterUtility</li>
    </include>
    <rulePack>
      <rulesStrings>
        <li>r_name->[townNameWordgen]</li>
        <li>r_name->[townNameCombo]</li>

        <li>townNameWordgen->[SylE][SylE]</li>
        <li>townNameWordgen->[SylE][SylE][SylE]</li>
        <li>townNameWordgen->[SylE][townEndToxic]</li>
        <li>townNameWordgen->[SylE][SylE][townEndToxic]</li>
        <li>townEndToxic->tox</li>
        <li>townEndToxic->vor</li>
        <li>townEndToxic->son</li>
        <li>townEndToxic->zon</li>
        <li>townEndToxic->toth</li>
        <li>townEndToxic->zoun</li>
        <li>townEndToxic->slick</li>
        <li>townEndToxic->pill</li>
        <li>townEndToxic->ash</li>
        <li>townEndToxic->ven</li>

        <li>townNameCombo->[poison] [town]</li>

        <li>town->town</li>
        <li>town->base</li>
        <li>town->fort</li>
        <li>town->port</li>
        <li>town->abattoir</li>
        <li>town->butchery</li>
        <li>town->tank</li>

      </rulesStrings>
    </rulePack>
  </RulePackDef>

</Defs>
