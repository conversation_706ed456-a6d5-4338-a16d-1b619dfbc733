<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <MentalBreakDef>
    <defName>FireTerror</defName>
    <label>fire terror</label>
    <intensity>Minor</intensity>
    <workerClass>MentalBreakWorker_FireTerror</workerClass>
    <mentalState>PanicFleeFire</mentalState>
    <requiredGene>FireTerror</requiredGene>
  </MentalBreakDef>

  <MentalStateDef ParentName="BaseMentalState">
    <defName>PanicFleeFire</defName>
    <label>panic-fleeing fire</label>
    <stateClass>MentalState_PanicFleeFire</stateClass>
    <category>Misc</category>
    <nameColor>(0.65, 0.9, 0.93)</nameColor>
    <beginLetterLabel>fleeing fire</beginLetterLabel>
    <beginLetter>{0} is fleeing fire.</beginLetter>
    <recoveryMessage>{0} is no longer fleeing fire.</recoveryMessage>
    <baseInspectLine>Mental state: Fleeing fire</baseInspectLine>
    <blockNormalThoughts>true</blockNormalThoughts>
    <minTicksBeforeRecovery>60</minTicksBeforeRecovery>
  </MentalStateDef>

  <MentalStateDef ParentName="BaseMentalState">
    <defName>CocoonDisturbed</defName>
    <stateClass>MentalState_CocoonDisturbed</stateClass>
    <label>cocoon disturbed</label>
    <category>Aggro</category>
    <nameColor>(0.9,0.2,0.5)</nameColor>
    <minTicksBeforeRecovery>99999999</minTicksBeforeRecovery>
    <baseInspectLine>Enraged: Cocoon disturbed</baseInspectLine>
    <stateEffecter>Berserk</stateEffecter>
  </MentalStateDef>

  <MentalStateDef ParentName="BaseMentalState">
    <defName>BerserkWarcall</defName>
    <stateClass>MentalState_BerserkWarcall</stateClass>
    <label>warcall berserk</label>
    <category>Aggro</category>
    <blockRandomInteraction>true</blockRandomInteraction>
    <nameColor>(0.9,0.2,0.5)</nameColor>
    <beginLetter>{0} has been roused by a warcall.\n\n[PAWN_pronoun] will attack any enemy of the colony.</beginLetter>
    <recoveryMessage>{0}'s rage has come to an end.</recoveryMessage>
    <baseInspectLine>Mental state: Warcall berserk</baseInspectLine>
    <stateEffecter>AnimalWarcallMentalState</stateEffecter>
    <recoverFromSleep>true</recoverFromSleep>
    <inCaravanCanDo>true</inCaravanCanDo>
    <blocksDefendAndExpandHive>true</blocksDefendAndExpandHive>
  </MentalStateDef>

</Defs>