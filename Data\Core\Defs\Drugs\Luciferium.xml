﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingDef ParentName="DrugPillBase">
    <defName>Luciferium</defName>
    <label>luciferium</label>
    <description>A concoction of mechanites that dramatically improve the body's functioning in all respects. Over time, it can even heal old scarred-over wounds or brain damage, though it cannot regenerate lost limbs. Unfortunately, without the moderating effects of regular doses every five or six days, the mechanites lose cohesion, causing continuous berserk rages and, eventually, death.\n\nAfter the first dose, there is no way to get the mechanites out, ever.\n\nOn the urbworlds, they call Luciferium the 'Devil's Bargain'. Many have been forced to kill friends when no more of the seductive red pills could be found.</description>
    <descriptionHyperlinks>
      <HediffDef>LuciferiumHigh</HediffDef>
      <HediffDef>LuciferiumAddiction</HediffDef>
    </descriptionHyperlinks>
    <graphicData>
      <texPath>Things/Item/Drug/Luciferium</texPath>
      <graphicClass>Graphic_StackCount</graphicClass>
    </graphicData>
    <rotatable>false</rotatable>
    <statBases>
      <MarketValue>70</MarketValue>
      <Mass>0.01</Mass>
    </statBases>
    <techLevel>Ultra</techLevel>
    <minRewardCount>10</minRewardCount>
    <ingestible>
      <drugCategory>Medical</drugCategory>
      <outcomeDoers>
        <li Class="IngestionOutcomeDoer_GiveHediff">
          <hediffDef>LuciferiumHigh</hediffDef>
          <severity>1.00</severity>
          <doToGeneratedPawnIfAddicted>true</doToGeneratedPawnIfAddicted>
        </li>
      </outcomeDoers>
    </ingestible>
    <comps>
      <li Class="CompProperties_Drug">
        <chemical>Luciferium</chemical>
        <addictiveness>1.00</addictiveness>
        <needLevelOffset>0.9</needLevelOffset>
        <listOrder>1010</listOrder>
      </li>
    </comps>
    <tradeTags>
      <li>ExoticMisc</li>
    </tradeTags>
    <thingSetMakerTags><li>RewardStandardCore</li></thingSetMakerTags>
    <allowedArchonexusCount>50</allowedArchonexusCount>
  </ThingDef>

  <HediffDef>
    <defName>LuciferiumHigh</defName>
    <hediffClass>Hediff_High</hediffClass>
    <label>luciferium</label>
    <description>Active luciferium mechanites in the body. They improve the body's functioning in all respects. However, if they are not regularly replenished with more doses of luciferium, they lose cohesion and drive the user insane.</description>
    <defaultLabelColor>(1,1,0.5)</defaultLabelColor>
    <scenarioCanAdd>true</scenarioCanAdd>
    <maxSeverity>1.0</maxSeverity>
    <isBad>false</isBad>
    <comps>
      <li Class="HediffCompProperties_HealPermanentWounds" />
    </comps>
    <stages>
      <li>
        <painFactor>0.8</painFactor>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>0.10</offset>
          </li>
          <li>
            <capacity>Moving</capacity>
            <offset>0.05</offset>
          </li>
          <li>
            <capacity>Sight</capacity>
            <offset>0.15</offset>
          </li>
          <li>
            <capacity>BloodFiltration</capacity>
            <offset>0.70</offset>
          </li>
          <li>
            <capacity>BloodPumping</capacity>
            <offset>0.15</offset>
          </li>
           <li>
            <capacity>Metabolism</capacity>
            <offset>0.20</offset>
          </li>
          <li>
            <capacity>Breathing</capacity>
            <offset>0.10</offset>
          </li>
        </capMods>
      </li>
    </stages>
  </HediffDef>

  <!-- Luciferium addiction -->

  <ChemicalDef>
    <defName>Luciferium</defName>
    <label>luciferium</label>
    <addictionHediff>LuciferiumAddiction</addictionHediff>
    <canBinge>false</canBinge>
    <generateAddictionGenes>false</generateAddictionGenes>
  </ChemicalDef>

  <NeedDef ParentName="DrugAddictionNeedBase">
    <defName>Chemical_Luciferium</defName>
    <needClass>Need_Chemical</needClass>
    <label>luciferium</label>
    <description>This person's body is enhanced by luciferium mechanites.\n\nWithout regular doses of luciferium, the mechanites will lose cohesion. If this happens, the user becomes continuously, violently insane and eventually dies.\n\nThis addiction never goes away.</description>
    <listPriority>45</listPriority>
    <fallPerDay>0.15</fallPerDay>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
  </NeedDef>

  <HediffDef ParentName="AddictionBase">
    <defName>LuciferiumAddiction</defName>
    <label>luciferium need</label>
    <description>Luciferium mechanites in the body. Luciferium mechanites decohere over time; only regular doses of fresh luciferium can prevent this process.
\nWithout luciferium, the mechanite decoherence will reach a critical state, causing the victim to go insane and die.</description>
    <hediffClass>Hediff_Addiction</hediffClass>
    <causesNeed>Chemical_Luciferium</causesNeed>
    <everCurableByItem>false</everCurableByItem>
    <stages>
      <li>
      </li>
      <li>
        <label>unmet</label>
        <painOffset>0.4</painOffset>
        <lifeThreatening>true</lifeThreatening>
        <deathMtbDays>10</deathMtbDays>
        <mtbDeathDestroysBrain>true</mtbDeathDestroysBrain>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <setMax>0.8</setMax>
          </li>
        </capMods>
        <mentalStateGivers>
          <li>
            <mentalState>Berserk</mentalState>
            <mtbDays>0.4</mtbDays>
          </li>
        </mentalStateGivers>
      </li>
    </stages>
  </HediffDef>
  
</Defs>
