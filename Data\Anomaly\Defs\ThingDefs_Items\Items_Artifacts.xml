<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <ThingDef ParentName="ArtifactBase">
    <defName>ShardAnimalPulser</defName>
    <label>shard animal pulser</label>
    <description>A one-use broad-wave psychic effector created from a dark archotech shard. The psychic pulse fills lower minds with terrifying imagery, driving all animals in the region into a manhunting rage. Animals currently under your control will not be affected.</description>
    <graphicData>
      <texPath>Things/Item/Artifact/ShardAnimalPulser</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <resourceReadoutPriority>Middle</resourceReadoutPriority>
    <stackLimit>1</stackLimit>
    <relicChance>1</relicChance>
    <tradeNeverStack>true</tradeNeverStack>
    <genericMarketSellable>false</genericMarketSellable>
    <tradeTags Inherit="False">
      <li>UtilitySpecial</li>
    </tradeTags>
    <thingSetMakerTags Inherit="False">
      <li>RewardStandardLowFreq</li>
    </thingSetMakerTags>
    <useHitPoints>true</useHitPoints>
    <statBases>
      <WorkToMake>19200</WorkToMake>
      <MaxHitPoints>60</MaxHitPoints>
    </statBases>
    <drawGUIOverlay>false</drawGUIOverlay>
    <recipeMaker>
      <researchPrerequisite>InsanityWeaponry</researchPrerequisite>
      <recipeUsers>
        <li>BioferriteShaper</li>
      </recipeUsers>
      <skillRequirements>
        <Crafting>6</Crafting>
      </skillRequirements>
      <displayPriority>110</displayPriority>
      <workSpeedStat>GeneralLaborSpeed</workSpeedStat>
      <workSkill>Crafting</workSkill>
      <effectWorking>Smith</effectWorking>
      <soundWorking>Recipe_Machining</soundWorking>
      <unfinishedThingDef>UnfinishedBelt</unfinishedThingDef>
    </recipeMaker>
    <costList>
      <Shard>1</Shard>
      <Bioferrite>75</Bioferrite>
    </costList>
    <comps>
      <li Class="CompProperties_Targetable">
        <compClass>CompTargetable_AllAnimalsOnTheMap</compClass>
        <psychicSensitiveTargetsOnly>true</psychicSensitiveTargetsOnly>
        <doCameraShake>true</doCameraShake>
        <fleckOnTarget>PsycastPsychicEffect</fleckOnTarget>
        <fleckOnUsed>PsycastAreaEffect</fleckOnUsed>
        <fleckOnUsedScale>10</fleckOnUsedScale>
        <ignorePlayerFactionPawns>True</ignorePlayerFactionPawns>
      </li>
      <li>
        <compClass>CompTargetEffect_Manhunter</compClass>
      </li>
      <li Class="CompProperties_TargetEffect_GoodwillImpact">
        <goodwillImpact>-200</goodwillImpact>
      </li>
      <li Class="CompProperties_UseEffectArtifact">
        <sound>PsychicAnimalPulserCast</sound>
      </li>
      <li Class="CompProperties_UseEffectPlayWarmupSound">
        <warmupSound>PsychicArtifactWarmupSustained</warmupSound>
      </li>
    </comps>
  </ThingDef>

  <ThingDef ParentName="ArtifactBase">
    <defName>BiomutationPulser</defName>
    <label>biomutation pulser</label>
    <description>A one-use broad-wave psychic effector created from a dark archotech shard. The device creates a pulse that reshapes the musculature of every animal within the region, turning them into terrifying creatures. Animals currently under your control will not be affected.</description>
    <graphicData>
      <texPath>Things/Item/Artifact/BiomutationPulser</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <resourceReadoutPriority>Middle</resourceReadoutPriority>
    <stackLimit>1</stackLimit>
    <relicChance>2</relicChance>
    <tradeNeverStack>true</tradeNeverStack>
    <useHitPoints>true</useHitPoints>
    <genericMarketSellable>false</genericMarketSellable>
    <tradeTags Inherit="False">
      <li>UtilitySpecial</li>
    </tradeTags>
    <thingSetMakerTags Inherit="False">
      <li>RewardStandardLowFreq</li>
    </thingSetMakerTags>
    <statBases>
      <WorkToMake>19200</WorkToMake>
      <MarketValue>800</MarketValue>
      <MaxHitPoints>80</MaxHitPoints>
    </statBases>
    <drawGUIOverlay>false</drawGUIOverlay>
    <recipeMaker>
      <researchPrerequisite>MutationWeaponry</researchPrerequisite>
      <recipeUsers>
        <li>BioferriteShaper</li>
      </recipeUsers>
      <skillRequirements>
        <Crafting>6</Crafting>
      </skillRequirements>
      <displayPriority>120</displayPriority>
      <workSpeedStat>GeneralLaborSpeed</workSpeedStat>
      <workSkill>Crafting</workSkill>
      <effectWorking>Smith</effectWorking>
      <soundWorking>Recipe_Machining</soundWorking>
      <unfinishedThingDef>UnfinishedBelt</unfinishedThingDef>
    </recipeMaker>
    <costList>
      <Shard>1</Shard>
      <Bioferrite>40</Bioferrite>
      <Meat_Twisted>30</Meat_Twisted>
    </costList>
    <comps>
      <li Class="CompProperties_Targetable">
        <compClass>CompTargetable_AllAnimalsOnTheMap</compClass>
        <psychicSensitiveTargetsOnly>true</psychicSensitiveTargetsOnly>
        <doCameraShake>true</doCameraShake>
        <fleckOnUsed>PsycastAreaEffect</fleckOnUsed>
        <fleckOnUsedScale>10</fleckOnUsedScale>
        <ignorePlayerFactionPawns>True</ignorePlayerFactionPawns>
      </li>
      <li Class="CompProperties_UseEffect">
        <compClass>CompUseEffect_SpawnFleshbeastFromTargetPawns</compClass>
      </li>
      <li Class="CompProperties_TargetEffect_GoodwillImpact">
        <goodwillImpact>-200</goodwillImpact>
      </li>
      <li Class="CompProperties_UseEffectArtifact">
        <sound>PsychicAnimalPulserCast</sound>
        <warmupEffecter>BiomutationPulserWarmup</warmupEffecter>
        <effecterOnUsed>BiomutationPulserUsed</effecterOnUsed>
      </li>
      <li Class="CompProperties_UseEffectPlayWarmupSound">
        <warmupSound>BiomutationPulserWarmup</warmupSound>
      </li>
    </comps>
  </ThingDef>
</Defs>