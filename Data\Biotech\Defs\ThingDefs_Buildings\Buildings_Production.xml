<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <!-- Mech gestators -->
  <ThingDef Abstract="True" Name="MechGestatorBase" ParentName="BuildingBase">
    <thingClass>Building_MechGestator</thingClass>
    <containedPawnsSelectable>true</containedPawnsSelectable>
    <tickerType>Normal</tickerType>
    <drawerType>MapMeshAndRealTime</drawerType>
    <altitudeLayer>Building</altitudeLayer>
    <passability>PassThroughOnly</passability>
    <fillPercent>0.5</fillPercent>
    <hasInteractionCell>true</hasInteractionCell>
    <castEdgeShadows>true</castEdgeShadows>
    <pathCost>42</pathCost>
    <surfaceType>Item</surfaceType>
    <inspectorTabs>
      <li>ITab_Bills</li>
    </inspectorTabs>
    <building>
      <spawnedConceptLearnOpportunity>BillsTab</spawnedConceptLearnOpportunity>
      <canPlaceOverImpassablePlant>false</canPlaceOverImpassablePlant>
      <ai_chillDestination>false</ai_chillDestination>
      <buildingTags>
        <li>Production</li>
        <li>Biotech</li>
      </buildingTags>
    </building>
    <designationCategory>Biotech</designationCategory>
    <uiOrder>2400</uiOrder>
    <defaultPlacingRot>South</defaultPlacingRot>
    <placeWorkers>
      <li>PlaceWorker_MechGestatorTop</li>
    </placeWorkers>
    <comps>
      <li Class="CompProperties_WasteProducer" />
      <li Class="CompProperties_Flickable"/>
    </comps>
  </ThingDef>

  <ThingDef ParentName="MechGestatorBase">
    <defName>MechGestator</defName>
    <label>mech gestator</label>
    <description>A tank of mechanite-rich fluid with support tubes for feeding in materials and nutrients. Mechanitors can use it to produce new mechanoids or to resurrect dead mechanoids. This basic type of mech gestator is only capable of generating light-weight mechs.\n\nThe process uses harsh chemicals which are stored in toxic wastepacks. Haulers must remove the wastepacks from time to time.</description>
    <graphicData>
      <texPath>Things/Building/Production/MechGestatorStandard</texPath>
      <graphicClass>Graphic_Multi</graphicClass>
      <drawSize>(5.6,4)</drawSize>
      <shadowData>
        <volume>(3, 2, 1.9)</volume>
      </shadowData>
    </graphicData>
    <uiIconPath>Things/Building/Production/MechGestatorStandard_MenuIcon</uiIconPath>
    <uiIconScale>1.85</uiIconScale>
    <interactionCellOffset>(1,0,2)</interactionCellOffset>
    <recipes>
      <li>Militor</li>
      <li>Lifter</li>
      <li>Constructoid</li>
      <li>Agrihand</li>
      <li>Cleansweeper</li>
      <li>Fabricor</li>
      <li>Paramedic</li>
      <li>ResurrectLightMech</li>
    </recipes>
    <statBases>
      <MaxHitPoints>250</MaxHitPoints>
      <WorkToBuild>8000</WorkToBuild>
      <Mass>25</Mass>
      <Flammability>0.5</Flammability>
    </statBases>
    <size>(3,2)</size>
    <costList>
      <Steel>150</Steel>
      <ComponentIndustrial>3</ComponentIndustrial>
    </costList>
    <researchPrerequisites>
      <li>BasicMechtech</li>
    </researchPrerequisites>
    <building>
      <barDrawData>
        <north>
          <preRotationOffset>(-1.06, 0.91)</preRotationOffset>
          <size>(0.7, 0.13)</size>
        </north>
        <south>
          <preRotationOffset>(-1.06, 0.82)</preRotationOffset>
          <size>(0.7, 0.13)</size>
        </south>
        <east>
          <preRotationOffset>(1.06, -0.97)</preRotationOffset>
          <size>(0.7, 0.13)</size>
        </east>
        <west>
          <preRotationOffset>(-1.06, -0.97)</preRotationOffset>
          <size>(0.7, 0.13)</size>
        </west>
      </barDrawData>
      <formingGraphicData>
        <texPath>Things/Pawn/Mechanoid/HalfGestatedMechSmall</texPath>
        <graphicClass>Graphic_Single</graphicClass>
        <drawSize>(1,1)</drawSize>
      </formingGraphicData>
      <formingMechPerRotationOffset>
        <li>(0, 0, 0.45)</li>
        <li>(0, 0, 0.45)</li>
        <li>(0, 0, 0.45)</li>
        <li>(0, 0, 0.45)</li>
      </formingMechPerRotationOffset>
      <mechGestatorCylinderGraphic>
        <texPath>Things/Building/Production/MechGestatorStandardGlass</texPath>
        <graphicClass>Graphic_Multi</graphicClass>
        <drawSize>(5.6,4)</drawSize>
        <shaderType>Transparent</shaderType>
      </mechGestatorCylinderGraphic>
      <mechGestatorTopGraphic>
        <texPath>Things/Building/Production/MechGestatorStandardTop</texPath>
        <graphicClass>Graphic_Multi</graphicClass>
        <drawSize>(5.6,4)</drawSize>
      </mechGestatorTopGraphic>
      <gestatorFormingMote>
        <north>MechGestatorForming_North</north>
        <east>MechGestatorForming_East</east>
        <west>MechGestatorForming_East</west>
        <south>MechGestatorForming_South</south>
      </gestatorFormingMote>
      <gestatorCycleCompleteMote>
        <north>MechGestatorCycleComplete_North</north>
        <east>MechGestatorCycleComplete_East</east>
        <west>MechGestatorCycleComplete_East</west>
        <south>MechGestatorCycleComplete_South</south>
      </gestatorCycleCompleteMote>
      <gestatorFormedMote>
        <north>MechGestatorFormed_North</north>
        <east>MechGestatorFormed_East</east>
        <west>MechGestatorFormed_East</west>
        <south>MechGestatorFormed_South</south>
      </gestatorFormedMote>
    </building>
    <comps>
      <li Class="CompProperties_Power">
        <compClass>CompPowerTrader</compClass>
        <basePowerConsumption>350</basePowerConsumption>
        <idlePowerDraw>50</idlePowerDraw>
      </li>
    </comps>
  </ThingDef>
  
  <ThingDef ParentName="MechGestatorBase">
    <defName>LargeMechGestator</defName>
    <label>large mech gestator</label>
    <description>A tank of mechanite-rich fluid with support tubes for feeding in materials and nutrients. Mechanitors can use it to produce new mechanoids or to resurrect dead mechanoids. This tier of mech gestator is capable of generating medium, heavy, and ultraheavy mechs.\n\nThe process uses harsh chemicals which are stored in toxic wastepacks. Haulers must remove the wastepacks from time to time.</description>
    <graphicData>
      <texPath>Things/Building/Production/MechGestatorLarge</texPath>
      <graphicClass>Graphic_Multi</graphicClass>
      <drawSize>(6, 5)</drawSize>
      <shadowData>
        <volume>(3.8, 3, 2.7)</volume>
      </shadowData>
    </graphicData>
    <uiIconPath>Things/Building/Production/MechGestatorLarge_MenuIcon</uiIconPath>
    <uiIconScale>1.5</uiIconScale>
    <interactionCellOffset>(2,0,2)</interactionCellOffset>
    <statBases>
      <MaxHitPoints>350</MaxHitPoints>
      <WorkToBuild>16000</WorkToBuild>
      <Mass>35</Mass>
      <Flammability>0.5</Flammability>
    </statBases>
    <size>(4,3)</size>
    <costList>
      <Steel>300</Steel>
      <ComponentIndustrial>6</ComponentIndustrial>
    </costList>
    <researchPrerequisites>
      <li>StandardMechtech</li>
    </researchPrerequisites>
    <building>
      <barDrawData>
        <north>
          <preRotationOffset>(-1.59, 1.4)</preRotationOffset>
          <size>(0.72, 0.12)</size>
        </north>
        <south>
          <preRotationOffset>(-1.59, 1.33)</preRotationOffset>
          <size>(0.72, 0.12)</size>
        </south>
        <east>
          <preRotationOffset>(1.59, -1.4)</preRotationOffset>
          <size>(0.72, 0.12)</size>
        </east>
        <west>
          <preRotationOffset>(-1.59, -1.4)</preRotationOffset>
          <size>(0.72, 0.12)</size>
        </west>
      </barDrawData>
      <formingGraphicData>
        <texPath>Things/Pawn/Mechanoid/HalfGestatedMechLarge</texPath>
        <graphicClass>Graphic_Single</graphicClass>
        <drawSize>(1.65,1.65)</drawSize>
      </formingGraphicData>
      <formingMechBobSpeed>0.0005</formingMechBobSpeed>
      <formingMechYBobDistance>0.08</formingMechYBobDistance>
      <formingMechPerRotationOffset>
        <li>(0, 0, 0.225)</li>
        <li>(-0.15, 0, 0.225)</li>
        <li>(0, 0, 0.225)</li>
        <li>(0.15, 0, 0.225)</li>
      </formingMechPerRotationOffset>
      <mechGestatorCylinderGraphic>
        <texPath>Things/Building/Production/MechGestatorLargeGlass</texPath>
        <graphicClass>Graphic_Multi</graphicClass>
        <drawSize>(6, 5)</drawSize>
        <shaderType>Transparent</shaderType>
      </mechGestatorCylinderGraphic>
      <mechGestatorTopGraphic>
        <texPath>Things/Building/Production/MechGestatorLargeTop</texPath>
        <graphicClass>Graphic_Multi</graphicClass>
        <drawSize>(6, 5)</drawSize>
      </mechGestatorTopGraphic>
      <gestatorFormingMote>
        <north>MechGestatorFormingLarge_North</north>
        <east>MechGestatorFormingLarge_East</east>
        <west>MechGestatorFormingLarge_West</west>
        <south>MechGestatorFormingLarge_South</south>
      </gestatorFormingMote>
      <gestatorCycleCompleteMote>
        <north>MechGestatorCycleCompleteLarge_North</north>
        <east>MechGestatorCycleCompleteLarge_East</east>
        <west>MechGestatorCycleCompleteLarge_West</west>
        <south>MechGestatorCycleCompleteLarge_South</south>
      </gestatorCycleCompleteMote>
      <gestatorFormedMote>
        <north>MechGestatorFormedLarge_North</north>
        <east>MechGestatorFormedLarge_East</east>
        <west>MechGestatorFormedLarge_West</west>
        <south>MechGestatorFormedLarge_South</south>
      </gestatorFormedMote>
    </building>
    <constructionSkillPrerequisite>6</constructionSkillPrerequisite>
    <recipes>
      <li>Pikeman</li>
      <li>Scorcher</li>
      <li>Scyther</li>
      <li>Tunneler</li>
      <li>Lancer</li>
      <li>Tesseron</li>
      <li>Legionary</li>
      <li>CentipedeGunner</li>
      <li>CentipedeBurner</li>
      <li>CentipedeBlaster</li>
      <li>Diabolus</li>
      <li>Centurion</li>
      <li>Warqueen</li>
      <li>ResurrectMediumMech</li>
      <li>ResurrectHeavyMech</li>
      <li>ResurrectUltraheavyMech</li>
    </recipes>
    <comps>
      <li Class="CompProperties_Power">
        <compClass>CompPowerTrader</compClass>
        <basePowerConsumption>500</basePowerConsumption>
        <idlePowerDraw>50</idlePowerDraw>
      </li>
    </comps>
  </ThingDef>

  <!-- Subcore -->
  <ThingDef ParentName="BenchBase">
    <defName>SubcoreEncoder</defName>
    <label>subcore encoder</label>
    <description>A work station where a mechanitor can produce basic-tier subcores. Subcores are the brains of mechanoids, and one is required to produce any mechanoid. Basic subcores can only be used in simple basic-tier mechs.\n\nHigher tier subcores can be created by building a subcore softscanner or a subcore ripscanner.</description>
    <thingClass>Building_WorkTable</thingClass>
    <canOverlapZones>false</canOverlapZones>
    <passability>PassThroughOnly</passability>
    <fillPercent>0.3</fillPercent>
    <graphicData>
      <texPath>Things/Building/Production/SubcoreEncoder</texPath>
      <graphicClass>Graphic_Multi</graphicClass>
      <drawSize>(3,2)</drawSize>
      <shadowData>
        <volume>(3, 2, 1.9)</volume>
      </shadowData>
    </graphicData>
    <altitudeLayer>Building</altitudeLayer>
    <building>
      <ai_chillDestination>false</ai_chillDestination>
      <buildingTags>
        <li>Biotech</li>
      </buildingTags>
    </building>
    <constructionSkillPrerequisite>4</constructionSkillPrerequisite>
    <designationCategory>Biotech</designationCategory>
    <uiOrder>2430</uiOrder>
    <statBases>
      <MaxHitPoints>200</MaxHitPoints>
      <WorkToBuild>8000</WorkToBuild>
      <Mass>25</Mass>
      <Flammability>0.5</Flammability>
    </statBases>
    <size>(3,2)</size>
    <costList>
      <Steel>100</Steel>
      <ComponentIndustrial>3</ComponentIndustrial>
    </costList>
    <researchPrerequisites>
      <li>BasicMechtech</li>
    </researchPrerequisites>
    <hasInteractionCell>true</hasInteractionCell>
    <interactionCellOffset>(0,0,-1)</interactionCellOffset>
    <inspectorTabs>
      <li>ITab_Bills</li>
    </inspectorTabs>
    <comps>
      <li Class="CompProperties_MoteEmitter">
        <perRotationMotes>
          <li>Mote_SubcoreEncoderNorth</li>
          <li>Mote_SubcoreEncoderEast</li>
          <li>Mote_SubcoreEncoderSouth</li>
          <li>Mote_SubcoreEncoderEast</li>
        </perRotationMotes>
        <offsetNorth>(0, 0, -0.34)</offsetNorth>
        <offsetSouth>(0, 0, 0.66)</offsetSouth>
        <offsetWest>(0.575, 0, 0.24)</offsetWest>
        <offsetEast>(-0.575, 0, 0.24)</offsetEast>
        <useParentRotation>true</useParentRotation>
      </li>
      <li Class="CompProperties_Power">
        <compClass>CompPowerTrader</compClass>
        <basePowerConsumption>50</basePowerConsumption>
      </li>
      <li Class="CompProperties_Flickable"/>
    </comps>
    <placeWorkers>
      <li>PlaceWorker_PreventInteractionSpotOverlap</li>
    </placeWorkers>
  </ThingDef>

  <ThingDef Abstract="True" Name="SubcoreScannerBase" ParentName="BuildingBase">
    <thingClass>Building_SubcoreScanner</thingClass>
    <containedPawnsSelectable>true</containedPawnsSelectable>
    <altitudeLayer>Building</altitudeLayer>
    <passability>PassThroughOnly</passability>
    <fillPercent>0.3</fillPercent>
    <graphicData>
      <graphicClass>Graphic_Multi</graphicClass>
      <drawSize>(3,2)</drawSize>
      <shadowData>
        <volume>(3, 2, 1.9)</volume>
      </shadowData>
    </graphicData>
    <building>
      <ai_chillDestination>false</ai_chillDestination>
      <buildingTags>
        <li>Biotech</li>
      </buildingTags>
      <subcoreScannerFixedIngredients>
        <li>
          <filter>
            <thingDefs>
              <li>Steel</li>
            </thingDefs>
          </filter>
          <count>50</count>
        </li>
        <li>
          <filter>
            <thingDefs>
              <li>ComponentIndustrial</li>
            </thingDefs>
          </filter>
          <count>4</count>
        </li>
      </subcoreScannerFixedIngredients>
    </building>
    <designationCategory>Biotech</designationCategory>
    <uiOrder>2430</uiOrder>
    <canOverlapZones>false</canOverlapZones>
    <size>(3,2)</size>
    <hasInteractionCell>true</hasInteractionCell>
    <interactionCellOffset>(0,0,-1)</interactionCellOffset>
    <statBases>
      <Mass>25</Mass>
      <Flammability>0.5</Flammability>
    </statBases>
    <comps>
      <li Class="CompProperties_Power">
        <compClass>CompPowerTrader</compClass>
        <basePowerConsumption>150</basePowerConsumption>
      </li>
      <li Class="CompProperties_Flickable"/>
    </comps>
    <tickerType>Normal</tickerType>
    <drawerType>MapMeshAndRealTime</drawerType>
  </ThingDef>

  <ThingDef ParentName="SubcoreScannerBase">
    <defName>SubcoreSoftscanner</defName>
    <label>subcore softscanner</label>
    <description>A pod with thousands of tiny tissue probes and a high-energy brain scanner. Once a person is inserted, the system uses the probes and scanner to sense a neuro-psychic pattern that it can analog-transfer to a new standard-tier mechanoid subcore. The person will be left temporarily sick, but unharmed.\n\nSubcores are mechanoid brains and producing any mechanoid requires one. Standard-tier subcores produced by this softscanner can only power standard-tier mechanoids.\n\nHigher tier subcores can be created by building a subcore ripscanner.</description>
    <graphicData>
      <texPath>Things/Building/Production/SubcoreSoftscanner</texPath>
    </graphicData>
    <statBases>
      <WorkToBuild>8000</WorkToBuild>
      <MaxHitPoints>250</MaxHitPoints>
    </statBases>
    <costList>
      <Steel>200</Steel>
      <Plasteel>50</Plasteel>
      <ComponentIndustrial>4</ComponentIndustrial>
    </costList>
    <researchPrerequisites>
      <li>StandardMechtech</li>
    </researchPrerequisites>
    <building>
      <subcoreScannerOutputDef>SubcoreRegular</subcoreScannerOutputDef>
      <subcoreScannerStartEffect>SubcoreSoftscanner_Start</subcoreScannerStartEffect>
      <subcoreScannerWorking>SubcoreSoftscanner_Working</subcoreScannerWorking>
      <subcoreScannerComplete>SubcoreSoftscanner_Complete</subcoreScannerComplete>
      <subcoreScannerHediff>ScanningSickness</subcoreScannerHediff>
    </building>
    <constructionSkillPrerequisite>5</constructionSkillPrerequisite>
  </ThingDef>
  
  <ThingDef ParentName="SubcoreScannerBase">
    <defName>SubcoreRipscanner</defName>
    <label>subcore ripscanner</label>
    <description>A pod with thousands of tissue probe injectors and an ultra-high-power vaporizing brain scanner. Once a person is inserted, the device will insert the probes into their body through the skin while the ripscanner chews through the brain, reading the reflected radiation and destroying the brain in the process. Ripscanning the mind generates a neuro-psychic pattern that the scanner can analog-transfer to a new high-tier mechanoid subcore.\n\nSubcores are mechanoid brains and producing any mechanoid requires one. High-tier subcores produced by this ripscanner can only power high-tier mechanoids.</description>
    <graphicData>
      <texPath>Things/Building/Production/SubcoreRipscanner</texPath>
    </graphicData>
    <statBases>
      <WorkToBuild>1200</WorkToBuild>
      <MaxHitPoints>250</MaxHitPoints>
    </statBases>
    <costList>
      <Steel>200</Steel>
      <Plasteel>150</Plasteel>
      <ComponentIndustrial>6</ComponentIndustrial>
    </costList>
    <researchPrerequisites>
      <li>HighMechtech</li>
    </researchPrerequisites>
    <building>
      <subcoreScannerOutputDef>SubcoreHigh</subcoreScannerOutputDef>
      <subcoreScannerStartEffect>SubcoreRipscanner_Start</subcoreScannerStartEffect>
      <destroyBrain>true</destroyBrain>
      <subcoreScannerWorking>SubcoreRipscanner_Working</subcoreScannerWorking>
      <subcoreScannerComplete>SubcoreRipscanner_Complete</subcoreScannerComplete>
    </building>
    <constructionSkillPrerequisite>6</constructionSkillPrerequisite>
  </ThingDef>

</Defs>