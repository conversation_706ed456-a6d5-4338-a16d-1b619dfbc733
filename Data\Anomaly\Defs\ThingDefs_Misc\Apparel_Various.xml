<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <ThingDef ParentName="ApparelMakeableBase">
    <defName>Apparel_LabCoat</defName>
    <label>lab coat</label>
    <description>A knee-length overcoat worn over other clothing. It has a number of large pockets for holding scientific equipment, helping the wearer conduct research more efficiently.</description>
    <possessionCount>1</possessionCount>
    <recipeMaker>
      <researchPrerequisite>ComplexClothing</researchPrerequisite>
      <displayPriority>210</displayPriority>
    </recipeMaker>
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/LabCoat/LabCoat</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>0.85</drawSize>
    </graphicData>
    <costStuffCount>70</costStuffCount>
    <stuffCategories>
      <li>Fabric</li>
      <li>Leathery</li>
    </stuffCategories>
    <thingCategories>
      <li>ApparelMisc</li>
    </thingCategories>
    <statBases>
      <MaxHitPoints>140</MaxHitPoints>
      <WorkToMake>5000</WorkToMake>
      <Mass>1.5</Mass>
      <StuffEffectMultiplierArmor>0.2</StuffEffectMultiplierArmor>
      <StuffEffectMultiplierInsulation_Cold>0.50</StuffEffectMultiplierInsulation_Cold>
      <StuffEffectMultiplierInsulation_Heat>0.60</StuffEffectMultiplierInsulation_Heat>
      <EquipDelay>1.5</EquipDelay>
    </statBases>
    <equippedStatOffsets>
      <ResearchSpeed>0.05</ResearchSpeed>
      <EntityStudyRate>0.1</EntityStudyRate>
    </equippedStatOffsets>
    <apparel>
      <bodyPartGroups>
        <li>Torso</li>
        <li>Neck</li>
        <li>Shoulders</li>
        <li>Arms</li>
      </bodyPartGroups>
      <wornGraphicPath>Things/Pawn/Humanlike/Apparel/LabCoat/LabCoat</wornGraphicPath>
      <layers>
        <li>Shell</li>
      </layers>
      <canBeDesiredForIdeo>false</canBeDesiredForIdeo>
      <canBeGeneratedToSatisfyWarmth>false</canBeGeneratedToSatisfyWarmth>
      <canBeGeneratedToSatisfyToxicEnvironmentResistance>false</canBeGeneratedToSatisfyToxicEnvironmentResistance>
    </apparel>
    <colorGenerator Class="ColorGenerator_Options">
      <options>
        <li>
          <weight>10</weight>
          <only>(0.33,0.33,0.33)</only>
        </li>
        <li>
          <weight>10</weight>
          <only>(0.65,0.65,0.65)</only>
        </li>
        <li>
          <weight>10</weight>
          <only>(0.4,0.3,0.15)</only>
        </li>
        <li>
          <weight>15</weight>
          <min>(0.3,0.3,0.3)</min>
          <max>(0.5,0.5,0.5)</max>
        </li>
        <li>
          <weight>6</weight>
          <min>(0.5,0.5,0.5)</min>
          <max>(1,1,1)</max>
        </li>
      </options>
    </colorGenerator>
    <tradeTags>
      <li>BasicClothing</li>
    </tradeTags>
  </ThingDef>
  
</Defs>