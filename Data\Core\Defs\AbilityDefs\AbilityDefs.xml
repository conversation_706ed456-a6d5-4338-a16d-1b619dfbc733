<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <AbilityDef Name="PsycastBase" Abstract="True">
    <abilityClass>Psycast</abilityClass>
    <category>Psychic</category>
    <statBases>
      <Ability_Duration>0</Ability_Duration>
    </statBases>
    <warmupSound>PsycastCastLoop</warmupSound>
    <verbProperties>
      <verbClass>Verb_CastPsycast</verbClass>
      <range>24.9</range>
      <requireLineOfSight>True</requireLineOfSight>
      <warmupTime>1</warmupTime>
      <targetParams>
        <canTargetBuildings>False</canTargetBuildings>
        <canTargetAnimals>False</canTargetAnimals>
        <thingCategory>Item</thingCategory>
        <mapObjectTargetsMustBeAutoAttackable>False</mapObjectTargetsMustBeAutoAttackable>
      </targetParams>
    </verbProperties>
  </AbilityDef>

  <AbilityDef Abstract="True" Name="SpeechBase">
    <iconPath>Things/Mote/SpeechSymbols/Speech</iconPath>
    <hotKey>Misc12</hotKey>
    <gizmoClass>Command_AbilitySpeech</gizmoClass>
    <displayGizmoWhileUndrafted>True</displayGizmoWhileUndrafted>
    <disableGizmoWhileUndrafted>False</disableGizmoWhileUndrafted>
    <targetRequired>False</targetRequired>
    <hostile>false</hostile>
    <casterMustBeCapableOfViolence>false</casterMustBeCapableOfViolence>
    <verbProperties>
      <verbClass>Verb_CastAbility</verbClass>
      <range>-1</range>
    </verbProperties>
    <comps>
      <li Class="CompProperties_AbilityRequiresCapacity">
        <capacity>Talking</capacity>
      </li>
    </comps>
  </AbilityDef>

  <AbilityDef Name="AbilityTouchBase" Abstract="True">
    <stunTargetWhileCasting>true</stunTargetWhileCasting>
    <displayGizmoWhileUndrafted>true</displayGizmoWhileUndrafted>
    <disableGizmoWhileUndrafted>false</disableGizmoWhileUndrafted>
    <jobDef>CastAbilityOnThingMelee</jobDef>
  </AbilityDef>

</Defs>