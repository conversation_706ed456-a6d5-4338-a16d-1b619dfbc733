<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <QuestScriptDef Name="AncientMechanitorComplexBase">
    <defName>OpportunitySite_AncientComplex_Mechanitor</defName>
    <rootSelectionWeight>0</rootSelectionWeight>
    <rootMinPoints>350</rootMinPoints>
    <autoAccept>true</autoAccept>
    <defaultChallengeRating>1</defaultChallengeRating>
    <isRootSpecial>true</isRootSpecial>
    <questNameRules>
      <rulesStrings>
        
        <!-- quest name -->
        <li>questName(p=3)->[complexAdj] mechanitor [complex]</li>
        <li>questName->mechanitor [complex]</li>

        <li>complexAdj->secret</li>
        <li>complexAdj->ancient</li>
        <li>complexAdj->old</li>
        <li>complexAdj->hidden</li>
        <li>complexAdj->dusty</li>
        <li>complexAdj->dangerous</li>
        <li>complexAdj->hidden</li>

        <li>complex->complex</li>
        <li>complex->lair</li>
        <li>complex->base</li>
        <li>complex->compound</li>
        <li>complex->crypt</li>
        <li>complex->mechplex</li>
        <li>complex->mechsite</li>

      </rulesStrings>
    </questNameRules>
    <questDescriptionRules>
      <rulesStrings>
        <li>questDescription->You've [discoveryDesc] of an ancient complex nearby that contains the remains of a long-dead mechanitor.\n\nMechanitors can create, control, and manipulate mechanoids for work and combat. If you can break into the complex and collect the mechanitor's corpse, you can extract their mechlink and turn one of your own colonists into a mechanitor.\n\nBe warned - these kinds of structures can contain a variety of threats, and your activity at the complex might draw unwanted attention.</li>
        
        <!-- Snippets -->
        <li>discoveryDesc(discovered==false)->learned</li>
        <li>discoveryDesc(discovered==true)->determined the location</li>

      </rulesStrings>
    </questDescriptionRules>
    <questDescriptionAndNameRules>
      <rulesStrings>
      </rulesStrings>
    </questDescriptionAndNameRules>
    <root Class="QuestNode_Root_Loot_AncientComplex_Mechanitor" />
  </QuestScriptDef>

</Defs>