<?xml version="1.0" encoding="utf-8"?>

<Defs>

  <BackstoryDef>
    <defName>MadScientist22</defName>
    <identifier>MadScientist22</identifier>
    <slot>Adulthood</slot>
    <title>mad scientist</title>
    <titleShort>scientist</titleShort>
    <description>Stripped of [PAWN_possessive] life's work and exiled after [PAWN_possessive] unethical experiments on the survivors of the Callos IX incident were published, [PAWN_nameDef] learned the skills necessary to survive in the outer rim.\n\nContinuing [PAWN_possessive] research with nothing left to lose, [PAWN_pronoun] made minions to carry out the tasks that bored him.</description>
    <skillGains>
      <Intellectual>5</Intellectual>
      <Shooting>5</Shooting>
      <Medicine>4</Medicine>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>Hauling, Mining</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
    <possessions>
      <Apparel_PackTurret MayRequire="Ludeon.RimWorld.Anomaly">1</Apparel_PackTurret>
    </possessions>
  </BackstoryDef>

  <BackstoryDef>
    <defName>CivilEngineer2</defName>
    <identifier>CivilEngineer2</identifier>
    <slot>Adulthood</slot>
    <title>civil engineer</title>
    <titleShort>engineer</titleShort>
    <description>[PAWN_nameDef] was a well-known civil engineer. [PAWN_possessive] job involved designing and maintaining rock fortification structures. [PAWN_pronoun] did enough statistical analysis to keep [PAWN_possessive] mind sharp.</description>
    <skillGains>
      <Construction>7</Construction>
      <Intellectual>3</Intellectual>
      <Mining>2</Mining>
      <Social>-3</Social>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ArmyCook0</defName>
    <identifier>ArmyCook0</identifier>
    <slot>Adulthood</slot>
    <title>army cook</title>
    <titleShort>cook</titleShort>
    <description>[PAWN_nameDef] joined the army as a worker and spent most of [PAWN_possessive] time cooking and repairing. While [PAWN_pronoun] was stationed on a dangerous planet, [PAWN_pronoun] underwent basic shooting and survival training.</description>
    <skillGains>
      <Construction>2</Construction>
      <Intellectual>-2</Intellectual>
      <Shooting>2</Shooting>
      <Melee>2</Melee>
      <Cooking>3</Cooking>
      <Crafting>1</Crafting>
    </skillGains>
    <workDisables>Caring, Social, Firefighting</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>UrbworldPolitican92</defName>
    <identifier>UrbworldPolitican92</identifier>
    <slot>Adulthood</slot>
    <title>urbworld politican</title>
    <titleShort>politician</titleShort>
    <description>It's no secret that the Companies own much of the urbworlds, but someone has to take the bribes. [PAWN_nameDef] was one such figurehead.\n\n[PAWN_pronoun] learned how to manipulate the hopes and dreams of the people while living comfortably in a penthouse.</description>
    <skillGains>
      <Social>8</Social>
    </skillGains>
    <workDisables>ManualDumb, Crafting</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <forcedTraits>
      <Greedy>0</Greedy>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>GraphicDesigner33</defName>
    <identifier>GraphicDesigner33</identifier>
    <slot>Adulthood</slot>
    <title>graphic designer</title>
    <titleShort>designer</titleShort>
    <description>[PAWN_nameDef]' interest in artistic expression led [PAWN_objective] to become a designer. [PAWN_possessive] eye for detail made [PAWN_objective] a reasonably well-known artist on [PAWN_possessive] planet, though [PAWN_possessive] meticulousness often led [PAWN_objective] to spend too much time perfecting a single project.</description>
    <skillGains>
      <Artistic>7</Artistic>
      <Crafting>1</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpaceTechnician29</defName>
    <identifier>SpaceTechnician29</identifier>
    <slot>Adulthood</slot>
    <title>space technician</title>
    <titleShort>technician</titleShort>
    <description>[PAWN_nameDef] finally managed to flee [PAWN_possessive] criminal life by getting a maintenance job on a nearby space station. [PAWN_pronoun] performed multiple repairing and building operations. [PAWN_pronoun] also used to participate in basic research activities and often defused co-workers' arguments. [PAWN_pronoun] never liked doing art.</description>
    <skillGains>
      <Construction>5</Construction>
      <Intellectual>1</Intellectual>
      <Mining>2</Mining>
      <Social>1</Social>
    </skillGains>
    <workDisables>Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>DoomsdayPariah18</defName>
    <identifier>DoomsdayPariah18</identifier>
    <slot>Adulthood</slot>
    <title>doomsday pariah</title>
    <titleShort>pariah</titleShort>
    <description>[PAWN_nameDef] managed to open a vault of otherworldly technology while scavenging a dig site. [PAWN_pronoun] unwittingly triggered a doomsday device that cleansed the planet of all life. More interested in the tech than human life, [PAWN_pronoun] boarded the vault's spacecraft and departed to find more relics to abuse.</description>
    <skillGains>
      <Construction>2</Construction>
      <Intellectual>3</Intellectual>
      <Shooting>2</Shooting>
      <Melee>2</Melee>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>Caring, Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <shuffleable>False</shuffleable>
    <possessions>
      <Apparel_CeremonialCultistMask MayRequire="Ludeon.RimWorld.Anomaly">1</Apparel_CeremonialCultistMask>
    </possessions>
  </BackstoryDef>

  <BackstoryDef>
    <defName>CombatEngineer30</defName>
    <identifier>CombatEngineer30</identifier>
    <slot>Adulthood</slot>
    <title>combat engineer</title>
    <titleShort>engineer</titleShort>
    <description>[PAWN_nameDef] was recruited by the military as an engineer and set to work improving the navy's space shuttles. The harsh training taught [PAWN_objective] how to build and repair military vehicles and structures.\n\nOne mission left [PAWN_nameDef] with pyrophobia and a strong desire to avoid plants.</description>
    <skillGains>
      <Construction>6</Construction>
      <Intellectual>3</Intellectual>
      <Shooting>4</Shooting>
      <Social>-3</Social>
      <Cooking>-2</Cooking>
      <Medicine>2</Medicine>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>Artistic, Firefighting, PlantWork</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MilitaryEngineer45</defName>
    <identifier>MilitaryEngineer45</identifier>
    <slot>Adulthood</slot>
    <title>military engineer</title>
    <titleShort>engineer</titleShort>
    <description>[PAWN_nameDef] worked as an engineer for a colonial military. [PAWN_pronoun] got to play with weapons, murder-drones, and other 'fun' stuff.\n\n[PAWN_pronoun] specialized in repairing and modifying mechanoids' tools. Sometimes [PAWN_pronoun] tested them [PAWN_objective]self.</description>
    <skillGains>
      <Construction>3</Construction>
      <Shooting>3</Shooting>
      <Melee>1</Melee>
      <Crafting>4</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ColiseumFighter22</defName>
    <identifier>ColiseumFighter22</identifier>
    <slot>Adulthood</slot>
    <title>coliseum fighter</title>
    <titleShort>fighter</titleShort>
    <description>Applying the knowledge that [PAWN_pronoun] gained from years around fighters, [PAWN_pronoun] became a successful gladiator in the coliseum that [PAWN_pronoun] once cleaned.</description>
    <skillGains>
      <Intellectual>2</Intellectual>
      <Shooting>5</Shooting>
      <Social>-1</Social>
      <Crafting>3</Crafting>
    </skillGains>
    <workDisables>Caring, Artistic, PlantWork, Mining</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <forcedTraits>
      <Brawler>0</Brawler>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>FighterController67</defName>
    <identifier>FighterController67</identifier>
    <slot>Adulthood</slot>
    <title>fighter controller</title>
    <titleShort>controller</titleShort>
    <description>During the Zartha crisis, [PAWN_nameDef] fought across many worlds, providing the vital link between the ground and the air as a fighter controller. During the campaign, [PAWN_pronoun] saw friends injured and killed. This mental trauma haunts [PAWN_possessive] dreams to this day, but [PAWN_possessive] aim remains sharp.</description>
    <skillGains>
      <Shooting>5</Shooting>
      <Melee>5</Melee>
      <Social>2</Social>
      <Cooking>1</Cooking>
      <Medicine>3</Medicine>
    </skillGains>
    <workDisables>Intellectual</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <disallowedTraits>
      <ShootingAccuracy>-1</ShootingAccuracy>
    </disallowedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>FerventResearcher72</defName>
    <identifier>FerventResearcher72</identifier>
    <slot>Adulthood</slot>
    <title>fervent researcher</title>
    <titleShort>researcher</titleShort>
    <description>As a young adult, [PAWN_nameDef] was sure that [PAWN_possessive] planet and its people were nearing their time of transcendence.\n\nA firm belief that [PAWN_possessive] research would be the catalyst drove [PAWN_objective] to increasingly alarming acts as bureaucrats and bioethics committees worked to stall [PAWN_possessive] progress.</description>
    <skillGains>
      <Intellectual>6</Intellectual>
      <Shooting>2</Shooting>
      <Medicine>8</Medicine>
    </skillGains>
    <workDisables>ManualDumb, ManualSkilled</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <forcedTraits>
      <Transhumanist>0</Transhumanist>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>VengefulExplorer54</defName>
    <identifier>VengefulExplorer54</identifier>
    <slot>Adulthood</slot>
    <title>vengeful explorer</title>
    <titleShort>explorer</titleShort>
    <description>A violent drifter without a home, [PAWN_nameDef] and [PAWN_possessive] father fought many battles across hundreds of years. [PAWN_pronoun] became a grizzled fighter and crack shot.\n\nAfter [PAWN_possessive] father's death in battle, [PAWN_nameDef] began exploring the stars alone, seeking [PAWN_possessive] father's killer, on the hunt for revenge.</description>
    <skillGains>
      <Construction>4</Construction>
      <Shooting>6</Shooting>
      <Melee>5</Melee>
    </skillGains>
    <workDisables>Caring</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>BanishedSoldier85</defName>
    <identifier>BanishedSoldier85</identifier>
    <slot>Adulthood</slot>
    <title>banished soldier</title>
    <titleShort>outlaw</titleShort>
    <description>[PAWN_nameDef] became the most physically intimidating of the slaves, spending [PAWN_possessive] little free time obsessively sculpting [PAWN_possessive] body. [PAWN_possessive] efforts eventually paid off when the lord offered [PAWN_objective] a job as a personal bodyguard.\n\nLater, when [PAWN_pronoun] plotted to use [PAWN_possessive] position to kill the lord and free the slaves, [PAWN_pronoun] was discovered and barely escaped with [PAWN_possessive] life.</description>
    <skillGains>
      <Melee>7</Melee>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>StalwartFarmer21</defName>
    <identifier>StalwartFarmer21</identifier>
    <slot>Adulthood</slot>
    <title>stalwart farmer</title>
    <titleShort>farmer</titleShort>
    <description>With the farm under constant threat from brigands, [PAWN_nameDef] learned to defend [PAWN_objective]self with [PAWN_possessive] father's hunting rifle. [PAWN_pronoun] also mastered how to construct fences to keep livestock in and the local wildlife out. [PAWN_nameDef] tried to paint a picture of [PAWN_possessive] home and learned [PAWN_pronoun] can only draw scribbles and ducks.</description>
    <skillGains>
      <Construction>3</Construction>
      <Plants>3</Plants>
      <Intellectual>-2</Intellectual>
      <Mining>2</Mining>
      <Shooting>5</Shooting>
      <Melee>2</Melee>
    </skillGains>
    <workDisables>Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Hunter</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <possessions>
      <Hay>10</Hay>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpaceResearcher25</defName>
    <identifier>SpaceResearcher25</identifier>
    <slot>Adulthood</slot>
    <title>space researcher</title>
    <titleShort>researcher</titleShort>
    <description>[PAWN_nameDef] inherited [PAWN_possessive] parents' ship and carried on their research.\n\n[PAWN_possessive] childhood experiences with diverse cultures allowed [PAWN_objective] to preserve [PAWN_possessive] caring nature despite spending most of [PAWN_possessive] life isolated in space.</description>
    <skillGains>
      <Plants>2</Plants>
      <Intellectual>6</Intellectual>
      <Mining>-1</Mining>
      <Crafting>3</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <forcedTraits>
      <Kind>0</Kind>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>GeneticScientist66</defName>
    <identifier>GeneticScientist66</identifier>
    <slot>Adulthood</slot>
    <title>genetic scientist</title>
    <titleShort>geneticist</titleShort>
    <description>After the war [PAWN_pronoun] joined a genetic engineering firm. [PAWN_pronoun] worked on enhancements for humans and livestock. [PAWN_possessive] experience in inflicting and healing wounds was very beneficial to [PAWN_possessive] work, but [PAWN_pronoun] developed a black sense of humor. This made social gatherings awkward.</description>
    <skillGains>
      <Intellectual>4</Intellectual>
      <Shooting>2</Shooting>
      <Medicine>3</Medicine>
    </skillGains>
    <workDisables>Social, Artistic, Crafting, Cooking</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <forcedTraits>
      <Abrasive>0</Abrasive>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Reporter71</defName>
    <identifier>Reporter71</identifier>
    <slot>Adulthood</slot>
    <title>reporter</title>
    <titleShort>reporter</titleShort>
    <description>[PAWN_nameDef] spent many years as an investigative reporter. [PAWN_pronoun] was notorious for making chaotic reports from exotic locations.\n\nAlthough seen as a lightweight by [PAWN_possessive] critics, [PAWN_pronoun] managed to break several high-profile stories after lengthy investigations.</description>
    <skillGains>
      <Intellectual>2</Intellectual>
      <Social>4</Social>
      <Artistic>3</Artistic>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>PopIdol59</defName>
    <identifier>PopIdol59</identifier>
    <slot>Adulthood</slot>
    <title>pop idol</title>
    <titleShort>pop idol</titleShort>
    <description>[PAWN_nameDef] performed and did interviews constantly. [PAWN_pronoun] refined [PAWN_possessive] skills in dancing and singing, and became a master of social presentation.</description>
    <skillGains>
      <Shooting>-4</Shooting>
      <Melee>-4</Melee>
      <Social>8</Social>
      <Artistic>8</Artistic>
    </skillGains>
    <workDisables>ManualDumb, Cleaning, Hauling, PlantWork, Mining</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <forcedTraits>
      <Beauty>2</Beauty>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>UnstableButcher31</defName>
    <identifier>UnstableButcher31</identifier>
    <slot>Adulthood</slot>
    <title>unstable butcher</title>
    <titleShort>butcher</titleShort>
    <description>[PAWN_nameDef] began hearing voices. The animals were talking to [PAWN_objective], insulting him. [PAWN_pronoun] came to hate them, and eventually killed [PAWN_possessive] furry former friends. [PAWN_pronoun] started selling the meat just to get human interaction.</description>
    <skillGains>
      <Construction>4</Construction>
      <Intellectual>-4</Intellectual>
      <Mining>3</Mining>
      <Melee>6</Melee>
      <Social>2</Social>
      <Medicine>-3</Medicine>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Archaeologist85</defName>
    <identifier>Archaeologist85</identifier>
    <slot>Adulthood</slot>
    <title>archaeologist</title>
    <titleShort>explorer</titleShort>
    <description>[PAWN_pronoun] was trapped in an uninspired life. One day, [PAWN_pronoun] jumped on a ship to learn archaeology and see the universe.\n\n[PAWN_pronoun] learned how to survive. [PAWN_pronoun] also learned about people - although [PAWN_pronoun] understood the dead better than the living.</description>
    <skillGains>
      <Construction>4</Construction>
      <Intellectual>5</Intellectual>
      <Shooting>2</Shooting>
      <Social>-2</Social>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>PirateKing69</defName>
    <identifier>PirateKing69</identifier>
    <slot>Adulthood</slot>
    <title>pirate king</title>
    <titleShort>pirate</titleShort>
    <description>With no real care for other people, [PAWN_nameDef] fit in perfectly with the pirate crew that had once held [PAWN_objective] captive. Shortly after joining the crew, [PAWN_nameDef] began to kill anyone or anything that got in [PAWN_possessive] way—including [PAWN_possessive] crewmates.</description>
    <skillGains>
      <Shooting>7</Shooting>
      <Melee>5</Melee>
      <Artistic>-2</Artistic>
    </skillGains>
    <workDisables>Caring, Social</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <forcedTraits>
      <Bloodlust>0</Bloodlust>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MilitaryInventor35</defName>
    <identifier>MilitaryInventor35</identifier>
    <slot>Adulthood</slot>
    <title>military inventor</title>
    <titleShort>inventor</titleShort>
    <description>Jackson, in a final act of rebellion, left [PAWN_possessive] father's lab and joined the local Imperial military.\n\n[PAWN_pronoun] used [PAWN_possessive] laboratory skills to design machines of war. At this [PAWN_pronoun] was one of the best on [PAWN_possessive] planet.</description>
    <skillGains>
      <Construction>3</Construction>
      <Intellectual>3</Intellectual>
      <Shooting>1</Shooting>
      <Social>2</Social>
      <Crafting>3</Crafting>
    </skillGains>
    <workDisables>Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>ImperialCommon</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>EngineeredPilot75</defName>
    <identifier>EngineeredPilot75</identifier>
    <slot>Adulthood</slot>
    <title>engineered pilot</title>
    <titleShort>pilot</titleShort>
    <description>After [PAWN_possessive] province was besieged by one of its neighbors, [PAWN_nameDef] volunteered for a generic modification program that created pilots for the very fighter craft that [PAWN_pronoun] had helped develop. The modifications allowed [PAWN_objective] to pilot the ship, but also made [PAWN_objective] less able to focus on the people around him.\n\nBy this time, people were calling [PAWN_objective] "Squirrel."</description>
    <skillGains>
      <Shooting>5</Shooting>
      <Melee>2</Melee>
      <Social>-2</Social>
      <Medicine>2</Medicine>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>BanditLeader74</defName>
    <identifier>BanditLeader74</identifier>
    <slot>Adulthood</slot>
    <title>bandit leader</title>
    <titleShort>bandit</titleShort>
    <description>[PAWN_nameDef] rose up the ranks of the crime organization, eventually becoming the leader of one of their most remote cells of bandits. [PAWN_possessive] dog Rest never left [PAWN_possessive] side.</description>
    <skillGains>
      <Shooting>5</Shooting>
      <Melee>5</Melee>
      <Social>2</Social>
    </skillGains>
    <workDisables>Intellectual, Cleaning</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Gigolo68</defName>
    <identifier>Gigolo68</identifier>
    <slot>Adulthood</slot>
    <title>gigolo</title>
    <titleShort>gigolo</titleShort>
    <description>[PAWN_nameDef] sold [PAWN_objective]self into slavery for money to purchase boomrat pizza. [PAWN_pronoun] was then traded to the Zeglar colonies, where [PAWN_pronoun] worked as a male prostitute.\n\nIn a fluke event, [PAWN_pronoun] managed to join other escaping slaves as they hijacked a ship. After fighting with the captain of the slave crew over the last piece of boomrat pizza, [PAWN_nameDef] was sent off the ship in an escape pod.</description>
    <skillGains>
      <Shooting>3</Shooting>
      <Melee>6</Melee>
      <Cooking>7</Cooking>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <forcedTraits>
      <Beauty>1</Beauty>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ColonialGovernor78</defName>
    <identifier>ColonialGovernor78</identifier>
    <slot>Adulthood</slot>
    <title>colonial governor</title>
    <titleShort>governor</titleShort>
    <description>Disillusioned by rampant government corruption, [PAWN_nameDef] ran for governor and won. However, the local gangs and their pet officials soon forced [PAWN_objective] out of power.\n\n[PAWN_pronoun] left the community to return to [PAWN_possessive] life as a marshal.</description>
    <skillGains>
      <Construction>3</Construction>
      <Shooting>1</Shooting>
      <Social>6</Social>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MercenaryChef44</defName>
    <identifier>MercenaryChef44</identifier>
    <slot>Adulthood</slot>
    <title>mercenary chef</title>
    <titleShort>mercenary</titleShort>
    <description>Following in [PAWN_possessive] family's footsteps, [PAWN_nameDef] was a soldier for hire.\n\n[PAWN_pronoun] was a naturally gifted sharpshooter. However, [PAWN_possessive] real passion is in perfecting a new recipe or dissecting a new gadget.</description>
    <skillGains>
      <Construction>-3</Construction>
      <Mining>-3</Mining>
      <Shooting>4</Shooting>
      <Cooking>5</Cooking>
      <Crafting>5</Crafting>
    </skillGains>
    <workDisables>PlantWork</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <forcedTraits>
      <ShootingAccuracy>1</ShootingAccuracy>
    </forcedTraits>
    <possessions>
      <MealSurvivalPack>2</MealSurvivalPack>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>CargoPilot58</defName>
    <identifier>CargoPilot58</identifier>
    <slot>Adulthood</slot>
    <title>cargo pilot</title>
    <titleShort>pilot</titleShort>
    <description>[PAWN_nameDef] was brought up by cargo hands on a long-haul ore transport ship. [PAWN_pronoun] perfected [PAWN_possessive] skills at trading and manipulation of people while running weapons between systems.</description>
    <skillGains>
      <Construction>2</Construction>
      <Mining>1</Mining>
      <Shooting>4</Shooting>
      <Melee>2</Melee>
      <Social>1</Social>
    </skillGains>
    <workDisables>Cooking, Cleaning</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpaceHumanitarian9</defName>
    <identifier>SpaceHumanitarian9</identifier>
    <slot>Adulthood</slot>
    <title>space humanitarian</title>
    <titleShort>activist</titleShort>
    <description>After seeing the rampant poverty common on overcrowded urban worlds, [PAWN_nameDef] decided to dedicate [PAWN_possessive] life to helping the less fortunate. This crusade taught [PAWN_objective] how to care for the sick and the hungry.</description>
    <skillGains>
      <Plants>2</Plants>
      <Shooting>-2</Shooting>
      <Cooking>4</Cooking>
      <Medicine>2</Medicine>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ExecutiveOfficer5</defName>
    <identifier>ExecutiveOfficer5</identifier>
    <slot>Adulthood</slot>
    <title>executive officer</title>
    <titleShort>executive</titleShort>
    <description>[PAWN_nameDef] was an executive officer for a large space trading corporation. After a failed attempt at manipulating [PAWN_possessive] way up the corporate ladder, [PAWN_pronoun] was framed for the rape and murder of another executive and [PAWN_possessive] family.\n\n[PAWN_pronoun] was found guilty. However, because of [PAWN_possessive] family's influence, [PAWN_pronoun] was exiled instead of put to death.</description>
    <skillGains>
      <Construction>3</Construction>
      <Intellectual>4</Intellectual>
      <Melee>2</Melee>
      <Social>7</Social>
    </skillGains>
    <workDisables>Caring</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>RugbyPlayer71</defName>
    <identifier>RugbyPlayer71</identifier>
    <slot>Adulthood</slot>
    <title>rugby player</title>
    <titleShort>winger</titleShort>
    <description>After years of addiction to virtual reality games, [PAWN_nameDef] was saved when the doctors unplugged him.\n\nUnfortunately, they unplugged [PAWN_objective] during a match of Rugby Rampage 4. The mental backlash merged [PAWN_possessive] real identity and [PAWN_possessive] virtual identity, leaving [PAWN_objective] with the personality and skills of a rugby player.</description>
    <skillGains>
      <Construction>4</Construction>
      <Mining>3</Mining>
      <Melee>5</Melee>
    </skillGains>
    <workDisables>Cleaning</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <forcedTraits>
      <Brawler>0</Brawler>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>StateEngineer60</defName>
    <identifier>StateEngineer60</identifier>
    <slot>Adulthood</slot>
    <title>state engineer</title>
    <titleShort>engineer</titleShort>
    <description>[PAWN_nameDef] enrolled in the state weapons research program.\n\nWhile brilliant in [PAWN_possessive] work, [PAWN_pronoun] was so enthusiastic in testing [PAWN_possessive] creations that [PAWN_pronoun] accidentally wounded some colleagues and was marked as unfit for service.</description>
    <skillGains>
      <Construction>2</Construction>
      <Intellectual>4</Intellectual>
      <Crafting>5</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>CombatNegotiator31</defName>
    <identifier>CombatNegotiator31</identifier>
    <slot>Adulthood</slot>
    <title>combat negotiator</title>
    <titleShort>negotiator</titleShort>
    <description>After escaping [PAWN_possessive] homeworld, [PAWN_nameDef] took [PAWN_possessive] skills to the stars.\n\n[PAWN_pronoun] chose to operate as a hired gun, working for various rebel organizations, enslaved groups, and merchant enclaves. [PAWN_possessive] specialty was negotiation in combat situations.</description>
    <skillGains>
      <Shooting>4</Shooting>
      <Melee>3</Melee>
      <Social>3</Social>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpaceStationCook63</defName>
    <identifier>SpaceStationCook63</identifier>
    <slot>Adulthood</slot>
    <title>space station cook</title>
    <titleShort>cook</titleShort>
    <description>The space station [PAWN_nameDef] came from was far from any populated planets, and was in itself understaffed. For this reason, [PAWN_nameDef] was forced to learn how to cook in order to keep the crew alive. While [PAWN_pronoun] appreciated this learning opportunity, [PAWN_pronoun] didn't appreciate the criticism which came with it, and has had self-esteem issues ever since.</description>
    <skillGains>
      <Melee>4</Melee>
      <Cooking>6</Cooking>
    </skillGains>
    <workDisables>Caring, Social</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
      <li>ImperialCommon</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpaceHunter79</defName>
    <identifier>SpaceHunter79</identifier>
    <slot>Adulthood</slot>
    <title>space hunter</title>
    <titleShort>huntsman</titleShort>
    <description>Now a man as well as proficient hunter and marksman, [PAWN_nameDef] yearned for more. [PAWN_pronoun] took leave from [PAWN_possessive] birth world, traveling the great expanse in search of exotic game.</description>
    <skillGains>
      <Shooting>5</Shooting>
      <Melee>1</Melee>
      <Cooking>1</Cooking>
      <Medicine>2</Medicine>
      <Crafting>3</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Hunter</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>PoliticalAssassin46</defName>
    <identifier>PoliticalAssassin46</identifier>
    <slot>Adulthood</slot>
    <title>political assassin</title>
    <titleShort>deathjack</titleShort>
    <description>[PAWN_nameDef] realized that [PAWN_possessive] true calling was in physical solutions to diplomatic problems. [PAWN_pronoun] sought training from the assassins guild on Ceti V.\n\n[PAWN_pronoun] was a quick study at the arts of subterfuge and death. [PAWN_pronoun] soon earned the guild rank of Deathjack.</description>
    <skillGains>
      <Shooting>8</Shooting>
      <Melee>8</Melee>
    </skillGains>
    <workDisables>ManualDumb, Caring, Social, Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>AIResearcher94</defName>
    <identifier>AIResearcher94</identifier>
    <slot>Adulthood</slot>
    <title>AI researcher</title>
    <titleShort>researcher</titleShort>
    <description>[PAWN_nameDef] was a renowned researcher in artificial intelligence, robotics, and interactive holography. [PAWN_possessive] friendships with the artificial minds [PAWN_pronoun] created gave [PAWN_objective] the confidence to interact with real people, and [PAWN_pronoun] soon excelled at personal communication.</description>
    <skillGains>
      <Intellectual>6</Intellectual>
      <Mining>-2</Mining>
      <Melee>-2</Melee>
      <Social>4</Social>
      <Crafting>1</Crafting>
    </skillGains>
    <workDisables>ManualDumb</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ArmyScientist35</defName>
    <identifier>ArmyScientist35</identifier>
    <slot>Adulthood</slot>
    <title>army scientist</title>
    <titleShort>scientist</titleShort>
    <description>[PAWN_nameDef] did scientific research for a planetary army. [PAWN_pronoun] studied war scenarios and designed weapon systems to help win battles with a minimum of fuss.</description>
    <skillGains>
      <Intellectual>8</Intellectual>
      <Medicine>2</Medicine>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>FelineScientist76</defName>
    <identifier>FelineScientist76</identifier>
    <slot>Adulthood</slot>
    <title>feline scientist</title>
    <titleShort>scientist</titleShort>
    <description>[PAWN_nameDef] became a renowned scientist in the field of felines. Wanting the best for [PAWN_possessive] animals and specializing in selective breeding, [PAWN_pronoun] set out on a journey to find the perfect breeding grounds for [PAWN_possessive] cats.</description>
    <skillGains>
      <Intellectual>4</Intellectual>
      <Social>2</Social>
      <Medicine>3</Medicine>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
    </spawnCategories>
    <bodyTypeGlobal>Fat</bodyTypeGlobal>
    <bodyTypeFemale>Fat</bodyTypeFemale>
    <bodyTypeMale>Fat</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpaceMarine51</defName>
    <identifier>SpaceMarine51</identifier>
    <slot>Adulthood</slot>
    <title>space marine</title>
    <titleShort>marine</titleShort>
    <description>[PAWN_nameDef] was recruited into the security forces of an off-planet corporation. [PAWN_pronoun] defended their ships against pirates and engaged in private space warfare contracts.</description>
    <skillGains>
      <Shooting>6</Shooting>
      <Melee>5</Melee>
      <Medicine>2</Medicine>
    </skillGains>
    <workDisables>Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Mercenary55</defName>
    <identifier>Mercenary55</identifier>
    <slot>Adulthood</slot>
    <title>mercenary</title>
    <titleShort>merc</titleShort>
    <description>[PAWN_nameDef] spent [PAWN_possessive] teenage years as a mercenary, being hired to kill and maim others. [PAWN_pronoun] was tall, menacing, and frightening. [PAWN_pronoun] could instill fear into the hearts of any man who crossed [PAWN_objective], and kill anyone [PAWN_pronoun] wanted.</description>
    <skillGains>
      <Construction>3</Construction>
      <Mining>2</Mining>
      <Shooting>4</Shooting>
      <Melee>3</Melee>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <forcedTraits>
      <Psychopath>0</Psychopath>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Microbiologist12</defName>
    <identifier>Microbiologist12</identifier>
    <slot>Adulthood</slot>
    <title>microbiologist</title>
    <titleShort>biologist</titleShort>
    <description>A plague struck [PAWN_nameDef]'s native urbworld. [PAWN_nameDef] and other researchers revealed that the illness was a bioweapon, but the people turned on them, blaming them for the sickness.\n\n[PAWN_nameDef] barely escaped [PAWN_possessive] homeworld as it fell into chaos.</description>
    <skillGains>
      <Intellectual>5</Intellectual>
      <Medicine>3</Medicine>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>AceFighterPilot54</defName>
    <identifier>AceFighterPilot54</identifier>
    <slot>Adulthood</slot>
    <title>ace fighter pilot</title>
    <titleShort>ace</titleShort>
    <description>[PAWN_nameDef] graduated from the Star Academy with honors and distinguished [PAWN_objective]self as an ace fighter pilot in three campaigns against more advanced aggressor cultures.\n\nHowever, after one vicious battle, [PAWN_pronoun] found [PAWN_objective]self stranded and [PAWN_possessive] carrier ship destroyed. With nowhere to go, [PAWN_pronoun] entered [PAWN_possessive] escape pod and prayed.</description>
    <skillGains>
      <Intellectual>2</Intellectual>
      <Mining>-3</Mining>
      <Shooting>3</Shooting>
      <Melee>2</Melee>
      <Social>3</Social>
      <Medicine>3</Medicine>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Brigand68</defName>
    <identifier>Brigand68</identifier>
    <slot>Adulthood</slot>
    <title>brigand</title>
    <titleShort>brigand</titleShort>
    <description>[PAWN_nameDef] flew with a band of brigands [PAWN_pronoun] called friends. [PAWN_pronoun] even had a ship of [PAWN_possessive] own - The Dung Mucker 2000.</description>
    <skillGains>
      <Construction>2</Construction>
      <Shooting>2</Shooting>
      <Melee>3</Melee>
      <Cooking>2</Cooking>
      <Medicine>1</Medicine>
      <Crafting>1</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Assassin7</defName>
    <identifier>Assassin7</identifier>
    <slot>Adulthood</slot>
    <title>assassin</title>
    <titleShort>assassin</titleShort>
    <description>[PAWN_nameDef] was specially trained as an operative to be used for high-risk assassinations. [PAWN_possessive] skill with a rifle was unrivalled.\n\n[PAWN_pronoun] was eventually betrayed by [PAWN_possessive] superiors when a target found out about the hit and offered to fund their future projects. [PAWN_nameDef] swore to never trust anyone again.</description>
    <skillGains>
      <Shooting>7</Shooting>
      <Melee>4</Melee>
    </skillGains>
    <workDisables>Caring, Artistic, Crafting, Cooking, Cleaning, PlantWork</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>BusinessGangster58</defName>
    <identifier>BusinessGangster58</identifier>
    <slot>Adulthood</slot>
    <title>business gangster</title>
    <titleShort>gangster</titleShort>
    <description>Staying one step ahead of the law, [PAWN_nameDef] became a legitimate businessman and silenced anyone who dared say otherwise. As proprietor of brothels, speakeasies and eateries, [PAWN_nameDef]'s good fortune continued while [PAWN_possessive] competition suffered a series of unfortunate and unexplained accidents.</description>
    <skillGains>
      <Shooting>3</Shooting>
      <Melee>5</Melee>
      <Social>7</Social>
      <Artistic>2</Artistic>
    </skillGains>
    <workDisables>Caring, Cleaning, PlantWork</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <forcedTraits>
      <Greedy>0</Greedy>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ParamilitaryAgent11</defName>
    <identifier>ParamilitaryAgent11</identifier>
    <slot>Adulthood</slot>
    <title>paramilitary agent</title>
    <titleShort>agent</titleShort>
    <description>After scraping by on assorted shady jobs, [PAWN_nameDef] joined with a large paramilitary group to fight a war on [PAWN_possessive] homeworld. [PAWN_possessive] role was infiltration and intelligence gathering</description>
    <skillGains>
      <Shooting>5</Shooting>
      <Melee>2</Melee>
      <Social>3</Social>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Storyteller63</defName>
    <identifier>Storyteller63</identifier>
    <slot>Adulthood</slot>
    <title>storyteller</title>
    <titleShort>fabulist</titleShort>
    <description>[PAWN_nameDef] traveled [PAWN_possessive] world recounting the legends that [PAWN_pronoun] had studied.\n\nObsessed with the legends of [PAWN_possessive] people, [PAWN_nameDef] seeks someone who can help [PAWN_objective] grow scales on [PAWN_possessive] skin, so [PAWN_pronoun] too can be as the ancients were.</description>
    <skillGains>
      <Intellectual>2</Intellectual>
      <Social>4</Social>
      <Artistic>3</Artistic>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>Cooking, Mining</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>InformationBroker77</defName>
    <identifier>InformationBroker77</identifier>
    <slot>Adulthood</slot>
    <title>information broker</title>
    <titleShort>broker</titleShort>
    <description>[PAWN_nameDef] caught a ship to the next star system and began a life of travel and learning, quickly collecting a massive bank of knowledge from people of all shapes and sizes.\n\n[PAWN_pronoun] learned to sell this knowledge for great profit, and became a powerful broker of information.</description>
    <skillGains>
      <Intellectual>4</Intellectual>
      <Shooting>-1</Shooting>
      <Melee>-2</Melee>
      <Social>5</Social>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>ImperialCommon</li>
      <li>ImperialRoyal</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <forcedTraits>
      <FastLearner>0</FastLearner>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>VersatileWorker13</defName>
    <identifier>VersatileWorker13</identifier>
    <slot>Adulthood</slot>
    <title>versatile worker</title>
    <titleShort>worker</titleShort>
    <description>Spending [PAWN_possessive] later teenage years in a colony of settlers, [PAWN_pronoun] offered what little skills [PAWN_pronoun] had to anyone who would take him.\n\nBeing a quick learner, [PAWN_pronoun] honed many of [PAWN_possessive] skills and, apart from [PAWN_possessive] crippling inability to understand fire, is now a fully functioning adult.</description>
    <skillGains>
      <Construction>1</Construction>
      <Plants>4</Plants>
      <Melee>5</Melee>
      <Medicine>-1</Medicine>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>Social, Intellectual, Firefighting</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <forcedTraits>
      <FastLearner>0</FastLearner>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>TestSubject90</defName>
    <identifier>TestSubject90</identifier>
    <slot>Adulthood</slot>
    <title>test subject</title>
    <titleShort>experiment</titleShort>
    <description>Kidnapped as a teenager, [PAWN_nameDef] was an unwilling test subject in experimental gene therapies. Labs and scientists scare [PAWN_objective].</description>
    <skillGains>
      <Shooting>6</Shooting>
      <Melee>8</Melee>
      <Social>-4</Social>
    </skillGains>
    <workDisables>Intellectual</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <forcedTraits>
      <BodyPurist>0</BodyPurist>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>AerospaceEngineer44</defName>
    <identifier>AerospaceEngineer44</identifier>
    <slot>Adulthood</slot>
    <title>aerospace engineer</title>
    <titleShort>engineer</titleShort>
    <description>[PAWN_nameDef] was an engineer responsible for the production of aircraft and spaceships. [PAWN_pronoun] focused on improving production lines, and was known for [PAWN_possessive] mastery of technical and research issues.</description>
    <skillGains>
      <Construction>2</Construction>
      <Intellectual>4</Intellectual>
      <Mining>2</Mining>
      <Shooting>2</Shooting>
      <Social>2</Social>
    </skillGains>
    <workDisables>Cleaning, PlantWork</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <possessions>
      <Plasteel>5</Plasteel>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>HiredMuscle5</defName>
    <identifier>HiredMuscle5</identifier>
    <slot>Adulthood</slot>
    <title>hired muscle</title>
    <titleShort>hired thug</titleShort>
    <description>Knowing what life on the streets was truly like, [PAWN_nameDef] slipped into police work. However, a particularly disturbing case ended with [PAWN_objective] beating the perpetrator to death.\n\nDishonorably discharged from [PAWN_possessive] duties, [PAWN_pronoun] used [PAWN_possessive] contacts and skills to find work as a thug for hire.</description>
    <skillGains>
      <Shooting>5</Shooting>
      <Melee>3</Melee>
      <Social>3</Social>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>AlcoholicTrucker93</defName>
    <identifier>AlcoholicTrucker93</identifier>
    <slot>Adulthood</slot>
    <title>alcoholic trucker</title>
    <titleShort>alcoholic</titleShort>
    <description>[PAWN_nameDef] was a space trucker working for Arcknight Industries when [PAWN_pronoun] killed [PAWN_possessive] wife and daughter in a crash.\n\n[PAWN_possessive] body was repaired with an experimental nano-genetic therapy. Now [PAWN_possessive] muscles continually regenerate and harden, and every movement tears the tissue anew. [PAWN_pronoun] bears the pain in silence.</description>
    <skillGains>
      <Mining>4</Mining>
      <Shooting>2</Shooting>
      <Melee>2</Melee>
    </skillGains>
    <workDisables>Caring, Social, Intellectual, Artistic, Cleaning</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>IntelligenceAgent10</defName>
    <identifier>IntelligenceAgent10</identifier>
    <slot>Adulthood</slot>
    <title>intelligence agent</title>
    <titleShort>agent</titleShort>
    <description>Because of [PAWN_possessive] hacking skills, [PAWN_nameDef] was recruited by [PAWN_possessive] home planet's governing body to serve as an intelligence agent. The government trained [PAWN_objective] in combat, but [PAWN_pronoun] prefers psychological warfare.\n\nGovernment rules proved too strict for [PAWN_objective], so [PAWN_pronoun] went rogue. [PAWN_possessive] current activities are unknown.</description>
    <skillGains>
      <Shooting>7</Shooting>
      <Melee>4</Melee>
      <Social>8</Social>
    </skillGains>
    <workDisables>Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>WarChief97</defName>
    <identifier>WarChief97</identifier>
    <slot>Adulthood</slot>
    <title>war chief</title>
    <titleShort>chief</titleShort>
    <description>[PAWN_nameDef] was a tribal gang leader on a rimworld. For [PAWN_objective], the battlefield is a game, and there is nothing more fun than the fires of war.</description>
    <skillGains>
      <Shooting>7</Shooting>
      <Melee>5</Melee>
      <Social>2</Social>
    </skillGains>
    <workDisables>Hauling, PlantWork</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Tribal</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <forcedTraits>
      <Psychopath>0</Psychopath>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>InvoluntaryHermit38</defName>
    <identifier>InvoluntaryHermit38</identifier>
    <slot>Adulthood</slot>
    <title>involuntary hermit</title>
    <titleShort>hermit</titleShort>
    <description>[PAWN_nameDef]'s escape pod was jettisoned onto an unexplored region of a frontier world. [PAWN_pronoun] eked out a lonely existence with only a herd of muffalo for company.\n\nLater, [PAWN_pronoun] was spotted clad in pygmy wombat furs, discussing the importance of hoof hygiene with a group of elderly muffalo.</description>
    <skillGains>
      <Construction>1</Construction>
      <Plants>3</Plants>
      <Melee>4</Melee>
      <Crafting>3</Crafting>
    </skillGains>
    <workDisables>Caring, Artistic, Cleaning</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Tribal</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MedicalScientist16</defName>
    <identifier>MedicalScientist16</identifier>
    <slot>Adulthood</slot>
    <title>medical scientist</title>
    <titleShort>scientist</titleShort>
    <description>[PAWN_nameDef] enlisted as a doctor on a passing science vessel. Roaming the stars with a large crew of xenohumans and transbirds, [PAWN_pronoun] did genetic research on encountered species. [PAWN_pronoun] developed a deep friendship with one transbird but was forced to leave the ship when the captain dismissed all non-avians.</description>
    <skillGains>
      <Intellectual>8</Intellectual>
      <Medicine>2</Medicine>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>VoidRaider74</defName>
    <identifier>VoidRaider74</identifier>
    <slot>Adulthood</slot>
    <title>void raider</title>
    <titleShort>Pirate</titleShort>
    <description>Craving adventure and glory, [PAWN_nameDef] set out across the void with a raider band, swearing [PAWN_possessive] loyalty to [PAWN_possessive] band and brothers, and spreading their glory through the stars with [PAWN_possessive] blade and pistol in hand.</description>
    <skillGains>
      <Shooting>6</Shooting>
      <Melee>6</Melee>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MidworldSailor91</defName>
    <identifier>MidworldSailor91</identifier>
    <slot>Adulthood</slot>
    <title>midworld sailor</title>
    <titleShort>sailor</titleShort>
    <description>[PAWN_nameDef] joined [PAWN_possessive] country's Royal Navy. After years as a non-commissioned officer, [PAWN_pronoun] eventually cracked and used the skills [PAWN_pronoun] had acquired to start a mutiny on [PAWN_possessive] ship.\n\nIn the end, [PAWN_nameDef] was the leader of a large group of trained and dangerous pirates. [PAWN_pronoun] henceforth always had chefs to cook for him.</description>
    <skillGains>
      <Construction>3</Construction>
      <Shooting>4</Shooting>
      <Melee>3</Melee>
      <Social>4</Social>
    </skillGains>
    <workDisables>Cooking</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <forcedTraits>
      <Gourmand>0</Gourmand>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>CoreworldJeweler19</defName>
    <identifier>CoreworldJeweler19</identifier>
    <slot>Adulthood</slot>
    <title>coreworld jeweler</title>
    <titleShort>jeweler</titleShort>
    <description>After [PAWN_nameDef] had completed all of [PAWN_possessive] surgeries, [PAWN_pronoun] found that [PAWN_pronoun] had a lot of time and energy to dedicate to [PAWN_possessive] passion: trinket crafting. [PAWN_pronoun] spent years perfecting [PAWN_possessive] art as a jeweler, then left [PAWN_possessive] home world to sell [PAWN_possessive] wares across the galaxy.</description>
    <skillGains>
      <Construction>3</Construction>
      <Shooting>-3</Shooting>
      <Artistic>5</Artistic>
      <Crafting>7</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MedievalKnight26</defName>
    <identifier>MedievalKnight26</identifier>
    <slot>Adulthood</slot>
    <title>medieval knight</title>
    <titleShort>knight</titleShort>
    <description>[PAWN_nameDef] fought for king and country on the battlefield, where [PAWN_pronoun] further honed [PAWN_possessive] skill as a swordsman. [PAWN_possessive] greatest glory was leading a charge into an enemy courtroom and capturing the king at sword point.</description>
    <skillGains>
      <Shooting>2</Shooting>
      <Melee>6</Melee>
    </skillGains>
    <workDisables>Cleaning</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>StarKnight5</defName>
    <identifier>StarKnight5</identifier>
    <slot>Adulthood</slot>
    <title>star knight</title>
    <titleShort>knight</titleShort>
    <description>Abducted from [PAWN_possessive] medieval homeworld, [PAWN_nameDef] later escaped [PAWN_possessive] captors via an impressive display of swordsmanship.\n\nFor a moment, as [PAWN_pronoun] drifted in the escape pod, [PAWN_possessive] childhood dream of becoming a knight among the stars felt so real. Then [PAWN_pronoun] crash-landed.</description>
    <skillGains>
      <Shooting>2</Shooting>
      <Melee>8</Melee>
      <Social>3</Social>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>TestSubject39</defName>
    <identifier>TestSubject39</identifier>
    <slot>Adulthood</slot>
    <title>test subject</title>
    <titleShort>subject</titleShort>
    <description>[PAWN_nameDef] enrolled in a scientific study that promised it could make [PAWN_objective] smarter. Unfortunately, the side effects were very different than [PAWN_pronoun] expected.[PAWN_pronoun] went insane and killed a number of people before escaping.</description>
    <skillGains>
      <Shooting>4</Shooting>
      <Melee>4</Melee>
    </skillGains>
    <workDisables>Caring, Social, Artistic, Cooking, Cleaning, PlantWork</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <forcedTraits>
      <Psychopath>0</Psychopath>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>GeneticEngineer89</defName>
    <identifier>GeneticEngineer89</identifier>
    <slot>Adulthood</slot>
    <title>genetic engineer</title>
    <titleShort>geneticist</titleShort>
    <description>[PAWN_nameDef] carried out [PAWN_possessive] first genetic experiments on farm animals. [PAWN_pronoun] soon grew to become quite skilled in all forms of genetic engineering.\n\nA horrifying lab accident left [PAWN_objective] with a mortal fear of rodents.</description>
    <skillGains>
      <Plants>1</Plants>
      <Intellectual>6</Intellectual>
      <Medicine>2</Medicine>
    </skillGains>
    <workDisables>Violent</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>NinjaAssassin31</defName>
    <identifier>NinjaAssassin31</identifier>
    <slot>Adulthood</slot>
    <title>ninja assassin</title>
    <titleShort>ninja</titleShort>
    <description>[PAWN_nameDef] performed stiletto-precise assassinations for an interplanetary conglomerate. [PAWN_pronoun] was assigned the most complex missions involving disguise, deception, subterfuge, and efficient violence.\n\nA nasty incident with a potted plant and an assassination gone wrong left [PAWN_objective] with an aversion to plant life.</description>
    <skillGains>
      <Shooting>4</Shooting>
      <Melee>3</Melee>
      <Social>2</Social>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>PlantWork</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>LazyProgrammer3</defName>
    <identifier>LazyProgrammer3</identifier>
    <slot>Adulthood</slot>
    <title>lazy programmer</title>
    <titleShort>programmer</titleShort>
    <description>[PAWN_nameDef] independently developed software that had a competitive edge against local urbworld corporations.\n\n[PAWN_possessive] legally questionable methods lead to a growing number in unsolved claims of intellectual property theft.</description>
    <skillGains>
      <Intellectual>8</Intellectual>
    </skillGains>
    <workDisables>PlantWork, Mining</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MindwipedAssassin50</defName>
    <identifier>MindwipedAssassin50</identifier>
    <slot>Adulthood</slot>
    <title>mindwiped assassin</title>
    <titleShort>mindwipe</titleShort>
    <description>The corporation took and remade [PAWN_nameDef]. They trained [PAWN_objective] into an tool of destruction and subversion. [PAWN_pronoun] took to [PAWN_possessive] new life, finding satisfaction in the jobs they sent [PAWN_objective] on.\n\nWhen [PAWN_pronoun] learned that they planned on disposing of [PAWN_objective], [PAWN_nameDef] turned on [PAWN_possessive] masters, killing several and fleeing the planet.</description>
    <skillGains>
      <Shooting>8</Shooting>
      <Melee>4</Melee>
      <Cooking>-2</Cooking>
    </skillGains>
    <workDisables>Caring, Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <forcedTraits>
      <Psychopath>0</Psychopath>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>TestSubject92</defName>
    <identifier>TestSubject92</identifier>
    <slot>Adulthood</slot>
    <title>test subject</title>
    <titleShort>subject</titleShort>
    <description>[PAWN_nameDef]'s tests went from geometry to quantum physics, and [PAWN_pronoun] could run the grand gauntlet without a scratch.\n\nOne day, Mother said that [PAWN_possessive] final test lay beyond the red door, and that even if [PAWN_pronoun] couldn't hear her voice, Mother would always be there.</description>
    <skillGains>
      <Intellectual>5</Intellectual>
      <Melee>5</Melee>
      <Cooking>1</Cooking>
    </skillGains>
    <workDisables>Caring, Social, PlantWork, Mining</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpaceshipSalesman34</defName>
    <identifier>SpaceshipSalesman34</identifier>
    <slot>Adulthood</slot>
    <title>spaceship salesman</title>
    <titleShort>salesman</titleShort>
    <description>[PAWN_nameDef] tried [PAWN_possessive] hand at various jobs, including building space rabbit cages and working at a mutant wheat research project. Finally, [PAWN_pronoun] found [PAWN_possessive] true calling as a spacecraft salesman. Specializing in recreational spacecraft, [PAWN_possessive] smooth talk and charming nature served [PAWN_objective] well.</description>
    <skillGains>
      <Construction>2</Construction>
      <Plants>1</Plants>
      <Intellectual>2</Intellectual>
      <Shooting>2</Shooting>
      <Social>8</Social>
      <Cooking>-1</Cooking>
      <Medicine>-1</Medicine>
      <Artistic>-2</Artistic>
      <Crafting>-1</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>AIProgrammer22</defName>
    <identifier>AIProgrammer22</identifier>
    <slot>Adulthood</slot>
    <title>AI programmer</title>
    <titleShort>programmer</titleShort>
    <description>[PAWN_nameDef] enlisted in the military's robotics division, developing smarter combat AI.\n\nThe whole time [PAWN_pronoun] was there, [PAWN_pronoun] never spoke with another human. [PAWN_pronoun] spent all [PAWN_possessive] time conversing with the AI [PAWN_pronoun] was developing.</description>
    <skillGains>
      <Intellectual>7</Intellectual>
      <Crafting>6</Crafting>
    </skillGains>
    <workDisables>PlantWork</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>WhiteHatHacker82</defName>
    <identifier>WhiteHatHacker82</identifier>
    <slot>Adulthood</slot>
    <title>white-hat hacker</title>
    <titleShort>hacker</titleShort>
    <description>From [PAWN_possessive] computer, [PAWN_nameDef] helped rid the online world of bad people.</description>
    <skillGains>
      <Intellectual>8</Intellectual>
      <Social>-2</Social>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>ManualDumb</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>VinhoKing98</defName>
    <identifier>VinhoKing98</identifier>
    <slot>Adulthood</slot>
    <title>vinho king</title>
    <titleShort>booze king</titleShort>
    <description>After prohibition came to Aracena VI, [PAWN_nameDef] expanded [PAWN_possessive] underground liquor business until it dominated the market on two continents. They called [PAWN_objective] the Vinho King.\n\nThough [PAWN_pronoun] mostly controlled [PAWN_possessive] competition by masterful political maneuvering, [PAWN_pronoun] was not above the occasional beat-down or stiletto assassination.</description>
    <skillGains>
      <Intellectual>3</Intellectual>
      <Shooting>3</Shooting>
      <Social>7</Social>
    </skillGains>
    <workDisables>PlantWork</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Fat</bodyTypeGlobal>
    <bodyTypeFemale>Fat</bodyTypeFemale>
    <bodyTypeMale>Fat</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>
  <BackstoryDef>
    <defName>SlaveChemist84</defName>
    <identifier>SlaveChemist84</identifier>
    <slot>Adulthood</slot>
    <title>slave chemist</title>
    <titleShort>chemist</titleShort>
    <description>[PAWN_nameDef] became a gifted pharmaceutical scientist, but was captured by slavers. [PAWN_pronoun] was sold to a warlord and forced to create drugs to test on other slaves. This left [PAWN_objective] with deep and troubling questions on the ethics of scientific research and medicine.</description>
    <skillGains>
      <Construction>-1</Construction>
      <Intellectual>4</Intellectual>
      <Mining>-2</Mining>
      <Melee>-1</Melee>
      <Medicine>5</Medicine>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>RocketEngineer53</defName>
    <identifier>RocketEngineer53</identifier>
    <slot>Adulthood</slot>
    <title>rocket engineer</title>
    <titleShort>engineer</titleShort>
    <description>[PAWN_nameDef] won a grant to a top school and earned a degree in rocket engineering. After several years working for a space mining company [PAWN_pronoun] left to travel and see the universe.</description>
    <skillGains>
      <Construction>4</Construction>
      <Intellectual>5</Intellectual>
      <Mining>3</Mining>
      <Social>-1</Social>
      <Cooking>-4</Cooking>
      <Medicine>-2</Medicine>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpaceMarine19</defName>
    <identifier>SpaceMarine19</identifier>
    <slot>Adulthood</slot>
    <title>space marine</title>
    <titleShort>marine</titleShort>
    <description>[PAWN_nameDef] joined a squad of space marines known for their brutal efficiency at killing. [PAWN_possessive] last name and [PAWN_possessive] lack of emotion during combat inspired [PAWN_possessive] nickname.</description>
    <skillGains>
      <Shooting>6</Shooting>
      <Melee>4</Melee>
      <Artistic>-3</Artistic>
      <Crafting>-2</Crafting>
    </skillGains>
    <workDisables>Caring, Social</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>CombatEngineer4</defName>
    <identifier>CombatEngineer4</identifier>
    <slot>Adulthood</slot>
    <title>combat engineer</title>
    <titleShort>engineer</titleShort>
    <description>[PAWN_nameDef] became a combat engineer. [PAWN_pronoun] built bases, fixed guns, and repaired vehicles for the army - when [PAWN_pronoun] wasn't shooting at people, that is.</description>
    <skillGains>
      <Construction>2</Construction>
      <Shooting>5</Shooting>
      <Crafting>3</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>PlanetaryDiplomat7</defName>
    <identifier>PlanetaryDiplomat7</identifier>
    <slot>Adulthood</slot>
    <title>planetary diplomat</title>
    <titleShort>diplomat</titleShort>
    <description>[PAWN_nameDef]'s passion and profession was interplanetary politics. [PAWN_pronoun] was a master of social manipulation and negotiation and devoted [PAWN_objective]self to working towards peace, compromise and mutual benefit.</description>
    <skillGains>
      <Construction>-2</Construction>
      <Intellectual>3</Intellectual>
      <Social>8</Social>
      <Artistic>2</Artistic>
    </skillGains>
    <workDisables>ManualDumb, Violent, Hauling, PlantWork, Mining</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Guardian55</defName>
    <identifier>Guardian55</identifier>
    <slot>Adulthood</slot>
    <title>guardian</title>
    <titleShort>guardian</titleShort>
    <description>After [PAWN_pronoun] learned what truly matters in life, [PAWN_nameDef] changed for the better. [PAWN_pronoun] learned to work hard and protect the things that matter - not money or material things, but friends and loved ones.\n\n[PAWN_pronoun] became a professional guardian.</description>
    <skillGains>
      <Shooting>4</Shooting>
      <Melee>4</Melee>
      <Medicine>4</Medicine>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <forcedTraits>
      <Industriousness>1</Industriousness>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>RenegadeEngineer43</defName>
    <identifier>RenegadeEngineer43</identifier>
    <slot>Adulthood</slot>
    <title>renegade engineer</title>
    <titleShort>engineer</titleShort>
    <description>After a daring escape from the prison planet with a small crew of fellow inmates, [PAWN_nameDef] set [PAWN_possessive] gaze on [PAWN_possessive] homeworld.\n\n[PAWN_pronoun] traveled there, recruited more followers, hijacked a space cruiser, and launched into the void.</description>
    <skillGains>
      <Intellectual>3</Intellectual>
      <Shooting>3</Shooting>
      <Melee>3</Melee>
      <Medicine>3</Medicine>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Osteologist74</defName>
    <identifier>Osteologist74</identifier>
    <slot>Adulthood</slot>
    <title>osteologist</title>
    <titleShort>scholar</titleShort>
    <description>After graduating university at the top of [PAWN_possessive] class, [PAWN_nameDef] quickly became a well-known and athletic college professor.\n\nWith [PAWN_possessive] new found fame and fitness came a sudden reluctance to preform menial labor - such tasks are better left to graduate students.</description>
    <skillGains>
      <Intellectual>7</Intellectual>
      <Mining>-3</Mining>
      <Melee>3</Melee>
      <Social>3</Social>
    </skillGains>
    <workDisables>ManualDumb</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Fat</bodyTypeGlobal>
    <bodyTypeFemale>Fat</bodyTypeFemale>
    <bodyTypeMale>Fat</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Gnomebiologist96</defName>
    <identifier>Gnomebiologist96</identifier>
    <slot>Adulthood</slot>
    <title>gnomebiologist</title>
    <titleShort>biologist</titleShort>
    <description>[PAWN_nameDef] was a xenobiology professor in a glitterworld university. [PAWN_pronoun] went to conferences and managed an art gallery.\n\nHowever, [PAWN_pronoun] never stopped sculpting yard gnomes, and considers this to be [PAWN_possessive] true profession.</description>
    <skillGains>
      <Intellectual>4</Intellectual>
      <Social>4</Social>
      <Artistic>7</Artistic>
      <Crafting>4</Crafting>
    </skillGains>
    <workDisables>Cooking, Firefighting, Cleaning</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>CriminalKingpin36</defName>
    <identifier>CriminalKingpin36</identifier>
    <slot>Adulthood</slot>
    <title>criminal kingpin</title>
    <titleShort>kingpin</titleShort>
    <description>[PAWN_nameDef] discovered [PAWN_possessive] gift - people did what [PAWN_pronoun] said, when [PAWN_pronoun] said it. Taking advantage of this, [PAWN_pronoun] set off for the rimworlds to make a name for [PAWN_objective]self in the criminal underworld.\n\nHaving people do things for [PAWN_objective] taught [PAWN_objective] to avoid manual labor at all costs.</description>
    <skillGains>
      <Shooting>2</Shooting>
      <Melee>2</Melee>
      <Social>5</Social>
    </skillGains>
    <workDisables>ManualDumb</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>UrbworldSexSlave25</defName>
    <identifier>UrbworldSexSlave25</identifier>
    <slot>Adulthood</slot>
    <title>urbworld sex slave</title>
    <titleShort>sex slave</titleShort>
    <description>[PAWN_nameDef] was sold into sexual slavery. Having been designed to be inhumanly attractive and too weak to rebel against [PAWN_possessive] masters, [PAWN_pronoun] was passed between dozens of owners and knew nothing of freedom.</description>
    <skillGains>
      <Social>6</Social>
      <Cooking>2</Cooking>
      <Artistic>-2</Artistic>
    </skillGains>
    <workDisables>Violent, Intellectual</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <forcedTraits>
      <Beauty>2</Beauty>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>UrbworldSergeant50</defName>
    <identifier>UrbworldSergeant50</identifier>
    <slot>Adulthood</slot>
    <title>urbworld sergeant</title>
    <titleShort>sergeant</titleShort>
    <description>[PAWN_nameDef]'s passion for all things militaristic kept with [PAWN_objective] all the way to adulthood.\n\nAfter enrolling as a local policeman, [PAWN_pronoun] was soon promoted to sergeant.</description>
    <skillGains>
      <Shooting>4</Shooting>
      <Melee>2</Melee>
      <Social>3</Social>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SystemLord77</defName>
    <identifier>SystemLord77</identifier>
    <slot>Adulthood</slot>
    <title>system lord</title>
    <titleShort>lord</titleShort>
    <description>For [PAWN_nameDef], ruling a country, or even a planet, was not good enough. [PAWN_pronoun] sought to expand [PAWN_possessive] influence across the system - by legal or extralegal means.</description>
    <skillGains>
      <Shooting>6</Shooting>
      <Melee>2</Melee>
      <Social>6</Social>
    </skillGains>
    <workDisables>Caring</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <forcedTraits>
      <Greedy>0</Greedy>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>HunterOfTheKing53</defName>
    <identifier>HunterOfTheKing53</identifier>
    <slot>Adulthood</slot>
    <title>hunter of the king</title>
    <titleShort>pest guard</titleShort>
    <description>[PAWN_nameDef] was forced to protect [PAWN_possessive] colony in the name of the king after a mysterious outbreak of murderous genetically-modified wildlife.\n\nDespite [PAWN_possessive] best efforts, [PAWN_possessive] kingdom was destroyed. [PAWN_nameDef] developed PTSD, a hatred of animals and a bad case of technophobia. [PAWN_pronoun] was named for the place they found him.</description>
    <skillGains>
      <Intellectual>-2</Intellectual>
      <Shooting>6</Shooting>
      <Melee>4</Melee>
      <Medicine>3</Medicine>
      <Crafting>-3</Crafting>
    </skillGains>
    <workDisables>Animals, Artistic, PlantWork</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <forcedTraits>
      <BodyPurist>0</BodyPurist>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Castaway81</defName>
    <identifier>Castaway81</identifier>
    <slot>Adulthood</slot>
    <title>castaway</title>
    <titleShort>castaway</titleShort>
    <description>[PAWN_nameDef] was a passenger on a routine interstellar transit. When [PAWN_possessive] ship was destroyed, [PAWN_pronoun] lived as a castaway on an uninhabited planet for several years.</description>
    <skillGains>
      <Construction>2</Construction>
      <Plants>3</Plants>
      <Mining>2</Mining>
      <Cooking>2</Cooking>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>EnergyResearcher89</defName>
    <identifier>EnergyResearcher89</identifier>
    <slot>Adulthood</slot>
    <title>energy researcher</title>
    <titleShort>researcher</titleShort>
    <description>[PAWN_nameDef] was working on an idea for rapid communication over long distances. [PAWN_pronoun] made an error which destroyed [PAWN_possessive] lab and several nearby buildings. [PAWN_pronoun] fled the planet to escape the authorities.</description>
    <skillGains>
      <Intellectual>8</Intellectual>
      <Social>-2</Social>
      <Artistic>-1</Artistic>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>BountyHunter93</defName>
    <identifier>BountyHunter93</identifier>
    <slot>Adulthood</slot>
    <title>bounty hunter</title>
    <titleShort>hunter</titleShort>
    <description>When the civil war ended, [PAWN_nameDef] had to find a new way to use [PAWN_possessive] fighting skills. [PAWN_pronoun] decided to hunt down war criminals and soon realized that [PAWN_pronoun] enjoyed capturing bad guys by any means necessary.</description>
    <skillGains>
      <Shooting>5</Shooting>
      <Melee>3</Melee>
      <Social>2</Social>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Missionary99</defName>
    <identifier>Missionary99</identifier>
    <slot>Adulthood</slot>
    <title>missionary</title>
    <titleShort>missionary</titleShort>
    <description>[PAWN_nameDef] decided to devote [PAWN_possessive] life to religious service. [PAWN_pronoun] made a vow of peace and spent several years preaching and serving food in soup kitchens for the homeless.</description>
    <skillGains>
      <Social>6</Social>
      <Cooking>4</Cooking>
      <Medicine>2</Medicine>
    </skillGains>
    <workDisables>Violent</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MercenaryPilot63</defName>
    <identifier>MercenaryPilot63</identifier>
    <slot>Adulthood</slot>
    <title>mercenary pilot</title>
    <titleShort>mercenary</titleShort>
    <description>[PAWN_nameDef] convinced [PAWN_possessive] squadron to abscond with their ships and make a course for the rimworlds. There they could fight for the causes they believed in.\n\n[PAWN_possessive] men were loyal and fought hard, but their ideals soon faded away and their cause became that of the highest bidder.</description>
    <skillGains>
      <Construction>3</Construction>
      <Shooting>6</Shooting>
      <Melee>4</Melee>
      <Social>-1</Social>
    </skillGains>
    <workDisables>Caring, Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>LoneTraveler95</defName>
    <identifier>LoneTraveler95</identifier>
    <slot>Adulthood</slot>
    <title>lone traveler</title>
    <titleShort>traveler</titleShort>
    <description>After escaping [PAWN_possessive] homeworld on a jury-rigged ship, [PAWN_nameDef] traveled to new worlds to experience their beauty.\n\n[PAWN_pronoun] joined resistance groups and applied the skills [PAWN_pronoun] cultivated on [PAWN_possessive] homeworld. [PAWN_pronoun] often warned of the nuclear horror [PAWN_pronoun] saw as a child.</description>
    <skillGains>
      <Plants>3</Plants>
      <Shooting>4</Shooting>
      <Medicine>3</Medicine>
      <Artistic>-2</Artistic>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Undertaker93</defName>
    <identifier>Undertaker93</identifier>
    <slot>Adulthood</slot>
    <title>undertaker</title>
    <titleShort>undertaker</titleShort>
    <description>[PAWN_nameDef] roamed, offering grief assistance to those who've had a recent loss. While [PAWN_pronoun] never shied away from physical confrontation, [PAWN_pronoun] prefers to confront tough situations with a calm word.</description>
    <skillGains>
      <Intellectual>3</Intellectual>
      <Social>6</Social>
      <Medicine>2</Medicine>
    </skillGains>
    <workDisables>Violent</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Mercenary4</defName>
    <identifier>Mercenary4</identifier>
    <slot>Adulthood</slot>
    <title>mercenary</title>
    <titleShort>mercenary</titleShort>
    <description>After escaping a crew of rimworld slavers, [PAWN_nameDef] developed a lack of respect for life. [PAWN_pronoun] proceeded to sign on to a mercenary group where [PAWN_pronoun] developed skills using guns and survival techniques. [PAWN_nameDef] did dirty work where [PAWN_pronoun] found it, and traveled to many worlds.</description>
    <skillGains>
      <Construction>3</Construction>
      <Mining>2</Mining>
      <Shooting>5</Shooting>
      <Artistic>-2</Artistic>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <forcedTraits>
      <Psychopath>0</Psychopath>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>HiredAssassin37</defName>
    <identifier>HiredAssassin37</identifier>
    <slot>Adulthood</slot>
    <title>hired assassin</title>
    <titleShort>assassin</titleShort>
    <description>[PAWN_nameDef] was a master thief, working mostly for organized crime. Over time, [PAWN_pronoun] made a name for [PAWN_objective]self and shifted [PAWN_possessive] business into the paid "removal" of human threats.</description>
    <skillGains>
      <Shooting>4</Shooting>
      <Melee>5</Melee>
      <Social>3</Social>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <forcedTraits>
      <Nimble>0</Nimble>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpaceshipChef41</defName>
    <identifier>SpaceshipChef41</identifier>
    <slot>Adulthood</slot>
    <title>spaceship chef</title>
    <titleShort>chef</titleShort>
    <description>[PAWN_nameDef] got a spot on a ship by becoming the ship's cook. [PAWN_pronoun] enjoyed cooking and helping the doctor clean wounds, but [PAWN_pronoun] never got used to cleaning the dishes.</description>
    <skillGains>
      <Cooking>7</Cooking>
      <Medicine>5</Medicine>
    </skillGains>
    <workDisables>Cleaning</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <possessions>
      <MealNutrientPaste>4</MealNutrientPaste>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>DreadedDude58</defName>
    <identifier>DreadedDude58</identifier>
    <slot>Adulthood</slot>
    <title>dreaded dude</title>
    <titleShort>dude</titleShort>
    <description>Some followers say that [PAWN_nameDef] and [PAWN_possessive] crew of misfits found the fountain of youth, many, many centuries ago, and that they caused the extinction of the dinosaurs on many planets.\n\nThere's even a religion about them, somewhere, involving noodles and meatballs.</description>
    <skillGains>
      <Intellectual>6</Intellectual>
      <Mining>-3</Mining>
      <Medicine>2</Medicine>
      <Artistic>8</Artistic>
      <Crafting>4</Crafting>
    </skillGains>
    <workDisables>Cleaning</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Engineer40</defName>
    <identifier>Engineer40</identifier>
    <slot>Adulthood</slot>
    <title>engineer</title>
    <titleShort>engineer</titleShort>
    <description>[PAWN_nameDef] found a job as a tinkering engineer on the trading hub planet Irithir. Tough local traders forced [PAWN_objective] to learn negotiation skills, and [PAWN_pronoun] made good profits selling refurbished weapons and tools.</description>
    <skillGains>
      <Construction>2</Construction>
      <Intellectual>1</Intellectual>
      <Social>2</Social>
      <Cooking>-2</Cooking>
      <Crafting>8</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <possessions>
      <ComponentIndustrial>3</ComponentIndustrial>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Minister88</defName>
    <identifier>Minister88</identifier>
    <slot>Adulthood</slot>
    <title>minister</title>
    <titleShort>minister</titleShort>
    <description>[PAWN_nameDef] managed to secure an important position in the government of a minor glitterworld. [PAWN_pronoun] got used to the opulence, while attempting to keep [PAWN_possessive] citizens content. [PAWN_pronoun] had ample time for pleasure and indulgence – though [PAWN_pronoun] never forgot how to do hard work.</description>
    <skillGains>
      <Intellectual>3</Intellectual>
      <Shooting>2</Shooting>
      <Social>4</Social>
      <Crafting>3</Crafting>
    </skillGains>
    <workDisables>ManualDumb</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <forcedTraits>
      <Industriousness>1</Industriousness>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Pirate56</defName>
    <identifier>Pirate56</identifier>
    <slot>Adulthood</slot>
    <title>pirate</title>
    <titleShort>pirate</titleShort>
    <description>As a pirate leader, [PAWN_nameDef] was well-known for [PAWN_possessive] cunning plans and chess-like traps. [PAWN_pronoun] was never afraid to sacrifice a few pawns to capture an important target.\n\n[PAWN_possessive] name has been linked more then once to strategies that overcame overwhelming odds.</description>
    <skillGains>
      <Shooting>4</Shooting>
      <Melee>2</Melee>
      <Social>8</Social>
    </skillGains>
    <workDisables>ManualDumb</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <possessions>
      <Apparel_GasMask MayRequire="Ludeon.RimWorld.Biotech">1</Apparel_GasMask>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>BountyHunter17</defName>
    <identifier>BountyHunter17</identifier>
    <slot>Adulthood</slot>
    <title>bounty hunter</title>
    <titleShort>hunter</titleShort>
    <description>[PAWN_nameDef] was a self-employed bounty hunter. [PAWN_pronoun] spent [PAWN_possessive] days hunting space pirates and collecting cash bounties.</description>
    <skillGains>
      <Shooting>6</Shooting>
      <Melee>4</Melee>
      <Medicine>2</Medicine>
    </skillGains>
    <workDisables>Cleaning</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Spiceminer81</defName>
    <identifier>Spiceminer81</identifier>
    <slot>Adulthood</slot>
    <title>spiceminer</title>
    <titleShort>spiceminer</titleShort>
    <description>[PAWN_nameDef] made [PAWN_possessive] mark on the universe as a spice miner assigned to the lucrative mining colony of Rural Pen'The.\n\nLittle did [PAWN_pronoun] know, [PAWN_nameDef] was about to embark on a career path that would change [PAWN_possessive] life.</description>
    <skillGains>
      <Construction>2</Construction>
      <Plants>-2</Plants>
      <Mining>8</Mining>
      <Social>2</Social>
      <Medicine>-2</Medicine>
    </skillGains>
    <workDisables>Intellectual</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Miner</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>CrimeLord10</defName>
    <identifier>CrimeLord10</identifier>
    <slot>Adulthood</slot>
    <title>crime lord</title>
    <titleShort>crime lord</titleShort>
    <description>Through brutality and cruelty, [PAWN_nameDef] rose to become a crime lord.\n\n[PAWN_pronoun] relied less on [PAWN_possessive] fists and more on [PAWN_possessive] guns. [PAWN_possessive] reputation spread quickly due to [PAWN_possessive] cold-bloodedness and ruthless methods.</description>
    <skillGains>
      <Intellectual>2</Intellectual>
      <Shooting>6</Shooting>
      <Melee>2</Melee>
      <Social>3</Social>
    </skillGains>
    <workDisables>Caring, Artistic, Cleaning</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <forcedTraits>
      <Psychopath>0</Psychopath>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MarbleDoctor99</defName>
    <identifier>MarbleDoctor99</identifier>
    <slot>Adulthood</slot>
    <title>marble doctor</title>
    <titleShort>doctor</titleShort>
    <description>On a bomb-blasted world, first aid is often the only aid.\n\n[PAWN_nameDef] used [PAWN_possessive] medical skills to help the injured and the sick. [PAWN_pronoun] believed that while guns and machines may be powerful, a doctor is even more so.</description>
    <skillGains>
      <Intellectual>4</Intellectual>
      <Medicine>8</Medicine>
    </skillGains>
    <workDisables>Crafting</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MobHenchman53</defName>
    <identifier>MobHenchman53</identifier>
    <slot>Adulthood</slot>
    <title>mob henchman</title>
    <titleShort>henchman</titleShort>
    <description>After being "saved" from [PAWN_possessive] home world by a slaver ship, [PAWN_nameDef] was sold to an urbworld crime lord, where [PAWN_pronoun] was used as a henchman. [PAWN_pronoun] specialized in low-tech beat-downs and intimidation.</description>
    <skillGains>
      <Shooting>3</Shooting>
      <Melee>7</Melee>
      <Social>-2</Social>
      <Medicine>2</Medicine>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>Artistic, Cleaning</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MechWarVeteran77</defName>
    <identifier>MechWarVeteran77</identifier>
    <slot>Adulthood</slot>
    <title>mech war veteran</title>
    <titleShort>veteran</titleShort>
    <description>[PAWN_nameDef] fought against swarms of rogue mechanoids since [PAWN_pronoun] was old enough to enlist. [PAWN_pronoun] has bled on half a dozen worlds and has lost more than [PAWN_pronoun] cares to recall. These experiences left [PAWN_objective] hardened, both mentally and physically.</description>
    <skillGains>
      <Shooting>7</Shooting>
      <Melee>4</Melee>
    </skillGains>
    <workDisables>Caring, Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <forcedTraits>
      <Tough>0</Tough>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>PoisonGardener29</defName>
    <identifier>PoisonGardener29</identifier>
    <slot>Adulthood</slot>
    <title>poison gardener</title>
    <titleShort>botanist</titleShort>
    <description>[PAWN_nameDef] spent almost all [PAWN_possessive] time tending to a poison garden and became withdrawn from society.\n\nIn that time [PAWN_pronoun] learnt a few safe culinary and medicinal uses for the otherwise deadly plants.</description>
    <skillGains>
      <Plants>4</Plants>
      <Intellectual>1</Intellectual>
      <Cooking>2</Cooking>
      <Medicine>2</Medicine>
    </skillGains>
    <workDisables>Social, Hauling, Mining</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ArtificerRampant95</defName>
    <identifier>ArtificerRampant95</identifier>
    <slot>Adulthood</slot>
    <title>artificer rampant</title>
    <titleShort>artificer</titleShort>
    <description>Fuelled by the thrill of discovery and [PAWN_possessive] own megalomania, [PAWN_nameDef] set out to invent new devices and improve existing ones, breaking laws and customs as necessary.\n\n[PAWN_pronoun] formed a crew to gather materials for [PAWN_possessive] work - and to deal with outsiders who might interfere.</description>
    <skillGains>
      <Construction>2</Construction>
      <Intellectual>6</Intellectual>
      <Medicine>2</Medicine>
      <Crafting>8</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SystemsEngineer1</defName>
    <identifier>SystemsEngineer1</identifier>
    <slot>Adulthood</slot>
    <title>systems engineer</title>
    <titleShort>engineer</titleShort>
    <description>Like [PAWN_possessive] father, [PAWN_nameDef] was a systems engineer. [PAWN_pronoun] was qualified in both colony and ship systems.\n\nWhen not working, [PAWN_pronoun] would make mechanical figurines, read old books, or occasionally travel to uncharted worlds. Being introverted, [PAWN_pronoun] did these things mostly alone.</description>
    <skillGains>
      <Construction>5</Construction>
      <Intellectual>7</Intellectual>
      <Crafting>4</Crafting>
    </skillGains>
    <workDisables>Social</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>NeuroScientist19</defName>
    <identifier>NeuroScientist19</identifier>
    <slot>Adulthood</slot>
    <title>neuro scientist</title>
    <titleShort>scientist</titleShort>
    <description>[PAWN_nameDef] worked for a long time to improve mankind's understanding of the human brain.\n\nAfter succeeding in mapping the whole brain, [PAWN_nameDef] joined a team of scientists on a quest to map the brains of the most exotic altered humans.</description>
    <skillGains>
      <Intellectual>8</Intellectual>
      <Social>2</Social>
      <Medicine>3</Medicine>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>PirateCaptain69</defName>
    <identifier>PirateCaptain69</identifier>
    <slot>Adulthood</slot>
    <title>pirate captain</title>
    <titleShort>captain</titleShort>
    <description>[PAWN_nameDef] worked [PAWN_possessive] way up through the ranks of a pirate ship, eventually becoming captain. [PAWN_pronoun] leads [PAWN_possessive] crew between worlds, making a name to remember.</description>
    <skillGains>
      <Shooting>5</Shooting>
      <Melee>5</Melee>
      <Social>6</Social>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>HedgeFundManager54</defName>
    <identifier>HedgeFundManager54</identifier>
    <slot>Adulthood</slot>
    <title>hedge fund manager</title>
    <titleShort>banker</titleShort>
    <description>A top hedge fund manager on a teeming urbworld, [PAWN_nameDef]'s portfolio grew rapidly as [PAWN_pronoun] charmed clients with a sweet voice and devious intent. When the deal turned out too good to be true, [PAWN_pronoun] was forced to flee the planet with a bounty on [PAWN_possessive] head.\n\nAs [PAWN_pronoun] traveled the stars [PAWN_pronoun] found a passion in the arts, making masterpieces fit for high society customers.</description>
    <skillGains>
      <Construction>-2</Construction>
      <Plants>-2</Plants>
      <Intellectual>3</Intellectual>
      <Social>4</Social>
      <Artistic>7</Artistic>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <forcedTraits>
      <Greedy>0</Greedy>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ComputerEngineer10</defName>
    <identifier>ComputerEngineer10</identifier>
    <slot>Adulthood</slot>
    <title>computer engineer</title>
    <titleShort>tech head</titleShort>
    <description>Bored of [PAWN_possessive] code-monkey office job and missing the glory days of the Academy, [PAWN_nameDef] saw a commercial about the frontier where "only the strong survive".\n\nWith [PAWN_possessive] wife Morgan, [PAWN_nameDef] undertook a journey into the unknown.</description>
    <skillGains>
      <Intellectual>8</Intellectual>
      <Social>4</Social>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>ManualDumb, Violent</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ExpertHandyman8</defName>
    <identifier>ExpertHandyman8</identifier>
    <slot>Adulthood</slot>
    <title>expert handyman</title>
    <titleShort>handyman</titleShort>
    <description>[PAWN_nameDef] worked as a traveling engineer. [PAWN_pronoun] fixed things ranging from a simple communications array to the intricate software harness enveloping [PAWN_possessive] ship's AI persona. Nothing is beyond repair for [PAWN_objective].</description>
    <skillGains>
      <Construction>7</Construction>
      <Intellectual>4</Intellectual>
      <Crafting>3</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Outlander</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MedicSoldier54</defName>
    <identifier>MedicSoldier54</identifier>
    <slot>Adulthood</slot>
    <title>medic soldier</title>
    <titleShort>medic</titleShort>
    <description>[PAWN_nameDef] enrolled in [PAWN_possessive] planetary army as a frontline medic. [PAWN_pronoun] soon knew the horrors of war and was deeply marked by them.\n\n[PAWN_pronoun] left the military and [PAWN_possessive] homeworld and began researching medicine and helping those [PAWN_pronoun] came across.</description>
    <skillGains>
      <Intellectual>5</Intellectual>
      <Medicine>7</Medicine>
    </skillGains>
    <workDisables>Violent</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ExiledResearcher44</defName>
    <identifier>ExiledResearcher44</identifier>
    <slot>Adulthood</slot>
    <title>exiled researcher</title>
    <titleShort>exile</titleShort>
    <description>[PAWN_nameDef]'s love of robotics was not appreciated in [PAWN_possessive] technophobic empire. The High Cardinal of the system excommunicated her, and [PAWN_pronoun] was never allowed to return.</description>
    <skillGains>
      <Intellectual>8</Intellectual>
      <Shooting>2</Shooting>
      <Medicine>4</Medicine>
      <Artistic>2</Artistic>
      <Crafting>3</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
      <li>ImperialCommon</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Villain89</defName>
    <identifier>Villain89</identifier>
    <slot>Adulthood</slot>
    <title>villain</title>
    <titleShort>villain</titleShort>
    <description>After rising to the top of [PAWN_possessive] criminal band, [PAWN_nameDef] was taught the arts of villainy, from handling firearms to developing new weapons technologies. [PAWN_pronoun] excelled at bending others to [PAWN_possessive] will.</description>
    <skillGains>
      <Intellectual>2</Intellectual>
      <Mining>-2</Mining>
      <Shooting>4</Shooting>
      <Social>6</Social>
    </skillGains>
    <workDisables>Hauling</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
    <possessions>
      <TextBook>1</TextBook>
    </possessions>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpaceNavyDoctor72</defName>
    <identifier>SpaceNavyDoctor72</identifier>
    <slot>Adulthood</slot>
    <title>space navy doctor</title>
    <titleShort>doctor</titleShort>
    <description>[PAWN_nameDef] served several tours as a doctor on one of the few warships defending [PAWN_possessive] homeworld. [PAWN_pronoun] learned combat skills while fighting occasional pirate boarding parties, and became an expert at treating gruesome combat wounds.</description>
    <skillGains>
      <Construction>-2</Construction>
      <Plants>-2</Plants>
      <Mining>-2</Mining>
      <Shooting>3</Shooting>
      <Melee>1</Melee>
      <Social>2</Social>
      <Medicine>5</Medicine>
      <Artistic>-1</Artistic>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <possessions>
      <Penoxycyline>10</Penoxycyline>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>GovernmentAgent61</defName>
    <identifier>GovernmentAgent61</identifier>
    <slot>Adulthood</slot>
    <title>government agent</title>
    <titleShort>agent</titleShort>
    <description>[PAWN_nameDef] worked for the government against rebel groups and excelled in this capacity. During [PAWN_possessive] training [PAWN_pronoun] was taught firearms, melee combat and medical skills.\n\nHaving grown up in a war-ravaged city, dirt, blood and dead bodies don't bother [PAWN_objective] and [PAWN_pronoun] sees no reason to clean them up.</description>
    <skillGains>
      <Shooting>4</Shooting>
      <Melee>4</Melee>
      <Medicine>3</Medicine>
    </skillGains>
    <workDisables>Cleaning</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SelflessHunter61</defName>
    <identifier>SelflessHunter61</identifier>
    <slot>Adulthood</slot>
    <title>selfless hunter</title>
    <titleShort>hunter</titleShort>
    <description>[PAWN_nameDef] was a hunter of mysterious creatures that many didn't even believe existed. Sometimes the people were right - and sometimes they were wrong.\n\nAlong the way, [PAWN_pronoun] helped people in need.</description>
    <skillGains>
      <Shooting>5</Shooting>
      <Social>6</Social>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Outlander</li>
      <li>Pirate</li>
      <li>Hunter</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <possessions>
      <ThrumboHorn>1</ThrumboHorn>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>AcolyteOfStars6</defName>
    <identifier>AcolyteOfStars6</identifier>
    <slot>Adulthood</slot>
    <title>acolyte of stars</title>
    <titleShort>priest</titleShort>
    <description>Ever the dreamer, [PAWN_nameDef] traveled across the universe to find the thing [PAWN_pronoun] felt was calling to him.\n\n[PAWN_pronoun] became poorer and poorer, but used every means possible to carry on.</description>
    <skillGains>
      <Intellectual>-3</Intellectual>
      <Social>-3</Social>
      <Medicine>4</Medicine>
      <Artistic>7</Artistic>
    </skillGains>
    <workDisables>ManualDumb, Violent</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpaceRaider71</defName>
    <identifier>SpaceRaider71</identifier>
    <slot>Adulthood</slot>
    <title>space raider</title>
    <titleShort>Pirate</titleShort>
    <description>[PAWN_nameDef] was the leader of a spaceborne pirate crew. Skipping between the rimworlds, [PAWN_pronoun] became the feared enemy of many settlements.</description>
    <skillGains>
      <Shooting>8</Shooting>
      <Melee>3</Melee>
      <Social>5</Social>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Spy58</defName>
    <identifier>Spy58</identifier>
    <slot>Adulthood</slot>
    <title>spy</title>
    <titleShort>spy</titleShort>
    <description>[PAWN_nameDef] was trained by [PAWN_possessive] government in infiltration and espionage. [PAWN_pronoun] learned to keep a cool head and to talk [PAWN_possessive] way out of tight situations. When talking didn't work, [PAWN_pronoun] was capable of more violent direct action.</description>
    <skillGains>
      <Shooting>2</Shooting>
      <Melee>2</Melee>
      <Social>7</Social>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>WanderingHealer52</defName>
    <identifier>WanderingHealer52</identifier>
    <slot>Adulthood</slot>
    <title>wandering healer</title>
    <titleShort>healer</titleShort>
    <description>[PAWN_nameDef] studied to be a doctor, but was dissatisfied with [PAWN_possessive] practice on an otherwise peaceful midworld.\n\nInstead, [PAWN_pronoun] chose to pull up roots and travel to the rimworlds in order to care for people who needed [PAWN_objective] most.</description>
    <skillGains>
      <Construction>-1</Construction>
      <Mining>-1</Mining>
      <Social>3</Social>
      <Cooking>2</Cooking>
      <Medicine>7</Medicine>
    </skillGains>
    <forcedTraits>
      <Kind />
    </forcedTraits>
    <workDisables>Violent</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <possessions>
      <MedicineIndustrial>3</MedicineIndustrial>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>CosmeticReject64</defName>
    <identifier>CosmeticReject64</identifier>
    <slot>Adulthood</slot>
    <title>cosmetic reject</title>
    <titleShort>reject</titleShort>
    <description>Living on the streets, [PAWN_nameDef] submitted [PAWN_objective]self to an unethical cosmetics lab in exchange for food and shelter.\n\nAfter some painful cosmetic modification, [PAWN_nameDef] barely resembles [PAWN_possessive] former self. Having been forced to work for the lab until [PAWN_pronoun] was rescued, [PAWN_nameDef] is now too afraid to lash out at anyone.</description>
    <skillGains>
      <Construction>4</Construction>
      <Cooking>5</Cooking>
    </skillGains>
    <workDisables>Violent</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>RaiderKing38</defName>
    <identifier>RaiderKing38</identifier>
    <slot>Adulthood</slot>
    <title>raider king</title>
    <titleShort>Pirate</titleShort>
    <description>Starting as nothing more than a slave, [PAWN_nameDef] rose quickly among the raider ranks until all knew [PAWN_possessive] name and [PAWN_possessive] ruthlessness.</description>
    <skillGains>
      <Mining>3</Mining>
      <Shooting>5</Shooting>
      <Melee>5</Melee>
    </skillGains>
    <workDisables>Intellectual</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <forcedTraits>
      <Psychopath>0</Psychopath>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>FrontierMarshal27</defName>
    <identifier>FrontierMarshal27</identifier>
    <slot>Adulthood</slot>
    <title>frontier marshal</title>
    <titleShort>marshal</titleShort>
    <description>[PAWN_nameDef] was the marshal of [PAWN_possessive] colony. [PAWN_pronoun] tried to honorably uphold the law, but the corrupt local mayor killed [PAWN_possessive] family and left [PAWN_objective] for dead.\n\nAfter regaining [PAWN_possessive] health, [PAWN_nameDef] arrested the mayor. [PAWN_pronoun] left [PAWN_possessive] homeworld and started a new life working to make a difference in the universe.</description>
    <skillGains>
      <Shooting>5</Shooting>
      <Social>4</Social>
      <Medicine>3</Medicine>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ReligiousHierarch16</defName>
    <identifier>ReligiousHierarch16</identifier>
    <slot>Adulthood</slot>
    <title>religious hierarch</title>
    <titleShort>hierarch</titleShort>
    <description>Ordained as a priest in [PAWN_possessive] local community, [PAWN_nameDef] used intrigue and diplomacy to rise to the higher echelon of the clergy. [PAWN_pronoun] held numerous titles and lands.\n\nOne day, a merchant landed [PAWN_possessive] spaceship near [PAWN_nameDef]'s home. [PAWN_pronoun] decided to accompany the merchant in search for new worlds and challenges.</description>
    <skillGains>
      <Construction>-3</Construction>
      <Mining>-3</Mining>
      <Shooting>-2</Shooting>
      <Melee>-2</Melee>
      <Social>8</Social>
      <Cooking>4</Cooking>
      <Medicine>5</Medicine>
    </skillGains>
    <workDisables>ManualDumb, Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>AnarchistRebel77</defName>
    <identifier>AnarchistRebel77</identifier>
    <slot>Adulthood</slot>
    <title>anarchist rebel</title>
    <titleShort>anarchist</titleShort>
    <description>After being abandoned on a special operation, [PAWN_nameDef] swore vengeance upon the state. [PAWN_pronoun] formed an anarchist group dedicated to bringing down coreworld governments.\n\nAfter too many close calls, and too few successes, [PAWN_nameDef] fled to the outer rim.</description>
    <skillGains>
      <Shooting>6</Shooting>
      <Melee>2</Melee>
      <Social>6</Social>
    </skillGains>
    <workDisables>Crafting, Cleaning</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>CriminalSurgeon99</defName>
    <identifier>CriminalSurgeon99</identifier>
    <slot>Adulthood</slot>
    <title>criminal surgeon</title>
    <titleShort>surgeon</titleShort>
    <description>Being near many injured fellow criminals, [PAWN_nameDef] took it upon [PAWN_objective]self to work on them. Although many people died, [PAWN_pronoun] did get better at using knives.</description>
    <skillGains>
      <Intellectual>-3</Intellectual>
      <Melee>6</Melee>
      <Social>-2</Social>
      <Medicine>5</Medicine>
      <Artistic>1</Artistic>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SanitationCaptain29</defName>
    <identifier>SanitationCaptain29</identifier>
    <slot>Adulthood</slot>
    <title>sanitation captain</title>
    <titleShort>janitor</titleShort>
    <description>After graduating officer training school, [PAWN_nameDef] was assigned to main battle deck on the Thunder-Child. [PAWN_pronoun] served there for a year before [PAWN_possessive] "gallant" actions saw [PAWN_objective] reassigned to the maintenance corps.\n\nIt was with the maintenance corps [PAWN_pronoun] was dubbed [PAWN_nameDef] on account of [PAWN_possessive] legs.</description>
    <skillGains>
      <Construction>5</Construction>
      <Crafting>3</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
      <li>ImperialCommon</li>
    </spawnCategories>
    <bodyTypeGlobal>Fat</bodyTypeGlobal>
    <bodyTypeFemale>Fat</bodyTypeFemale>
    <bodyTypeMale>Fat</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>OverwatchSniper42</defName>
    <identifier>OverwatchSniper42</identifier>
    <slot>Adulthood</slot>
    <title>overwatch sniper</title>
    <titleShort>sniper</titleShort>
    <description>[PAWN_nameDef] didn't know why [PAWN_pronoun] joined the military, but [PAWN_pronoun] stayed to protect [PAWN_possessive] fellow soldiers.\n\nArmy life broke [PAWN_objective] down and built [PAWN_objective] back up. It taught [PAWN_objective] marksmanship, hand to hand combat and how to patch a bullet wound in a hurry.</description>
    <skillGains>
      <Shooting>5</Shooting>
      <Melee>3</Melee>
      <Medicine>3</Medicine>
    </skillGains>
    <workDisables>Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <possessions>
      <Gun_SniperRifle>1</Gun_SniperRifle>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Sightseer70</defName>
    <identifier>Sightseer70</identifier>
    <slot>Adulthood</slot>
    <title>sightseer</title>
    <titleShort>sightseer</titleShort>
    <description>[PAWN_nameDef] traveled the stars. [PAWN_pronoun] could barely scrape together travel costs doing odd-jobs, but it was all worth it.\n\n[PAWN_pronoun] loved camping in exotic places and learning the patterns of strange wildlife. [PAWN_pronoun] never could focus on intellectual endeavors, as [PAWN_possessive] sightseeing distracted [PAWN_objective] far too much.</description>
    <skillGains>
      <Plants>2</Plants>
      <Shooting>2</Shooting>
      <Melee>2</Melee>
      <Medicine>2</Medicine>
      <Artistic>2</Artistic>
      <Animals>2</Animals>
    </skillGains>
    <workDisables>Intellectual</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>GameTester77</defName>
    <identifier>GameTester77</identifier>
    <slot>Adulthood</slot>
    <title>game tester</title>
    <titleShort>tester</titleShort>
    <description>Making a new life on a barely-livable planet after nuclear war, [PAWN_nameDef] was able to fulfill [PAWN_possessive] desires with money borrowed from [PAWN_possessive] father.\n\n[PAWN_pronoun] loved playing a particular colony simulator. [PAWN_possessive] favorite thing to do in the game was to build elaborate hospitals. Spending most of [PAWN_possessive] time playing, [PAWN_pronoun] forgot horrors of war and [PAWN_possessive] need for revenge.</description>
    <skillGains>
      <Intellectual>5</Intellectual>
      <Shooting>4</Shooting>
      <Melee>4</Melee>
      <Social>-2</Social>
      <Medicine>4</Medicine>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>WanderingCrafter42</defName>
    <identifier>WanderingCrafter42</identifier>
    <slot>Adulthood</slot>
    <title>wandering crafter</title>
    <titleShort>crafter</titleShort>
    <description>[PAWN_nameDef] continued to follow [PAWN_possessive] parents' nomadic ways, supporting [PAWN_objective]self with [PAWN_possessive] crafting skills. But wandering is a hard life, and [PAWN_pronoun] often thought of finding somewhere to settle down and put [PAWN_possessive] skills to good use.</description>
    <skillGains>
      <Intellectual>2</Intellectual>
      <Shooting>3</Shooting>
      <Crafting>8</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Outlander</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MutinousCaptain52</defName>
    <identifier>MutinousCaptain52</identifier>
    <slot>Adulthood</slot>
    <title>mutinous captain</title>
    <titleShort>captain</titleShort>
    <description>[PAWN_nameDef] was taken to the stars by slavers after [PAWN_possessive] tribe was decimated in a raid. Over the years [PAWN_pronoun] learned the language of [PAWN_possessive] captors, eventually working for them as an interpreter and diplomat.\n\nThe captain only learned of [PAWN_nameDef]'s true cunning when revenge was taken by form of mutiny.</description>
    <skillGains>
      <Shooting>4</Shooting>
      <Social>8</Social>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>IntimateAssassin35</defName>
    <identifier>IntimateAssassin35</identifier>
    <slot>Adulthood</slot>
    <title>intimate assassin</title>
    <titleShort>assassin</titleShort>
    <description>[PAWN_nameDef] was a professional assassin. [PAWN_pronoun] was sadistic, calculating, and appeared utterly harmless.\n\n[PAWN_pronoun] preferred using intimate means to dispatch [PAWN_possessive] targets. This fulfilled both [PAWN_possessive] contracts and [PAWN_possessive] own personal desire for control.</description>
    <skillGains>
      <Melee>8</Melee>
      <Social>4</Social>
    </skillGains>
    <workDisables>Caring, Animals</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <forcedTraits>
      <Beauty>1</Beauty>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>CorpResearcher93</defName>
    <identifier>CorpResearcher93</identifier>
    <slot>Adulthood</slot>
    <title>corp researcher</title>
    <titleShort>researcher</titleShort>
    <description>[PAWN_nameDef] was a leading researcher for a massive space exploration corporation.</description>
    <skillGains>
      <Intellectual>8</Intellectual>
      <Social>3</Social>
      <Cooking>-2</Cooking>
      <Medicine>6</Medicine>
      <Crafting>3</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SoleSurvivor85</defName>
    <identifier>SoleSurvivor85</identifier>
    <slot>Adulthood</slot>
    <title>sole survivor</title>
    <titleShort>survivor</titleShort>
    <description>[PAWN_nameDef] lived in a remote colony. When mechanoid centipedes attacked [PAWN_possessive] home and killed [PAWN_possessive] people, [PAWN_pronoun] was the last survivor.</description>
    <skillGains>
      <Shooting>4</Shooting>
      <Melee>5</Melee>
      <Social>-3</Social>
    </skillGains>
    <workDisables>Animals, Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>AWOLSoldier49</defName>
    <identifier>AWOLSoldier49</identifier>
    <slot>Adulthood</slot>
    <title>AWOL soldier</title>
    <titleShort>soldier</titleShort>
    <description>[PAWN_nameDef] was an infantryman. When [PAWN_pronoun] was informed of [PAWN_possessive] assignment to the Xennoa-Zartza War, [PAWN_pronoun] decided [PAWN_pronoun] did not want to be a soldier. [PAWN_pronoun] was able to escape via military spacejet.</description>
    <skillGains>
      <Shooting>6</Shooting>
      <Melee>5</Melee>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Explorer49</defName>
    <identifier>Explorer49</identifier>
    <slot>Adulthood</slot>
    <title>explorer</title>
    <titleShort>explorer</titleShort>
    <description>[PAWN_nameDef] traveled from planet to planet, gathering samples, stories, and research. [PAWN_pronoun] hoped one day to compile a record of every animal, plant, mineral, and machine and device in known space: The Complete Traveler's Guide to the RimWorlds.</description>
    <skillGains>
      <Intellectual>3</Intellectual>
      <Mining>3</Mining>
      <Shooting>2</Shooting>
      <Melee>2</Melee>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>StarshipDoctor37</defName>
    <identifier>StarshipDoctor37</identifier>
    <slot>Adulthood</slot>
    <title>starship doctor</title>
    <titleShort>doctor</titleShort>
    <description>[PAWN_nameDef] was a doctor on a starship. [PAWN_pronoun] was a brilliant surgeon and dabbled in research of [PAWN_possessive] own. However, [PAWN_pronoun] was quite reclusive and never had the best bedside manner.</description>
    <skillGains>
      <Intellectual>5</Intellectual>
      <Melee>-2</Melee>
      <Social>-3</Social>
      <Medicine>8</Medicine>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <possessions>
      <MedicineUltratech>2</MedicineUltratech>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>BountyHunter41</defName>
    <identifier>BountyHunter41</identifier>
    <slot>Adulthood</slot>
    <title>bounty hunter</title>
    <titleShort>hunter</titleShort>
    <description>[PAWN_nameDef] was a bounty hunter on a massive glitterworld. [PAWN_pronoun] worked sensitive assignments for extremely wealthy clients, and was known for quiet execution and total discretion.</description>
    <skillGains>
      <Shooting>8</Shooting>
      <Melee>5</Melee>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>FallenOfficial12</defName>
    <identifier>FallenOfficial12</identifier>
    <slot>Adulthood</slot>
    <title>fallen official</title>
    <titleShort>official</titleShort>
    <description>[PAWN_nameDef] was an official for an autocratic government. When [PAWN_possessive] superiors demanded [PAWN_pronoun] participate in atrocities, [PAWN_nameDef] resigned and escaped from [PAWN_possessive] homeworld with nothing but the clothes on [PAWN_possessive] back.</description>
    <skillGains>
      <Melee>2</Melee>
      <Social>5</Social>
      <Medicine>2</Medicine>
    </skillGains>
    <workDisables>ManualSkilled</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>OrbitalReservist22</defName>
    <identifier>OrbitalReservist22</identifier>
    <slot>Adulthood</slot>
    <title>orbital reservist</title>
    <titleShort>reservist</titleShort>
    <description>When the government declared that it would strengthen its military defenses, [PAWN_nameDef] was drafted and trained.\n\n[PAWN_pronoun] was stationed on an orbital base, manning the defense systems and, in dire situations, descending in a dropship to the surface.</description>
    <skillGains>
      <Shooting>7</Shooting>
      <Melee>5</Melee>
      <Medicine>2</Medicine>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ShamanOfShadows47</defName>
    <identifier>ShamanOfShadows47</identifier>
    <slot>Adulthood</slot>
    <title>shaman of shadows</title>
    <titleShort>shaman</titleShort>
    <description>When the monsters of fire came from the sky, [PAWN_nameDef] was called upon by [PAWN_possessive] tribe to guide them into battle.\n\n[PAWN_nameDef] made use of medicines and rituals to inspire and lead the warriors of many tribes against the invaders. The pirates were forced to retreat without capturing a single slave.</description>
    <skillGains>
      <Construction>-2</Construction>
      <Plants>2</Plants>
      <Intellectual>2</Intellectual>
      <Social>6</Social>
      <Medicine>4</Medicine>
    </skillGains>
    <workDisables>Violent</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Tribal</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>StationSecurity38</defName>
    <identifier>StationSecurity38</identifier>
    <slot>Adulthood</slot>
    <title>station security</title>
    <titleShort>security</titleShort>
    <description>[PAWN_nameDef] was commissioned as a special constable on a network of space stations. There [PAWN_pronoun] prevented illegal human trafficking.\n\nOnce, fire broke out on the station, and [PAWN_pronoun] saved many from the flames. Since then, [PAWN_pronoun] has avoided fires.</description>
    <skillGains>
      <Shooting>4</Shooting>
      <Melee>3</Melee>
      <Social>3</Social>
      <Medicine>3</Medicine>
      <Artistic>-3</Artistic>
    </skillGains>
    <workDisables>Firefighting</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpaceTrafficker68</defName>
    <identifier>SpaceTrafficker68</identifier>
    <slot>Adulthood</slot>
    <title>space trafficker</title>
    <titleShort>trafficker</titleShort>
    <description>[PAWN_nameDef] discovered [PAWN_pronoun] was heir of [PAWN_possessive] distant uncle's weapons-trading business.\n\nTo protect the future of the company [PAWN_pronoun] went through extensive military training, in case of any "unforeseen circumstances".</description>
    <skillGains>
      <Shooting>5</Shooting>
      <Melee>5</Melee>
      <Social>5</Social>
      <Medicine>-2</Medicine>
    </skillGains>
    <workDisables>Animals, Artistic, Crafting</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>PopIdolPirate39</defName>
    <identifier>PopIdolPirate39</identifier>
    <slot>Adulthood</slot>
    <title>pop idol pirate</title>
    <titleShort>pirate</titleShort>
    <description>[PAWN_nameDef] and [PAWN_possessive] fans set out as a space pirate crew. [PAWN_pronoun] raided corporations for money and staged performances to spread [PAWN_possessive] name.\n\n[PAWN_nameDef] became afraid of fire when a rival burned [PAWN_possessive] stage down. [PAWN_pronoun] relied on [PAWN_possessive] fans to do the work [PAWN_pronoun] found unappealing.</description>
    <skillGains>
      <Plants>-2</Plants>
      <Shooting>5</Shooting>
      <Melee>7</Melee>
      <Social>4</Social>
      <Cooking>-2</Cooking>
    </skillGains>
    <workDisables>ManualDumb, Caring, Firefighting</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <forcedTraits>
      <Beauty>2</Beauty>
      <Greedy>0</Greedy>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Roboticist1</defName>
    <identifier>Roboticist1</identifier>
    <slot>Adulthood</slot>
    <title>roboticist</title>
    <titleShort>roboticist</titleShort>
    <description>Sailors often died on the dangerous waters of [PAWN_nameDef]'s ocean planet. [PAWN_pronoun] created robots to replace them.\n\nOver time, [PAWN_pronoun] collected a small crew of robots to do [PAWN_possessive] bidding. [PAWN_pronoun] became proficient at mechanics and engineering, but lost the taste for manual labor.</description>
    <skillGains>
      <Construction>3</Construction>
      <Intellectual>7</Intellectual>
      <Crafting>3</Crafting>
    </skillGains>
    <workDisables>ManualDumb</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Chemist78</defName>
    <identifier>Chemist78</identifier>
    <slot>Adulthood</slot>
    <title>chemist</title>
    <titleShort>chemist</titleShort>
    <description>[PAWN_nameDef] was a renowned chemist on a thriving midworld.\n\nAfter performing inhumane experiments that turned men into beasts, [PAWN_pronoun] was arrested and banished to a far-off rimworld.</description>
    <skillGains>
      <Intellectual>6</Intellectual>
      <Social>-3</Social>
      <Crafting>6</Crafting>
    </skillGains>
    <workDisables>Caring</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>StalwartFarmer90</defName>
    <identifier>StalwartFarmer90</identifier>
    <slot>Adulthood</slot>
    <title>stalwart farmer</title>
    <titleShort>farmer</titleShort>
    <description>With the farm under constant threat from brigands, [PAWN_nameDef] learned to defend [PAWN_objective]self with [PAWN_possessive] father's rifle. [PAWN_pronoun] also mastered how to construct walls to keep livestock in and predators out.\n\n[PAWN_pronoun] once tried to paint a picture of [PAWN_possessive] home, but discovered that [PAWN_pronoun] could only draw scribbles and ducks.</description>
    <skillGains>
      <Construction>4</Construction>
      <Shooting>4</Shooting>
      <Melee>2</Melee>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>Animals, Artistic, Cooking</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
      <li>Farmer</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <possessions>
      <Hay>10</Hay>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>UrbworldPimp93</defName>
    <identifier>UrbworldPimp93</identifier>
    <slot>Adulthood</slot>
    <title>urbworld pimp</title>
    <titleShort>pimp</titleShort>
    <description>At first, [PAWN_nameDef] made a small profit from selling drugs. Then, [PAWN_pronoun] switched to selling women and made a fortune.\n\n[PAWN_pronoun] developed great skills with [PAWN_possessive] fists. [PAWN_pronoun] also learned to fix up the injuries [PAWN_pronoun] inflicted on the girls.</description>
    <skillGains>
      <Melee>6</Melee>
      <Social>4</Social>
      <Medicine>5</Medicine>
    </skillGains>
    <workDisables>Cleaning</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ParticlePhysicist44</defName>
    <identifier>ParticlePhysicist44</identifier>
    <slot>Adulthood</slot>
    <title>particle physicist</title>
    <titleShort>physicist</titleShort>
    <description>[PAWN_nameDef] performed cutting-edge physics research. [PAWN_pronoun] wanted to help build a better and more peaceful future.</description>
    <skillGains>
      <Plants>2</Plants>
      <Intellectual>8</Intellectual>
      <Medicine>3</Medicine>
      <Artistic>3</Artistic>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>DefenseLawyer71</defName>
    <identifier>DefenseLawyer71</identifier>
    <slot>Adulthood</slot>
    <title>defense lawyer</title>
    <titleShort>lawyer</titleShort>
    <description>[PAWN_nameDef] found that [PAWN_pronoun] could make more money with a quick word than a blaster. Seeking a job where [PAWN_possessive] silver tongue could be useful, [PAWN_pronoun] turned to the law.\n\n[PAWN_pronoun] often defended the illegal speeder racers [PAWN_pronoun] grew up with.</description>
    <skillGains>
      <Intellectual>5</Intellectual>
      <Shooting>3</Shooting>
      <Social>8</Social>
    </skillGains>
    <workDisables>ManualDumb, ManualSkilled, Caring</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Ranger6</defName>
    <identifier>Ranger6</identifier>
    <slot>Adulthood</slot>
    <title>ranger</title>
    <titleShort>ranger</titleShort>
    <description>[PAWN_nameDef] lived alone, deep in a forest. [PAWN_pronoun] protected the wildlife against poachers, and learned to satisfy [PAWN_possessive] own material needs without outside help.</description>
    <skillGains>
      <Construction>2</Construction>
      <Plants>2</Plants>
      <Shooting>4</Shooting>
      <Cooking>2</Cooking>
      <Medicine>2</Medicine>
    </skillGains>
    <workDisables>Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ClanChief14</defName>
    <identifier>ClanChief14</identifier>
    <slot>Adulthood</slot>
    <title>clan chief</title>
    <titleShort>chief</titleShort>
    <description>[PAWN_nameDef] managed a small criminal clan on an outlying trade hub world.</description>
    <skillGains>
      <Shooting>6</Shooting>
      <Social>7</Social>
    </skillGains>
    <workDisables>Caring</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>FactionLeader74</defName>
    <identifier>FactionLeader74</identifier>
    <slot>Adulthood</slot>
    <title>faction leader</title>
    <titleShort>leader</titleShort>
    <description>Early in [PAWN_possessive] adulthood, [PAWN_nameDef] found [PAWN_objective]self surrounded by a gaggle of low-lifes.\n\nFor fun, [PAWN_pronoun] gathered them and lead them to a certain death. [PAWN_pronoun] knew then that [PAWN_pronoun] had the talent to realize [PAWN_possessive] ambitions of leadership.</description>
    <skillGains>
      <Intellectual>3</Intellectual>
      <Melee>2</Melee>
      <Social>8</Social>
      <Medicine>2</Medicine>
    </skillGains>
    <workDisables>Cleaning</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Fat</bodyTypeGlobal>
    <bodyTypeFemale>Fat</bodyTypeFemale>
    <bodyTypeMale>Fat</bodyTypeMale>
    <forcedTraits>
      <Psychopath>0</Psychopath>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ProstheticSurgeon0</defName>
    <identifier>ProstheticSurgeon0</identifier>
    <slot>Adulthood</slot>
    <title>prosthetic surgeon</title>
    <titleShort>surgeon</titleShort>
    <description>[PAWN_nameDef] used [PAWN_possessive] talent for prosthetics to 'advance' others by replacing limbs and organs with metal and electronics.</description>
    <skillGains>
      <Plants>-2</Plants>
      <Intellectual>4</Intellectual>
      <Medicine>6</Medicine>
      <Crafting>4</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <possessions>
      <AestheticNose MayRequire="Ludeon.RimWorld.Royalty">1</AestheticNose>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>CorpResearcher71</defName>
    <identifier>CorpResearcher71</identifier>
    <slot>Adulthood</slot>
    <title>corp researcher</title>
    <titleShort>researcher</titleShort>
    <description>[PAWN_nameDef] was sold to a corporation and put to work as a corporate slave doing scientific research.\n\nUnable to adapt to the social politics of [PAWN_possessive] workplace, [PAWN_pronoun] buried [PAWN_objective]self in [PAWN_possessive] work and failed to progress up the corporate ladder. Every day, [PAWN_pronoun] yearned to be left alone.</description>
    <skillGains>
      <Intellectual>8</Intellectual>
      <Shooting>-3</Shooting>
      <Melee>-3</Melee>
      <Medicine>4</Medicine>
      <Crafting>4</Crafting>
    </skillGains>
    <workDisables>Social</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpaceMarine9</defName>
    <identifier>SpaceMarine9</identifier>
    <slot>Adulthood</slot>
    <title>space marine</title>
    <titleShort>marine</titleShort>
    <description>[PAWN_nameDef] was a member of an elite imperial space warrior unit. Subjected to intense training, [PAWN_pronoun] developed remarkable combat skills.\n\n[PAWN_possessive] unit's quasi-religious ceremonies and beliefs helped [PAWN_objective] stay more-or-less sane through the horrors of the war.</description>
    <skillGains>
      <Shooting>6</Shooting>
      <Melee>5</Melee>
      <Social>-2</Social>
    </skillGains>
    <workDisables>Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
      <li>ImperialCommon</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>TechnologyDoctor35</defName>
    <identifier>TechnologyDoctor35</identifier>
    <slot>Adulthood</slot>
    <title>soldier-farmer</title>
    <titleShort>soldier</titleShort>
    <description>[PAWN_nameDef] was a farmer on [PAWN_possessive] midworld, and spent [PAWN_possessive] free time practicing with guns. [PAWN_pronoun] knew how important the skills of farming and shooting would be in hard times, and trained to serve in a local militia.</description>
    <skillGains>
      <Plants>4</Plants>
      <Shooting>4</Shooting>
      <Medicine>2</Medicine>
      <Animals>2</Animals>
    </skillGains>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
      <li>Farmer</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>false</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>PirateTrooper73</defName>
    <identifier>PirateTrooper73</identifier>
    <slot>Adulthood</slot>
    <title>pirate trooper</title>
    <titleShort>trooper</titleShort>
    <description>[PAWN_nameDef] joined a renowned interstellar criminal organization, and was often part of shock-assault boarding parties during starship raids.\n\nThough [PAWN_possessive] combat experience made [PAWN_objective] a good fighter, the ruthlessness of the job left [PAWN_objective] cold-hearted and unenthusiastic about social interaction.</description>
    <skillGains>
      <Shooting>6</Shooting>
      <Melee>5</Melee>
    </skillGains>
    <workDisables>Caring, Social</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>CombatMedic82</defName>
    <identifier>CombatMedic82</identifier>
    <slot>Adulthood</slot>
    <title>combat medic</title>
    <titleShort>medic</titleShort>
    <description>After [PAWN_possessive] home country entered a large-scale war, [PAWN_nameDef] was drafted into the army as a combat medic.\n\n[PAWN_possessive] few years in the trenches trying to keep [PAWN_possessive] fellow soldiers alive gave [PAWN_objective] an acute sense for first aid. Losing many friend has made [PAWN_objective] stoic and reserved.</description>
    <skillGains>
      <Shooting>2</Shooting>
      <Melee>1</Melee>
      <Medicine>6</Medicine>
      <Artistic>-2</Artistic>
    </skillGains>
    <workDisables>Social</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>HiredGun70</defName>
    <identifier>HiredGun70</identifier>
    <slot>Adulthood</slot>
    <title>hired gun</title>
    <titleShort>hired gun</titleShort>
    <description>[PAWN_nameDef] always wanted to prove [PAWN_pronoun] could make a living without [PAWN_possessive] parents' help. [PAWN_pronoun] did shady jobs that often involved pointing guns at innocent people.\n\n[PAWN_pronoun] eventually made enough money to board a cheap freighter. [PAWN_pronoun] discovered why it was so cheap when it crashed.</description>
    <skillGains>
      <Shooting>6</Shooting>
      <Melee>2</Melee>
      <Cooking>2</Cooking>
      <Medicine>3</Medicine>
    </skillGains>
    <workDisables>PlantWork</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>StilettoAssassin34</defName>
    <identifier>StilettoAssassin34</identifier>
    <slot>Adulthood</slot>
    <title>stiletto assassin</title>
    <titleShort>assassin</titleShort>
    <description>Because of [PAWN_possessive] skills, [PAWN_nameDef] developed a sense of superiority. In time, [PAWN_pronoun] lost [PAWN_possessive] sense of empathy. [PAWN_pronoun] often got into fights, and discovered how easy it is to kill a person.\n\nAfter realizing [PAWN_possessive] talents, [PAWN_pronoun] became one of the most efficient assassins in the system.</description>
    <skillGains>
      <Construction>-3</Construction>
      <Mining>-3</Mining>
      <Shooting>6</Shooting>
      <Melee>8</Melee>
      <Medicine>2</Medicine>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Hunter73</defName>
    <identifier>Hunter73</identifier>
    <slot>Adulthood</slot>
    <title>hunter</title>
    <titleShort>hunter</titleShort>
    <description>[PAWN_nameDef] was a master hunter.\n\n[PAWN_pronoun] learned to track and trap any animal in the most dangerous of places, and had deep knowledge of many ingenious methods for taking down specific large animals.</description>
    <skillGains>
      <Plants>2</Plants>
      <Shooting>6</Shooting>
      <Medicine>4</Medicine>
    </skillGains>
    <workDisables>Mining</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Tribal</li>
      <li>Hunter</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>BattlefieldTech52</defName>
    <identifier>BattlefieldTech52</identifier>
    <slot>Adulthood</slot>
    <title>battlefield tech</title>
    <titleShort>technician</titleShort>
    <description>[PAWN_nameDef] had received firearms training, but [PAWN_possessive] work was focused on technical adaptation and manipulation of combat mechanoids on the battlefield.\n\n[PAWN_pronoun] developed PTSD after narrowly escaping a primitive firebomb attack in a wheat field. The army medically discharged [PAWN_objective] from service.</description>
    <skillGains>
      <Construction>4</Construction>
      <Intellectual>4</Intellectual>
      <Shooting>3</Shooting>
      <Cooking>-3</Cooking>
      <Crafting>4</Crafting>
    </skillGains>
    <workDisables>Firefighting, PlantWork</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MasterTrader65</defName>
    <identifier>MasterTrader65</identifier>
    <slot>Adulthood</slot>
    <title>master trader</title>
    <titleShort>trader</titleShort>
    <description>[PAWN_nameDef] was a master at trading. [PAWN_pronoun] earned [PAWN_possessive] nickname by helping a village through a trying time.</description>
    <skillGains>
      <Social>8</Social>
      <Cooking>2</Cooking>
      <Artistic>4</Artistic>
    </skillGains>
    <workDisables>Mining</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ExoticChef96</defName>
    <identifier>ExoticChef96</identifier>
    <slot>Adulthood</slot>
    <title>exotic chef</title>
    <titleShort>chef</titleShort>
    <description>[PAWN_nameDef] has always gone overboard when it comes to food. [PAWN_pronoun] is always seeking new and exotic ingredients.\n\n[PAWN_possessive] quest for ingredients has brought [PAWN_objective] into some close calls with local flora and fauna.</description>
    <skillGains>
      <Cooking>6</Cooking>
      <Artistic>2</Artistic>
      <Crafting>1</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <possessions>
      <Meat_Thrumbo>10</Meat_Thrumbo>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MadScientist2</defName>
    <identifier>MadScientist2</identifier>
    <slot>Adulthood</slot>
    <title>mad scientist</title>
    <titleShort>scientist</titleShort>
    <description>[PAWN_nameDef]'s lust for knowledge was only matched by [PAWN_possessive] disdain for anyone who would stand in the way of [PAWN_possessive] research.\n\nTraveling from world to world, [PAWN_pronoun] sought answers to questions few dared to ask.</description>
    <skillGains>
      <Construction>3</Construction>
      <Intellectual>7</Intellectual>
      <Social>-2</Social>
    </skillGains>
    <workDisables>Caring, Animals, Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Warrior73</defName>
    <identifier>Warrior73</identifier>
    <slot>Adulthood</slot>
    <title>warrior</title>
    <titleShort>warrior</titleShort>
    <description>[PAWN_nameDef] was a war hero during [PAWN_possessive] planet's third world war. [PAWN_pronoun] helped prevent the launch of nuclear weapons during a particularly tense standoff.</description>
    <skillGains>
      <Shooting>5</Shooting>
      <Melee>4</Melee>
      <Cooking>2</Cooking>
      <Medicine>3</Medicine>
    </skillGains>
    <workDisables>Intellectual</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <possessions>
      <Pila>1</Pila>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpaceExplorer53</defName>
    <identifier>SpaceExplorer53</identifier>
    <slot>Adulthood</slot>
    <title>space explorer</title>
    <titleShort>explorer</titleShort>
    <description>[PAWN_nameDef] spent years trapped, alone on an unknown planet. [PAWN_pronoun] quickly adapted to the local environment, and learned to communicate with [PAWN_possessive] local enemies.\n\nThis long period alone left [PAWN_objective] without close friends. [PAWN_possessive] heart is sensitive, but [PAWN_pronoun] learned to hide [PAWN_possessive] emotions very well.</description>
    <skillGains>
      <Construction>3</Construction>
      <Plants>3</Plants>
      <Mining>3</Mining>
      <Shooting>4</Shooting>
      <Social>4</Social>
    </skillGains>
    <workDisables>Caring</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Smuggler23</defName>
    <identifier>Smuggler23</identifier>
    <slot>Adulthood</slot>
    <title>smuggler</title>
    <titleShort>smuggler</titleShort>
    <description>As a contract pilot, [PAWN_nameDef] had a knack for stealing goods and trafficking contraband without getting caught.\n\n[PAWN_pronoun] eventually saved up enough to buy [PAWN_possessive] own ship, and used it to travel from world to world dealing [PAWN_possessive] goods in person.</description>
    <skillGains>
      <Intellectual>-2</Intellectual>
      <Shooting>2</Shooting>
      <Melee>2</Melee>
      <Social>5</Social>
      <Medicine>2</Medicine>
    </skillGains>
    <workDisables>Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <possessions>
      <Flake>20</Flake>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>DestroyerGeneral26</defName>
    <identifier>DestroyerGeneral26</identifier>
    <slot>Adulthood</slot>
    <title>destroyer-general</title>
    <titleShort>general</titleShort>
    <description>[PAWN_nameDef] held the rank of Destroyer-General in a powerful midworld military.\n\n[PAWN_pronoun] was known for [PAWN_possessive] mastery of weapons, and was also a good ship pilot.</description>
    <skillGains>
      <Shooting>6</Shooting>
      <Melee>3</Melee>
      <Social>6</Social>
      <Artistic>-3</Artistic>
    </skillGains>
    <workDisables>ManualDumb</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SerialMurderer47</defName>
    <identifier>SerialMurderer47</identifier>
    <slot>Adulthood</slot>
    <title>serial murderer</title>
    <titleShort>murderer</titleShort>
    <description>[PAWN_nameDef] started to enjoy killing people, so [PAWN_pronoun] did more often and perfected [PAWN_possessive] methods. [PAWN_pronoun] lurked in the darkest streets, stalking prey for hours.\n\n[PAWN_pronoun] enjoyed making artworks of [PAWN_possessive] victims, and was known for leaving [PAWN_possessive] signature on the bodies of those [PAWN_pronoun] killed.</description>
    <skillGains>
      <Shooting>4</Shooting>
      <Melee>4</Melee>
      <Artistic>4</Artistic>
    </skillGains>
    <workDisables>Animals</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <forcedTraits>
      <Psychopath>0</Psychopath>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MadScientist31</defName>
    <identifier>MadScientist31</identifier>
    <slot>Adulthood</slot>
    <title>mad scientist</title>
    <titleShort>scientist</titleShort>
    <description>After finishing [PAWN_possessive] education, [PAWN_nameDef] was hired by the planet's leading genetic researcher. Later, [PAWN_pronoun] was caught running illegal, inhumane experiments.\n\nDespite [PAWN_possessive] family's influence, [PAWN_pronoun] was convicted and sentenced to hard labor on a penal colony.</description>
    <skillGains>
      <Intellectual>8</Intellectual>
      <Mining>3</Mining>
      <Medicine>3</Medicine>
      <Artistic>-3</Artistic>
      <Animals>-2</Animals>
    </skillGains>
    <workDisables>PlantWork</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SoftwareDeveloper84</defName>
    <identifier>SoftwareDeveloper84</identifier>
    <slot>Adulthood</slot>
    <title>software developer</title>
    <titleShort>developer</titleShort>
    <description>After [PAWN_pronoun] gained the ability to use automation to remove tedium, [PAWN_nameDef] could no longer tolerate traditional education and employment.</description>
    <skillGains>
      <Plants>-3</Plants>
      <Intellectual>7</Intellectual>
      <Social>-3</Social>
      <Artistic>3</Artistic>
      <Crafting>5</Crafting>
    </skillGains>
    <workDisables>ManualDumb, Cleaning, Hauling</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>HumanTrafficker35</defName>
    <identifier>HumanTrafficker35</identifier>
    <slot>Adulthood</slot>
    <title>human trafficker</title>
    <titleShort>trafficker</titleShort>
    <description>[PAWN_nameDef] stole children from their homes to sell them to interested buyers.\n\nOver time, [PAWN_pronoun] lost what little moral sense [PAWN_pronoun] had left.</description>
    <skillGains>
      <Melee>4</Melee>
      <Social>3</Social>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>Caring, Artistic, PlantWork</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <forcedTraits>
      <Psychopath>0</Psychopath>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MentalPatient69</defName>
    <identifier>MentalPatient69</identifier>
    <slot>Adulthood</slot>
    <title>mental patient</title>
    <titleShort>patient</titleShort>
    <description>[PAWN_nameDef] was placed in a psychiatric hospital. There, [PAWN_pronoun] spent [PAWN_possessive] time engaged in the calming activities of the asylum.\n\n[PAWN_pronoun] was eventually released, but [PAWN_pronoun] retained [PAWN_possessive] emotional distance, [PAWN_possessive] curiosity about human anatomy, and [PAWN_possessive] namesake manic giggling.</description>
    <skillGains>
      <Plants>3</Plants>
      <Intellectual>2</Intellectual>
      <Shooting>-2</Shooting>
      <Artistic>3</Artistic>
      <Crafting>3</Crafting>
    </skillGains>
    <workDisables>Cooking, Firefighting, Mining</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpaceMarine94</defName>
    <identifier>SpaceMarine94</identifier>
    <slot>Adulthood</slot>
    <title>space marine</title>
    <titleShort>marine</titleShort>
    <description>[PAWN_nameDef] was a warrior in an Imperial navy.\n\n[PAWN_possessive] job was to punch into enemy starships, gun down the crew, and capture the ship intact. And [PAWN_pronoun] was good at it.</description>
    <skillGains>
      <Shooting>7</Shooting>
      <Melee>5</Melee>
    </skillGains>
    <workDisables>Cooking</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
      <li>ImperialCommon</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>EnvoyOfTheStars19</defName>
    <identifier>EnvoyOfTheStars19</identifier>
    <slot>Adulthood</slot>
    <title>envoy of the stars</title>
    <titleShort>envoy</titleShort>
    <description>[PAWN_nameDef] entered the diplomatic corps. Honor and tradition were the values [PAWN_pronoun] took with [PAWN_objective] on every mission.\n\n[PAWN_possessive] prestigious position distanced [PAWN_objective] from backbreaking manual labor, allowing [PAWN_objective] to focus on [PAWN_possessive] wordsmithing abilities. [PAWN_pronoun] left the blacksmithing to others.</description>
    <skillGains>
      <Shooting>1</Shooting>
      <Social>8</Social>
      <Medicine>2</Medicine>
      <Artistic>2</Artistic>
    </skillGains>
    <workDisables>ManualDumb, ManualSkilled</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>ImperialCommon</li>
      <li>ImperialRoyal</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Blacksmith7</defName>
    <identifier>Blacksmith7</identifier>
    <slot>Adulthood</slot>
    <title>blacksmith</title>
    <titleShort>blacksmith</titleShort>
    <description>As a blacksmith on a medieval world, [PAWN_nameDef] gained a reputation for the high quality of [PAWN_possessive] work. [PAWN_pronoun] wasn't bad at using the swords [PAWN_pronoun] forged either.</description>
    <skillGains>
      <Mining>2</Mining>
      <Melee>2</Melee>
      <Crafting>8</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>CloneFarmer58</defName>
    <identifier>CloneFarmer58</identifier>
    <slot>Adulthood</slot>
    <title>clone farmer</title>
    <titleShort>cloner</titleShort>
    <description>Harvesting the products of clone farms is mostly done by the clones themselves - particularly to those whose sims tended towards the social. This clone was one such, who escaped from the farms and headed out to the stars.</description>
    <skillGains>
      <Social>6</Social>
      <Medicine>4</Medicine>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>Cooking, Firefighting, Mining</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>GangBoss49</defName>
    <identifier>GangBoss49</identifier>
    <slot>Adulthood</slot>
    <title>gang boss</title>
    <titleShort>boss</titleShort>
    <description>To survive, [PAWN_nameDef]'s gang often had to rob trade ships for food and supplies. Sometimes [PAWN_pronoun] had to kill the guards. Sometimes [PAWN_pronoun] had to kill the police who arrived to stop him.\n\nAfter years of this, [PAWN_pronoun] became one of the most-wanted criminals in the local empire.</description>
    <skillGains>
      <Construction>4</Construction>
      <Shooting>8</Shooting>
      <Melee>6</Melee>
      <Social>-3</Social>
      <Medicine>3</Medicine>
    </skillGains>
    <workDisables>Animals</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>LostMarine81</defName>
    <identifier>LostMarine81</identifier>
    <slot>Adulthood</slot>
    <title>lost marine</title>
    <titleShort>traitor</titleShort>
    <description>Shortly after being promoted to a respected position in [PAWN_possessive] planet's military, [PAWN_nameDef] disappeared without a trace.\n\nLater, [PAWN_pronoun] reappeared at the head of [PAWN_possessive] own military force. No one knows why [PAWN_pronoun] became who [PAWN_pronoun] was.</description>
    <skillGains>
      <Shooting>7</Shooting>
      <Melee>5</Melee>
      <Medicine>2</Medicine>
      <Crafting>6</Crafting>
    </skillGains>
    <workDisables>ManualDumb, Mining</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Healer35</defName>
    <identifier>Healer35</identifier>
    <slot>Adulthood</slot>
    <title>healer</title>
    <titleShort>healer</titleShort>
    <description>[PAWN_nameDef] took the time to admire the beauty of the world.\n\n[PAWN_possessive] passive-aggressiveness surfaced once in a while, but [PAWN_pronoun] tried to adapt to [PAWN_possessive] changing life.</description>
    <skillGains>
      <Plants>5</Plants>
      <Medicine>7</Medicine>
    </skillGains>
    <workDisables>ManualDumb, Mining</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Outlander</li>
      <li>Pirate</li>
      <li>Tribal</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ToasterRepairman22</defName>
    <identifier>ToasterRepairman22</identifier>
    <slot>Adulthood</slot>
    <title>toaster repairman</title>
    <titleShort>repairman</titleShort>
    <description>Good with [PAWN_possessive] hands and passionate about technology, [PAWN_nameDef] aspired to invent gadgets and machines that would change [PAWN_possessive] home world forever.\n\nA few failed and occasionally disastrous creations later, [PAWN_pronoun] gave up on [PAWN_possessive] dream and took a job more befitting [PAWN_possessive] skill set.</description>
    <skillGains>
      <Construction>7</Construction>
      <Mining>-3</Mining>
      <Crafting>6</Crafting>
    </skillGains>
    <workDisables>Intellectual, Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ArchotechSpy75</defName>
    <identifier>ArchotechSpy75</identifier>
    <slot>Adulthood</slot>
    <title>archotech researcher</title>
    <titleShort>researcher</titleShort>
    <description>[PAWN_nameDef] researched archotech intelligences.\n\nStudying a mysterious archotechnological object from orbit, [PAWN_pronoun] became convinced that it was communicating directly with [PAWN_objective]. Eventually [PAWN_possessive] colleagues caught [PAWN_objective] transmitting secret information directed towards the dark sphere, and he was forced to flee.</description>
    <skillGains>
      <Construction>2</Construction>
      <Intellectual>5</Intellectual>
      <Crafting>2</Crafting>
      <Cooking>-2</Cooking>
    </skillGains>
    <workDisables>Mining</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>BehaviourResearch74</defName>
    <identifier>BehaviourResearch74</identifier>
    <slot>Adulthood</slot>
    <title>behaviour research</title>
    <titleShort>scientist</titleShort>
    <description>Captured as a feral child, [PAWN_nameDef] was forced into the ways of modern society. [PAWN_pronoun] quickly adapted and became a behavioural researcher, eventually owning a private facility.\n\n[PAWN_pronoun] specialized in canines, but learned a great deal about all animals. [PAWN_pronoun] never had interest in plants, however.</description>
    <skillGains>
      <Plants>-3</Plants>
      <Intellectual>8</Intellectual>
      <Melee>1</Melee>
      <Social>2</Social>
      <Cooking>-2</Cooking>
      <Medicine>6</Medicine>
    </skillGains>
    <workDisables>Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>PoliticalActivist20</defName>
    <identifier>PoliticalActivist20</identifier>
    <slot>Adulthood</slot>
    <title>political activist</title>
    <titleShort>activist</titleShort>
    <description>[PAWN_nameDef] was involved with a radical political faction which worked against [PAWN_possessive] homeworld's government.\n\nThe guerilla tactics training that [PAWN_nameDef] received from [PAWN_possessive] associates gave [PAWN_objective] the skills to fight, but also drove [PAWN_objective] to compromise [PAWN_possessive] own beliefs.</description>
    <skillGains>
      <Intellectual>2</Intellectual>
      <Shooting>5</Shooting>
      <Melee>3</Melee>
      <Medicine>2</Medicine>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Flaneur30</defName>
    <identifier>Flaneur30</identifier>
    <slot>Adulthood</slot>
    <title>flaneur</title>
    <titleShort>flaneur</titleShort>
    <description>[PAWN_nameDef] strolled and sipped [PAWN_possessive] way through the streets and cafes of [PAWN_possessive] world, pursuing freedom at every turn, never becoming attached.\n\nThrough all this time, [PAWN_pronoun] remained distrustful of others. And never took an order from anyone.</description>
    <skillGains>
      <Intellectual>3</Intellectual>
      <Social>6</Social>
      <Cooking>3</Cooking>
      <Artistic>3</Artistic>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ChiefEngineer62</defName>
    <identifier>ChiefEngineer62</identifier>
    <slot>Adulthood</slot>
    <title>chief engineer</title>
    <titleShort>engineer</titleShort>
    <description>As chief engineer on board a large spaceship, [PAWN_nameDef] was an expert in all things fiddly and complex.\n\n[PAWN_pronoun] relied on other members of the crew for some of the basic necessities of life. [PAWN_pronoun] and [PAWN_possessive] ship eventually disappeared into a longsleep voyage of exploration.</description>
    <skillGains>
      <Construction>3</Construction>
      <Intellectual>3</Intellectual>
      <Mining>2</Mining>
      <Social>-2</Social>
      <Crafting>3</Crafting>
    </skillGains>
    <workDisables>Animals</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ReptileResearcher37</defName>
    <identifier>ReptileResearcher37</identifier>
    <slot>Adulthood</slot>
    <title>reptile researcher</title>
    <titleShort>researcher</titleShort>
    <description>After leaving [PAWN_possessive] parents' vineyard, [PAWN_nameDef] traveled across much of known space, and learned many skills along the way.\n\n[PAWN_pronoun] spent most of [PAWN_possessive] time researching and interacting with reptile species. [PAWN_pronoun] was renowned for making several breakthroughs, and for discovering the "Thorny Devil".</description>
    <skillGains>
      <Intellectual>7</Intellectual>
      <Cooking>2</Cooking>
      <Medicine>3</Medicine>
      <Artistic>3</Artistic>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>Violent</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <possessions>
      <EggCobraFertilized>1</EggCobraFertilized>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>BlacksmithShooter21</defName>
    <identifier>BlacksmithShooter21</identifier>
    <slot>Adulthood</slot>
    <title>blacksmith shooter</title>
    <titleShort>gunsmith</titleShort>
    <description>As a young blacksmith, [PAWN_nameDef]'s family shop was raided by the police. [PAWN_pronoun] shot two officers before they knocked [PAWN_objective] out and arrested him.\n\nSentenced to life in prison, [PAWN_nameDef] learned about traveling and raiding from other criminals.</description>
    <skillGains>
      <Construction>3</Construction>
      <Shooting>5</Shooting>
      <Crafting>3</Crafting>
    </skillGains>
    <workDisables>Intellectual</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>AddictionCounsel60</defName>
    <identifier>AddictionCounsel60</identifier>
    <slot>Adulthood</slot>
    <title>counselor</title>
    <titleShort>counselor</titleShort>
    <description>After recovering from a joywire addiction, [PAWN_nameDef] adopted a non-violent way of life and vowed to help others.\n\nTravelling between communities, [PAWN_pronoun] used medicine, arts, and crafts to aid others who struggled with their own addictions.</description>
    <skillGains>
      <Social>8</Social>
      <Medicine>3</Medicine>
      <Artistic>3</Artistic>
      <Crafting>3</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>BloodgameSurvivor6</defName>
    <identifier>BloodgameSurvivor6</identifier>
    <slot>Adulthood</slot>
    <title>bloodgame survivor</title>
    <titleShort>bloodgamer</titleShort>
    <description>[PAWN_nameDef] was sold to the Corestars Entertainment Company and used as meat in one of the company's system-wide broadcast shows.
 
[PAWN_possessive] manipulative nature made [PAWN_objective] quite a nightmare, even to the audience. [PAWN_pronoun] was kicked out and exiled to a planet full of deadly animals, criminals and armed camera-drones.</description>
    <skillGains>
      <Construction>2</Construction>
      <Melee>7</Melee>
      <Social>6</Social>
    </skillGains>
    <workDisables>Crafting</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>VoidspaceRaider94</defName>
    <identifier>VoidspaceRaider94</identifier>
    <slot>Adulthood</slot>
    <title>voidspace raider</title>
    <titleShort>Pirate</titleShort>
    <description>After years of seeking the assassin that broke up [PAWN_possessive] family, [PAWN_nameDef] was offered a place with a group of space-faring raiders. [PAWN_pronoun] accepted, hoping to find the killer [PAWN_pronoun] sought.\n\nDue to [PAWN_possessive] seemingly-unending ability to narrowly escape danger, [PAWN_pronoun] earned the nickname 'Blackjack' in recognition of [PAWN_possessive] luck.</description>
    <skillGains>
      <Mining>2</Mining>
      <Shooting>4</Shooting>
      <Melee>2</Melee>
      <Medicine>2</Medicine>
      <Crafting>3</Crafting>
    </skillGains>
    <workDisables>Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Vagabond73</defName>
    <identifier>Vagabond73</identifier>
    <slot>Adulthood</slot>
    <title>vagabond</title>
    <titleShort>vagabond</titleShort>
    <description>[PAWN_nameDef] awoke on a dangerous planet with no resources and few survival skills. [PAWN_pronoun] bounced between camps, doing whatever work needed to be done, just to see the sun rise once more.</description>
    <skillGains>
      <Construction>4</Construction>
      <Shooting>3</Shooting>
      <Cooking>-2</Cooking>
      <Crafting>4</Crafting>
      <Animals>4</Animals>
    </skillGains>
    <workDisables>Intellectual, Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MachineFixer31</defName>
    <identifier>MachineFixer31</identifier>
    <slot>Adulthood</slot>
    <title>machine fixer</title>
    <titleShort>fixer</titleShort>
    <description>[PAWN_nameDef] was adept at dealing with mechanical problems, as long as the problem didn't involve the kitchen.\n\nAfter a life spent around dangerous machinery and flammable chemicals, [PAWN_pronoun] learned how to patch [PAWN_objective]self up pretty well. Despite the pain, [PAWN_pronoun] never learned to dislike fire.</description>
    <skillGains>
      <Construction>5</Construction>
      <Medicine>5</Medicine>
      <Crafting>3</Crafting>
    </skillGains>
    <workDisables>Cooking, Firefighting</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpaceMerchant97</defName>
    <identifier>SpaceMerchant97</identifier>
    <slot>Adulthood</slot>
    <title>space merchant</title>
    <titleShort>merchant</titleShort>
    <description>As part of a freelance interplanetary lifestyle, [PAWN_nameDef] sought to dominate [PAWN_possessive] romantic interests as much as [PAWN_possessive] business adversaries. When [PAWN_pronoun] didn't have [PAWN_possessive] own ship to fly, [PAWN_pronoun] worked charter ships for hire.</description>
    <skillGains>
      <Construction>4</Construction>
      <Shooting>2</Shooting>
      <Social>5</Social>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>Caring</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>BeastSlayer67</defName>
    <identifier>BeastSlayer67</identifier>
    <slot>Adulthood</slot>
    <title>beast slayer</title>
    <titleShort>slayer</titleShort>
    <description>[PAWN_nameDef] traveled between planets, following news of animal attacks. [PAWN_pronoun] would camp on the planet for weeks, learning about [PAWN_possessive] prey before striking. [PAWN_pronoun] most enjoyed hunting thrumbos.\n\nPreferring to work alone, [PAWN_nameDef] enjoyed the thrill of the hunt, and the meat from the kill.</description>
    <skillGains>
      <Plants>2</Plants>
      <Shooting>4</Shooting>
      <Melee>4</Melee>
      <Cooking>3</Cooking>
    </skillGains>
    <workDisables>Caring</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>DrugLieutenant98</defName>
    <identifier>DrugLieutenant98</identifier>
    <slot>Adulthood</slot>
    <title>drug lieutenant</title>
    <titleShort>drugman</titleShort>
    <description>[PAWN_nameDef] worked the drug trade under kingpin Grady Loughman. [PAWN_pronoun] had [PAWN_possessive] men sell drugs when it was profitable, and fight the other cartels when the government came around.\n\n[PAWN_possessive] social skills kept [PAWN_objective] on top of the political game, and [PAWN_pronoun] always had servants to cook and clean.</description>
    <skillGains>
      <Plants>4</Plants>
      <Social>7</Social>
      <Medicine>7</Medicine>
    </skillGains>
    <workDisables>Artistic, Cooking, Cleaning</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>UrbworldEnforcer11</defName>
    <identifier>UrbworldEnforcer11</identifier>
    <slot>Adulthood</slot>
    <title>urbworld enforcer</title>
    <titleShort>enforcer</titleShort>
    <description>[PAWN_nameDef] was an enforcer of law on an ancient urbworld.\n\n[PAWN_pronoun] dealt with the worst of humanity, from protecting greedy urbworld nobility in their spire palaces to rooting out cannibal cults in the deepest reaches of the underground hive.</description>
    <skillGains>
      <Shooting>7</Shooting>
      <Melee>4</Melee>
      <Social>3</Social>
      <Medicine>2</Medicine>
    </skillGains>
    <workDisables>Intellectual</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <forcedTraits>
      <Tough>0</Tough>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ToolMechanic77</defName>
    <identifier>ToolMechanic77</identifier>
    <slot>Adulthood</slot>
    <title>tool mechanic</title>
    <titleShort>mechanic</titleShort>
    <description>[PAWN_nameDef] was a famous tool mechanic on a dusty rimworld.\n\nAfter [PAWN_possessive] firm closed due to scandal, [PAWN_pronoun] joined a tribe to survive, crafting fine knives and hammers, traveling, and dealing [PAWN_possessive] wares between settlements.</description>
    <skillGains>
      <Construction>5</Construction>
      <Artistic>2</Artistic>
      <Crafting>8</Crafting>
    </skillGains>
    <workDisables>Animals, PlantWork</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Tribal</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>CraftShaper37</defName>
    <identifier>CraftShaper37</identifier>
    <slot>Adulthood</slot>
    <title>craft shaper</title>
    <titleShort>shaper</titleShort>
    <description>[PAWN_nameDef] locked [PAWN_objective]self in [PAWN_possessive] studio for years, ordering delivery food to save time on cooking.\n\nEventually, [PAWN_possessive] creations brought [PAWN_objective] the highest honor on Semantic World: Permission to materialize any design in minutes using the most advanced technologies.</description>
    <skillGains>
      <Construction>4</Construction>
      <Social>-3</Social>
      <Artistic>5</Artistic>
      <Crafting>8</Crafting>
    </skillGains>
    <workDisables>Cooking, Cleaning</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
    <possessions>
      <TextBook>1</TextBook>
    </possessions>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MercenaryLeader28</defName>
    <identifier>MercenaryLeader28</identifier>
    <slot>Adulthood</slot>
    <title>mercenary leader</title>
    <titleShort>mercenary</titleShort>
    <description>Alone, [PAWN_nameDef] set out to rebuild [PAWN_possessive] father's life's work. [PAWN_pronoun] challenged the leader of a small band of mercenaries and took control, then led the band as they hijacked trade ships and stole valuable cargo.\n\n[PAWN_possessive] men mutinied. [PAWN_pronoun] was abandoned, arrested, and sentenced to death.</description>
    <skillGains>
      <Shooting>4</Shooting>
      <Melee>4</Melee>
      <Social>4</Social>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpecialForces13</defName>
    <identifier>SpecialForces13</identifier>
    <slot>Adulthood</slot>
    <title>special forces</title>
    <titleShort>specialist</titleShort>
    <description>Give me a reason to kill - a good reason.\n\n[PAWN_nameDef] was a soldier and a skilled one - so skilled that [PAWN_pronoun] entered the special forces to battle militants and xenohuman raiders. [PAWN_pronoun] never imagined [PAWN_objective]self as lone hero, but [PAWN_pronoun] played [PAWN_possessive] part in the group well.</description>
    <skillGains>
      <Shooting>7</Shooting>
      <Melee>6</Melee>
      <Social>2</Social>
    </skillGains>
    <workDisables>Intellectual, Animals, Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>DromedaryKnight37</defName>
    <identifier>DromedaryKnight37</identifier>
    <slot>Adulthood</slot>
    <title>dromedary knight</title>
    <titleShort>knight</titleShort>
    <description>Having caught the eye of [PAWN_possessive] people's Sand King, [PAWN_nameDef] was trained in the Sandy Boomrat fighting technique.\n\nAlways with a camel to carry [PAWN_possessive] things, [PAWN_pronoun] served [PAWN_possessive] sand people faithfully.</description>
    <skillGains>
      <Shooting>-3</Shooting>
      <Melee>4</Melee>
      <Social>4</Social>
      <Animals>4</Animals>
    </skillGains>
    <workDisables>Hauling</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Outlander</li>
      <li>Tribal</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>BattleMechanic27</defName>
    <identifier>BattleMechanic27</identifier>
    <slot>Adulthood</slot>
    <title>battle mechanic</title>
    <titleShort>mechanic</titleShort>
    <description>[PAWN_nameDef] was a tech-obsessed mercenary in an early fusion-era system.\n\n[PAWN_pronoun] was unusually obsessed with gathering the right gear, and often customized [PAWN_possessive] own equipment with original modifications.</description>
    <skillGains>
      <Shooting>5</Shooting>
      <Melee>3</Melee>
      <Crafting>4</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>DischargedSoldier53</defName>
    <identifier>DischargedSoldier53</identifier>
    <slot>Adulthood</slot>
    <title>discharged soldier</title>
    <titleShort>soldier</titleShort>
    <description>[PAWN_nameDef] prostituted [PAWN_objective]self to fund [PAWN_possessive] drug habits. As an escape, [PAWN_pronoun] joined the military and learned to fight.\n\nToo smart for the army, [PAWN_pronoun] questioned and often disagreed with [PAWN_possessive] superiors' decisions. This eventually led to [PAWN_possessive] dishonorable discharge and left [PAWN_objective] with a chip on [PAWN_possessive] shoulder.</description>
    <skillGains>
      <Shooting>6</Shooting>
      <Melee>4</Melee>
      <Cooking>2</Cooking>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <forcedTraits>
      <DrugDesire>2</DrugDesire>
      <TooSmart>0</TooSmart>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Chemist73</defName>
    <identifier>Chemist73</identifier>
    <slot>Adulthood</slot>
    <title>chemist</title>
    <titleShort>chemist</titleShort>
    <description>[PAWN_nameDef] was a research chemist. [PAWN_pronoun] worked with chemicals, from pharmaceuticals to explosives, to develop new substances and treatments.</description>
    <skillGains>
      <Intellectual>6</Intellectual>
      <Social>2</Social>
      <Medicine>4</Medicine>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SuperSoldier95</defName>
    <identifier>SuperSoldier95</identifier>
    <slot>Adulthood</slot>
    <title>super soldier</title>
    <titleShort>soldier</titleShort>
    <description>Product of a super-soldier program run by a now-deposed government, [PAWN_nameDef] was lost. [PAWN_pronoun] survived the final suicide attack on the enemy mothership. What now.\n\nFor the first time in [PAWN_possessive] life, [PAWN_pronoun] had no orders, no objectives. [PAWN_pronoun] rose from wreckage and strove towards uncertainty, towards freedom.</description>
    <skillGains>
      <Shooting>8</Shooting>
      <Melee>7</Melee>
      <Crafting>-3</Crafting>
    </skillGains>
    <workDisables>Social, Animals, Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>CorporateBuilder58</defName>
    <identifier>CorporateBuilder58</identifier>
    <slot>Adulthood</slot>
    <title>corporate builder</title>
    <titleShort>builder</titleShort>
    <description>Even as [PAWN_pronoun] stewed in [PAWN_possessive] own wasted potential, [PAWN_nameDef] found [PAWN_pronoun] had a knack for moving objects quickly. It was just what the planetary mega-corps wanted in their construction divisions.\n\nUnfortunately, the monotonous work erased [PAWN_possessive] remaining creative impulses.</description>
    <skillGains>
      <Construction>7</Construction>
      <Mining>4</Mining>
    </skillGains>
    <workDisables>Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MechanicsEngineer64</defName>
    <identifier>MechanicsEngineer64</identifier>
    <slot>Adulthood</slot>
    <title>mechanics engineer</title>
    <titleShort>engineer</titleShort>
    <description>[PAWN_nameDef] loved solving problems and, with [PAWN_possessive] steady hands and a reputation for quality work, earned a job as a mechanical engineer. [PAWN_pronoun] built and redesigned devices of all shapes and sizes.\n\nEventually, [PAWN_pronoun] was able to pick and choose [PAWN_possessive] corporate clients. [PAWN_pronoun] made several wealthy friends, as well as a few powerful enemies.</description>
    <skillGains>
      <Construction>5</Construction>
      <Plants>-2</Plants>
      <Intellectual>2</Intellectual>
      <Crafting>6</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>PlagueDoctor31</defName>
    <identifier>PlagueDoctor31</identifier>
    <slot>Adulthood</slot>
    <title>plague doctor</title>
    <titleShort>doctor</titleShort>
    <description>[PAWN_nameDef] provided medical care on multiple plague-wracked worlds, always working under the Hippocratic oath. [PAWN_pronoun] improved conditions for those under quarantine while administering complex medical treatments and developing medicines.\n\n[PAWN_pronoun] never had time to enjoy a normal life outside of work.</description>
    <skillGains>
      <Intellectual>4</Intellectual>
      <Social>-2</Social>
      <Medicine>8</Medicine>
    </skillGains>
    <workDisables>Violent, Animals, Artistic, Cooking</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>NetworkEngineer34</defName>
    <identifier>NetworkEngineer34</identifier>
    <slot>Adulthood</slot>
    <title>network engineer</title>
    <titleShort>engineer</titleShort>
    <description>[PAWN_nameDef] spent all [PAWN_possessive] time working on computer networking systems.\n\n[PAWN_pronoun] lived without a care in the world - other than uptime and dropped packets.</description>
    <skillGains>
      <Construction>4</Construction>
      <Intellectual>4</Intellectual>
      <Shooting>4</Shooting>
      <Crafting>4</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ImperialGeneral27</defName>
    <identifier>ImperialGeneral27</identifier>
    <slot>Adulthood</slot>
    <title>imperial general</title>
    <titleShort>general</titleShort>
    <description>[PAWN_nameDef]'s thirst for power defined [PAWN_objective]. [PAWN_pronoun] quickly rose through the military ranks and soon attained the rank of general.\n\n[PAWN_pronoun] was given the most challenging task - conquering distant and unexplored rimworlds. Victory here could even position [PAWN_objective] as a successor to the emperor.</description>
    <skillGains>
      <Construction>-2</Construction>
      <Intellectual>4</Intellectual>
      <Mining>-3</Mining>
      <Shooting>4</Shooting>
      <Melee>4</Melee>
      <Social>5</Social>
      <Medicine>2</Medicine>
    </skillGains>
    <workDisables>ManualDumb</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Fat</bodyTypeGlobal>
    <bodyTypeFemale>Fat</bodyTypeFemale>
    <bodyTypeMale>Fat</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Caravaneer53</defName>
    <identifier>Caravaneer53</identifier>
    <slot>Adulthood</slot>
    <title>caravaneer</title>
    <titleShort>caravaneer</titleShort>
    <description>[PAWN_nameDef] was the leader of a caravan. [PAWN_pronoun] was responsible for the safety of [PAWN_possessive] caravan and was the head negotiator in countless trade deals.\n\n[PAWN_pronoun] and [PAWN_possessive] people prospered, but their wealth made them a target for brigands. They often had to take up arms to fend off bandits and highwaymen.</description>
    <skillGains>
      <Shooting>4</Shooting>
      <Social>7</Social>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MilitaryGunsmith27</defName>
    <identifier>MilitaryGunsmith27</identifier>
    <slot>Adulthood</slot>
    <title>military gunsmith</title>
    <titleShort>gunsmith</titleShort>
    <description>When [PAWN_possessive] world was thrown into conflict, [PAWN_nameDef] was conscripted along with many of [PAWN_possessive] peers into the war machine that tore across the planet.\n\nWith [PAWN_possessive] particular expertise, [PAWN_pronoun] was lucky enough to find a position serving as small arms mechanic rather than on the front line.</description>
    <skillGains>
      <Construction>2</Construction>
      <Shooting>5</Shooting>
      <Melee>4</Melee>
      <Cooking>1</Cooking>
      <Medicine>2</Medicine>
      <Crafting>5</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Aromatherapist80</defName>
    <identifier>Aromatherapist80</identifier>
    <slot>Adulthood</slot>
    <title>aromatherapist</title>
    <titleShort>therapist</titleShort>
    <description>[PAWN_nameDef] practiced aromatherapy, using essences extracted from home-grown plants.</description>
    <skillGains>
      <Plants>6</Plants>
      <Shooting>-3</Shooting>
      <Melee>-3</Melee>
      <Cooking>4</Cooking>
      <Medicine>4</Medicine>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>RebelFighter39</defName>
    <identifier>RebelFighter39</identifier>
    <slot>Adulthood</slot>
    <title>rebel fighter</title>
    <titleShort>rebel</titleShort>
    <description>When civil war broke out [PAWN_nameDef] remained neutral, until [PAWN_possessive] home was firebombed by a loyalist militia.\n\nLater, [PAWN_possessive] unit gave [PAWN_objective] the nickname "Rare" after [PAWN_pronoun] burnt down the luxury villa of a loyalist leader, with them still inside it.</description>
    <skillGains>
      <Shooting>4</Shooting>
      <Melee>2</Melee>
    </skillGains>
    <workDisables>Caring, Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Roboticist41</defName>
    <identifier>Roboticist41</identifier>
    <slot>Adulthood</slot>
    <title>roboticist</title>
    <titleShort>roboticist</titleShort>
    <description>[PAWN_nameDef] felt more comfortable with robots and artificial intelligences than with organic people, so [PAWN_pronoun] worked as a roboticist. [PAWN_pronoun] took a particular interest in mechanites and ancient technology.\n\n[PAWN_nameDef] relied on other people for [PAWN_possessive] basic needs.</description>
    <skillGains>
      <Construction>3</Construction>
      <Intellectual>4</Intellectual>
      <Crafting>6</Crafting>
    </skillGains>
    <workDisables>Cooking, PlantWork</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>PrivateDetective66</defName>
    <identifier>PrivateDetective66</identifier>
    <slot>Adulthood</slot>
    <title>private detective</title>
    <titleShort>detective</titleShort>
    <description>As a private eye, sharp wits and a silver tongue were [PAWN_nameDef]'s greatest tools. [PAWN_pronoun] often spent [PAWN_possessive] nights working cases that ranged from uncovering affairs and learning trade secrets to solving murder cases and infiltrating criminal organizations. There wasn't a case [PAWN_pronoun] wouldn't accept for the right price.</description>
    <skillGains>
      <Intellectual>4</Intellectual>
      <Social>7</Social>
    </skillGains>
    <workDisables>PlantWork, Mining</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpaceBartender0</defName>
    <identifier>SpaceBartender0</identifier>
    <slot>Adulthood</slot>
    <title>space bartender</title>
    <titleShort>bartender</titleShort>
    <description>[PAWN_nameDef] became a bartender on a space station. [PAWN_pronoun] interacted with and befriended many strangers.\n\nAs time went on, [PAWN_nameDef] used [PAWN_possessive] knowledge of herbs to craft [PAWN_possessive] own strange concoctions for [PAWN_possessive] customers.</description>
    <skillGains>
      <Plants>4</Plants>
      <Social>4</Social>
      <Cooking>5</Cooking>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <forcedTraits>
      <Kind>0</Kind>
    </forcedTraits>
    <possessions>
      <PsychiteTea>3</PsychiteTea>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>CosmeticSurgeon36</defName>
    <identifier>CosmeticSurgeon36</identifier>
    <slot>Adulthood</slot>
    <title>cosmetic surgeon</title>
    <titleShort>surgeon</titleShort>
    <description>[PAWN_nameDef] had a terrible bedside manner due to [PAWN_possessive] abrasiveness. [PAWN_pronoun] often bullied [PAWN_possessive] patients into getting cosmetic surgery. [PAWN_pronoun] made a lot of money this way.\n\nWhile very studious and careful in [PAWN_possessive] work, [PAWN_pronoun] never had to lift a finger doing domestic chores.</description>
    <skillGains>
      <Intellectual>3</Intellectual>
      <Medicine>8</Medicine>
      <Artistic>6</Artistic>
    </skillGains>
    <workDisables>Cooking</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <forcedTraits>
      <Abrasive>0</Abrasive>
    </forcedTraits>
    <possessions>
      <AestheticNose MayRequire="Ludeon.RimWorld.Royalty">1</AestheticNose>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>UprightDiplomat49</defName>
    <identifier>UprightDiplomat49</identifier>
    <slot>Adulthood</slot>
    <title>upright diplomat</title>
    <titleShort>diplomat</titleShort>
    <description>As a diplomat, [PAWN_nameDef] was assigned to deal with diplomatic tasks in the blood-soaked outer rim sectors.\n\n[PAWN_pronoun] hoped to end the endless wars, until [PAWN_possessive] consularship, the St. Anthem, was attacked by a raider fleet.</description>
    <skillGains>
      <Construction>-3</Construction>
      <Shooting>4</Shooting>
      <Social>8</Social>
      <Crafting>-3</Crafting>
    </skillGains>
    <workDisables>Hauling</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>VideoProducer83</defName>
    <identifier>VideoProducer83</identifier>
    <slot>Adulthood</slot>
    <title>video producer</title>
    <titleShort>producer</titleShort>
    <description>Too awkward to socialize, [PAWN_nameDef] found comfort in the production of videos. Oddly, people enjoyed [PAWN_possessive] videos, calling them "perfect sitcom dramas".\n\n[PAWN_nameDef] took advantage of this and broadcast more, ever hopeful that [PAWN_pronoun] would be contacted by the Wizards.</description>
    <skillGains>
      <Intellectual>2</Intellectual>
      <Artistic>7</Artistic>
      <Crafting>4</Crafting>
    </skillGains>
    <workDisables>ManualDumb, Social</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Fat</bodyTypeGlobal>
    <bodyTypeFemale>Fat</bodyTypeFemale>
    <bodyTypeMale>Fat</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>BushSniper94</defName>
    <identifier>BushSniper94</identifier>
    <slot>Adulthood</slot>
    <title>bush sniper</title>
    <titleShort>sniper</titleShort>
    <description>[PAWN_nameDef]'s peaceful life was cut short when [PAWN_possessive] country was brutally invaded.\n\n[PAWN_nameDef] was drafted into service as soon as [PAWN_pronoun] was of legal age, and quickly showed an aptitude for rifles. [PAWN_pronoun] spent months living in the forests picking off soldiers who dared cross the border.</description>
    <skillGains>
      <Plants>-3</Plants>
      <Shooting>6</Shooting>
      <Cooking>2</Cooking>
      <Artistic>-2</Artistic>
      <Animals>4</Animals>
    </skillGains>
    <workDisables>Caring</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
      <li>Tribal</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <forcedTraits>
      <ShootingAccuracy>1</ShootingAccuracy>
    </forcedTraits>
    <possessions>
      <Gun_SniperRifle>1</Gun_SniperRifle>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>PirateSympathizer24</defName>
    <identifier>PirateSympathizer24</identifier>
    <slot>Adulthood</slot>
    <title>pirate sympathizer</title>
    <titleShort>pirate</titleShort>
    <description>From [PAWN_possessive] youth, [PAWN_nameDef] developed connections in the underworld. There [PAWN_pronoun] trained in melee combat and became a trusted underworld advisor.\n\nAfter participating in a failed rebellion, [PAWN_pronoun] fled [PAWN_possessive] homeworld to travel with a pirate band.</description>
    <skillGains>
      <Shooting>4</Shooting>
      <Melee>8</Melee>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>UnethicalDoctor29</defName>
    <identifier>UnethicalDoctor29</identifier>
    <slot>Adulthood</slot>
    <title>unethical doctor</title>
    <titleShort>doctor</titleShort>
    <description>[PAWN_nameDef] became a doctor to save lives and research medicine. [PAWN_pronoun] soon realized the only way to make progress on [PAWN_possessive] research was to perform experiments on human patients.\n\n[PAWN_pronoun] often used patients for [PAWN_possessive] experiments - whether they were willing or not.</description>
    <skillGains>
      <Intellectual>6</Intellectual>
      <Medicine>6</Medicine>
    </skillGains>
    <workDisables>ManualDumb, Social</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <possessions>
      <Kidney>1</Kidney>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ShockTrooper15</defName>
    <identifier>ShockTrooper15</identifier>
    <slot>Adulthood</slot>
    <title>shock trooper</title>
    <titleShort>shocktroop</titleShort>
    <description>[PAWN_nameDef] was in a deep space shock troop unit deployed on multiple planets in various wars.\n\nDuring a risky assault, [PAWN_possessive] whole unit was wiped out. Nobody else knew what happened on the field that day, but [PAWN_nameDef] will carry the burden forever.</description>
    <skillGains>
      <Shooting>7</Shooting>
      <Melee>5</Melee>
      <Medicine>2</Medicine>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>CombatMedtech59</defName>
    <identifier>CombatMedtech59</identifier>
    <slot>Adulthood</slot>
    <title>combat medtech</title>
    <titleShort>medic</titleShort>
    <description>As a military medtech, [PAWN_nameDef]'s job was to fix and build anything [PAWN_pronoun] could get [PAWN_possessive] hands on, from living people to machines and mechs.\n\nSent into dangerous areas, [PAWN_pronoun] never had a problem saving soldiers' lives and building the technology and machines needed to win.</description>
    <skillGains>
      <Construction>3</Construction>
      <Intellectual>1</Intellectual>
      <Medicine>5</Medicine>
      <Crafting>5</Crafting>
    </skillGains>
    <workDisables>PlantWork</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>WildlifeRanger99</defName>
    <identifier>WildlifeRanger99</identifier>
    <slot>Adulthood</slot>
    <title>wildlife ranger</title>
    <titleShort>ranger</titleShort>
    <description>Dismayed at the poachers driving several animal species to near-extinction, [PAWN_nameDef] became a wildlife ranger. [PAWN_pronoun] cared for and protect the animals - by force if necessary.\n\n[PAWN_pronoun] grew to hate people slaughtering animals for luxury and profit.</description>
    <skillGains>
      <Shooting>2</Shooting>
      <Medicine>2</Medicine>
      <Animals>8</Animals>
    </skillGains>
    <workDisables>Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>TreasureHunter82</defName>
    <identifier>TreasureHunter82</identifier>
    <slot>Adulthood</slot>
    <title>treasure hunter</title>
    <titleShort>adventurer</titleShort>
    <description>[PAWN_nameDef] explored outlying rimworlds in hopes of finding rare treasures.\n\nAfter succeeding in finding one legendary sculpture, [PAWN_pronoun] was betrayed by [PAWN_possessive] men. [PAWN_pronoun] survived their treasonous attack, but the sculpture was taken.\n\n[PAWN_pronoun] created the Anvil Mercenary Company to help [PAWN_objective] seek revenge.</description>
    <skillGains>
      <Shooting>5</Shooting>
      <Melee>5</Melee>
      <Social>4</Social>
      <Cooking>2</Cooking>
      <Medicine>2</Medicine>
    </skillGains>
    <workDisables>ManualDumb, Animals, Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <possessions>
      <Gold>5</Gold>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>StarfighterPilot79</defName>
    <identifier>StarfighterPilot79</identifier>
    <slot>Adulthood</slot>
    <title>starfighter pilot</title>
    <titleShort>pilot</titleShort>
    <description>After years of training and drilling, [PAWN_nameDef] finally became a starfighter pilot.\n\nHundreds of combat missions later, [PAWN_pronoun] grew bored and started seeking newer thrills. [PAWN_pronoun] joined a band of space pirates, which eagerly welcomed [PAWN_objective] because of [PAWN_possessive] piloting skill.</description>
    <skillGains>
      <Intellectual>4</Intellectual>
      <Mining>-3</Mining>
      <Shooting>7</Shooting>
      <Melee>3</Melee>
      <Artistic>-3</Artistic>
      <Animals>-3</Animals>
    </skillGains>
    <workDisables>Caring</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>GangSoldier39</defName>
    <identifier>GangSoldier39</identifier>
    <slot>Adulthood</slot>
    <title>gang soldier</title>
    <titleShort>soldier</titleShort>
    <description>Living for many years between three raider settlements, [PAWN_nameDef] persevered through many challenges: a spacecraft implosion, cryptostasis, crashlanding on a miserable dustball of a planet, and nursing an old man back to health, and many of battles.\n\n[PAWN_pronoun] eventually took control of a violent gang.</description>
    <skillGains>
      <Construction>2</Construction>
      <Shooting>6</Shooting>
      <Melee>3</Melee>
      <Cooking>2</Cooking>
      <Medicine>2</Medicine>
    </skillGains>
    <workDisables>Artistic, Hauling</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpaceMarine5</defName>
    <identifier>SpaceMarine5</identifier>
    <slot>Adulthood</slot>
    <title>space marine</title>
    <titleShort>marine</titleShort>
    <description>[PAWN_nameDef] joined Interplanetary Marines to travel the stars fight for [PAWN_possessive] planet. [PAWN_pronoun] distinguished [PAWN_objective]self in several battles.\n\n[PAWN_possessive] experiences desensitized [PAWN_objective] to people around [PAWN_objective], and [PAWN_possessive] social skills degraded. But, [PAWN_pronoun] did learn how to fight.</description>
    <skillGains>
      <Shooting>8</Shooting>
      <Melee>6</Melee>
    </skillGains>
    <workDisables>Caring, Social</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Adventurer19</defName>
    <identifier>Adventurer19</identifier>
    <slot>Adulthood</slot>
    <title>adventurer</title>
    <titleShort>adventurer</titleShort>
    <description>[PAWN_nameDef]'s thirst for adventure took [PAWN_objective] to many planets. [PAWN_pronoun] visited the brightest glitterworlds and the darkest war-torn toxic planets in [PAWN_possessive] quest to find novelty and excitement.\n\n[PAWN_pronoun] worked as little as possible, often skirting ethical boundaries to make a quick buck. Among all jobs, [PAWN_pronoun] hated cooking the most.</description>
    <skillGains>
      <Shooting>3</Shooting>
      <Melee>3</Melee>
      <Social>3</Social>
      <Medicine>3</Medicine>
      <Crafting>3</Crafting>
      <Animals>2</Animals>
    </skillGains>
    <workDisables>Cooking</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>BloodyWanderer28</defName>
    <identifier>BloodyWanderer28</identifier>
    <slot>Adulthood</slot>
    <title>bloody wanderer</title>
    <titleShort>wanderer</titleShort>
    <description>[PAWN_nameDef] was a wanderer, traveling from town to town, taking odd jobs and stealing to live.\n\nOne day, [PAWN_pronoun] had a mental break and went on a long rampage, destroying several towns and killing many. [PAWN_pronoun] eventually calmed, but [PAWN_possessive] bloodlust never left [PAWN_objective].</description>
    <skillGains>
      <Construction>2</Construction>
      <Intellectual>-2</Intellectual>
      <Shooting>3</Shooting>
      <Melee>4</Melee>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>Social, Firefighting</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <forcedTraits>
      <Bloodlust>0</Bloodlust>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>PirateDoctor0</defName>
    <identifier>PirateDoctor0</identifier>
    <slot>Adulthood</slot>
    <title>pirate doctor</title>
    <titleShort>bad doc</titleShort>
    <description>[PAWN_nameDef] had a passion for medicine, but never bothered to get a medical degree. Only underfunded pirates and terrorists were desperate enough to hire [PAWN_possessive] services, and [PAWN_possessive] surgical patients had about a 50% survival rate.\n\nDespite this, [PAWN_possessive] optimistic attitude drove [PAWN_objective] to keep trying to heal those in need. As long as they paid up front.</description>
    <skillGains>
      <Intellectual>3</Intellectual>
      <Mining>-3</Mining>
      <Social>-2</Social>
      <Medicine>4</Medicine>
      <Artistic>-3</Artistic>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <forcedTraits>
      <NaturalMood>1</NaturalMood>
    </forcedTraits>
    <possessions>
      <Kidney>1</Kidney>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>BloodyDentist9</defName>
    <identifier>BloodyDentist9</identifier>
    <slot>Adulthood</slot>
    <title>bloody dentist</title>
    <titleShort>dentist</titleShort>
    <description>After studying at a famous college, [PAWN_nameDef]'s weak personality eventually snapped under the strain of angry administrators and whining patients. [PAWN_pronoun] went on a secret murder spree, killing many of those under [PAWN_possessive] care.\n\nAfter a pursuit, [PAWN_pronoun] managed to escape [PAWN_possessive] planet and travel to a new world.</description>
    <skillGains>
      <Intellectual>4</Intellectual>
      <Medicine>6</Medicine>
      <Artistic>2</Artistic>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <possessions>
      <Heart>1</Heart>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MasterChef48</defName>
    <identifier>MasterChef48</identifier>
    <slot>Adulthood</slot>
    <title>master chef</title>
    <titleShort>chef</titleShort>
    <description>On a glitterworld, [PAWN_nameDef] found a job washing dishes at a famous restaurant and worked [PAWN_possessive] way up the ranks.\n\nA few years later, [PAWN_possessive] career came to an end when someone poisoned the meal [PAWN_pronoun] was preparing for the mayor. Jobless, [PAWN_pronoun] left to explore the stars once again.</description>
    <skillGains>
      <Cooking>8</Cooking>
      <Artistic>4</Artistic>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <forcedTraits>
      <Gourmand>0</Gourmand>
    </forcedTraits>
    <possessions>
      <MealLavish>2</MealLavish>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Mercenary20</defName>
    <identifier>Mercenary20</identifier>
    <slot>Adulthood</slot>
    <title>mercenary</title>
    <titleShort>mercenary</titleShort>
    <description>After escaping [PAWN_possessive] home planet, [PAWN_nameDef] fell in with a band of mercenaries. [PAWN_pronoun] fought alongside [PAWN_possessive] brothers and sisters in arms for many years.\n\nOne day, they were caught in a fight they couldn't win. Refusing to retreat, [PAWN_nameDef] was shot down by [PAWN_possessive] enemies. [PAWN_pronoun] was alarmed to awake in a heap of corpses, mysteriously unharmed.</description>
    <skillGains>
      <Construction>2</Construction>
      <Shooting>6</Shooting>
      <Melee>6</Melee>
      <Social>2</Social>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>Animals</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ExplorerWriter10</defName>
    <identifier>ExplorerWriter10</identifier>
    <slot>Adulthood</slot>
    <title>explorer-writer</title>
    <titleShort>explorer</titleShort>
    <description>[PAWN_nameDef] traveled from planet to planet, gathering samples, stories, and research.\n\n[PAWN_possessive] goal was to bring together enough knowledge to make a survival guide for future generations.</description>
    <skillGains>
      <Construction>3</Construction>
      <Plants>3</Plants>
      <Intellectual>4</Intellectual>
      <Cooking>2</Cooking>
      <Medicine>3</Medicine>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MilitiaSoldier79</defName>
    <identifier>MilitiaSoldier79</identifier>
    <slot>Adulthood</slot>
    <title>militia soldier</title>
    <titleShort>soldier</titleShort>
    <description>[PAWN_nameDef] joined the militia forces and fought for rights and peace.\n\nAfter [PAWN_possessive] homeworld was unexpectedly destroyed in the wars, [PAWN_pronoun] traveled great distances, seeking a refuge where [PAWN_pronoun] could build a new, free way of life.</description>
    <skillGains>
      <Construction>3</Construction>
      <Melee>3</Melee>
      <Social>4</Social>
      <Medicine>-3</Medicine>
      <Artistic>4</Artistic>
      <Crafting>3</Crafting>
    </skillGains>
    <workDisables>Cooking, Cleaning</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>WarshipCaptain67</defName>
    <identifier>WarshipCaptain67</identifier>
    <slot>Adulthood</slot>
    <title>warship captain</title>
    <titleShort>captain</titleShort>
    <description>Years of being commanding officer of an advanced corvette taught [PAWN_nameDef] the art of leadership, the techniques of higher engineering, and how to stay calm while enemies try to kill you.\n\nUnfortunately, [PAWN_nameDef] only very rarely had contact with nature.</description>
    <skillGains>
      <Construction>3</Construction>
      <Plants>-3</Plants>
      <Intellectual>5</Intellectual>
      <Shooting>3</Shooting>
      <Melee>2</Melee>
      <Social>5</Social>
      <Animals>-3</Animals>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ForestProwler15</defName>
    <identifier>ForestProwler15</identifier>
    <slot>Adulthood</slot>
    <title>forest prowler</title>
    <titleShort>prowler</titleShort>
    <description>[PAWN_nameDef] led a band of wild men in the forest. They foraged for food, hunted, and sometimes murdered more civilized people to steal their shiny objects.</description>
    <skillGains>
      <Melee>3</Melee>
      <Social>4</Social>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>Mining</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
      <li>Tribal</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>RunawayDancer35</defName>
    <identifier>RunawayDancer35</identifier>
    <slot>Adulthood</slot>
    <title>runaway dancer</title>
    <titleShort>dancer</titleShort>
    <description>Grown by and for science, [PAWN_nameDef] gave it up. Instead, [PAWN_pronoun] wanted to dance for crowds across many worlds, from the glamor of the glitterworlds to the dangers of the rim.\n\nSuch work required wearing a smile on [PAWN_possessive] face and a pistol under [PAWN_possessive] costume.</description>
    <skillGains>
      <Shooting>2</Shooting>
      <Social>6</Social>
      <Artistic>4</Artistic>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ProstituteIdol28</defName>
    <identifier>ProstituteIdol28</identifier>
    <slot>Adulthood</slot>
    <title>prostitute idol</title>
    <titleShort>prostitute</titleShort>
    <description>After [PAWN_possessive] father was deposed, [PAWN_nameDef]'s beauty led [PAWN_objective] to be forced into pornography to survive.\n\nOver years of difficult treatments, [PAWN_possessive] body was remodeled to appear temporarily ageless. Sold to an orbital brothel, [PAWN_pronoun] became popular, with a steady stream of clients from all over the planet.</description>
    <skillGains>
      <Social>7</Social>
      <Cooking>3</Cooking>
      <Artistic>3</Artistic>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SicklyLawyer49</defName>
    <identifier>SicklyLawyer49</identifier>
    <slot>Adulthood</slot>
    <title>sickly lawyer</title>
    <titleShort>lawyer</titleShort>
    <description>As a lawyer, [PAWN_nameDef] could convict a deaf man for stealing music. [PAWN_possessive] sharp mind and lack of empathy let [PAWN_objective] say anything to get the other person to talk. With the right amount of money, [PAWN_pronoun] knew, any case can be won.\n\n[PAWN_possessive] childhood cough never went away.</description>
    <skillGains>
      <Social>8</Social>
    </skillGains>
    <workDisables>ManualDumb, Mining</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <forcedTraits>
      <Psychopath>0</Psychopath>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Tinkerer86</defName>
    <identifier>Tinkerer86</identifier>
    <slot>Adulthood</slot>
    <title>tinkerer</title>
    <titleShort>tinkerer</titleShort>
    <description>"How does this work?\n\nWhy does this move?\n\nMaybe if I change this... oops, I hope it works better... or maybe I broke it..."</description>
    <skillGains>
      <Construction>4</Construction>
      <Intellectual>4</Intellectual>
      <Artistic>2</Artistic>
      <Crafting>4</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>RocketPioneer19</defName>
    <identifier>RocketPioneer19</identifier>
    <slot>Adulthood</slot>
    <title>rocket pioneer</title>
    <titleShort>rocketeer</titleShort>
    <description>As a self-taught engineer, [PAWN_nameDef] escaped [PAWN_possessive] dying homeworld on a rocket [PAWN_pronoun] built [PAWN_objective]self.\n\n[PAWN_pronoun] later spent years of [PAWN_possessive] life on several worlds, helping less-developed planets develop rocket technology.</description>
    <skillGains>
      <Construction>8</Construction>
      <Plants>-3</Plants>
      <Intellectual>5</Intellectual>
    </skillGains>
    <workDisables>Violent</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>TheaterTechnician80</defName>
    <identifier>TheaterTechnician80</identifier>
    <slot>Adulthood</slot>
    <title>theater technician</title>
    <titleShort>technician</titleShort>
    <description>[PAWN_nameDef] spent countless hours behind the scenes of a famous theater. [PAWN_pronoun] built scenery, programmed lights, and monitored the machines that moved the sets.\n\nOff duty, [PAWN_pronoun] would talk with the cast members. Navigating the social drama of the actors helped [PAWN_objective] learn how to communicate well and manipulate others.</description>
    <skillGains>
      <Construction>3</Construction>
      <Social>3</Social>
      <Artistic>2</Artistic>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ArmySergeant16</defName>
    <identifier>ArmySergeant16</identifier>
    <slot>Adulthood</slot>
    <title>army sergeant</title>
    <titleShort>sergeant</titleShort>
    <description>[PAWN_nameDef] served in [PAWN_possessive] country's military, commanding small units as a non-commissioned officer.\n\n[PAWN_pronoun] excelled creating tactically sound plans despite difficult circumstances.</description>
    <skillGains>
      <Construction>2</Construction>
      <Shooting>8</Shooting>
      <Melee>2</Melee>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>StellarPirate10</defName>
    <identifier>StellarPirate10</identifier>
    <slot>Adulthood</slot>
    <title>stellar pirate</title>
    <titleShort>pirate</titleShort>
    <description>[PAWN_nameDef] was captain of a pirate ship. [PAWN_pronoun] and [PAWN_possessive] crew made their living capturing traders who wandered off-course.\n\n[PAWN_pronoun] was known for [PAWN_possessive] balanced approach to problems, building, fighting, or negotiating as needed.</description>
    <skillGains>
      <Shooting>4</Shooting>
      <Social>4</Social>
      <Crafting>4</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>LostSoldier13</defName>
    <identifier>LostSoldier13</identifier>
    <slot>Adulthood</slot>
    <title>lost soldier</title>
    <titleShort>lost</titleShort>
    <description>[PAWN_nameDef]'s childhood soldier conditioning failed, and the experiment was shut down. [PAWN_pronoun] was abandoned, purposeless, friendless.\n\n[PAWN_nameDef] eventually found solace in physically demanding work. [PAWN_pronoun] especially liked building houses and crafting heavy armor.</description>
    <skillGains>
      <Construction>5</Construction>
      <Intellectual>-3</Intellectual>
      <Social>-2</Social>
      <Crafting>5</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>LanguageAnalyst27</defName>
    <identifier>LanguageAnalyst27</identifier>
    <slot>Adulthood</slot>
    <title>language analyst</title>
    <titleShort>linguist</titleShort>
    <description>After war broke out on [PAWN_possessive] homeworld, [PAWN_nameDef] was conscripted as a linguist. [PAWN_pronoun] spent [PAWN_possessive] days in a space station, decoding enemy communications and tending the hydroponic crops.\n\nAfter a meteorite damaged the station, [PAWN_pronoun] was forced to enter cryptosleep and hope for rescue.</description>
    <skillGains>
      <Plants>4</Plants>
      <Intellectual>8</Intellectual>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>PrisonerOfWar2</defName>
    <identifier>PrisonerOfWar2</identifier>
    <slot>Adulthood</slot>
    <title>prisoner of war</title>
    <titleShort>prisoner</titleShort>
    <description>[PAWN_nameDef] was captured and put on death row for war crimes.\n\nFortunately for [PAWN_objective], someone cut the power to [PAWN_possessive] cell block. Riots ignited. Guards and trained wargs were deployed to quell the uprising. Organizing a few other inmates, [PAWN_nameDef] escaped in a small spacecraft.</description>
    <skillGains>
      <Shooting>4</Shooting>
      <Melee>4</Melee>
      <Social>4</Social>
    </skillGains>
    <workDisables>Animals</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MadAccountant61</defName>
    <identifier>MadAccountant61</identifier>
    <slot>Adulthood</slot>
    <title>mad accountant</title>
    <titleShort>accountant</titleShort>
    <description>As an accountant, [PAWN_nameDef] often socialized with colleagues, and made many friends in upper management.\n\nOne day, [PAWN_nameDef] discovered a corruption racket run by some of [PAWN_possessive] bosses. After reporting this, [PAWN_pronoun] was fired. Wishing to hunt down those responsible, [PAWN_pronoun] went on a mass murder spree.</description>
    <skillGains>
      <Intellectual>6</Intellectual>
      <Shooting>4</Shooting>
      <Social>4</Social>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Politician57</defName>
    <identifier>Politician57</identifier>
    <slot>Adulthood</slot>
    <title>politician</title>
    <titleShort>politician</titleShort>
    <description>[PAWN_nameDef] was an activist in a powerful political faction. There [PAWN_pronoun] learned the art of persuasion and speech.\n\n[PAWN_pronoun] had many enemies, so [PAWN_pronoun] took secret courses in shooting and hand-to-hand combat.</description>
    <skillGains>
      <Shooting>2</Shooting>
      <Melee>2</Melee>
      <Social>5</Social>
    </skillGains>
    <workDisables>PlantWork, Mining</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Philosopher97</defName>
    <identifier>Philosopher97</identifier>
    <slot>Adulthood</slot>
    <title>philosopher</title>
    <titleShort>sage</titleShort>
    <description>[PAWN_nameDef]'s thirst for archaeo-technological knowledge drove [PAWN_objective] to spend years surveying abandoned data centers and studying ancient coding languages.\n\n[PAWN_pronoun] claimed to be one of the few who could piece together the tragedy of the past - and offer a path to the future.</description>
    <skillGains>
      <Intellectual>4</Intellectual>
      <Social>2</Social>
      <Medicine>3</Medicine>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>AdventurousWeirdo55</defName>
    <identifier>AdventurousWeirdo55</identifier>
    <slot>Adulthood</slot>
    <title>adventurous weirdo</title>
    <titleShort>weirdo</titleShort>
    <description>A creative but strange individual, [PAWN_nameDef] dedicated [PAWN_possessive] life to [PAWN_possessive] twin passions of medieval world history and high technology.\n\n[PAWN_pronoun] was fascinated by the idea of becoming a 'skyknight' with glittering armor and a giant sword.</description>
    <skillGains>
      <Shooting>-2</Shooting>
      <Melee>6</Melee>
      <Cooking>2</Cooking>
      <Artistic>2</Artistic>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>Mining</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>CasketBuilder52</defName>
    <identifier>CasketBuilder52</identifier>
    <slot>Adulthood</slot>
    <title>casket builder</title>
    <titleShort>builder</titleShort>
    <description>[PAWN_nameDef] was fascinated with cryptosleep, and spent [PAWN_possessive] life learning about the mysterious technology.\n\nWorking as an assembler on a midworld, [PAWN_nameDef] built an experimental prototype casket from discarded parts. Unfortunately, while [PAWN_pronoun] was searching for the final component, [PAWN_possessive] home was leveled by a bomb.</description>
    <skillGains>
      <Construction>4</Construction>
      <Intellectual>4</Intellectual>
      <Medicine>3</Medicine>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MilitaryOfficer17</defName>
    <identifier>MilitaryOfficer17</identifier>
    <slot>Adulthood</slot>
    <title>military officer</title>
    <titleShort>officer</titleShort>
    <description>Obsessed with interstellar travel, [PAWN_nameDef] left [PAWN_possessive] desolate life behind to join a military academy in hopes of becoming a pilot.\n\n[PAWN_pronoun] was trained in advanced combat and survival skills. While [PAWN_pronoun] excelled in [PAWN_possessive] studies, [PAWN_nameDef] was a loner and never cared about [PAWN_possessive] crewmates.</description>
    <skillGains>
      <Construction>2</Construction>
      <Intellectual>2</Intellectual>
      <Shooting>8</Shooting>
      <Social>-3</Social>
    </skillGains>
    <workDisables>Caring</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>FearfulChef49</defName>
    <identifier>FearfulChef49</identifier>
    <slot>Adulthood</slot>
    <title>fearful chef</title>
    <titleShort>chef</titleShort>
    <description>[PAWN_nameDef] was a excellent chef who worked in a high-end restaurant.\n\nOne day, [PAWN_pronoun] left the stove on, and accidentally burned down the whole building. This event shook [PAWN_objective] so much that [PAWN_pronoun] swore to never touch a stove again.</description>
    <skillGains>
      <Plants>2</Plants>
      <Intellectual>2</Intellectual>
      <Medicine>2</Medicine>
    </skillGains>
    <workDisables>Cooking, Firefighting</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
    </spawnCategories>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MuffaloResearcher67</defName>
    <identifier>MuffaloResearcher67</identifier>
    <slot>Adulthood</slot>
    <title>muffalo researcher</title>
    <titleShort>researcher</titleShort>
    <description>[PAWN_nameDef] specialized in studying and manipulating the genetic code of the muffalo.\n\n[PAWN_possessive] work left little time to develop an appreciation for the arts or other people's company.</description>
    <skillGains>
      <Intellectual>5</Intellectual>
      <Melee>-3</Melee>
      <Medicine>4</Medicine>
      <Animals>4</Animals>
    </skillGains>
    <workDisables>Social, Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <possessions>
      <WoolMuffalo>4</WoolMuffalo>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ArtifactHunter48</defName>
    <identifier>ArtifactHunter48</identifier>
    <slot>Adulthood</slot>
    <title>artifact hunter</title>
    <titleShort>artifacter</titleShort>
    <description>[PAWN_nameDef] became depressed and lost interest in life. [PAWN_pronoun] traveled from planet to planet, searching for psychic artifacts amid mechanoid ruins in the hope that they would cure the pain in [PAWN_possessive] soul.\n\nA steady trigger finger and careful planning kept [PAWN_nameDef] alive.</description>
    <skillGains>
      <Construction>4</Construction>
      <Shooting>8</Shooting>
    </skillGains>
    <workDisables>Social</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <forcedTraits>
      <ShootingAccuracy>1</ShootingAccuracy>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Herpetologist3</defName>
    <identifier>Herpetologist3</identifier>
    <slot>Adulthood</slot>
    <title>herpetologist</title>
    <titleShort>herper</titleShort>
    <description>After a terrible mining accident, [PAWN_nameDef] set off into space in search of exotic new animal species.\n\n[PAWN_pronoun] hoped to leave [PAWN_possessive] past behind and find long-lived companions. [PAWN_pronoun] knew the pain of loss too well.</description>
    <skillGains>
      <Plants>2</Plants>
      <Intellectual>2</Intellectual>
      <Medicine>4</Medicine>
      <Animals>6</Animals>
    </skillGains>
    <workDisables>Mining</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <possessions>
      <InsectJelly>5</InsectJelly>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Fugitive4</defName>
    <identifier>Fugitive4</identifier>
    <slot>Adulthood</slot>
    <title>fugitive</title>
    <titleShort>fugitive</titleShort>
    <description>[PAWN_nameDef] rebelled against the corporation [PAWN_pronoun] worked for. [PAWN_pronoun] failed, and was forced [PAWN_objective] to go on the run.\n\n[PAWN_pronoun] was always on the move, always leaving everything behind. [PAWN_pronoun] tried to gain supporters for [PAWN_possessive] cause, but failed due to [PAWN_possessive] poor social skills.</description>
    <skillGains>
      <Plants>3</Plants>
      <Social>-2</Social>
      <Cooking>4</Cooking>
      <Medicine>4</Medicine>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>GunDealer14</defName>
    <identifier>GunDealer14</identifier>
    <slot>Adulthood</slot>
    <title>gun dealer</title>
    <titleShort>gun dealer</titleShort>
    <description>[PAWN_nameDef] traded high-tech firearms between warring factions.\n\nOver time, [PAWN_pronoun] became very skilled at conducting product demonstrations and negotiating profitable deals.</description>
    <skillGains>
      <Shooting>4</Shooting>
      <Social>8</Social>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ColonyEngineer7</defName>
    <identifier>ColonyEngineer7</identifier>
    <slot>Adulthood</slot>
    <title>colony engineer</title>
    <titleShort>engineer</titleShort>
    <description>The children took over the facility, and they declared independence from the corporation.\n\n[PAWN_nameDef] had used the VR system to become a master engineer, and [PAWN_pronoun] worked diligently to prepare the colony for the corporation's inevitable return.</description>
    <skillGains>
      <Construction>7</Construction>
      <Intellectual>4</Intellectual>
      <Medicine>-3</Medicine>
      <Crafting>4</Crafting>
    </skillGains>
    <workDisables>Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>DeepSpaceSurveyor1</defName>
    <identifier>DeepSpaceSurveyor1</identifier>
    <slot>Adulthood</slot>
    <title>deep space surveyor</title>
    <titleShort>surveyor</titleShort>
    <description>[PAWN_nameDef] was a deep space surveyor. [PAWN_pronoun] studied which worlds were the most suitable for colonization.\n\n[PAWN_possessive] expeditions were met by hostile natives, green-painted space-women, and wannabe Greek gods. Surviving many firefights, [PAWN_pronoun] learned to build rudimentary lathes and rock cannons to fend off enemies.</description>
    <skillGains>
      <Construction>4</Construction>
      <Plants>-3</Plants>
      <Intellectual>6</Intellectual>
      <Shooting>4</Shooting>
      <Social>2</Social>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>CostumeCrafter41</defName>
    <identifier>CostumeCrafter41</identifier>
    <slot>Adulthood</slot>
    <title>costume crafter</title>
    <titleShort>costumer</titleShort>
    <description>By day, [PAWN_nameDef] was an office worker on a midworld. At night, [PAWN_pronoun] designed and built elaborate monster costumes for science fiction conventions.\n\n[PAWN_pronoun] learned a great deal about sewing. Unfortunately this came at the cost of being able to cook anything more complex than an instant meal.</description>
    <skillGains>
      <Intellectual>4</Intellectual>
      <Crafting>8</Crafting>
    </skillGains>
    <workDisables>Cooking</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Gunfighter51</defName>
    <identifier>Gunfighter51</identifier>
    <slot>Adulthood</slot>
    <title>gunfighter</title>
    <titleShort>gunfighter</titleShort>
    <description>[PAWN_nameDef] was a deadly shot. Peace wasn't for him. [PAWN_pronoun] found [PAWN_possessive] family among the brothers and sisters that [PAWN_pronoun] spent years fighting alongside.\n\nAfter most of those close to [PAWN_objective] died, [PAWN_pronoun] once again set off to lend [PAWN_possessive] gun to those who needed it - or those who could pay.</description>
    <skillGains>
      <Shooting>8</Shooting>
      <Melee>2</Melee>
      <Medicine>6</Medicine>
    </skillGains>
    <workDisables>Social</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <possessions>
      <Gun_Revolver>1</Gun_Revolver>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>RenownedProfessor51</defName>
    <identifier>RenownedProfessor51</identifier>
    <slot>Adulthood</slot>
    <title>renowned professor</title>
    <titleShort>professor</titleShort>
    <description>[PAWN_nameDef]'s bottomless compassion and love of knowledge drove [PAWN_objective] to a life of teaching. [PAWN_pronoun] became one of the best-known names in academia throughout the urbworld bubble.\n\n[PAWN_possessive] holo-lectures were revered by all that watched, and the few students [PAWN_pronoun] accepted into [PAWN_possessive] seminars spoke widely of [PAWN_possessive] kindness and patience.</description>
    <skillGains>
      <Intellectual>5</Intellectual>
      <Social>6</Social>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <forcedTraits>
      <Kind>0</Kind>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>NavyTechOfficer0</defName>
    <identifier>NavyTechOfficer0</identifier>
    <slot>Adulthood</slot>
    <title>navy tech officer</title>
    <titleShort>navy tech</titleShort>
    <description>After repairing a fleet admiral's holoscreen with nothing but a pair of rusty pliers and scrap wire, [PAWN_nameDef] was invited to the Federation Naval Academy.\n\n[PAWN_pronoun] received an excellent technical education and extensive firearms training, but had no interest in studying more menial skills.</description>
    <skillGains>
      <Intellectual>6</Intellectual>
      <Shooting>4</Shooting>
      <Cooking>-2</Cooking>
      <Crafting>5</Crafting>
      <Animals>-2</Animals>
    </skillGains>
    <workDisables>ManualDumb</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>KingOfPirates25</defName>
    <identifier>KingOfPirates25</identifier>
    <slot>Adulthood</slot>
    <title>king of pirates</title>
    <titleShort>pirate</titleShort>
    <description>As a self-proclaimed king of pirates, [PAWN_nameDef] was ruthless. Manipulative in nature, [PAWN_pronoun] was not afraid to sacrifice the people around [PAWN_objective] in service of [PAWN_possessive] own goals. [PAWN_pronoun] was a good shot, but even more skilled with a sword, and [PAWN_pronoun] loved the adrenaline rush of close combat.</description>
    <skillGains>
      <Shooting>5</Shooting>
      <Melee>8</Melee>
      <Social>4</Social>
      <Animals>3</Animals>
    </skillGains>
    <workDisables>Artistic, Cooking, Firefighting</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpaceNavyTech37</defName>
    <identifier>SpaceNavyTech37</identifier>
    <slot>Adulthood</slot>
    <title>space navy tech</title>
    <titleShort>navy tech</titleShort>
    <description>[PAWN_nameDef] was in a space navy for the most powerful planet in the local star group. [PAWN_pronoun] developed skills in maintenance, electronic systems, radar and imaging, and logistics management.\n\n[PAWN_pronoun] served with pride and patriotic love for [PAWN_possessive] planet.</description>
    <skillGains>
      <Intellectual>8</Intellectual>
      <Shooting>4</Shooting>
      <Melee>2</Melee>
      <Crafting>3</Crafting>
    </skillGains>
    <workDisables>Mining</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Defector95</defName>
    <identifier>Defector95</identifier>
    <slot>Adulthood</slot>
    <title>defector</title>
    <titleShort>defector</titleShort>
    <description>[PAWN_nameDef] decided to leave the oppressive dictatorship where [PAWN_pronoun] lived. [PAWN_possessive] defection was not well-received, and agents were sent after [PAWN_objective].\n\n[PAWN_pronoun] spent years on the run. Since [PAWN_pronoun] could trust no doctor, [PAWN_pronoun] treated [PAWN_possessive] own wounds. The ordeal made [PAWN_objective] bitter and untrusting.</description>
    <skillGains>
      <Construction>3</Construction>
      <Shooting>4</Shooting>
      <Melee>2</Melee>
      <Social>-2</Social>
      <Medicine>4</Medicine>
      <Crafting>3</Crafting>
    </skillGains>
    <workDisables>Animals, Artistic</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpaceMarineMedic10</defName>
    <identifier>SpaceMarineMedic10</identifier>
    <slot>Adulthood</slot>
    <title>space marine medic</title>
    <titleShort>medic</titleShort>
    <description>[PAWN_nameDef] joined a colony contact expeditionary force as a medic. The mission turned bad when the colonists turned out to be suffering from a zombifying disease. [PAWN_pronoun] ran and fought for days.\n\nCornered with wounded men, [PAWN_pronoun] sprayed enough ammunition to wear out four machine gun barrels, and learned the value of high-volume fire. But [PAWN_possessive] nickname, Noob, stuck from that day forward.</description>
    <skillGains>
      <Shooting>6</Shooting>
      <Melee>2</Melee>
      <Medicine>6</Medicine>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <forcedTraits>
      <SpeedOffset>2</SpeedOffset>
      <ShootingAccuracy>-1</ShootingAccuracy>
    </forcedTraits>
    <possessions>
      <MedicineIndustrial>3</MedicineIndustrial>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Technician9</defName>
    <identifier>Technician9</identifier>
    <slot>Adulthood</slot>
    <title>technician</title>
    <titleShort>techie</titleShort>
    <description>As an IT technician, [PAWN_nameDef] didn't have much time to write code any more. [PAWN_pronoun] got called to work all the time to fix failures in the cryptosleep chamber and AI persuasion systems.</description>
    <skillGains>
      <Construction>2</Construction>
      <Intellectual>6</Intellectual>
      <Medicine>4</Medicine>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MercenaryLord13</defName>
    <identifier>MercenaryLord13</identifier>
    <slot>Adulthood</slot>
    <title>mercenary lord</title>
    <titleShort>merc lord</titleShort>
    <description>[PAWN_nameDef] formed a mercenary company out of freelancers who wanted to make good money while also making slightly better-than-average moral choices. [PAWN_pronoun] called it the Ashmarines.\n\nTo avoid the coreworlds' military forces, [PAWN_pronoun] moved [PAWN_possessive] company to the rimworlds. There, [PAWN_pronoun] established bases and communities. In ruling, [PAWN_pronoun] maintained a form of order among the raiders and madmen of deep space.</description>
    <skillGains>
      <Intellectual>4</Intellectual>
      <Shooting>2</Shooting>
      <Social>7</Social>
      <Medicine>3</Medicine>
      <Artistic>2</Artistic>
    </skillGains>
    <workDisables>Animals, Mining</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ShadowMarine63</defName>
    <identifier>ShadowMarine63</identifier>
    <slot>Adulthood</slot>
    <title>shadow marine</title>
    <titleShort>marine</titleShort>
    <description>After perfecting [PAWN_possessive] skills as a pathfinder, [PAWN_nameDef] left [PAWN_possessive] unit to put [PAWN_possessive] skills to use on the planets [PAWN_pronoun] helped chart.\n\nFamiliar with orbital trade routes, [PAWN_pronoun] was able to get onto and off any planet without being detected.</description>
    <skillGains>
      <Intellectual>4</Intellectual>
      <Shooting>4</Shooting>
      <Melee>2</Melee>
      <Medicine>3</Medicine>
      <Animals>3</Animals>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Warlordess56</defName>
    <identifier>Warlordess56</identifier>
    <slot>Adulthood</slot>
    <title>warlordess</title>
    <titleShort>warlordess</titleShort>
    <description>[PAWN_nameDef] was the tough leader of a pirate band. [PAWN_pronoun] would accept any challenge, no matter how violent or dangerous. Others called [PAWN_objective] a killing machine.</description>
    <skillGains>
      <Mining>-2</Mining>
      <Melee>-2</Melee>
      <Social>6</Social>
      <Cooking>6</Cooking>
      <Medicine>5</Medicine>
      <Artistic>3</Artistic>
    </skillGains>
    <workDisables>ManualDumb</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ComputerEngineer36</defName>
    <identifier>ComputerEngineer36</identifier>
    <slot>Adulthood</slot>
    <title>computer engineer</title>
    <titleShort>engineer</titleShort>
    <description>Leveraging [PAWN_possessive] childhood experience, [PAWN_nameDef] became a computer engineer. [PAWN_pronoun] also learned to craft delicate sculptures and random electronics for fun.\n\n[PAWN_nameDef] became fond of technology and mostly gave up on talking to people. Using computers as friends has left [PAWN_nameDef] unable to care for others.</description>
    <skillGains>
      <Intellectual>6</Intellectual>
      <Social>-2</Social>
      <Artistic>2</Artistic>
      <Crafting>5</Crafting>
    </skillGains>
    <workDisables>Caring</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>CommonerLord45</defName>
    <identifier>CommonerLord45</identifier>
    <slot>Adulthood</slot>
    <title>commoner lord</title>
    <titleShort>lord</titleShort>
    <description>After years of training in martial arts and diplomacy, [PAWN_nameDef] was eventually given land and subjects of [PAWN_possessive] own to rule.\n\nBecause of [PAWN_nameDef]'s commoner heritage, the other lords disapproved. They framed [PAWN_objective] for a heinous crime, and the emperor had no choice but to exile [PAWN_objective].</description>
    <skillGains>
      <Melee>7</Melee>
      <Social>6</Social>
    </skillGains>
    <workDisables>Cooking, Cleaning</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <possessions>
      <Apparel_Cape MayRequireAnyOf="Ludeon.RimWorld.Royalty,Ludeon.RimWorld.Biotech">1</Apparel_Cape>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>TournamentFighter28</defName>
    <identifier>TournamentFighter28</identifier>
    <slot>Adulthood</slot>
    <title>tournament fighter</title>
    <titleShort>fighter</titleShort>
    <description>Motivated by the violence in [PAWN_possessive] environment, [PAWN_nameDef] studied martial arts, eventually signing up for an unarmed fighting tournament.\n\nUnlike [PAWN_possessive] opponents, [PAWN_nameDef] looked to the ancient warriors for guidance. [PAWN_pronoun] trained from dusk till dawn, perfecting [PAWN_possessive] skills.</description>
    <skillGains>
      <Intellectual>2</Intellectual>
      <Melee>8</Melee>
      <Medicine>1</Medicine>
    </skillGains>
    <workDisables>ManualSkilled</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
      <li>Offworld</li>
      <li>Outlander</li>
    </spawnCategories>
    <bodyTypeGlobal>Hulk</bodyTypeGlobal>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>HypnocultLeader61</defName>
    <identifier>HypnocultLeader61</identifier>
    <slot>Adulthood</slot>
    <title>hypnocult leader</title>
    <titleShort>cultist</titleShort>
    <description>Desiring to understand people, [PAWN_nameDef] learned hypnotism and discovered [PAWN_possessive] talent for the art.\n\n[PAWN_pronoun] gathered followers and formed a cult around [PAWN_possessive] vibrant personality. The authorities discovered [PAWN_possessive] illegal operations, and [PAWN_nameDef] fled before [PAWN_pronoun] could be arrested.</description>
    <skillGains>
      <Intellectual>5</Intellectual>
      <Social>8</Social>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ArtStudent79</defName>
    <identifier>ArtStudent79</identifier>
    <slot>Adulthood</slot>
    <title>art student</title>
    <titleShort>artist</titleShort>
    <description>After years of practice, Freya was able to enrol into an art university and take her skills further. She sought art industry work after graduating with good grades. She made some progress with private commissions, managing to make a decent living from heart.</description>
    <skillGains>
      <Construction>-2</Construction>
      <Plants>-1</Plants>
      <Cooking>-1</Cooking>
      <Medicine>1</Medicine>
      <Artistic>8</Artistic>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>Mining</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>VengefulNomad67</defName>
    <identifier>VengefulNomad67</identifier>
    <slot>Adulthood</slot>
    <title>vengeful nomad</title>
    <titleShort>nomad</titleShort>
    <description>After refusing the wild demands of a tyrannical king, [PAWN_nameDef]'s nomad band was marked as an outlaw group and systematically hunted.\n\n[PAWN_nameDef]'s family was eventually captured. Those who weren't killed were taken as slaves. Sold to the owner of a manufactory, [PAWN_nameDef] swore that one day [PAWN_pronoun] would have [PAWN_possessive] revenge.</description>
    <skillGains>
      <Construction>5</Construction>
      <Mining>1</Mining>
      <Crafting>7</Crafting>
    </skillGains>
    <workDisables>Intellectual</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
      <li>Outlander</li>
      <li>Offworld</li>
      <li>Cult</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MercenaryCaptain98</defName>
    <identifier>MercenaryCaptain98</identifier>
    <slot>Adulthood</slot>
    <title>mercenary captain</title>
    <titleShort>captain</titleShort>
    <description>[PAWN_nameDef] was in the crew of a mercenary ship. Disciplined and confident, [PAWN_pronoun] quickly rose through the ranks and gained friends.\n\nIn [PAWN_possessive] time as a mercenary captain, [PAWN_pronoun] was known for being gregarious and calm - possibly thanks to the joywires.</description>
    <skillGains>
      <Intellectual>2</Intellectual>
      <Shooting>5</Shooting>
      <Social>5</Social>
    </skillGains>
    <workDisables>Cleaning</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Female</bodyTypeGlobal>
    <bodyTypeFemale>Female</bodyTypeFemale>
    <bodyTypeMale>Female</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Restorer70</defName>
    <identifier>Restorer70</identifier>
    <slot>Adulthood</slot>
    <title>restorer</title>
    <titleShort>restorer</titleShort>
    <description>[PAWN_nameDef] traveled the world, always looking for old and forgotten things to repair for the good of others, working to see the true beauty in all things.</description>
    <skillGains>
      <Construction>5</Construction>
      <Crafting>9</Crafting>
      <Social>-2</Social>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <forcedTraits>
      <NaturalMood>1</NaturalMood>
    </forcedTraits>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>RimworldExile32</defName>
    <identifier>RimworldExile32</identifier>
    <slot>Adulthood</slot>
    <title>Rimworld exile</title>
    <titleShort>Exile</titleShort>
    <description>After [PAWN_possessive] planet was technologically uplifted by a benevolent glitterworld charity, [PAWN_nameDef] saw an opportunity to make some money by defrauding the wealthy glitterworld visitors.\n\nAfter [PAWN_pronoun] was caught, [PAWN_pronoun] was exiled to the rim, where [PAWN_pronoun] targeted rich trading companies to continue [PAWN_possessive] life of crime.</description>
    <skillGains>
      <Intellectual>6</Intellectual>
      <Shooting>-2</Shooting>
      <Social>5</Social>
      <Cooking>2</Cooking>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
      <li>Slave</li>
      <li>Trader</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>Streamer52</defName>
    <identifier>Streamer52</identifier>
    <slot>Adulthood</slot>
    <title>Streamer</title>
    <titleShort>Streamer</titleShort>
    <description>[PAWN_nameDef] worked as a videogame streamer, and was known for [PAWN_possessive] monkey-related impressions and jokes.\n\nA pandemic forced [PAWN_objective] to go into cryptosleep for several years. During [PAWN_possessive] sleep, [PAWN_pronoun] was launched on an interstellar diplomatic mission. Nothing was heard of [PAWN_objective] ever again.</description>
    <skillGains>
      <Social>6</Social>
      <Animals>5</Animals>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
      <li>Slave</li>
      <li>Trader</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>CivilServant25</defName>
    <identifier>CivilServant25</identifier>
    <slot>Adulthood</slot>
    <title>Civil servant</title>
    <titleShort>Bureaucrat</titleShort>
    <description>[PAWN_nameDef] was bureaucrat in the civil service.\n\nAlthough [PAWN_possessive] family connections took [PAWN_objective] far, [PAWN_possessive] vocal opposition to [PAWN_possessive] bosses' plans hindered [PAWN_possessive] advancement. Called a "red prince" by some, [PAWN_nameDef] sympathized with the poor, leading ultimately to disgrace in [PAWN_possessive] career.</description>
    <skillGains>
      <Intellectual>3</Intellectual>
      <Social>6</Social>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Civil</li>
      <li>Pirate</li>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Trader</li>
    </spawnCategories>
    <bodyTypeGlobal>Fat</bodyTypeGlobal>
    <bodyTypeFemale>Fat</bodyTypeFemale>
    <bodyTypeMale>Fat</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>TravelingBard93</defName>
    <identifier>TravelingBard93</identifier>
    <slot>Adulthood</slot>
    <title>Traveling bard</title>
    <titleShort>Musician</titleShort>
    <description>[PAWN_nameDef] joined the famous Star Pilots On Channel K (aka S.P.O.C.K) and toured the star system, but eventually the band split up and their fame faded away.\n\n[PAWN_pronoun] ended up as a travelling bard playing on [PAWN_possessive] portable synthesizer as [PAWN_pronoun] traveled from spaceport to spaceport.</description>
    <skillGains>
      <Social>5</Social>
      <Artistic>4</Artistic>
      <Crafting>2</Crafting>
    </skillGains>
    <workDisables>Intellectual</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
      <li>Slave</li>
      <li>Trader</li>
    </spawnCategories>
    <bodyTypeGlobal>Thin</bodyTypeGlobal>
    <bodyTypeFemale>Thin</bodyTypeFemale>
    <bodyTypeMale>Thin</bodyTypeMale>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>GameDeveloper95</defName>
    <identifier>GameDeveloper95</identifier>
    <slot>Adulthood</slot>
    <title>game developer</title>
    <titleShort>game dev</titleShort>
    <description>[PAWN_nameDef] was an independent game developer.\n\nAfter an early success, [PAWN_possessive] career quickly degenerated into a circus of misguided ideas, deals gone wrong, and desperate, failed PR stunts.</description>
    <skillGains>
      <Construction>-2</Construction>
      <Mining>-2</Mining>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>
    <spawnCategories>
      <li>Pirate</li>
    </spawnCategories>
    <bodyTypeGlobal>Male</bodyTypeGlobal>
    <bodyTypeFemale>Male</bodyTypeFemale>
    <bodyTypeMale>Male</bodyTypeMale>
    <forcedTraits>
      <Industriousness>-2</Industriousness>
    </forcedTraits>
    <possessions>
      <GameOfUrBoard>1</GameOfUrBoard>
    </possessions>
    <shuffleable>False</shuffleable>
  </BackstoryDef>

</Defs>