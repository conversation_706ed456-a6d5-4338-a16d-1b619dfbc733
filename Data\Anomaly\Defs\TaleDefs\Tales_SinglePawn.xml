﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <TaleDef>
    <defName>ClosedTheVoid</defName>
    <label>closed the void node</label>
    <taleClass>Tale_SinglePawn</taleClass>
    <type>PermanentHistorical</type>
    <baseInterest>20</baseInterest>
    <rulePack>
      <rulesStrings>
        <li>tale_noun->[PAWN_nameDef] closing the void node.</li>
        <li>image->[PAWN_nameFull] sealing the connection to the void, ensuring the safety of the colony [circumstance_group]</li>
        <li>image->[PAWN_nameFull] standing defiantly before the void node, determined to protect the colony [circumstance_group]</li>
        <li>circumstance_phrase->amidst crackling energy</li>
        <li>circumstance_phrase->as the void node emits a haunting glow</li>
        <li>circumstance_phrase->while the void whispers in the background</li>
        <li>desc_sentence->The gray metal presses in from all sides.</li>
        <li>desc_sentence->The other colonists are shown [side_position], anxiously awaiting [PAWN_nameDef]'s return.</li>
        <li>desc_sentence->[PAWN_nameDef]'s face betrays that [PAWN_pronoun] is agonising over the choice before [PAWN_objective].</li>
      </rulesStrings>
    </rulePack>
  </TaleDef>

  <TaleDef>
    <defName>EmbracedTheVoid</defName>
    <label>embraced the void</label>
    <taleClass>Tale_SinglePawn</taleClass>
    <type>PermanentHistorical</type>
    <baseInterest>20</baseInterest>
    <rulePack>
      <rulesStrings>
        <li>tale_noun->[PAWN_nameDef] embracing the void</li>
        <li>image->[PAWN_nameFull] harnessing the forbidden knowledge of the void, transcending mortal limitations, [circumstance_group]</li>
        <li>image->[PAWN_nameFull] surrendering to the allure of the void [circumstance_group]</li>
        <li>image->[PAWN_nameFull] giving in to the sinister whispers of the void [circumstance_group]</li>
        <li>circumstance_phrase->as tendrils of dark energy coil around [PAWN_possessive] limbs</li>
        <li>circumstance_phrase->amidst a swirling vortex of energy</li>
        <li>circumstance_phrase->with an unsettling smile</li>
        <li>desc_sentence->[PAWN_nameDef] radiates an aura of ominous power, a testament to [PAWN_possessive] pact with the void.</li>
        <li>desc_sentence->[PAWN_nameDef]'s eyes begin to gleam with an otherworldly glow.</li>
        <li>desc_sentence->The other colonists are shown [side_position], questioning the wisdom of [PAWN_nameDef]'s choice.</li>
      </rulesStrings>
    </rulePack>
  </TaleDef>

  <TaleDef>
    <defName>PerformedPsychicRitual</defName>
    <label>performed a psychic ritual</label>
    <taleClass>Tale_SinglePawn</taleClass>
    <type>Volatile</type>
    <baseInterest>1</baseInterest>
    <rulePack>
      <rulesStrings>
        <li>tale_noun->[PAWN_nameDef] leading a psychic ritual</li>
        <li>image->[PAWN_nameFull] channeling energy that dances around [PAWN_objective] as [PAWN_pronoun] weaves a potent psychic ritual, [circumstance_group]</li>
        <li>image->[PAWN_nameFull] reciting dark incantations and tapping into mysterious forces to perform a psychic ritual, [circumstance_group]</li>
        <li>image->[PAWN_nameFull] focusing [PAWN_possessive] gaze as [PAWN_pronoun] performs a psychic ritual, [circumstance_group]</li>
        <li>image->[PAWN_nameFull] tracing intricate symbols in the air, harnessing hidden energies to manifest a psychic ritual, [circumstance_group]</li>
        <li>circumstance_phrase->while focusing intently on the tome in front of [PAWN_objective]</li>
        <li>circumstance_phrase->with focused determination, showcasing [PAWN_possessive] mastery of the psychic arts</li>
        <li>circumstance_phrase->with a cruel smile</li>
        <li>circumstance_phrase->while [PAWN_possessive] voice fights to be heard over the roar of energy</li>
        <li>desc_sentence->The chanters can be seen [side_position] lending their support.</li>
        <li>desc_sentence->The air shimmers as [PAWN_nameDef] completes the psychic ritual.</li>
        <li>desc_sentence->The monolith shimmers in the background.</li>
        <li>desc_sentence->The surrounding candles flicker and sputter in the swirling energy.</li>
      </rulesStrings>
    </rulePack>
  </TaleDef>
</Defs>