<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <RecipeDef>
    <defName>Make_BabyFood</defName>
    <label>make baby food</label>
    <description>Make baby food, a nutritionally balanced vegetarian mash for babies. While easy to make in large quantities, only babies appreciate its bland taste and mushy texture.</description>
    <jobString>Making baby food.</jobString>
    <workAmount>450</workAmount>
    <workSpeedStat>CookSpeed</workSpeedStat>
    <effectWorking>Cook</effectWorking>
    <soundWorking>Recipe_CookMeal</soundWorking>
    <allowMixingIngredients>true</allowMixingIngredients>
    <ingredientValueGetterClass>IngredientValueGetter_Nutrition</ingredientValueGetterClass>
    <requiredGiverWorkType>Cooking</requiredGiverWorkType>
    <ingredients>
      <li>
        <filter>
          <customSummary>vegetarian ingredients</customSummary>
          <categories>
            <li>PlantFoodRaw</li>
            <li>AnimalProductRaw</li>
          </categories>
        </filter>
        <count>0.25</count>
      </li>
    </ingredients>
    <fixedIngredientFilter>
      <categories>
        <li>FoodRaw</li>
      </categories>
      <disallowedCategories>
        <li>MeatRaw</li>
        <li>EggsFertilized</li>
      </disallowedCategories>
    </fixedIngredientFilter>
    <workSkill>Cooking</workSkill>
    <products>
      <BabyFood>10</BabyFood>
    </products>
    <displayPriority>1430</displayPriority>
  </RecipeDef>

  <RecipeDef>
    <defName>Make_BabyFoodBulk</defName>
    <label>make baby food x4</label>
    <description>Make bulk baby food, a nutritionally balanced vegetarian mash for babies. While easy to make in large quantities, only babies appreciate its bland taste and mushy texture.</description>
    <jobString>Making bulk baby food.</jobString>
    <workAmount>1800</workAmount>
    <workSpeedStat>CookSpeed</workSpeedStat>
    <effectWorking>Cook</effectWorking>
    <soundWorking>Recipe_CookMeal</soundWorking>
    <allowMixingIngredients>true</allowMixingIngredients>
    <ingredientValueGetterClass>IngredientValueGetter_Nutrition</ingredientValueGetterClass>
    <requiredGiverWorkType>Cooking</requiredGiverWorkType>
    <ingredients>
      <li>
        <filter>
          <customSummary>vegetarian ingredients</customSummary>
          <categories>
            <li>PlantFoodRaw</li>
            <li>AnimalProductRaw</li>
          </categories>
        </filter>
        <count>1.00</count>
      </li>
    </ingredients>
    <fixedIngredientFilter>
      <categories>
        <li>FoodRaw</li>
      </categories>
      <disallowedCategories>
        <li>MeatRaw</li>
        <li>EggsFertilized</li>
      </disallowedCategories>
    </fixedIngredientFilter>
    <workSkill>Cooking</workSkill>
    <products>
      <BabyFood>40</BabyFood>
    </products>
    <displayPriority>1431</displayPriority>
  </RecipeDef>

</Defs>