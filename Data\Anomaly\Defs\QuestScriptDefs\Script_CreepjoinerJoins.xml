<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <QuestScriptDef Name="CreepjoinerArrivalBase" Abstract="True">
    <autoAccept>true</autoAccept>
    <defaultHidden>true</defaultHidden>
    <isRootSpecial>true</isRootSpecial>
    <questNameRules>
      <rulesStrings>
        <li>questDescription->creepjoiner</li> <!-- hidden -->
      </rulesStrings>
    </questNameRules>
    <questDescriptionRules>
      <rulesStrings>
        <li>questDescription->creepjoiner</li> <!-- hidden -->
      </rulesStrings>
    </questDescriptionRules>
  </QuestScriptDef>

  <QuestScriptDef ParentName="CreepjoinerArrivalBase">
    <defName>CreepJoinerArrival</defName>
    <root Class="QuestNode_Root_Creepjoiner_Arrival" />
  </QuestScriptDef>

  <QuestScriptDef ParentName="CreepjoinerArrivalBase">
    <defName>CreepJoinerArrival_Metalhorror</defName>
    <root Class="QuestNode_Sequence">
      <nodes>
        <li Class="QuestNode_SetupCreepjoiner">
          <downside>Metalhorror</downside>
          <aggressive>Assault</aggressive>
          <rejection>AggressiveRejection</rejection>

          <storeFormAs>form</storeFormAs>
          <storeBenefitAs>benefit</storeBenefitAs>
          <storeDownsideAs>downside</storeDownsideAs>
          <storeAggressiveAs>aggressive</storeAggressiveAs>
          <storeRejectionAs>rejection</storeRejectionAs>
        </li>
        <li Class="QuestNode_Root_Creepjoiner_Arrival" />
      </nodes>
    </root>
  </QuestScriptDef>

</Defs>