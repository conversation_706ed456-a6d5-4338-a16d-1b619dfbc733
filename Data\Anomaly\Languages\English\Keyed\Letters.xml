<?xml version="1.0" encoding="utf-8" ?>
<LanguageData>

  <!-- Inhumanization -->
  <ReHumanizedLabel>Rehumanized</ReHumanizedLabel>
  <ReHumanizedText>{0_nameDef} has regained {0_possessive} connection to humanity. {0_nameDef}'s 'inhumanized' health condition has been removed.</ReHumanizedText>
  <WarningFromLabel>Warning from {0_nameDef}</WarningFromLabel>

  <!-- Hate chanters -->
  <HateChantersLabel>Cultist hate chanters</HateChantersLabel>
  <HateChantersText>Cultist hate chanters have arrived. They intend to perform a hate chant ritual to force psychic rage into your colonists and drive them mad. The chant will grow louder and louder until the cultists are stopped.</HateChantersText>
  <HateChantersAttackingLabel>Cultists attacking</HateChantersAttackingLabel>
  <HateChantersAttackingText>The cultists have ended their hate chant and have begun to come out of their ritual trance.\n\nPrepare to defend.</HateChantersAttackingText>

  <!-- Psychic rituals -->
  <PsychicRitualCompleteLabel>{0} complete</PsychicRitualCompleteLabel>

  <VoidProvocationCompletedText>{INVOKER_nameDef} has finished performing {RITUAL_definite} ritual, sending out a psychic pulse that will attract entities.</VoidProvocationCompletedText>
  <VoidProvocationSucceeded>Prepare for what comes next.</VoidProvocationSucceeded>
  <VoidProvocationFailed>Unfortunately, your call has been left unanswered.</VoidProvocationFailed>
  <VoidProvocationDarkPsychicShock>In the process, {INVOKER_nameDef} glimpsed something horrifying beyond human understanding. The psychic darkness shocked {INVOKER_objective} into a short-term coma.</VoidProvocationDarkPsychicShock>
  <PhilophagyCompleteText>{INVOKER_nameDef} has completed {RITUAL_definite} ritual, absorbing skills and memories of {TARGET_nameDef}.</PhilophagyCompleteText>
  <PawnGainedXPInSkill>{0_nameDef} gained {1} XP in {2_label}</PawnGainedXPInSkill>
  <PawnLostXPInSkill>{0_nameDef} lost {1} XP in {2_label}</PawnLostXPInSkill>
  <PhilophagySkillLevelGained>rising from skill level {0} to {1}.</PhilophagySkillLevelGained>
  <PhilophagySkillLevelLost>falling from skill level {0} to {1}.</PhilophagySkillLevelLost>
  <PhilophagySkillLevelRemains>remaining at skill level {0}.</PhilophagySkillLevelRemains>
  <PhilophagyDamagedTarget>The process damaged {TARGET_nameDef}'s brain, putting {TARGET_objective} into a coma.</PhilophagyDamagedTarget>

  <ImbueDeathRefuralCompleteText>{0_nameDef} has been imbued with death refusal. {0_pronoun} can now self-resurrect after dying.\n\nThe process has atrophied {0_nameDef}'s brain, causing {0_objective} to lose a total of {1} XP in {0_possessive} skills.</ImbueDeathRefuralCompleteText>

  <SummonAnimalsSuccessText>{0_nameDef} has completed {RITUAL_definite} ritual. Expect a herd of animals shortly.</SummonAnimalsSuccessText>
  <SummonAnimalsFailureText>{0_nameDef} has completed {RITUAL_definite} ritual but something has gone terribly wrong! The ritual has attracted scaria-infected manhunting animals. Prepare for their arrival!</SummonAnimalsFailureText>
  
  <SummonShamblersCompleteText>{0_nameDef} has successfully completed {RITUAL_definite} ritual. Prepare for their arrival.</SummonShamblersCompleteText>

  <AttackingFromMultipleDirections>They are attacking from multiple directions.</AttackingFromMultipleDirections> 

  <ChronophagyCompleteText>{0_nameDef} has successfully completed {RITUAL_definite} ritual. The ritual had the following effects:</ChronophagyCompleteText>
  <ChronophagyTargetAged>{0_nameDef} aged {1} years</ChronophagyTargetAged>
  <ChronophagyInvokerRejuvenated>{0_nameDef} became {1} years younger</ChronophagyInvokerRejuvenated>
  <ChronophagyTargetDiedOfOldAge>{0_nameDef} could not take the strain of the aging from the chronophagy ritual and died due to old age.</ChronophagyTargetDiedOfOldAge>
  <ChronophagyTargetOldAgeDiseases>While {0_nameDef} survived the chronophagy ritual, {0_pronoun} developed the following conditions</ChronophagyTargetOldAgeDiseases>
  <ChronophagyInvokerCuredDiseases>Additionally, the process has cured {0_nameDef} of the following ailments:</ChronophagyInvokerCuredDiseases>
  <ChronophagyCurrentAge>and is now {0} years old</ChronophagyCurrentAge>

  <PleasurePulseCompleteText>{0_nameDef} has finished performing {RITUAL_definite} ritual. For the next {1}, everyone impacted by {RITUAL_definite} will be happier but work slower.</PleasurePulseCompleteText>

  <NeurosisPulseCompleteText>{0_nameDef} has finished performing {RITUAL_definite} ritual. For the next {1}, everyone who heard the pulse will work faster but be more irritable.</NeurosisPulseCompleteText>

  <BloodRainCompleteText>{0_nameDef} has finished performing {RITUAL_definite} ritual. Thick red droplets have begun to fall from the sky. Keep everyone indoors to prevent them from being driven berserk.</BloodRainCompleteText>

  <BrainwipeCompleteText>{INVOKER_nameDef} has finished performing {RITUAL_definite} ritual, chaotically erasing {TARGET_nameDef}'s memories.</BrainwipeCompleteText>
  <BrainwipeTargetComa>{TARGET_pronoun} will remain in a coma for {1}.</BrainwipeTargetComa>
  <BrainwipeUnwaveringlyLoyal>{TARGET_nameDef} is no longer unwaveringly loyal.</BrainwipeUnwaveringlyLoyal>
  <BrainwipeRehumanized>{TARGET_nameDef} has been re-humanized.</BrainwipeRehumanized>

  <PsychophagyCompleteText>{INVOKER_nameDef} has finished performing {RITUAL_definite} ritual and has consumed {TARGET_nameDef}'s psychic presence.</PsychophagyCompleteText>
  <PsychophagyTargetSurvived>{TARGET_nameDef} is now psychically deaf. The process has damaged {TARGET_nameDef}'s brain, putting {TARGET_objective} into a coma.</PsychophagyTargetSurvived>
  
  <PsychicRitualTargetBrainLiquified>The process has liquified {TARGET_nameDef}'s brain, killing {TARGET_objective}.</PsychicRitualTargetBrainLiquified>

  <SummonFleshbeastsPlayerCompleteText>{0_nameDef} has finished performing {RITUAL_definite} ritual. Fleshbeasts are emerging from the ground!</SummonFleshbeastsPlayerCompleteText>

  <GhoulInfusionCompleteText>{INVOKER_nameDef} has finished infusing {TARGET_nameDef} with bioferrite. The ritual has twisted {TARGET_nameDef}'s mind and mutated {TARGET_possessive} body, creating a terrifying creature known as a ghoul. {INVOKER_nameDef} shares a psychic link with {TARGET_nameDef}, allowing {INVOKER_objective} to control {TARGET_nameDef}.\n\nGhouls eat raw meat to survive. If they go hungry, they can turn hostile.</GhoulInfusionCompleteText>
  
  <AttackersMayCastRitualMultipleTimes>They might perform the ritual multiple times. You need to go stop them.</AttackersMayCastRitualMultipleTimes>

  <SkipAbductionPlayerCompleteText>{INVOKER_nameDef} has finished performing {RITUAL_definite} ritual. By manipulating space and time, {INVOKER_nameDef} has abducted {TARGET_nameDef} of the {FACTION_name} faction. The terrifying nature of the ritual has put {TARGET_nameDef} into a short-term coma.</SkipAbductionPlayerCompleteText>

  <FleshmassResponseLabel>Fleshmass response</FleshmassResponseLabel> 
  <FleshmassResponseText>The death of fleshmass has triggered a violent response! The fleshmass shudders as it births new fleshbeasts to defend itself.</FleshmassResponseText> 

  <!-- Void awakening -->
  <VoidAwakeningStageOneStructuresActivatedLabel>Dimming sky</VoidAwakeningStageOneStructuresActivatedLabel>
  <VoidAwakeningStageTwoStructuresActivatedLabel>Structures activated</VoidAwakeningStageTwoStructuresActivatedLabel>
  <VoidAwakeningStageOneStructuresActivatedText>As the second structure is activated, the darkness begins to intensify. An unnatural darkness will soon descend. Make sure your colony is prepared. Stockpile resources, build lights, craft disruptor flares, and reinforce defenses. Only those in the light will be safe.\n\nMore twisted structures will appear soon.</VoidAwakeningStageOneStructuresActivatedText>
  <VoidAwakeningStageTwoStructuresActivatedText>All void structures have been activated. The space around the void monolith has begun to shimmer. Get ready.</VoidAwakeningStageTwoStructuresActivatedText> 
  <VoidAwakeningFinalStructuresActivatedLabel>{STUDIER_nameDef} teleported</VoidAwakeningFinalStructuresActivatedLabel> 
  <VoidAwakeningFinalStructuresActivatedText>{STUDIER_nameDef} has activated the monolith and been ripped from our spacetime into another place.\n\n{STUDIER_pronoun} finds {STUDIER_objective}self in a room of jagged metallic spikes. Every surface is slick with dark fluid and the air vibrates with power.\n\nThis metal hell is a connecting channel to an inhuman hyperintelligence.</VoidAwakeningFinalStructuresActivatedText> 
  <VoidAwakeningShamblerArrivalLabel>Shambler assault</VoidAwakeningShamblerArrivalLabel>
  <VoidAwakeningShamblerArrivalText>A horde of shamblers have been attracted by the void structures. Get ready!</VoidAwakeningShamblerArrivalText>
  <VoidAwakeningFleshbeastBurrowLabel>Fleshbeast tunneling</VoidAwakeningFleshbeastBurrowLabel>
  <VoidAwakeningFleshbeastBurrowText>The monolith has generated a powerful psychic pulse, attracting nearby entities.\n\nBeneath the ground, you can hear meaty shrieks and scratching claws. Fleshbeasts are tunneling towards you. Get ready!</VoidAwakeningFleshbeastBurrowText>
  <VoidAwakeningMetalhorrorArrivalLabel>Metalhorror attack</VoidAwakeningMetalhorrorArrivalLabel>
  <VoidAwakeningMetalhorrorArrivalText>Metalhorrors are appearing near the void monolith. Get ready!</VoidAwakeningMetalhorrorArrivalText>
  <VoidAwakeningEntityArrivalLabel>Entity attack</VoidAwakeningEntityArrivalLabel> 
  <VoidAwakeningEntityArrivalText>The activated void structure has generated a twisted psychic pulse, attracting nearby entities. Get ready!</VoidAwakeningEntityArrivalText>
  <VoidAwakeningBreadcrumbLetterLabel>Embrace the void</VoidAwakeningBreadcrumbLetterLabel>
  <VoidAwakeningBreadcrumbLetterText>While interacting with the void structure, {PAWN_nameDef} saw a vision of the void. For a moment {PAWN_pronoun} swam the black ocean of infinite depth and felt its endless inhuman power.\n\n{PAWN_nameDef} thinks there may be a way to control that power by embracing the void. If you were to permanently open the conduit to the void, rather than shutting it down, it could offer untold possibilities.</VoidAwakeningBreadcrumbLetterText>

  <!-- Void node -->
  <VoidNodeReturnedLabel>{PAWN_nameDef} returns</VoidNodeReturnedLabel>
  <VoidNodeReturnedMessage>{PAWN_nameDef} has reappeared.</VoidNodeReturnedMessage>
  <EmbracedVoidText>{PAWN_nameDef} has reappeared.\n\n{PAWN_pronoun} looks withered by the inhuman thought-patterns of the void. All traces of humanity have left {PAWN_possessive} eyes.\n\n{PAWN_nameDef}'s mind and body are now permanently linked to the dark void of inhuman rage. Powered by this new connection, {PAWN_nameDef} no longer feels tethered to mortal needs like comfort or sleep. {PAWN_pronoun} has gained the ability to induce terror in others using the power of the void.\n\nThe monolith is awakened. It hums louder with {PAWN_nameDef}'s reappearance, a reminder that its connection to the void has been opened and made permanent.</EmbracedVoidText>
  <DisruptedLinkText>{PAWN_nameDef} has reappeared. {PAWN_pronoun} is weary from {PAWN_possessive} efforts, but otherwise healthy.\n\nBy closing the channel to the void, {PAWN_nameDef} has stopped any more unnatural phenomena from entering our world. Hostile entities in the area have all fallen to the ground, lifeless.\n\nTales of {PAWN_possessive} heroism are sure to spread far and wide.</DisruptedLinkText>
  <RewardFleshcraftingAbility>fleshcrafting</RewardFleshcraftingAbility>
  <RewardSlaughterAbility>slaughter</RewardSlaughterAbility>
  <RewardShamblerControlAbility>shambler control</RewardShamblerControlAbility>

  <!-- Duplicate sickness -->
  <DuplicateSicknessLabel>Duplicate sickness</DuplicateSicknessLabel>
  <DuplicateSicknessText>Both {0_nameDef} and {0_possessive} duplicate are feeling unwell. Their condition seems to be related to the duplication process and is worsening. The psychic connection that binds them is causing them to slowly lose consciousness. The condition won't get better until one of them dies.</DuplicateSicknessText>

  <!-- Frenzied animals -->
  <FrenziedAnimalsLabel>Frenzied animals</FrenziedAnimalsLabel>
  <FrenziedAnimalsText>A pack of man-hunting {0} have entered the area! They've been attracted by your ritual. They will roam the region hunting for humanoid flesh.\n\nThey won't attack doors unless they see someone go through the door. Hide inside and you'll be safe. They also won't attack powered-off turrets, so turn your turrets off to save them.\n\nThe {0} will leave the area in one or two days. You can hide and wait them out or fight them.</FrenziedAnimalsText>

  <!-- Ghouls -->
  <GhoulBetrayalLabel>Ghoul betrayal</GhoulBetrayalLabel>
  <GhoulBetrayalText>{PAWN_nameDef} has been driven mad by {PAWN_possessive} ravenous hunger for raw meat!</GhoulBetrayalText>
  
  <!-- Distress signal -->
  <DistressSignalLabel>Distress signal</DistressSignalLabel>
  <DistressSignalText>You've intercepted a distress signal from a nearby camp of {FACTION_name}.\n\nThe frantic voice begs for immediate assistance defending against a threat. They offer everything at their camp in return for help, including shards of powerful archotechnology.\n\nAs the voice tries to explain the nature of the threat, the signal goes dead.</DistressSignalText>
  <DistressSignalAmbushLabel>Ambush</DistressSignalAmbushLabel>
  <DistressSignalAmbushText>Fleshbeasts are emerging from a nearby pit burrow!</DistressSignalAmbushText>

  <!-- Chimeras -->
  <ChimeraAssaultLabel>Chimeras approach</ChimeraAssaultLabel>
  <ChimeraAssaultText>A group of twisted animal-like chimera monsters have appeared nearby.\n\nThe misshapen beasts will watch your colony, waiting for an opportunity to attack. They are skittish and may retaliate if provoked.</ChimeraAssaultText>

  <!-- Unnatural darkness -->
  <DarknessLiftingEarlyLetterLabel>Darkness lifting</DarknessLiftingEarlyLetterLabel>
  <DarknessLiftingEarlyLetterText>The last of the nightmarish pillars has collapsed. The unnatural darkness has begun to dissipate. Colonists breathe a sigh of relief in the clear air.</DarknessLiftingEarlyLetterText>
  <PawnAttackedInDarknessLabel>{0_nameDef} attacked</PawnAttackedInDarknessLabel>
  <PawnAttackedInDarknessText>Something in the darkness has attacked {0_nameDef}!\n\nThese attacks will continue until {0_pronoun} is safely in the light.</PawnAttackedInDarknessText>
  <NoctolAttackLetterLabel>Noctol attack</NoctolAttackLetterLabel>
  <NoctolAttackLetterText>The darkness has begun to stir. Inhuman chittering sounds emerge from somewhere within the inky black.\n\nBlack, insect-like noctols will attack from the darkness soon.</NoctolAttackLetterText>
  <DarknessWarningLetterLabel>Darkness soon</DarknessWarningLetterLabel>
  <DarknessWarningLetterText>The darkness is almost here. Only those in direct light from lamps, torches, or flares will be safe.\n\nTo keep colonists in the light, you can restrict their movement in the SCHEDULE tab by assigning allowed areas.</DarknessWarningLetterText>
  <DarknessWaveringLetterLabel>Darkness wavering</DarknessWaveringLetterLabel>
  <DarknessWaveringLetterText>With the destruction of the noctolith, a faint crack appears in the darkness, before being filled with more of the sooty black.\n\nDestroy the remaining noctoliths to end the unnatural darkness.</DarknessWaveringLetterText>

  <!-- Shambler Swarms -->
  <LetterLabelShamblerSwarmArrived>Shamblers approach</LetterLabelShamblerSwarmArrived>
  <LetterLabelShamblerArrived>Shambler approaches</LetterLabelShamblerArrived>
  <LetterShamblerSwarmArrived>A group of {0} shambling, rotting corpses is approaching. Some inhuman force has reanimated these lifeless bodies and set them to murder.\n\nThey will now wander through the area. If they see a human, they will attack. If not, they will leave in a few days.\n\nIf you can capture one, you can study it for anomaly knowledge.</LetterShamblerSwarmArrived>
  <LetterShamblerArrived>A shambling, rotting corpse is approaching. Some inhuman force has reanimated this lifeless body and set it to murder.\n\nIt will now wander through the area. If it sees a human, it will attack. If not, it will leave in a few days.\n\nIf you can capture it, you can study it for anomaly knowledge.</LetterShamblerArrived>
  <LetterLabelShamblerAnimalsArrived>{ANIMALKIND_label} shamblers</LetterLabelShamblerAnimalsArrived>
  <LetterShamblerAnimalsArrived>A group of shambling, rotting {ANIMALKIND_label} corpses is approaching. Some inhuman force has reanimated these lifeless bodies and set them to murder.\n\nThey will now wander through the area. If they see a human, they will attack. If not, they will leave in a few days.\n\nIf you can capture one, you can study it for anomaly knowledge.</LetterShamblerAnimalsArrived>

  <!-- Ghoul pod crash -->
  <GhoulPodCrashLabel>Transport pod crash</GhoulPodCrashLabel>
  <GhoulPodCrashText>A transport pod carrying a ravenous hostile ghoul has landed nearby.\n\nThe ghoul is currently in shock, but it will soon recover and attack anyone it sees. It can be captured to a holding platform for further study.\n\nCapture or kill it before it wakes.</GhoulPodCrashText>
  <GhoulPodCrashStartingPawnText>A transport pod carrying a ravenous hostile ghoul has landed nearby. On closer inspection, it appears to be {PAWN_nameDef}, a previous member of your crew.\n\nThe ghoul is currently in shock, but it will soon recover and attack anyone it sees. It can be captured to a holding platform for further study.\n\nCapture or kill it before it wakes.</GhoulPodCrashStartingPawnText>

  <!-- Entity codex -->
  <LetterLabelEntityDiscovery>New research available</LetterLabelEntityDiscovery>
  <LetterTextEntityDiscoveryResearch>Your discovery of {ENTITY_definite} has yielded new insights. You can now research the following Anomaly projects</LetterTextEntityDiscoveryResearch>

  <!-- Nociosphere -->
  <NociosphereDefeatedLabel>Nociosphere defeated</NociosphereDefeatedLabel>
  <NociosphereDefeatedText>The nociosphere has been destroyed, leaving behind a number of valuable resources.</NociosphereDefeatedText>
  <NociosphereBecomingUnstableLabel>Nociosphere destabilising</NociosphereBecomingUnstableLabel> 
  <NociosphereBecomingUnstableText>The nociosphere’s energy pattern has begun to shift and adapt. Within 10 days, it will no longer respond to suppression.\n\nThe nociosphere will activate if not suppressed.</NociosphereBecomingUnstableText> 
  <NociosphereBecomingUnstableTextExtraSendable>Dispose of the nociosphere by sending it to attack a large hostile force. Once its onslaught is complete, it will permanently depart.</NociosphereBecomingUnstableTextExtraSendable>
  <NociosphereBecomingUnstableTextExtraNotSendable>Study the nociosphere to find a way to dispose of it.</NociosphereBecomingUnstableTextExtraNotSendable>
  <NociosphereUnstableLabel>Nociosphere unstable</NociosphereUnstableLabel>
  <NociosphereUnstableText>The nociosphere has become unstable and is no longer responding to suppression. When its activity reaches 100%, it will awaken.</NociosphereUnstableText>
  <NociosphereUnstableTextExtraSendable>Dispose of the nociosphere by sending it to attack a large hostile force. Once its onslaught is complete, it will permanently depart.</NociosphereUnstableTextExtraSendable> 
  <NociosphereUnstableTextExtraNotSendable>Prepare for the nociosphere’s onslaught.</NociosphereUnstableTextExtraNotSendable>

  <UnnaturalCorpseArrivalLabel>Strange corpse</UnnaturalCorpseArrivalLabel>
  <UnnaturalCorpseArrivalText>An unnatural human corpse has appeared, as if out of thin air.\n\nOn further inspection, the corpse looks identical to {PAWN_nameDef}. Its skin feels waxy and warm to the touch.\n\nStudy the corpse to learn more about it.</UnnaturalCorpseArrivalText>

  <GoldenCubeArrivalLabel>Golden cube</GoldenCubeArrivalLabel>
  <GoldenCubeArrivalText>A strange cube has landed nearby. It shines like gold but is impossible to scratch. It's light and warm to the touch. The way light plays across its surface is captivating.\n\n{PAWN_nameDef} feels inexplicably drawn to the cube.\n\nStudy the cube to learn more about it.</GoldenCubeArrivalText>

  <VoidCuriosityLabel>Void curiosity</VoidCuriosityLabel>
  <VoidCuriosityText_Researched>{PAWN_nameDef} has begun to fixate on the void, and wishes to investigate it using the void provocation ritual.\n\nUse the void provocation ritual to discover and capture entities. Captured entities can be studied to complete Anomaly research projects, or connected to a bioferrite harvester to generate bioferrite.\n\nPlace a psychic ritual spot to perform the void provocation ritual.</VoidCuriosityText_Researched>
  <VoidCuriosityText_NotResearched>{PAWN_nameDef} has begun to fixate on the void, and wishes to investigate it using the void provocation ritual.\n\nUse the void provocation ritual to discover and capture entities. Captured entities can be studied to complete Anomaly research projects, or connected to a bioferrite harvester to generate bioferrite.\n\nResearch void provocation, then place a psychic ritual spot to perform the ritual.</VoidCuriosityText_NotResearched>

</LanguageData>