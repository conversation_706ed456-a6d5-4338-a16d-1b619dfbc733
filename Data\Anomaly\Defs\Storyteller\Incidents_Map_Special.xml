﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <IncidentDef>
    <defName>HarbingerTreeSpawn</defName>
    <label>harbinger tree</label>
    <category>Special</category>
    <targetTags>
      <li>Map_PlayerHome</li>
    </targetTags>
    <workerClass>IncidentWorker_HarbingerTreeSpawn</workerClass>
    <treeDef>Plant_TreeHarbinger</treeDef>
    <treeGenStepDef>HarbingerTrees</treeGenStepDef>
    <treeGrowth>0.15</treeGrowth>
    <letterLabel>Harbinger tree sprout</letterLabel>
    <letterText>A harbinger tree has sprouted nearby. These gnarled trees grow flesh-like coverings, and can feed on corpses and raw meat placed nearby. If well fed, the grove will continue to grow.\n\nIn a tribal myth, these trees are the emissaries of a shapeless god who rules an endless black ocean. The myth ends after the shapeless god reaches up from the water and tears down the sky.</letterText>
    <letterTextPlural>Harbinger trees have sprouted nearby. These gnarled trees grow flesh-like coverings and can feed on corpses and raw meat placed nearby. If well fed, the grove will continue to grow.\n\nIn a tribal myth, these trees are the emissaries of a shapeless god who rules an endless black ocean. The myth ends after the shapeless god reaches up from the water and tears down the sky.</letterTextPlural>
    <letterDef>NeutralEvent</letterDef>
  </IncidentDef>

  <IncidentDef>
    <defName>MonolithMigration</defName>
    <label>Strange signal</label>
    <category>Special</category>
    <targetTags>
      <li>Map_PlayerHome</li>
    </targetTags>
    <workerClass>IncidentWorker_GiveQuest</workerClass>
    <questScriptDef>MonolithMigration</questScriptDef>
    <letterLabel>Strange signal</letterLabel>
    <letterText>Your colonists feel a strange psychic signal. It's possible to respond to the signal, although it is unclear what will happen if you do.</letterText>
    <letterDef>NeutralEvent</letterDef>
  </IncidentDef>

  <IncidentDef>
    <defName>RefugeePodCrash_Ghoul</defName>
    <label>ghoul transport pod crash</label>
    <category>Misc</category>
    <targetTags>
      <li>Map_PlayerHome</li>
    </targetTags>
    <workerClass>IncidentWorker_GiveQuest</workerClass>
    <questScriptDef>RefugeePodCrash_Ghoul</questScriptDef>
  </IncidentDef>

  <IncidentDef>
    <defName>VoidCuriosity</defName>
    <label>void curiosity</label>
    <category>Special</category>
    <targetTags>
      <li>Map_PlayerHome</li>
    </targetTags>
    <workerClass>IncidentWorker_VoidCuriosity</workerClass>
    <letterLabel>Void curiosity</letterLabel>
    <letterDef>NeutralEvent</letterDef>
  </IncidentDef>

</Defs>