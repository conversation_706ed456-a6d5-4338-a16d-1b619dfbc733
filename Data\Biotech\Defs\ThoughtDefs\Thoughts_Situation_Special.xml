<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThoughtDef>
    <defName>PregnancyMood_Collapse</defName>
    <workerClass>ThoughtWorker_HediffThoughtRandom</workerClass>
    <developmentalStageFilter>Adult</developmentalStageFilter>
    <hediff>PregnancyMood</hediff>
    <labelInBracketsExtraForHediff>collapse</labelInBracketsExtraForHediff>
    <validWhileDespawned>true</validWhileDespawned>
    <stages>
      <li>
        <label>pregnancy mood collapse</label>
        <description>This pregnancy is so hard. My body always feels awful. So exhausting!</description>
        <baseMoodEffect>-14</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>PregnancyMood_Down</defName>
    <workerClass>ThoughtWorker_HediffThoughtRandom</workerClass>
    <developmentalStageFilter>Adult</developmentalStageFilter>
    <hediff>PregnancyMood</hediff>
    <labelInBracketsExtraForHediff>down</labelInBracketsExtraForHediff>
    <validWhileDespawned>true</validWhileDespawned>
    <stages>
      <li>
        <label>pregnancy mood down</label>
        <description>It feels like everything is going wrong for me today. Pregnancy is tough.</description>
        <baseMoodEffect>-8</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>PregnancyMood_Up</defName>
    <workerClass>ThoughtWorker_HediffThoughtRandom</workerClass>
    <developmentalStageFilter>Adult</developmentalStageFilter>
    <labelInBracketsExtraForHediff>up</labelInBracketsExtraForHediff>
    <hediff>PregnancyMood</hediff>
    <validWhileDespawned>true</validWhileDespawned>
    <stages>
      <li>
        <label>pregnancy mood up</label>
        <description>Everything feels a little bit more pleasant today. Pregnancy is nice.</description>
        <baseMoodEffect>4</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>PregnancyMood_High</defName>
    <thoughtClass>ThoughtWorker_HediffThoughtRandom</thoughtClass>
    <developmentalStageFilter>Adult</developmentalStageFilter>
    <hediff>PregnancyMood</hediff>
    <labelInBracketsExtraForHediff>high</labelInBracketsExtraForHediff>
    <validWhileDespawned>true</validWhileDespawned>
    <stages>
      <li>
        <label>pregnancy mood high</label>
        <description>I feel amazing! Being pregnant is a gift!</description>
        <baseMoodEffect>8</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>KillThirst</defName>
    <thoughtClass>Thought_Situational_KillThirst</thoughtClass>
    <workerClass>ThoughtWorker_KillThirst</workerClass>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
    <stages>
      <li>
        <label>kill thirst</label>
        <description>Must kill... it has been too long since I have killed somebody up close and personal.</description>
        <baseMoodEffect>-1</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>SunlightSensitivity_Mild</defName>
    <workerClass>ThoughtWorker_InSunlight</workerClass>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
    <requiredGenes>
      <li>UVSensitivity_Mild</li>
    </requiredGenes>
    <stages>
      <li>
        <label>sunlight sensitivity</label>
        <description>The sun hurts my eyes and my skin is tingling unpleasantly.</description>
        <baseMoodEffect>-6</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>SunlightSensitivity_Major</defName>
    <workerClass>ThoughtWorker_InSunlight</workerClass>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
    <requiredGenes>
      <li>UVSensitivity_Intense</li>
    </requiredGenes>
    <stages>
      <li>
        <label>sunlight sensitivity</label>
        <description>The sunlight burns my eyes and my skin feels like it's being scorched.</description>
        <baseMoodEffect>-12</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>ScanningSickness</defName>
    <workerClass>ThoughtWorker_Hediff</workerClass>
    <hediff>ScanningSickness</hediff>
    <validWhileDespawned>true</validWhileDespawned>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
    <stages>
      <li>
        <label>scanning sickness</label>
        <description>Ever since that scan I've been dizzy and I can't think.</description>
        <baseMoodEffect>-8</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>DeathrestExhaustion</defName>
    <workerClass>ThoughtWorker_DeathrestExhaustion</workerClass>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
    <stages>
      <li>
        <label>deathrest exhaustion</label>
        <description>The casket calls and I fall weary. The world's pall makes all so dreary. I need to deathrest.</description>
        <baseMoodEffect>-12</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>HateAura</defName>
    <workerClass>ThoughtWorker_HateAura</workerClass>
    <effectMultiplyingStat>PsychicSensitivity</effectMultiplyingStat>
    <validWhileDespawned>true</validWhileDespawned>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
    <stages>
      <li>
        <label>apocriton hatred (intense)</label>
        <description>I feel its hatred inside me, the way I feel my own emotions. It's so close and so cold. They all deserve death.</description>
        <baseMoodEffect>-16</baseMoodEffect>
      </li>
      <li>
        <label>apocriton hatred (strong)</label>
        <description>I hear its hatred echoing between my thoughts. Reasons, always reasons why my friends ought to die.</description>
        <baseMoodEffect>-10</baseMoodEffect>
      </li>
      <li>
        <label>apocriton hatred (distant)</label>
        <description>I feel it out there somewhere. It's always thinking about why these people deserve death. Always following the logic of murder. Hard to ignore.</description>
        <baseMoodEffect>-4</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  
  <ThoughtDef>
    <defName>NoxiousHaze</defName>
    <validWhileDespawned>true</validWhileDespawned>
    <workerClass>ThoughtWorker_NoxiousHaze</workerClass>
    <nullifyingGenes>
      <li>ToxResist_Total</li>
      <li>ToxicEnvironmentResistance_Total</li>
    </nullifyingGenes>
    <nullifyingHediffs>
      <li>DetoxifierLung</li>
      <li MayRequire="Ludeon.RimWorld.Anomaly">FleshmassLung</li>
    </nullifyingHediffs>
    <stages>
      <li>
        <label>acidic smog</label>
        <description>The smog makes my eyes water and it smells awful. I wish I could just have a few fresh breaths.</description>
        <baseMoodEffect>-5</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>Recluse</defName>
    <thoughtClass>Thought_Situational_Recluse</thoughtClass>
    <workerClass>ThoughtWorker_Recluse</workerClass>
    <requiredTraits>
      <li>Recluse</li>
    </requiredTraits>
    <stages>
      <li>
        <label>recluse alone</label>
        <description>It's a relief not to have to think about other people.</description>
        <baseMoodEffect>12</baseMoodEffect>
      </li>
      <li>
        <label>recluse in small group</label>
        <description>I'm happy I don't have too many people to think about. Alone would be better, but this is good too.</description>
        <baseMoodEffect>6</baseMoodEffect>
      </li>
      <li>
        <label>recluse crowded</label>
        <description>There's too many people around here. The weight of having to think about them bears down on me.</description>
        <baseMoodEffect>-4</baseMoodEffect>
      </li>
      <li>
        <label>recluse very crowded</label>
        <description>There are so many people I'm supposed to be working with. They're overwhelming.</description>
        <baseMoodEffect>-8</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>ChildlikePositivity</defName>
    <workerClass>ThoughtWorker_ChildlikePositivity</workerClass>
    <validWhileDespawned>true</validWhileDespawned>
    <stages>
      <li>
        <label>childlike joy</label>
        <description>New! Fun! Pretty! Oooh!</description>
        <baseMoodEffect>16</baseMoodEffect>
      </li>
      <li>
        <label>childlike wonder</label>
        <description>The world is a magical place and I can't wait to grow up!</description>
        <baseMoodEffect>12</baseMoodEffect>
      </li>
      <li>
        <label>childlike enthusiasm</label>
        <description>The world is full of wonders.</description>
        <baseMoodEffect>8</baseMoodEffect>
      </li>
      <li>
        <label>childlike confidence</label>
        <description>There are so many interesting things to learn.</description>
        <baseMoodEffect>4</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>PregnancyAttitude</defName>
    <workerClass>ThoughtWorker_PregnancyAttitude</workerClass>
    <nullifyingPrecepts>
      <li>GrowthVat_Essential</li>
    </nullifyingPrecepts>
    <stages>
      <li>
        <label>new pregnancy</label>
        <description>I'm pregnant! It makes me happy.</description>
        <baseMoodEffect>6</baseMoodEffect>
      </li>
      <li>
        <label>new pregnancy</label>
        <description>I'm pregnant and not very happy about it.</description>
        <baseMoodEffect>-3</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>PsychicBondProximity</defName>
    <workerClass>ThoughtWorker_PsychicBondProximity</workerClass>
    <validWhileDespawned>true</validWhileDespawned>
    <stages>
      <li>
        <label>psychic bond</label>
        <description>The bond brings us together, sharing feelings and thoughts. It's like a friend who is always with me.</description>
        <baseMoodEffect>12</baseMoodEffect>
      </li>
      <li>
        <label>psychic bond distance</label>
        <description>The distance makes it so hard to hear. I feel alone, like standing in a dark, silent cavern.</description>
        <baseMoodEffect>-10</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>DeathrestChamber</defName>
    <workerClass>ThoughtWorker_DeathrestChamber</workerClass>
    <validWhileDespawned>true</validWhileDespawned>
    <stages>
      <li>
        <label>deathrested outside</label>
        <description>I had to deathrest outdoors. It was awful.</description>
        <baseMoodEffect>-4</baseMoodEffect>
      </li>
      <li>
        <label>awful deathrest chamber</label>
        <description>I had to deathrest in an awful chamber.</description>
        <baseMoodEffect>-4</baseMoodEffect>
      </li>
      <li IsNull="True" /> <!-- dull -->
      <li IsNull="True" /> <!-- mediocre -->
      <li>
        <label>decent deathrest chamber</label>
        <description>I deathrested in a decent chamber.</description>
        <baseMoodEffect>2</baseMoodEffect>
      </li>
      <li>
        <label>good deathrest chamber</label>
        <description>I deathrested in a slightly impressive chamber. It was nice.</description>
        <baseMoodEffect>3</baseMoodEffect>
      </li>
      <li>
        <label>great deathrest chamber</label>
        <description>I deathrested in an impressive chamber. I feel rejuvenated.</description>
        <baseMoodEffect>4</baseMoodEffect>
      </li>
      <li>
        <label>excellent deathrest chamber</label>
        <description>I deathrested in a very impressive chamber. I feel amazing.</description>
        <baseMoodEffect>5</baseMoodEffect>
      </li>
      <li>
        <label>exceptional deathrest chamber</label>
        <description>I deathrested in an extremely impressive chamber. It was glorious.</description>
        <baseMoodEffect>6</baseMoodEffect>
      </li>
      <li>
        <label>unbelievable deathrest chamber</label>
        <description>I deathrested in an unbelievably impressive chamber. It was spectacular.</description>
        <baseMoodEffect>7</baseMoodEffect>
      </li>
      <li>
        <label>wondrous deathrest chamber</label>
        <description>I deathrested in such a luxurious chamber! It was wondrous.</description>
        <baseMoodEffect>8</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>BloodTorch</defName>
    <workerClass>ThoughtWorker_BloodTorch</workerClass>
    <requiredGenes>
      <li>Bloodfeeder</li>
    </requiredGenes>
    <stages>
      <li>
        <label>blood torch</label>
        <description>No flame burns as softly as a blood torch.</description>
        <baseMoodEffect>2</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>HemogenCraving</defName>
    <workerClass>ThoughtWorker_Hediff</workerClass>
    <hediff>HemogenCraving</hediff>
    <stages>
      <li>
        <label>hemogen craving</label>
        <description>My bones ache. I really need hemogen.</description>
        <baseMoodEffect>-10</baseMoodEffect>
      </li>
      <li>
        <label>hemogen craving</label>
        <description>This hurts bad and my head is fuzzy. I would kill for some blood. Maybe I should.</description>
        <baseMoodEffect>-15</baseMoodEffect>
      </li>
      <li>
        <label>hemogen craving</label>
        <description>Can't think. Thirst. Thirst. Must. Have. Blood.</description>
        <baseMoodEffect>-20</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

</Defs>