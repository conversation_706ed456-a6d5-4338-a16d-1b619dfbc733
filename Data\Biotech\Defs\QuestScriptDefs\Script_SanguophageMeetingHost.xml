<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <QuestScriptDef>
    <defName>SanguophageMeetingHost</defName>
    <hideInvolvedFactionsInfo>true</hideInvolvedFactionsInfo>
    <rootMinPoints>500</rootMinPoints>
    <rootSelectionWeight>0.5</rootSelectionWeight>
    <expireDaysRange>2</expireDaysRange>
    <minRefireDays>200</minRefireDays>
    <questNameRules>
      <rulesStrings>
        <li>questName->[meetAdjective] [meetNoun]</li>
        <li>meetAdjective->sanguophage</li>
        <li>meetAdjective->bloodthirsty</li>
        <li>meetAdjective->blood</li>
        <li>meetAdjective->shadow</li>
        <li>meetAdjective->dark</li>
        <li>meetAdjective->crimson</li>
        <li>meetAdjective->red</li>
        <li>meetAdjective->scab</li>
        <li>meetAdjective->hemic</li>
        <li>meetAdjective->sanguine</li>
        <li>meetNoun->meeting</li>
        <li>meetNoun->host</li>
        <li>meetNoun->gathering</li>
        <li>meetNoun->reunion</li>
        <li>meetNoun->assembly</li>
        <li>meetNoun->conclave</li>
        <li>meetNoun->congress</li>
        <li>meetNoun->parley</li>
      </rulesStrings>
    </questNameRules>
    <questDescriptionRules>
      <rulesStrings>
        <li>questDescription->A sanguophage named [asker_nameDef] is looking for a safe place to meet with [sanguophageCountMinusOne] other [sanguophageSingOrPlural] where they won't be tracked. Their leader [asker_nameDef] is asking for you to host the meeting at [map_definite].\n\nIf you accept, [sanguophageCount] sanguophages will arrive at [map_definite] from different directions. They will spend several hours discussing their own secret issues before departing. They promise to give you your reward before they leave.\n\nYou may betray the sanguophages and attack them. Downed sanguophages can be forced to turn one of your own colonists into a sanguophage.\n\n[sanguophageInfo]</li>

        <li>sanguophageSingOrPlural(sanguophageCountMinusOne==1)->sanguophage</li>
        <li>sanguophageSingOrPlural(sanguophageCountMinusOne>=2)->sanguophages</li>

        <li>sanguophageInfo->(*Gray)About sanguophages: Sanguophages are ageless, deathless super-humans powered by archotech-created archites in the bloodstream. They are beautiful and extremely intelligent. They can heal any injury, and never suffer from disease or poison. In combat, they can launch deadly spines and heal injured friends. The price is that sanguophages must consume hemogen derived from human blood to survive, and they must periodically deathrest for long periods. They're easily destroyed by fire, and slowed down by UV light.(/Gray)</li>
      </rulesStrings>
    </questDescriptionRules>
    <questContentRules>
      <rulesStrings>
        <li>sanguophagesArriveLetterLabel->Sanguophages arrived</li>
        <li>sanguophagesArriveLetterText->The [sanguophageCount] sanguophages are arriving at [map_definite] from different directions. They will meet and carry out their discussion in the colony.</li>

        <li>successLetterLabel->Meeting complete</li>
        <li>successLetterText->The sanguophages have completed their meeting and will now give you your reward.</li>

        <li>raidArrivedLetterLabel->Sanguophage hunters</li>
        <li>raidArrivedLetterText->A group of sanguophage hunters from [enemyFaction_name] are approaching!\n\nThey will attack the meeting sanguophages as well as those who hosted them.</li>
      </rulesStrings>
    </questContentRules>
    <root Class="QuestNode_Root_SanguophageMeetingHost" />
  </QuestScriptDef>

</Defs>