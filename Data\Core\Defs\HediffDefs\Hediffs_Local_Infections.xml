﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <HediffDef Name="InfectionBase" Abstract="True">
    <hediffClass>HediffWithComps</hediffClass>
    <defaultLabelColor>(0.8, 0.8, 0.35)</defaultLabelColor>
    <initialSeverity>0.001</initialSeverity>
    <isInfection>true</isInfection>
    <removeOnRedressChanceByDaysCurve>
      <points>
        <li>(0, 0)</li>
        <li>(2, 0)</li>
        <li>(7, 0.5)</li>
        <li>(20, 1)</li>
      </points>
    </removeOnRedressChanceByDaysCurve>
  </HediffDef>

  <HediffDef ParentName="InfectionBase">
    <defName>GutWorms</defName>
    <label>gut worms</label>
    <description>Parasitic worms in the gut. They cause vomiting. They also consume the victim's food, which increases hunger.</description>
    <hediffClass>HediffWithComps</hediffClass>
    <makesSickThought>true</makesSickThought>
    <tendable>true</tendable>
    <stages>
      <li>
        <painOffset>0.2</painOffset>
        <hungerRateFactorOffset>1</hungerRateFactorOffset>
        <vomitMtbDays>1.0</vomitMtbDays>
      </li>
    </stages>
    <comps>
      <li Class="HediffCompProperties_TendDuration">
        <baseTendDurationHours>48</baseTendDurationHours>
        <disappearsAtTotalTendQuality>3</disappearsAtTotalTendQuality>
      </li>
    </comps>
  </HediffDef>

  <HediffDef ParentName="InfectionBase">
    <defName>MuscleParasites</defName>
    <label>muscle parasites</label>
    <description>Parasitic creatures in the muscles. These cause weakness and a lack of coordination.</description>
    <hediffClass>HediffWithComps</hediffClass>
    <makesSickThought>true</makesSickThought>
    <tendable>true</tendable>
    <stages>
      <li>
        <painOffset>0.2</painOffset>
        <statOffsets>
          <RestFallRateFactor>1</RestFallRateFactor>
        </statOffsets>
        <capMods>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.3</offset>
          </li>
          <li>
            <capacity>Moving</capacity>
            <offset>-0.3</offset>
          </li>
        </capMods>
      </li>
    </stages>
    <comps>
      <li Class="HediffCompProperties_TendDuration">
        <baseTendDurationHours>48</baseTendDurationHours>
        <disappearsAtTotalTendQuality>3</disappearsAtTotalTendQuality>
      </li>
    </comps>
  </HediffDef>

  <HediffDef Abstract="True" ParentName="InfectionBase" Name="MechanitesBase">
    <hediffClass>HediffWithComps</hediffClass>
    <makesSickThought>true</makesSickThought>
    <makesAlert>false</makesAlert>
    <minSeverity>0.001</minSeverity>
    <maxSeverity>1</maxSeverity>
    <initialSeverity>0.001</initialSeverity>
    <tendable>true</tendable>
    <comps>
      <li Class="HediffCompProperties_Disappears">
        <disappearsAfterTicks>900000~1800000</disappearsAfterTicks>
      </li>
      <li Class="HediffCompProperties_TendDuration">
        <baseTendDurationHours>48</baseTendDurationHours>
        <severityPerDayTended>-1.0</severityPerDayTended>
      </li>
      <li Class="HediffCompProperties_Immunizable">
        <severityPerDayNotImmune>0.25</severityPerDayNotImmune>
      </li>
    </comps>
  </HediffDef>
  
  <HediffDef ParentName="MechanitesBase">
    <defName>FibrousMechanites</defName>
    <label>fibrous mechanites</label>
    <description>Semi-coherent mechanites which develop fibrous links in muscle tissue. They enhance the victim's strength, but also cause intense pain. These are probably a mutated strain of strength-enhancing mechanites.</description>
    <stages>
      <li>
        <painOffset>0.2</painOffset>
        <label>mild pain</label>
        <statOffsets>
          <RestFallRateFactor>0.7</RestFallRateFactor>
        </statOffsets>
        <capMods>
          <li>
            <capacity>Manipulation</capacity>
            <offset>0.5</offset>
          </li>
          <li>
            <capacity>Moving</capacity>
            <offset>0.5</offset>
          </li>
          <li>
            <capacity>BloodPumping</capacity>
            <offset>0.5</offset>
          </li>
        </capMods>
      </li>
      <li>
        <minSeverity>0.5</minSeverity>
        <label>intense pain</label>
        <painOffset>0.6</painOffset>
        <statOffsets>
          <RestFallRateFactor>1</RestFallRateFactor>
        </statOffsets>
        <capMods>
          <li>
            <capacity>Manipulation</capacity>
            <offset>0.5</offset>
          </li>
          <li>
            <capacity>Moving</capacity>
            <offset>0.5</offset>
          </li>
          <li>
            <capacity>BloodPumping</capacity>
            <offset>0.5</offset>
          </li>
        </capMods>
      </li>
    </stages>
  </HediffDef>

  <HediffDef ParentName="MechanitesBase">
    <defName>SensoryMechanites</defName>
    <label>sensory mechanites</label>
    <description>Semi-coherent mechanites which reside in the nervous system. They enhance the victim's senses, but also cause intense pain. These are probably a mutated strain of sense-enhancing mechanites.</description>
    <stages>
      <li>
        <label>mild pain</label>
        <painOffset>0.2</painOffset>
        <statOffsets>
          <RestFallRateFactor>0.7</RestFallRateFactor>
        </statOffsets>
        <capMods>
          <li>
            <capacity>Sight</capacity>
            <offset>0.5</offset>
          </li>
          <li>
            <capacity>Hearing</capacity>
            <offset>0.5</offset>
          </li>
          <li>
            <capacity>Talking</capacity>
            <offset>0.5</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>0.3</offset>
          </li>
        </capMods>
      </li>
      <li>
        <label>intense pain</label>
        <minSeverity>0.5</minSeverity>
        <painOffset>0.6</painOffset>
        <statOffsets>
          <RestFallRateFactor>1</RestFallRateFactor>
        </statOffsets>
        <capMods>
          <li>
            <capacity>Sight</capacity>
            <offset>0.5</offset>
          </li>
          <li>
            <capacity>Hearing</capacity>
            <offset>0.5</offset>
          </li>
          <li>
            <capacity>Talking</capacity>
            <offset>0.5</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>0.3</offset>
          </li>
        </capMods>
      </li>
    </stages>
  </HediffDef>

  <HediffDef ParentName="InfectionBase">
    <defName>WoundInfection</defName>
    <label>infection</label>
    <labelNoun>an infection</labelNoun>
    <description>Bacterial infection in a wound. Without treatment, the bacteria will multiply, killing local tissue, and eventually causing blood poisoning and death.
\nThrough aeons of human warfare, infections have often taken more lives than the wounds themselves.</description>
    <makesSickThought>true</makesSickThought>
    <lethalSeverity>1</lethalSeverity>
    <tendable>true</tendable>
    <comps>
      <li Class="HediffCompProperties_TendDuration">
        <baseTendDurationHours>12</baseTendDurationHours>
        <severityPerDayTended>-0.53</severityPerDayTended>
      </li>
      <li Class="HediffCompProperties_Immunizable">
        <severityPerDayNotImmune>0.84</severityPerDayNotImmune>
        <immunityPerDaySick>0.6441</immunityPerDaySick>
        <severityPerDayImmune>-0.70</severityPerDayImmune>
        <immunityPerDayNotSick>-0.40</immunityPerDayNotSick>
      </li>
      <li Class="HediffCompProperties_Discoverable">
        <sendLetterWhenDiscovered>true</sendLetterWhenDiscovered>
      </li>
    </comps>
    <stages>
      <li>
        <label>minor</label>
        <painOffset>0.05</painOffset>
      </li> 
      <li>
        <minSeverity>0.33</minSeverity>
        <label>major</label>
        <painOffset>0.08</painOffset>
      </li>
      <li>
        <minSeverity>0.78</minSeverity>
        <label>extreme</label>
        <lifeThreatening>true</lifeThreatening>
        <painOffset>0.12</painOffset>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.05</offset>
          </li>
        </capMods>
      </li>
      <li>
        <minSeverity>0.87</minSeverity>
        <label>extreme</label>
        <lifeThreatening>true</lifeThreatening>
        <painOffset>0.85</painOffset>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <setMax>0.1</setMax>
          </li>
          <li>
            <capacity>Breathing</capacity>
            <offset>-0.05</offset>
          </li>
        </capMods>
      </li>
    </stages>
  </HediffDef>

  <HediffDef ParentName="InfectionBase">
    <defName>Flu</defName>
    <label>flu</label>
    <description>An infectious disease caused by the influenza virus.</description>
    <makesSickThought>true</makesSickThought>
    <lethalSeverity>1</lethalSeverity>
    <tendable>true</tendable>
    <comps>
      <li Class="HediffCompProperties_TendDuration">
        <baseTendDurationHours>12</baseTendDurationHours>
        <severityPerDayTended>-0.0773</severityPerDayTended>
      </li>
      <li Class="HediffCompProperties_Immunizable">
        <severityPerDayNotImmune>0.2488</severityPerDayNotImmune>
        <immunityPerDaySick>0.2388</immunityPerDaySick>
        <severityPerDayImmune>-0.4947</severityPerDayImmune>
        <immunityPerDayNotSick>-0.06</immunityPerDayNotSick>
      </li>
    </comps>
    <stages>
      <li>
        <label>minor</label>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.05</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.05</offset>
          </li>
          <li>
            <capacity>Breathing</capacity>
            <offset>-0.1</offset>
          </li>
        </capMods>
      </li>
      <li>
        <minSeverity>0.666</minSeverity>
        <label>major</label>
        <vomitMtbDays>1.5</vomitMtbDays>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.1</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.1</offset>
          </li>
          <li>
            <capacity>Breathing</capacity>
            <offset>-0.15</offset>
          </li>
        </capMods>
      </li>
      <li>
        <minSeverity>0.833</minSeverity>
        <label>extreme</label>
        <lifeThreatening>true</lifeThreatening>
        <vomitMtbDays>0.75</vomitMtbDays>
        <painOffset>0.05</painOffset>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.15</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.2</offset>
          </li>
          <li>
            <capacity>Breathing</capacity>
            <offset>-0.2</offset>
          </li>
        </capMods>
      </li>
    </stages>
  </HediffDef>

  <HediffDef ParentName="InfectionBase">
    <defName>Animal_Flu</defName>
    <label>flu</label>
    <description>An infectious disease caused by the influenza virus. This strain is adapted for infecting non-human species.</description>
    <debugLabelExtra>animal</debugLabelExtra>
    <makesSickThought>true</makesSickThought>
    <lethalSeverity>1</lethalSeverity>
    <tendable>true</tendable>
    <comps>
      <li Class="HediffCompProperties_TendDuration">
        <baseTendDurationHours>48</baseTendDurationHours>
        <severityPerDayTended>-0.1105</severityPerDayTended>
      </li>
      <li Class="HediffCompProperties_Immunizable">
        <severityPerDayNotImmune>0.2488</severityPerDayNotImmune>
        <immunityPerDaySick>0.2614</immunityPerDaySick>
        <severityPerDayImmune>-0.4947</severityPerDayImmune>
        <immunityPerDayNotSick>-0.06</immunityPerDayNotSick>
      </li>
    </comps>
    <stages>
      <li>
        <label>minor</label>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.05</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.05</offset>
          </li>
          <li>
            <capacity>Breathing</capacity>
            <offset>-0.1</offset>
          </li>
        </capMods>
      </li>
      <li>
        <minSeverity>0.666</minSeverity>
        <label>major</label>
        <vomitMtbDays>1.5</vomitMtbDays>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.1</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.1</offset>
          </li>
          <li>
            <capacity>Breathing</capacity>
            <offset>-0.15</offset>
          </li>
        </capMods>
      </li>
      <li>
        <minSeverity>0.833</minSeverity>
        <label>extreme</label>
        <lifeThreatening>true</lifeThreatening>
        <vomitMtbDays>0.75</vomitMtbDays>
        <painOffset>0.05</painOffset>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.15</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.2</offset>
          </li>
          <li>
            <capacity>Breathing</capacity>
            <offset>-0.2</offset>
          </li>
        </capMods>
      </li>
    </stages>
  </HediffDef>

  <HediffDef ParentName="InfectionBase">
    <defName>Plague</defName>
    <label>plague</label>
    <description>An infectious disease caused by bacteria.</description>
    <makesSickThought>true</makesSickThought>
    <lethalSeverity>1</lethalSeverity>
    <tendable>true</tendable>
    <comps>
      <li Class="HediffCompProperties_TendDuration">
        <baseTendDurationHours>12</baseTendDurationHours>
        <severityPerDayTended>-0.3628</severityPerDayTended>
      </li>
      <li Class="HediffCompProperties_Immunizable">
        <severityPerDayNotImmune>0.666</severityPerDayNotImmune>
        <immunityPerDaySick>0.5224</immunityPerDaySick>
        <severityPerDayImmune>-0.333</severityPerDayImmune>
        <immunityPerDayNotSick>-0.02</immunityPerDayNotSick>
      </li>
    </comps>
    <stages>
      <li>
        <label>minor</label>
        <painOffset>0.2</painOffset>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.05</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.05</offset>
          </li>
        </capMods>
      </li>
      <li>
        <minSeverity>0.6</minSeverity>
        <label>major</label>
        <painOffset>0.35</painOffset>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.2</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.2</offset>
          </li>
        </capMods>
      </li>
      <li>
        <minSeverity>0.8</minSeverity>
        <label>extreme</label>
        <painOffset>0.6</painOffset>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.3</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.3</offset>
          </li>
        </capMods>
      </li>
      <li>
        <minSeverity>0.9</minSeverity>
        <label>extreme</label>
        <lifeThreatening>true</lifeThreatening>
        <painOffset>0.85</painOffset>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.3</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.3</offset>
          </li>
          <li>
            <capacity>Breathing</capacity>
            <offset>-0.15</offset>
          </li>
        </capMods>
      </li>
    </stages>
  </HediffDef>

  <HediffDef ParentName="InfectionBase">
    <defName>Animal_Plague</defName>
    <label>plague</label>
    <description>An infectious disease caused by bacteria. This strain is adapted to infecting non-human species.</description>
    <debugLabelExtra>animal</debugLabelExtra>
    <makesSickThought>true</makesSickThought>
    <lethalSeverity>1</lethalSeverity>
    <tendable>true</tendable>
    <comps>
      <li Class="HediffCompProperties_TendDuration">
        <baseTendDurationHours>48</baseTendDurationHours>
        <severityPerDayTended>-0.4254</severityPerDayTended>
      </li>
      <li Class="HediffCompProperties_Immunizable">
        <severityPerDayNotImmune>0.666</severityPerDayNotImmune>
        <immunityPerDaySick>0.6092</immunityPerDaySick>
        <severityPerDayImmune>-0.333</severityPerDayImmune>
        <immunityPerDayNotSick>-0.02</immunityPerDayNotSick>
      </li>
    </comps>
    <stages>
      <li>
        <label>minor</label>
        <painOffset>0.2</painOffset>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.05</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.05</offset>
          </li>
        </capMods>
      </li>
      <li>
        <minSeverity>0.6</minSeverity>
        <label>major</label>
        <painOffset>0.35</painOffset>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.2</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.2</offset>
          </li>
        </capMods>
      </li>
      <li>
        <minSeverity>0.8</minSeverity>
        <label>extreme</label>
        <painOffset>0.6</painOffset>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.3</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.3</offset>
          </li>
        </capMods>
      </li>
      <li>
        <minSeverity>0.9</minSeverity>
        <label>extreme</label>
        <lifeThreatening>true</lifeThreatening>
        <painOffset>0.85</painOffset>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.3</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.3</offset>
          </li>
          <li>
            <capacity>Breathing</capacity>
            <offset>-0.15</offset>
          </li>
        </capMods>
      </li>
    </stages>
  </HediffDef>


  <HediffDef ParentName="InfectionBase">
    <defName>Malaria</defName>
    <label>malaria</label>
    <description>An infectious disease caused by a mosquito-borne parasite.</description>
    <lethalSeverity>1</lethalSeverity>
    <makesSickThought>true</makesSickThought>
    <scenarioCanAdd>true</scenarioCanAdd>
    <tendable>true</tendable>
    <comps>
      <li Class="HediffCompProperties_TendDuration">
        <baseTendDurationHours>12</baseTendDurationHours>
        <severityPerDayTended>-0.232</severityPerDayTended>
      </li>
      <li Class="HediffCompProperties_Immunizable">
        <severityPerDayNotImmune>0.3702</severityPerDayNotImmune>
        <immunityPerDaySick>0.3145</immunityPerDaySick>
        <severityPerDayImmune>-0.7297</severityPerDayImmune>
        <immunityPerDayNotSick>-0.03</immunityPerDayNotSick>
      </li>
    </comps>
    <stages>
      <li>
        <label>minor</label>
        <capMods>
          <li>
            <capacity>BloodFiltration</capacity>
            <offset>-0.1</offset>
          </li>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.05</offset>
          </li>
        </capMods>
      </li>
      <li>
        <minSeverity>0.78</minSeverity>
        <label>major</label>
        <vomitMtbDays>1.5</vomitMtbDays>
        <painOffset>0.3</painOffset>
        <capMods>
          <li>
            <capacity>BloodFiltration</capacity>
            <offset>-0.2</offset>
          </li>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.12</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.08</offset>
          </li>
        </capMods>
      </li>
      <li>
        <minSeverity>0.91</minSeverity>
        <label>extreme</label>
        <lifeThreatening>true</lifeThreatening>
        <vomitMtbDays>0.75</vomitMtbDays>
        <painOffset>0.3</painOffset>
        <capMods>
          <li>
            <capacity>BloodFiltration</capacity>
            <offset>-0.22</offset>
          </li>
          <li>
            <capacity>Consciousness</capacity>
            <setMax>0.1</setMax>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.1</offset>
          </li>
        </capMods>
      </li>
    </stages>
  </HediffDef>

  <HediffDef ParentName="InfectionBase">
    <defName>SleepingSickness</defName>
    <label>sleeping sickness</label>
    <description>An infectious disease caused by an insect-borne parasite. Sleeping sickness is not as deadly as some other diseases, but progresses slowly. The body takes a long time to clear the infection.</description>
    <lethalSeverity>1</lethalSeverity>
    <makesSickThought>true</makesSickThought>
    <tendable>true</tendable>
    <comps>
      <li Class="HediffCompProperties_TendDuration">
        <baseTendDurationHours>32</baseTendDurationHours>
        <severityPerDayTended>-0.07</severityPerDayTended>
      </li>
      <li Class="HediffCompProperties_Immunizable">
        <severityPerDayNotImmune>0.12</severityPerDayNotImmune>
        <immunityPerDaySick>0.10</immunityPerDaySick>
        <severityPerDayImmune>-0.176</severityPerDayImmune>
        <immunityPerDayNotSick>-0.02</immunityPerDayNotSick>
      </li>
    </comps>
    <stages>
      <li>
        <label>minor</label>
        <painOffset>0.02</painOffset>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.02</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.02</offset>
          </li>
        </capMods>
      </li>
      <li>
        <minSeverity>0.4375</minSeverity>
        <label>minor</label>
        <painOffset>0.03</painOffset>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.04</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.04</offset>
          </li>
        </capMods>
      </li>
      <li>
        <minSeverity>0.625</minSeverity>
        <label>major</label>
        <vomitMtbDays>3.5</vomitMtbDays>
        <painOffset>0.05</painOffset>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.15</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.06</offset>
          </li>
        </capMods>
      </li>
      <li>
        <minSeverity>0.875</minSeverity>
        <label>extreme</label>
        <lifeThreatening>true</lifeThreatening>
        <vomitMtbDays>1.75</vomitMtbDays>
        <painOffset>0.1</painOffset>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.2</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.2</offset>
          </li>
        </capMods>
      </li>
      <li>
        <minSeverity>0.9375</minSeverity>
        <label>extreme</label>
        <lifeThreatening>true</lifeThreatening>
        <vomitMtbDays>1</vomitMtbDays>
        <painOffset>0.15</painOffset>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <setMax>0.1</setMax>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.2</offset>
          </li>
        </capMods>
      </li>
    </stages>
  </HediffDef>

  <HediffDef>
    <defName>LungRotExposure</defName>
    <label>rot stink exposure</label>
    <description>Long-term exposure to rot stink gas. Rot stink exposure has no direct health effects. However, serious rot stink exposure has a chance to result in a painful bacterial infection known as lung rot. When not exposed to rot stink gas, rot stink exposure will slowly decrease.</description>
    <hediffClass>Hediff_RotStinkExposure</hediffClass>
    <minSeverity>0</minSeverity>
    <defaultLabelColor>(214, 90, 24)</defaultLabelColor>
    <initialSeverity>0.001</initialSeverity>
    <maxSeverity>1</maxSeverity>
    <canApplyDodChanceForCapacityChanges>true</canApplyDodChanceForCapacityChanges>
    <comps>
      <li Class="HediffCompProperties_SeverityFromGas">
        <gasType>RotStink</gasType>
        <severityGasDensityFactor>0.04</severityGasDensityFactor><!-- 4% -->
        <intervalTicks>60</intervalTicks><!-- 1 second -->
        <severityNotExposed>-0.00020</severityNotExposed><!-- 20% per day -->
        <exposureStatFactor>ToxicEnvironmentResistance</exposureStatFactor>
      </li>
      <li Class="HediffCompProperties_GiveHediffLungRot">
        <hediffDef>LungRot</hediffDef>
        <mtbOverRotGasExposureCurve>
          <points>
            <li>(0.5, 8)</li>
            <li>(1, 0.5)</li>
          </points>
        </mtbOverRotGasExposureCurve>
        <mtbCheckDuration>600</mtbCheckDuration><!-- 10 seconds -->
        <minSeverity>0.5</minSeverity>
      </li>
      <li Class="HediffCompProperties_SurgeryInspectable">
        <surgicalDetectionDesc>{PAWN_nameDef} is beginning to suffer from exposure to rot stink gas. Keep {PAWN_objective} away from rotting corpses and other sources of rot stink.</surgicalDetectionDesc>
      </li>
    </comps>
    <stages>
      <li>
        <becomeVisible>false</becomeVisible>
      </li>
      <li>
        <label>minor</label>
        <minSeverity>0.15</minSeverity>
      </li>
      <li>
        <label>major</label>
        <minSeverity>0.35</minSeverity>
      </li>
      <li>
        <label>serious</label>
        <minSeverity>0.5</minSeverity>
      </li>
    </stages>
  </HediffDef>

  <HediffDef ParentName="DiseaseBase">
    <defName>LungRot</defName>
    <label>lung rot</label>
    <description>A bacterial disease that targets an individual's lungs. Minor cases are little more than an irritation, but if left unchecked the bacterial growth results in an increasing amount of fluid buildup in the victim's lungs, eventually resulting in death.\n\nIf treated properly, the body's immune system will eventually clear lung rot.</description>
    <descriptionShort>A painful bacterial disease caused by exposure to rot stink gas. Upon reaching full saturation, it will kill the affected individual.</descriptionShort>
    <hediffClass>HediffWithComps</hediffClass>
    <lethalSeverity>1</lethalSeverity>
    <makesSickThought>true</makesSickThought>
    <minSeverity>0.001</minSeverity>
    <initialSeverity>0.001</initialSeverity>
    <stages>
      <li>
        <label>minor</label>
        <painOffset>0.05</painOffset>
        <capMods>
          <li>
            <capacity>Breathing</capacity>
            <offset>-0.05</offset>
          </li>
        </capMods>
      </li>
      <li>
        <minSeverity>0.6</minSeverity>
        <label>major</label>
        <painOffset>0.05</painOffset>
        <capMods>
          <li>
            <capacity>Breathing</capacity>
            <offset>-0.1</offset>
          </li>
        </capMods>
      </li>
      <li>
        <minSeverity>0.85</minSeverity>
        <label>extreme</label>
        <lifeThreatening>true</lifeThreatening>
        <painOffset>0.1</painOffset>
        <capMods>
          <li>
            <capacity>Breathing</capacity>
            <offset>-0.15</offset>
          </li>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.1</offset>
          </li>
        </capMods>
      </li>
    </stages>
    <tendable>true</tendable>
    <comps>
      <li Class="HediffCompProperties_SeverityPerDay">
        <severityPerDay>0.3</severityPerDay>
      </li>
      <li Class="HediffCompProperties_TendDuration">
        <severityPerDayTended>-1</severityPerDayTended>
        <baseTendDurationHours>48</baseTendDurationHours>
      </li>
      <li Class="HediffCompProperties_Disappears">
        <disappearsAfterTicks>240000~480000</disappearsAfterTicks> <!-- 4 ! 8 days -->
        <showRemainingTime>true</showRemainingTime>
      </li>
    </comps>
  </HediffDef>

</Defs>