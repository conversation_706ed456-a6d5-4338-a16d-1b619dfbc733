<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <BackstoryDef>
    <defName>FallenProdigy40</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>FallenProdigy40</identifier>  
    <slot>Childhood</slot>  
    <title>fallen prodigy</title>  
    <titleShort>prodigy</titleShort>  
    <description>[PAWN_nameDef] was born on a glitterworld falling into chaos. [PAWN_possessive] father was killed in action.

[PAWN_pronoun] struggled for a scholarship at Utmaior Academy and had to prove [PAWN_possessive] right to be there. A child genius, [PAWN_pronoun] was bullied as a charity case and couldn't make friends with the other kids.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Plants>2</Plants>    
      <Intellectual>5</Intellectual>    
      <Medicine>3</Medicine>    
      <Crafting>2</Crafting>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>Social</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <TooSmart>0</TooSmart>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>FarmKid60</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>FarmKid60</identifier>  
    <slot>Childhood</slot>  
    <title>farm kid</title>  
    <titleShort>farm kid</titleShort>  
    <description>[PAWN_nameDef] grew up on [PAWN_possessive] family's beef farm. [PAWN_pronoun] enjoyed working the fields and taking care of the animals.</description>  
    <skillGains>
      <Plants>3</Plants>    
      <Medicine>2</Medicine>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>FarmerBoy88</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>FarmerBoy88</identifier>  
    <slot>Childhood</slot>  
    <title>farmer boy</title>  
    <titleShort>farmer</titleShort>  
    <description>[PAWN_nameDef]'s parents died in a fire when [PAWN_pronoun] was seven. [PAWN_pronoun] grew up on [PAWN_possessive] uncle's farm, plowing fields and doing manual labor.

[PAWN_pronoun] could not bear answering questions about [PAWN_possessive] parents, so [PAWN_pronoun] replaced social contact with hard work.</description>  
    <skillGains>
      <Construction>1</Construction>    
      <Plants>4</Plants>    
      <Mining>1</Mining>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>Caring, Social, Firefighting</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <spawnCategories>
      <li>Farmer</li>
    </spawnCategories>  
    <forcedTraits>
      <Industriousness>1</Industriousness>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>UrbworldCriminal6</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>UrbworldCriminal6</identifier>  
    <slot>Childhood</slot>  
    <title>urbworld criminal</title>  
    <titleShort>criminal</titleShort>  
    <description>Born into bad circumstance on an overpopulated urbworld, [PAWN_nameDef] turned to small-time crime as a way to avoid starvation and hypothermia.

Robbery and murder were daily activities for [PAWN_objective]. [PAWN_nameDef] learned that surviving was winning.</description>  
    <skillGains>
      <Shooting>2</Shooting>    
      <Melee>3</Melee>    
      <Medicine>1</Medicine>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>OptimisticChild30</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>OptimisticChild30</identifier>  
    <slot>Childhood</slot>  
    <title>optimistic child</title>  
    <titleShort>optimistic</titleShort>  
    <description>[PAWN_nameDef] was blessed with loving parents and a pleasant midworld life. [PAWN_pronoun] was an  optimistic and sociable kid who loved making friends and listening to their stories. [PAWN_possessive] imagination would run wild with ideas and questions.</description>  
    <skillGains>
      <Social>4</Social>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <NaturalMood>1</NaturalMood>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>UrbworldHooligan12</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>UrbworldHooligan12</identifier>  
    <slot>Childhood</slot>  
    <title>urbworld hooligan</title>  
    <titleShort>hooligan</titleShort>  
    <description>[PAWN_nameDef] grew up in a dangerous urbworld. [PAWN_pronoun] quickly learned how to get [PAWN_possessive] daily meal - by crime. Such means left little to no space for cooking and growing activities.</description>  
    <skillGains>
      <Shooting>2</Shooting>    
      <Melee>3</Melee>    
      <Cooking>-3</Cooking>
    </skillGains>  
    <workDisables>PlantWork</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>IndworldUrchin73</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>IndworldUrchin73</identifier>  
    <slot>Childhood</slot>  
    <title>indworld urchin</title>  
    <titleShort>urchin</titleShort>  
    <description>[PAWN_nameDef] grew up on the streets of a world in the early stages of its industrial revolution. [PAWN_pronoun] passed [PAWN_possessive] days stealing, begging and tinkering with discarded machine scraps, always on the lookout for a way to escape poverty.</description>  
    <skillGains>
      <Construction>1</Construction>    
      <Melee>1</Melee>    
      <Crafting>2</Crafting>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>PlantWork</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ChildScientist30</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ChildScientist30</identifier>  
    <slot>Childhood</slot>  
    <title>child scientist</title>  
    <titleShort>scientist</titleShort>  
    <description>After graduating college very young, [PAWN_nameDef] devoted [PAWN_possessive] life to becoming immortal. [PAWN_pronoun] was arrested for trying to genetically modify [PAWN_objective]self.

The authorities released [PAWN_objective] on the condition that [PAWN_pronoun] would work in a government lab on spacecraft technology. [PAWN_pronoun] was permitted to continue [PAWN_possessive] personal research in [PAWN_possessive] free time.</description>  
    <skillGains>
      <Intellectual>4</Intellectual>    
      <Social>-3</Social>    
      <Cooking>-2</Cooking>    
      <Medicine>3</Medicine>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>MechanoidNerd10</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>MechanoidNerd10</identifier>  
    <slot>Childhood</slot>  
    <title>mechanoid nerd</title>  
    <titleShort>mechanerd</titleShort>  
    <description>[PAWN_nameDef] grew up in an urbworld as the only child of a pair of mechanoid designers.
They encouraged [PAWN_possessive] interest in the machines. Eventually, [PAWN_pronoun] became obsessed with building [PAWN_possessive] own.

Unfortunately this also lead to [PAWN_objective] being a loner as [PAWN_pronoun] preferred technical books to friends.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Intellectual>3</Intellectual>    
      <Social>-2</Social>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <disallowedTraits>
      <Gay>0</Gay>
    </disallowedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ColiseumCleaner54</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ColiseumCleaner54</identifier>  
    <slot>Childhood</slot>  
    <title>coliseum cleaner</title>  
    <titleShort>cleaner</titleShort>  
    <description>During the 'Inner Destrian War' fallout, [PAWN_possessive] parents were captured and [PAWN_pronoun] was born into a life of slavery. Forced to clean coliseums after bloody battles for money. Watching, waiting, and learning. Coliseums were the only thing [PAWN_pronoun] ever knew. One night while cleaning the coliseum, [PAWN_possessive] family was murdered.</description>  
    <skillGains>
      <Intellectual>4</Intellectual>    
      <Social>2</Social>    
      <Crafting>1</Crafting>
    </skillGains>  
    <workDisables>Caring, Firefighting, Mining</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>OfficerCadet25</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>OfficerCadet25</identifier>  
    <slot>Childhood</slot>  
    <title>officer cadet</title>  
    <titleShort>cadet</titleShort>  
    <description>From a young age, [PAWN_nameDef] trained to be a leader of men. [PAWN_pronoun] enrolled in the Vanu Defense College when [PAWN_pronoun] was a boy and became proficient with a range of weapons and survival techniques.</description>  
    <skillGains>
      <Plants>1</Plants>    
      <Shooting>3</Shooting>    
      <Melee>3</Melee>    
      <Social>1</Social>    
      <Medicine>1</Medicine>
    </skillGains>  
    <workDisables>Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ReclusiveProdigy96</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ReclusiveProdigy96</identifier>  
    <slot>Childhood</slot>  
    <title>reclusive prodigy</title>  
    <titleShort>prodigy</titleShort>  
    <description>Growing up on a flourishing glitterworld afforded [PAWN_nameDef] the chance to fully devote [PAWN_objective]self to [PAWN_possessive] studies.

Having taken a keen interest in genetic modification and neural augmentation from a young age, [PAWN_pronoun] had little time (or desire) for a regular childhood.</description>  
    <skillGains>
      <Intellectual>4</Intellectual>    
      <Medicine>4</Medicine>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>Social, Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <TooSmart>0</TooSmart>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>WarRefugee96</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>WarRefugee96</identifier>  
    <slot>Childhood</slot>  
    <title>war refugee</title>  
    <titleShort>refugee</titleShort>  
    <description>A war broke on out [PAWN_nameDef]'s home planet.

Gathering what little food and belongings they could, [PAWN_nameDef] and [PAWN_possessive] father stole a ship and escaped into space.</description>  
    <skillGains>
      <Intellectual>5</Intellectual>    
      <Shooting>6</Shooting>    
      <Melee>5</Melee>    
      <Cooking>-3</Cooking>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>Social, Artistic, PlantWork</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>WorkCampSlave37</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>WorkCampSlave37</identifier>  
    <slot>Childhood</slot>  
    <title>work camp slave</title>  
    <titleShort>slave</titleShort>  
    <description>[PAWN_nameDef] grew up as a slave to a particularly nasty noble family on a medieval world. 

While [PAWN_pronoun] received no formal education, the harsh labor regimen made [PAWN_possessive] body strong.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Plants>2</Plants>    
      <Mining>2</Mining>    
      <Social>-2</Social>
    </skillGains>  
    <workDisables>Intellectual</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Tough>0</Tough>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>DisciplinedFarmer28</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>DisciplinedFarmer28</identifier>  
    <slot>Childhood</slot>  
    <title>disciplined farmer</title>  
    <titleShort>farmer</titleShort>  
    <description>[PAWN_nameDef] was the younger of two sons on the family farm, a frontier plot that struggled to turn a profit. [PAWN_nameDef]'s father was a strict disciplinarian, constantly at [PAWN_possessive] sons to work harder and keep quiet. [PAWN_nameDef] burnt [PAWN_possessive] hand making breakfast once and swore to cook only when [PAWN_possessive] life was in danger.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Plants>3</Plants>    
      <Intellectual>-1</Intellectual>    
      <Mining>2</Mining>
    </skillGains>  
    <workDisables>Social, Cooking</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <spawnCategories>
      <li>Farmer</li>
    </spawnCategories>  
    <forcedTraits>
      <Industriousness>1</Industriousness>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>VoyagerChild94</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>VoyagerChild94</identifier>  
    <slot>Childhood</slot>  
    <title>voyager child</title>  
    <titleShort>voyager</titleShort>  
    <description>[PAWN_nameDef] was raised on an R&amp;D starship, and spent most of [PAWN_possessive] childhood travelling through the void.

Approaching a mysterious planetoid, the ship was severely damaged. [PAWN_possessive] next memory is of the ship heading out of orbit, fully repaired. [PAWN_pronoun] soon developed an unnatural gift for technological research.</description>  
    <skillGains>
      <Intellectual>4</Intellectual>
    </skillGains>  
    <workDisables>Social</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>VatgrownMedic53</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>VatgrownMedic53</identifier>  
    <slot>Childhood</slot>  
    <title>vatgrown medic</title>  
    <titleShort>medic</titleShort>  
    <description>Raised to be a combat medic, [PAWN_nameDef] was trained in firearms as well as medicine. [PAWN_possessive] favorite phrase when coming upon a wounded soldier was, "Life or death?" Most chose life.</description>  
    <skillGains>
      <Shooting>3</Shooting>    
      <Medicine>3</Medicine>
    </skillGains>  
    <workDisables>Social, Artistic, Crafting, Cooking</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>CoreDilettante33</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>CoreDilettante33</identifier>  
    <slot>Childhood</slot>  
    <title>core dilettante</title>  
    <titleShort>dilettante</titleShort>  
    <description>[PAWN_nameDef] spent [PAWN_possessive] formative years on a glitterworld in the core region, where [PAWN_pronoun] and [PAWN_possessive] friends pursued lives of idle pleasure.

As [PAWN_pronoun] grew up, [PAWN_pronoun] began to find the life of ease unfulfilling. [PAWN_pronoun] decided to look for a more challenging path.</description>  
    <skillGains>
      <Intellectual>2</Intellectual>    
      <Social>2</Social>    
      <Artistic>2</Artistic>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>ManualDumb, Violent</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>AspiringPopIdol28</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>AspiringPopIdol28</identifier>  
    <slot>Childhood</slot>  
    <title>aspiring pop idol</title>  
    <titleShort>pop idol</titleShort>  
    <description>[PAWN_nameDef] was being trained in song and dance to become the next big pop idol. [PAWN_pronoun] grew up being taken care of by company handlers.

[PAWN_pronoun] never knew manual labor, but learned a lot about music and social etiquette.</description>  
    <skillGains>
      <Shooting>-4</Shooting>    
      <Melee>-4</Melee>    
      <Social>4</Social>    
      <Artistic>4</Artistic>
    </skillGains>  
    <workDisables>ManualDumb, Cleaning, Hauling, PlantWork, Mining</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Beauty>2</Beauty>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>FarmBoy64</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>FarmBoy64</identifier>  
    <slot>Childhood</slot>  
    <title>farm boy</title>  
    <titleShort>farm boy</titleShort>  
    <description>[PAWN_nameDef] worked on [PAWN_possessive] family's farm, looking after the animals and treating them when they were injured. Preferring hands-on tasks and the outdoors, [PAWN_pronoun] avoided softer jobs that might have kept [PAWN_objective] cooped up inside.

[PAWN_pronoun] enjoyed this lonely work, and tended to stay out of people's way and do [PAWN_possessive] own thing.</description>  
    <skillGains>
      <Plants>2</Plants>    
      <Mining>3</Mining>    
      <Melee>4</Melee>    
      <Social>-3</Social>    
      <Cooking>-2</Cooking>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>LogicalChild2</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>LogicalChild2</identifier>  
    <slot>Childhood</slot>  
    <title>logical child</title>  
    <titleShort>logic kid</titleShort>  
    <description>[PAWN_pronoun] preferred logical activities like computers and was completely useless at art.

Being awkward in social situations, [PAWN_possessive] friends were few but close.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Intellectual>2</Intellectual>    
      <Social>-2</Social>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <TooSmart>0</TooSmart>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>StationWhelp94</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>StationWhelp94</identifier>  
    <slot>Childhood</slot>  
    <title>station whelp</title>  
    <titleShort>whelp</titleShort>  
    <description>[PAWN_nameDef] spent [PAWN_possessive] childhood as a penniless orphan living on a space station. [PAWN_pronoun] managed to scrape by doing filthy jobs and stealing. [PAWN_pronoun] became so violent due to poor treatment by [PAWN_possessive] “betters” that [PAWN_pronoun] was banished from the station, just to be picked up by space pirates.</description>  
    <skillGains>
      <Shooting>3</Shooting>    
      <Melee>5</Melee>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>Caring, Social</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ConstructionGrunt84</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ConstructionGrunt84</identifier>  
    <slot>Childhood</slot>  
    <title>construction grunt</title>  
    <titleShort>builder</titleShort>  
    <description>Growing up in [PAWN_possessive] father's lab, [PAWN_nameDef] was given the life of an intellectual elite. [PAWN_pronoun] got everything [PAWN_pronoun] desired, and price was never an object.

However [PAWN_nameDef] did not partake in the luxury given to him. Instead, [PAWN_pronoun] pursued more physical, and - in [PAWN_possessive] father's eyes - lower-class jobs.</description>  
    <skillGains>
      <Construction>4</Construction>    
      <Social>1</Social>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>Cleaning, PlantWork</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>StudentEngineer34</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>StudentEngineer34</identifier>  
    <slot>Childhood</slot>  
    <title>student engineer</title>  
    <titleShort>engineer</titleShort>  
    <description>A genius child, [PAWN_nameDef] was put in a special training program covering aerospace warfare and engineering.

One day, [PAWN_pronoun] tried to befriend a squirrel by giving it a bracelet. The squirrel bit [PAWN_objective], earning [PAWN_objective] the nickname “Squirrel {PAWN_gender ? Boy : Girl}”.

[PAWN_nameDef] came to believe that work involving manual labor is beneath [PAWN_objective].</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Intellectual>4</Intellectual>    
      <Mining>-2</Mining>    
      <Cooking>-2</Cooking>    
      <Artistic>-2</Artistic>    
      <Crafting>4</Crafting>
    </skillGains>  
    <workDisables>ManualDumb</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>StreetKid19</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>StreetKid19</identifier>  
    <slot>Childhood</slot>  
    <title>street kid</title>  
    <titleShort>street kid</titleShort>  
    <description>Growing up on the streets and fields of a rimworld, [PAWN_nameDef]'s [PAWN_possessive] only friend was a dog named Rest. The pair scraped by, finding just enough food to survive.

When [PAWN_pronoun] was 13 years old, a criminal group took [PAWN_objective] under their wing.</description>  
    <skillGains>
      <Melee>3</Melee>    
      <Social>3</Social>
    </skillGains>  
    <workDisables>PlantWork</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>PizzaLover94</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>PizzaLover94</identifier>  
    <slot>Childhood</slot>  
    <title>pizza lover</title>  
    <titleShort>pizza kid</titleShort>  
    <description>[PAWN_nameDef] loved boomrat pizza intensely. Rarely eating anything else, [PAWN_pronoun] was prone to bad health and low oxygen uptake.

[PAWN_pronoun] loved bashing in the skulls of random creatures to see what was inside.</description>  
    <skillGains>
      <Melee>5</Melee>    
      <Cooking>3</Cooking>
    </skillGains>  
    <workDisables>Caring</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Psychopath>0</Psychopath>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>FrontierMarshal54</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>FrontierMarshal54</identifier>  
    <slot>Childhood</slot>  
    <title>frontier marshal</title>  
    <titleShort>marshal</titleShort>  
    <description>[PAWN_nameDef] inherited a sense of duty from [PAWN_possessive] father, and began a law enforcement career on [PAWN_possessive] frontier homeworld.

[PAWN_pronoun] tried to uphold the law with honor, but the corruption and greed of local government officials often discouraged him.</description>  
    <skillGains>
      <Shooting>4</Shooting>    
      <Melee>3</Melee>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>MercenaryRecruit36</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>MercenaryRecruit36</identifier>  
    <slot>Childhood</slot>  
    <title>mercenary recruit</title>  
    <titleShort>mercenary</titleShort>  
    <description>Descended from a long line of mercenaries, [PAWN_nameDef] grew up in a busy trading hub.

[PAWN_possessive] interest in the foreign goods at the market often distracted [PAWN_objective] from [PAWN_possessive] chores and training.</description>  
    <skillGains>
      <Shooting>3</Shooting>    
      <Melee>1</Melee>    
      <Crafting>3</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>AbandonedChild30</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>AbandonedChild30</identifier>  
    <slot>Childhood</slot>  
    <title>abandoned child</title>  
    <titleShort>abandoned</titleShort>  
    <description>[PAWN_nameDef] was a quick witted, funny child. One day, [PAWN_pronoun] wandered off on [PAWN_possessive] wealthy parents, and was ultimately lost in a supposedly unused ore warehouse. Due to cloning technology, [PAWN_possessive] parents decided that the time looking for [PAWN_objective] was better spent on just cloning a better son.</description>  
    <skillGains>
      <Construction>1</Construction>    
      <Shooting>1</Shooting>    
      <Melee>1</Melee>    
      <Social>1</Social>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>Cooking, Cleaning</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>TradersChild62</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>TradersChild62</identifier>  
    <slot>Childhood</slot>  
    <title>traders' child</title>  
    <titleShort>trader</titleShort>  
    <description>[PAWN_nameDef] grew up in a family of nomadic traders, making a living from selling and buying goods on many worlds.

[PAWN_nameDef] learned the customs and methods of trade of many cultures by participating in [PAWN_possessive] parents' business.</description>  
    <skillGains>
      <Construction>-2</Construction>    
      <Plants>-1</Plants>    
      <Social>1</Social>    
      <Crafting>1</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>CorpStudent95</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>CorpStudent95</identifier>  
    <slot>Childhood</slot>  
    <title>corp student</title>  
    <titleShort>student</titleShort>  
    <description>De Dion was bred to become an executive officer for the large space trading corporation that owned [PAWN_possessive] home planet. [PAWN_possessive] family had been working for that corporation for seven generations.

At age six, [PAWN_pronoun] started to study at the corporate academy, where [PAWN_pronoun] became a loyal follower and a ruthless executive.</description>  
    <skillGains>
      <Intellectual>3</Intellectual>    
      <Social>3</Social>    
      <Medicine>-2</Medicine>    
      <Artistic>-3</Artistic>
    </skillGains>  
    <workDisables>Cleaning</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>VRAddict29</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>VRAddict29</identifier>  
    <slot>Childhood</slot>  
    <title>VR addict</title>  
    <titleShort>VR addict</titleShort>  
    <description>[PAWN_nameDef] was a VR head - someone who is permanently jacked into the virtual gaming universe by [PAWN_possessive] spinal plug. [PAWN_pronoun] moved through [PAWN_possessive] daily tasks barely aware of the physical reality around him. This left [PAWN_objective] nearly unable to socialize.</description>  
    <skillGains>
      <Intellectual>2</Intellectual>    
      <Shooting>1</Shooting>    
      <Melee>1</Melee>    
      <Artistic>1</Artistic>    
      <Crafting>1</Crafting>
    </skillGains>  
    <workDisables>Social</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Nerd92</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Nerd92</identifier>  
    <slot>Childhood</slot>  
    <title>nerd</title>  
    <titleShort>nerd</titleShort>  
    <description>[PAWN_nameDef] was curious about everything. While other kids played tag in the suburbs of their industrial city, [PAWN_nameDef] read every book [PAWN_pronoun] could find about technology, robots, and weapons - whatever looked coolest.

[PAWN_possessive] strong French accent kept [PAWN_objective] from making real friends.</description>  
    <skillGains>
      <Construction>1</Construction>    
      <Intellectual>4</Intellectual>    
      <Medicine>2</Medicine>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>Social</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <AnnoyingVoice>0</AnnoyingVoice>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>RebelSlave14</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>RebelSlave14</identifier>  
    <slot>Childhood</slot>  
    <title>rebel slave</title>  
    <titleShort>rebel</titleShort>  
    <description>[PAWN_nameDef] was born into corporate slavery and raised to perform menial tasks for minimum pay. [PAWN_pronoun] was just another cog in the machine. 

[PAWN_nameDef] found [PAWN_possessive] freedom by joining a rebel organization whose goal was to break free from their corporate masters.</description>  
    <skillGains>
      <Construction>1</Construction>    
      <Plants>1</Plants>    
      <Intellectual>-2</Intellectual>    
      <Shooting>2</Shooting>    
      <Social>2</Social>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>Artistic, Mining</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>MedievalPlower14</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>MedievalPlower14</identifier>  
    <slot>Childhood</slot>  
    <title>medieval plower</title>  
    <titleShort>plower</titleShort>  
    <description>[PAWN_nameDef] lived on a planet where kings and queens ruled with little regard for the peasants beneath them. [PAWN_possessive] family owned a large farm, but the king took most of the food it produced. This left [PAWN_nameDef]'s family poor, and unable to pay for [PAWN_possessive] education. Instead, [PAWN_pronoun] was required to work the fields with [PAWN_possessive] parents, and never had time to practice creativity.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Plants>5</Plants>    
      <Intellectual>-1</Intellectual>    
      <Artistic>-2</Artistic>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Industriousness>1</Industriousness>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>SonOfAHuntress75</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>SonOfAHuntress75</identifier>  
    <slot>Childhood</slot>  
    <title>son of a huntress</title>  
    <titleShort>hunter</titleShort>  
    <description>In the mountains of the planet Ticonderoga, [PAWN_nameDef] was raised by [PAWN_possessive] tribal mother. She taught [PAWN_objective] survival skills - trapping, tracking, shooting, skinning, cooking and healing.

[PAWN_possessive] departed father had left behind an old bolt-action rifle, and [PAWN_nameDef] practiced with it every chance [PAWN_pronoun] got.</description>  
    <skillGains>
      <Shooting>2</Shooting>    
      <Melee>2</Melee>    
      <Cooking>2</Cooking>    
      <Medicine>1</Medicine>    
      <Crafting>1</Crafting>
    </skillGains>  
    <workDisables>Intellectual</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <spawnCategories>
      <li>Hunter</li>
    </spawnCategories>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>StudentSocialite89</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>StudentSocialite89</identifier>  
    <slot>Childhood</slot>  
    <title>student socialite</title>  
    <titleShort>socialite</titleShort>  
    <description>[PAWN_nameDef] learned politics in an elite training school for socially gifted students on Kalthas IV.

While [PAWN_pronoun] was naturally adept at reading and manipulating people, [PAWN_pronoun] found no joy in it. [PAWN_pronoun] left the academy several years before graduation.</description>  
    <skillGains>
      <Social>6</Social>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>ManualDumb, ManualSkilled, Caring, Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>AwkwardNerd48</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>AwkwardNerd48</identifier>  
    <slot>Childhood</slot>  
    <title>awkward nerd</title>  
    <titleShort>nerd</titleShort>  
    <description>Always fascinated by machines, [PAWN_nameDef] spent [PAWN_possessive] time studying robotics and holography. [PAWN_pronoun] preferred the company of [PAWN_possessive] scientific creations to that of other people.</description>  
    <skillGains>
      <Intellectual>5</Intellectual>    
      <Melee>-2</Melee>    
      <Social>-2</Social>    
      <Crafting>4</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>MidworldGeek18</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>MidworldGeek18</identifier>  
    <slot>Childhood</slot>  
    <title>midworld geek</title>  
    <titleShort>geek</titleShort>  
    <description>[PAWN_nameDef] grew up in a quiet suburban neighborhood. [PAWN_pronoun] was never a master of social interaction, but [PAWN_possessive] interest in computers blossomed into a true talent at an early age.</description>  
    <skillGains>
      <Intellectual>6</Intellectual>    
      <Social>-3</Social>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>CatHerder4</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>CatHerder4</identifier>  
    <slot>Childhood</slot>  
    <title>cat herder</title>  
    <titleShort>herder</titleShort>  
    <description>[PAWN_nameDef] helped out in [PAWN_possessive] father's cat breeding business, socializing, herding, and feeding hundreds of cats.

When [PAWN_pronoun] refused to clean up after the cats, [PAWN_pronoun] was transferred to the breeding science division.</description>  
    <skillGains>
      <Social>2</Social>    
      <Cooking>3</Cooking>
    </skillGains>  
    <workDisables>Cleaning, Hauling</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Scavenger62</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Scavenger62</identifier>  
    <slot>Childhood</slot>  
    <title>scavenger</title>  
    <titleShort>scavenger</titleShort>  
    <description>[PAWN_nameDef] grew up in one of the few remaining cities on a world ravaged by nuclear war.

[PAWN_pronoun] learned to survive on [PAWN_possessive] own in this post-apocalyptic wasteland by scavenging for technology and supplies.</description>  
    <skillGains>
      <Shooting>2</Shooting>    
      <Melee>2</Melee>    
      <Social>-2</Social>    
      <Cooking>2</Cooking>
    </skillGains>  
    <workDisables>Intellectual, Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>VatgrownAssassin20</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>VatgrownAssassin20</identifier>  
    <slot>Childhood</slot>  
    <title>vatgrown assassin</title>  
    <titleShort>clone</titleShort>  
    <description>[PAWN_nameDef] was not made as a man, but a cold hearted assassin. [PAWN_pronoun] was bio-engineered by scientists, and trained as a killer. [PAWN_pronoun] was bred to have no emotion, no feelings, and no care for who lives, and who dies.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Shooting>4</Shooting>    
      <Melee>1</Melee>
    </skillGains>  
    <workDisables>Caring, Artistic, Cooking, PlantWork</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Psychopath>0</Psychopath>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>UrbworldChild56</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>UrbworldChild56</identifier>  
    <slot>Childhood</slot>  
    <title>urbworld child</title>  
    <titleShort>urbkid</titleShort>  
    <description>[PAWN_nameDef] had an isolated upbringing on an urbworld. [PAWN_possessive] hatred of heights kept [PAWN_objective] indoors often, and made [PAWN_objective] an introverted child.

When [PAWN_pronoun] did go out onto the catwalks of the city, [PAWN_pronoun] enjoyed playing war games, becoming proficient with various simulation weapons.</description>  
    <skillGains>
      <Plants>1</Plants>    
      <Intellectual>1</Intellectual>    
      <Shooting>3</Shooting>    
      <Artistic>1</Artistic>    
      <Crafting>1</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>StarforceCadet79</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>StarforceCadet79</identifier>  
    <slot>Childhood</slot>  
    <title>starforce cadet</title>  
    <titleShort>cadet</titleShort>  
    <description>Raised on Amen-Ti, glitterworld capital of the Star Empire, [PAWN_nameFull] had an easy childhood.

As a youth, [PAWN_nameDef] decided that joining the Starforce would be [PAWN_possessive] way of giving back to the Empire.</description>  
    <skillGains>
      <Intellectual>2</Intellectual>    
      <Mining>-3</Mining>    
      <Social>2</Social>    
      <Medicine>1</Medicine>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Stableboy49</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Stableboy49</identifier>  
    <slot>Childhood</slot>  
    <title>stableboy</title>  
    <titleShort>stableboy</titleShort>  
    <description>[PAWN_nameDef] was a stableboy on a medieval planet, mucking dung almost every day to earn [PAWN_possessive] keep.

By the time [PAWN_pronoun] was exiting childhood, [PAWN_nameDef] had saved up enough money to pay a space trader to take [PAWN_objective] offworld.</description>  
    <skillGains>
      <Mining>2</Mining>    
      <Melee>2</Melee>    
      <Artistic>2</Artistic>    
      <Crafting>1</Crafting>
    </skillGains>  
    <workDisables>Social</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Industriousness>1</Industriousness>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ArtfulDodger78</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ArtfulDodger78</identifier>  
    <slot>Childhood</slot>  
    <title>artful dodger</title>  
    <titleShort>dodger</titleShort>  
    <description>[PAWN_nameDef] grew up on one of the urbworlds and had to fend for [PAWN_objective]self all [PAWN_possessive] life. [PAWN_pronoun] never trusted anyone, relying on [PAWN_possessive] persuasiveness and cunning to survive.

[PAWN_pronoun] soon became skilled with firearms and knives as well as words.</description>  
    <skillGains>
      <Shooting>1</Shooting>    
      <Melee>2</Melee>    
      <Social>3</Social>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>Caring, Artistic, Crafting, Cooking, Cleaning, PlantWork</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Pickpocket82</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Pickpocket82</identifier>  
    <slot>Childhood</slot>  
    <title>pickpocket</title>  
    <titleShort>pickpocket</titleShort>  
    <description>Orphaned and abandoned on the tumultuous streets of [PAWN_possessive] polluted, teeming homeworld, [PAWN_nameDef] survived by deft hand and fleet foot. A natural born thief and trickster, [PAWN_pronoun] rose to prominence early, running the notorious and violent urchin gang 'Doomben Rats'.</description>  
    <skillGains>
      <Melee>2</Melee>    
      <Social>2</Social>    
      <Artistic>2</Artistic>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>Cleaning, PlantWork</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Nimble>0</Nimble>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>DisasterSurvivor65</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>DisasterSurvivor65</identifier>  
    <slot>Childhood</slot>  
    <title>disaster survivor</title>  
    <titleShort>survivor</titleShort>  
    <description>[PAWN_nameDef] was uprooted when marauders attacked [PAWN_possessive] family farm, destroying machinery and killing farmhands and beasts alike.

After the death of all [PAWN_pronoun] knew, [PAWN_pronoun] was left in the ruins to fend for [PAWN_objective]self.</description>  
    <skillGains>
      <Plants>2</Plants>    
      <Melee>3</Melee>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>HistoryStudent12</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>HistoryStudent12</identifier>  
    <slot>Childhood</slot>  
    <title>history student</title>  
    <titleShort>student</titleShort>  
    <description>As a child, [PAWN_nameDef] learned many artistic forms including music and novel writing. As [PAWN_pronoun] grew older, [PAWN_pronoun] began to study legends from [PAWN_possessive] home world - legends of the lizard-like beings at the center of [PAWN_possessive] culture's folklore.</description>  
    <skillGains>
      <Social>2</Social>    
      <Artistic>4</Artistic>    
      <Crafting>1</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>NobleWard61</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>NobleWard61</identifier>  
    <slot>Childhood</slot>  
    <title>noble ward</title>  
    <titleShort>ward</titleShort>  
    <description>[PAWN_nameDef] was adopted by a prominent noble family after being left on their doorstep by [PAWN_possessive] mother.

[PAWN_pronoun] quickly learned secrets that passed between the nobles like cheap wine - of worlds beyond [PAWN_possessive] own, languages, cultures and technologies both new and old.</description>  
    <skillGains>
      <Intellectual>4</Intellectual>    
      <Shooting>-1</Shooting>    
      <Melee>-2</Melee>    
      <Social>3</Social>
    </skillGains>  
    <workDisables>ManualDumb, Firefighting, PlantWork, Mining</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>FeralChild96</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>FeralChild96</identifier>  
    <slot>Childhood</slot>  
    <title>feral child</title>  
    <titleShort>feral</titleShort>  
    <description>Abandoned on an animal planet as a small child with nought but a blanket with a name embroidered on it, [PAWN_nameDef] made [PAWN_objective]self one with the wilderness.

When [PAWN_pronoun] was 13, [PAWN_pronoun] encountered a team of mineral surveyors, who "offered" [PAWN_objective] passage off of the planet.</description>  
    <skillGains>
      <Plants>2</Plants>    
      <Melee>4</Melee>    
      <Crafting>1</Crafting>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>Social, Intellectual</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpoiledChild33</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>SpoiledChild33</identifier>  
    <slot>Childhood</slot>  
    <title>spoiled child</title>  
    <titleShort>spoiled</titleShort>  
    <description>[PAWN_nameDef] grew up the child of an industrious colony manager. In this position, most manual and dangerous tasks were forbidden by [PAWN_possessive] parents, but on the rim everyone has to dirty their hands.</description>  
    <skillGains>
      <Construction>-2</Construction>    
      <Plants>2</Plants>    
      <Mining>-2</Mining>    
      <Social>2</Social>    
      <Cooking>2</Cooking>    
      <Artistic>2</Artistic>
    </skillGains>  
    <workDisables>Firefighting</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>AmateurEngineer3</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>AmateurEngineer3</identifier>  
    <slot>Childhood</slot>  
    <title>amateur engineer</title>  
    <titleShort>engineer</titleShort>  
    <description>[PAWN_nameDef] grew up in a family with a military and engineering background. [PAWN_pronoun] wanted to join the army but [PAWN_possessive] father didn't like the idea, so [PAWN_possessive] interest turned to great flying machines and works of engineering. [PAWN_pronoun] secretly hoped that this would one day lead [PAWN_objective] to a life of galactic exploration.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Intellectual>2</Intellectual>    
      <Mining>1</Mining>    
      <Shooting>1</Shooting>
    </skillGains>  
    <workDisables>Cleaning, PlantWork</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>StreetUrchin45</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>StreetUrchin45</identifier>  
    <slot>Childhood</slot>  
    <title>street urchin</title>  
    <titleShort>street rat</titleShort>  
    <description>Born to a poor family, [PAWN_nameDef] was a sickly child who never quite developed the strength of [PAWN_possessive] siblings.

[PAWN_possessive] parents, unable to afford to provide for [PAWN_objective] and certain [PAWN_pronoun] couldn't pay [PAWN_possessive] own way, threw [PAWN_objective] to the streets. [PAWN_pronoun] learned hard and fast how to survive by any means necessary.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Intellectual>-2</Intellectual>    
      <Mining>2</Mining>    
      <Melee>3</Melee>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Immunity>-1</Immunity>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>WildChild5</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>WildChild5</identifier>  
    <slot>Childhood</slot>  
    <title>wild child</title>  
    <titleShort>wild child</titleShort>  
    <description>[PAWN_nameDef] was abandoned on a toxic world overgrown with hostile plant life. [PAWN_pronoun] became accustomed early to the cold burn of loneliness.

[PAWN_pronoun] learned to fight, but ate [PAWN_possessive] food raw and uncooked.</description>  
    <skillGains>
      <Shooting>4</Shooting>    
      <Melee>6</Melee>    
      <Social>-2</Social>    
      <Cooking>-3</Cooking>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>Caring, Intellectual, Artistic, Cleaning</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Ascetic>0</Ascetic>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>HackerProdigy84</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>HackerProdigy84</identifier>  
    <slot>Childhood</slot>  
    <title>hacker prodigy</title>  
    <titleShort>hacker</titleShort>  
    <description>[PAWN_nameDef] was raised as the only child of wealthy surgeons. After being pressured into accepting a medical apprenticeship, [PAWN_pronoun] snapped. [PAWN_pronoun] seized [PAWN_possessive] inheritance and went underground to learn how to exploit computer networks. [PAWN_pronoun] quickly became one of the most feared hackers on [PAWN_possessive] home planet.</description>  
    <skillGains>
      <Intellectual>7</Intellectual>    
      <Medicine>4</Medicine>
    </skillGains>  
    <workDisables>ManualDumb, Cooking</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>MadScientist47</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>MadScientist47</identifier>  
    <slot>Childhood</slot>  
    <title>mad scientist</title>  
    <titleShort>scientist</titleShort>  
    <description>[PAWN_nameDef] grew up miserable on the plains of a rimworld. [PAWN_pronoun] found the strength to fight under the teachings of an old, broken scientist.

[PAWN_pronoun] quickly learned to play with people's fears, and was nicknamed 'mad scientist'.</description>  
    <skillGains>
      <Intellectual>4</Intellectual>    
      <Melee>1</Melee>    
      <Social>2</Social>
    </skillGains>  
    <workDisables>Cleaning</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Psychopath>0</Psychopath>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>YouthDelinquent30</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>YouthDelinquent30</identifier>  
    <slot>Childhood</slot>  
    <title>youth delinquent</title>  
    <titleShort>delinquent</titleShort>  
    <description>[PAWN_nameDef] was the only child of celebrity sommeliers. [PAWN_possessive] parents expected [PAWN_objective] to take up the family profession, but after [PAWN_possessive] third admission to a youth detention center, this career path became unlikely.

While in detention, [PAWN_nameDef] became skilled at making shanks and developed an interest in ornithology.</description>  
    <skillGains>
      <Shooting>3</Shooting>    
      <Melee>1</Melee>    
      <Social>-1</Social>    
      <Crafting>4</Crafting>
    </skillGains>  
    <workDisables>Caring, Artistic, Cleaning</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>HillbillyProdigy60</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>HillbillyProdigy60</identifier>  
    <slot>Childhood</slot>  
    <title>hillbilly prodigy</title>  
    <titleShort>hillbilly</titleShort>  
    <description>Born on a cold rimworld inhabited mainly by furred xenohumans, [PAWN_nameDef] grew up to the sound of bar fights and shootouts. Though [PAWN_possessive] childhood was spent playing the banjo and working as a mechanic, [PAWN_possessive] abnormal intelligence made [PAWN_objective] want something more; [PAWN_pronoun] entered medical school at a young age.</description>  
    <skillGains>
      <Intellectual>2</Intellectual>    
      <Melee>2</Melee>    
      <Medicine>2</Medicine>
    </skillGains>  
    <workDisables>Social, Artistic, Cleaning</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>UrbworldUrchin90</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>UrbworldUrchin90</identifier>  
    <slot>Childhood</slot>  
    <title>urbworld urchin</title>  
    <titleShort>urchin</titleShort>  
    <description>Abandoned among the filthy concrete spires, [PAWN_nameDef] never knew [PAWN_possessive] parents. [PAWN_pronoun] never received formal education, and often resorted to doing menial labor in exchange for food. Sometimes [PAWN_pronoun] lied, cheated, and stole to survive.</description>  
    <skillGains>
      <Mining>4</Mining>    
      <Melee>2</Melee>
    </skillGains>  
    <workDisables>Artistic, Crafting</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>MidworldSketcher71</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>MidworldSketcher71</identifier>  
    <slot>Childhood</slot>  
    <title>midworld sketcher</title>  
    <titleShort>sketcher</titleShort>  
    <description>[PAWN_nameDef] was born into a loving but poor family.

[PAWN_pronoun] discovered [PAWN_pronoun] had an artistic talent which [PAWN_pronoun] inherited from [PAWN_possessive] father. [PAWN_possessive] mother taught [PAWN_objective] the benefits of hard work and determination. As [PAWN_pronoun] grew older, [PAWN_nameDef] developed a fascination with technology and military history.</description>  
    <skillGains>
      <Shooting>2</Shooting>    
      <Melee>1</Melee>    
      <Artistic>3</Artistic>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>CoreworldStudent50</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>CoreworldStudent50</identifier>  
    <slot>Childhood</slot>  
    <title>coreworld student</title>  
    <titleShort>student</titleShort>  
    <description>Born a male named Alex, [PAWN_nameDef] didn't fit in well with the other boys. When very young, [PAWN_pronoun] preferred creative pursuits, such as crafting jewelry, over sports. [PAWN_pronoun] felt [PAWN_pronoun] had the wrong body. Luckily, [PAWN_possessive] family and friends supported [PAWN_possessive] sex reassignment; as a teen, [PAWN_pronoun] underwent surgery to become a female.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Shooting>-1</Shooting>    
      <Artistic>4</Artistic>    
      <Crafting>4</Crafting>
    </skillGains>  
    <workDisables>Cooking, PlantWork</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>MedievalSquire5</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>MedievalSquire5</identifier>  
    <slot>Childhood</slot>  
    <title>medieval squire</title>  
    <titleShort>squire</titleShort>  
    <description>[PAWN_nameDef] grew up on a medieval planet as a knight in training. [PAWN_pronoun] trained directly under the king's war adviser for most of [PAWN_possessive] youth, and learned to fight with a sword and shield.</description>  
    <skillGains>
      <Construction>-1</Construction>    
      <Mining>-1</Mining>    
      <Shooting>3</Shooting>    
      <Melee>5</Melee>
    </skillGains>  
    <workDisables>Cooking, PlantWork</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>StarSquire9</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>StarSquire9</identifier>  
    <slot>Childhood</slot>  
    <title>star squire</title>  
    <titleShort>squire</titleShort>  
    <description>Born on a medieval world, [PAWN_nameDef]'s first memory is of gazing up into the night's sky. 

Ever since, [PAWN_pronoun] dreamed of squiring for a knight amidst the star-filled heavens. [PAWN_possessive] focus on that vision left [PAWN_objective] somewhat single-minded.</description>  
    <skillGains>
      <Shooting>2</Shooting>    
      <Melee>3</Melee>
    </skillGains>  
    <workDisables>Animals, Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>DrugMule80</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>DrugMule80</identifier>  
    <slot>Childhood</slot>  
    <title>drug mule</title>  
    <titleShort>mule</titleShort>  
    <description>Raised in an orphanage, [PAWN_nameDef] turned towards the gang life at age ten.

[PAWN_pronoun] was exploited by the local gang as a drug mule to traffic "substance F" across rival gang territory. [PAWN_pronoun] took a bullet or two.</description>  
    <skillGains>
      <Shooting>2</Shooting>    
      <Melee>3</Melee>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>Caring, Social, Intellectual, Artistic, Cleaning, PlantWork</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ArtisanFarmer23</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ArtisanFarmer23</identifier>  
    <slot>Childhood</slot>  
    <title>artisan farmer</title>  
    <titleShort>farmer</titleShort>  
    <description>[PAWN_nameDef] grew up on a [PAWN_possessive] family's glitterworld farm. They were one of the last farms on the planet that preferred traditional farming methods to glitterworld technology.</description>  
    <skillGains>
      <Plants>7</Plants>    
      <Intellectual>-2</Intellectual>    
      <Cooking>2</Cooking>
    </skillGains>  
    <workDisables>Violent</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <spawnCategories>
      <li>Farmer</li>
    </spawnCategories>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Pampered87</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Pampered87</identifier>  
    <slot>Childhood</slot>  
    <title>pampered</title>  
    <titleShort>pampered</titleShort>  
    <description>Born on a decadent glitterworld, [PAWN_nameDef] was given every expensive toy.

This pampered lifestyle caused [PAWN_objective] to miss many basic life lessons. [PAWN_pronoun] developed a special aversion to cooking, and always ordered the staff to do the kitchen work.</description>  
    <skillGains>
      <Intellectual>2</Intellectual>    
      <Melee>-3</Melee>    
      <Social>3</Social>    
      <Medicine>3</Medicine>
    </skillGains>  
    <workDisables>Cooking</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>MilitaryChild82</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>MilitaryChild82</identifier>  
    <slot>Childhood</slot>  
    <title>military child</title>  
    <titleShort>military</titleShort>  
    <description>[PAWN_nameDef] grew up in a family with a rich military background. [PAWN_possessive] teenage years were spent traveling system to system wherever [PAWN_possessive] parents were deployed.

Following the tradition of [PAWN_possessive] family, [PAWN_pronoun] enlisted at the earliest possible age.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Shooting>3</Shooting>    
      <Melee>1</Melee>    
      <Medicine>2</Medicine>    
      <Artistic>-3</Artistic>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>GangMember83</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>GangMember83</identifier>  
    <slot>Childhood</slot>  
    <title>gang member</title>  
    <titleShort>ganger</titleShort>  
    <description>[PAWN_nameDef] grew up homeless on an urbworld. [PAWN_pronoun] was forced to fight and struggle for everything, making [PAWN_objective] hard and ruthless long before adulthood.

[PAWN_nameDef] would have stayed in those streets, had [PAWN_pronoun] not been injured in a shootout and had [PAWN_possessive] body confiscated for use by one of the worldwide city's ruling corporations.</description>  
    <skillGains>
      <Plants>-2</Plants>    
      <Shooting>2</Shooting>    
      <Melee>5</Melee>
    </skillGains>  
    <workDisables>Social, Intellectual</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>TestSubject82</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>TestSubject82</identifier>  
    <slot>Childhood</slot>  
    <title>test subject</title>  
    <titleShort>subject</titleShort>  
    <description>Test subject #11,529,914 of experiment #56,048 spent [PAWN_possessive] childhood in white rooms performing mental and physical tests.

[PAWN_possessive] only companion was a voice that called itself Mother. Mother rewarded success with praise - and failure with harsh punishment.</description>  
    <skillGains>
      <Intellectual>2</Intellectual>    
      <Melee>2</Melee>    
      <Cooking>1</Cooking>
    </skillGains>  
    <workDisables>Social, PlantWork, Mining</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>FireScarredChild1</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>FireScarredChild1</identifier>  
    <slot>Childhood</slot>  
    <title>fire-scarred child</title>  
    <titleShort>scarred</titleShort>  
    <description>[PAWN_nameDef] was an active child who lived an uneventful childhood until [PAWN_pronoun] fell into a fire and suffered horrific burns to [PAWN_possessive] hands and arms. Although the scars have faded, [PAWN_pronoun] can't bear to be in close proximity to fire.</description>  
    <skillGains>
      <Mining>1</Mining>    
      <Melee>1</Melee>
    </skillGains>  
    <workDisables>Cooking, Firefighting</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>TechEnthusiast28</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>TechEnthusiast28</identifier>  
    <slot>Childhood</slot>  
    <title>tech enthusiast</title>  
    <titleShort>tech nerd</titleShort>  
    <description>[PAWN_nameDef] had all the latest gadgets as a child. [PAWN_pronoun] spent all [PAWN_possessive] time playing games with AIs instead of with other children. [PAWN_pronoun] always felt awkward around new people.</description>  
    <skillGains>
      <Intellectual>4</Intellectual>    
      <Shooting>4</Shooting>    
      <Social>-3</Social>
    </skillGains>  
    <workDisables>PlantWork</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>DiplomatsChild97</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>DiplomatsChild97</identifier>  
    <slot>Childhood</slot>  
    <title>diplomat's child</title>  
    <titleShort>diplomat</titleShort>  
    <description>[PAWN_nameDef] was born in diplomatic family.

Travelling often, [PAWN_pronoun] was home-schooled and saw the world mostly through a computer.</description>  
    <skillGains>
      <Intellectual>1</Intellectual>    
      <Artistic>1</Artistic>
    </skillGains>  
    <workDisables>Violent, Social</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Winerunner8</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Winerunner8</identifier>  
    <slot>Childhood</slot>  
    <title>winerunner</title>  
    <titleShort>winerunner</titleShort>  
    <description>Abandoned by [PAWN_possessive] parents, [PAWN_nameDef] was raised by the monks of the Novo Mosteiro dos Jerónimos monastery on Aracena VI.

The monks taught [PAWN_objective] discipline and hard work. In return, [PAWN_pronoun] would go into the city and 'acquire' exotic wines for the monks' nightly tastings.</description>  
    <skillGains>
      <Melee>2</Melee>    
      <Social>2</Social>    
      <Cooking>2</Cooking>    
      <Crafting>2</Crafting>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Industriousness>1</Industriousness>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>StreetChild4</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>StreetChild4</identifier>  
    <slot>Childhood</slot>  
    <title>street child</title>  
    <titleShort>street</titleShort>  
    <description>[PAWN_nameDef]'s drug-addicted mother died, leaving [PAWN_objective] an orphan at a young age. To stay alive, [PAWN_pronoun] resorted to theft, and when necessary, prostitution. When [PAWN_nameDef] was caught stealing from a church, the priestess saw a wounded but smart child, and took [PAWN_objective] in to give [PAWN_objective] a second chance at life.</description>  
    <skillGains>
      <Social>3</Social>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>SlaveFarmer99</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>SlaveFarmer99</identifier>  
    <slot>Childhood</slot>  
    <title>slave farmer</title>  
    <titleShort>slave</titleShort>  
    <description>Abandoned as a little girl, [PAWN_nameDef] was found by a farmer. Being infertile [PAWN_objective]self, the farmer kept [PAWN_objective] as a slave. The constant beatings left [PAWN_objective] scarred and unable to handle violence. After many years, [PAWN_pronoun] ran away.</description>  
    <skillGains>
      <Plants>4</Plants>
    </skillGains>  
    <workDisables>Violent</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <spawnCategories>
      <li>Farmer</li>
    </spawnCategories>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>MedievalSlave50</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>MedievalSlave50</identifier>  
    <slot>Childhood</slot>  
    <title>medieval slave</title>  
    <titleShort>slave</titleShort>  
    <description>Born into slavery, [PAWN_nameDef] never knew [PAWN_possessive] parents. [PAWN_pronoun] eventually escaped from [PAWN_possessive] masters and boarded a military cargo ship, where [PAWN_pronoun] was pressed into military service.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Plants>3</Plants>    
      <Mining>1</Mining>    
      <Cooking>2</Cooking>    
      <Artistic>-2</Artistic>
    </skillGains>  
    <workDisables>Intellectual</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>FoundryApprentice76</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>FoundryApprentice76</identifier>  
    <slot>Childhood</slot>  
    <title>foundry apprentice</title>  
    <titleShort>foundryman</titleShort>  
    <description>[PAWN_nameDef] grew up as an apprentice in the foundries of an industrial world. This experience gave [PAWN_objective] metalworking skills and strong muscles, but stunted [PAWN_possessive] artistic development.</description>  
    <skillGains>
      <Construction>3</Construction>    
      <Melee>1</Melee>    
      <Artistic>-2</Artistic>    
      <Crafting>3</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>PrivilegedProdigy70</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>PrivilegedProdigy70</identifier>  
    <slot>Childhood</slot>  
    <title>privileged prodigy</title>  
    <titleShort>prodigy</titleShort>  
    <description>[PAWN_nameDef] was recognized as gifted early in [PAWN_possessive] glitterworld upbringing. Luckily, [PAWN_possessive] family was in a position to cultivate that genius through advanced education. [PAWN_possessive] training included science, leadership, and the arts.</description>  
    <skillGains>
      <Intellectual>2</Intellectual>    
      <Medicine>1</Medicine>    
      <Artistic>2</Artistic>
    </skillGains>  
    <workDisables>ManualDumb, Violent, Hauling, PlantWork, Mining</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>PrivilegedChild86</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>PrivilegedChild86</identifier>  
    <slot>Childhood</slot>  
    <title>privileged child</title>  
    <titleShort>privileged</titleShort>  
    <description>Born to an upper-class family, [PAWN_nameDef] grew up with all the best things - the best education, the best social contacts, and of course the best technology money could buy.

Unfortunately, living in such decadence left [PAWN_nameDef] rather spoiled when it came to labor.</description>  
    <skillGains>
      <Social>3</Social>
    </skillGains>  
    <workDisables>ManualDumb</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ChildProdigy76</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ChildProdigy76</identifier>  
    <slot>Childhood</slot>  
    <title>child prodigy</title>  
    <titleShort>prodigy</titleShort>  
    <description>After graduating college at age twelve, [PAWN_nameDef] devoted [PAWN_possessive] life to becoming immortal. However [PAWN_possessive] research crossed the line when [PAWN_pronoun] tried to modify [PAWN_possessive] body using gene therapy and mechanical augmentation. [PAWN_pronoun] was arrested and condemned to a prison planet.</description>  
    <skillGains>
      <Intellectual>6</Intellectual>    
      <Mining>-3</Mining>    
      <Cooking>-3</Cooking>    
      <Medicine>6</Medicine>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>BoneCollector14</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>BoneCollector14</identifier>  
    <slot>Childhood</slot>  
    <title>bone collector</title>  
    <titleShort>pupil</titleShort>  
    <description>Born to a family of fortune hunters, [PAWN_nameDef] always had a passion for ancient history.

Though never a tough or social boy, [PAWN_pronoun] loved to dig through history books as well as dirt piles.</description>  
    <skillGains>
      <Plants>2</Plants>    
      <Mining>2</Mining>    
      <Shooting>-3</Shooting>    
      <Melee>-3</Melee>    
      <Social>-3</Social>    
      <Medicine>2</Medicine>    
      <Artistic>2</Artistic>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>GNomeSculptor34</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>GNomeSculptor34</identifier>  
    <slot>Childhood</slot>  
    <title>g-nome sculptor</title>  
    <titleShort>sculptor</titleShort>  
    <description>[PAWN_nameDef] was engineered in a lab as part of the G-nome Project. [PAWN_pronoun] was implanted at birth with encyclopedic knowledge of all aspects of xenobiology.

The G-nome scientists nicknamed [PAWN_objective] [PAWN_nameDef] and occupied [PAWN_objective] with sculpting. [PAWN_pronoun] developed a love of sculpting gnomes and gnome accessories.</description>  
    <skillGains>
      <Intellectual>2</Intellectual>    
      <Social>2</Social>    
      <Artistic>4</Artistic>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>Cooking, Cleaning</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>CircusPerformer37</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>CircusPerformer37</identifier>  
    <slot>Childhood</slot>  
    <title>circus performer</title>  
    <titleShort>performer</titleShort>  
    <description>Growing up in the circus, [PAWN_nameDef] learned a lot of interesting things. More interesting than the balls [PAWN_pronoun] juggled were the pockets [PAWN_pronoun] picked between shows.

A mistake with some firesticks made [PAWN_objective] develop a deathly fear of fire.</description>  
    <skillGains>
      <Social>3</Social>    
      <Artistic>2</Artistic>
    </skillGains>  
    <workDisables>Firefighting</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>VatgrownSlavegirl8</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>VatgrownSlavegirl8</identifier>  
    <slot>Childhood</slot>  
    <title>vatgrown slavegirl</title>  
    <titleShort>slave girl</titleShort>  
    <description>[PAWN_nameDef] was vat-grown as a slave in an illegal urbworld laboratory. As a young child, [PAWN_pronoun] was taught only to cook, clean and serve [PAWN_possessive] masters. For most of [PAWN_possessive] childhood, [PAWN_pronoun] knew nothing else.</description>  
    <skillGains>
      <Cooking>5</Cooking>    
      <Artistic>-2</Artistic>
    </skillGains>  
    <workDisables>Violent, Intellectual</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>GunKid30</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>GunKid30</identifier>  
    <slot>Childhood</slot>  
    <title>gun kid</title>  
    <titleShort>gun kid</titleShort>  
    <description>Growing up in a urbworld, [PAWN_nameDef] never had it easy. Every day was a struggle and pollution, hunger, and gangs of older kids.

In the little space [PAWN_pronoun] had to [PAWN_objective]self, [PAWN_pronoun] studied [PAWN_possessive] passion - guns, combat tactics, and war history.</description>  
    <skillGains>
      <Shooting>3</Shooting>    
      <Melee>3</Melee>    
      <Artistic>-3</Artistic>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>FeudalLordling56</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>FeudalLordling56</identifier>  
    <slot>Childhood</slot>  
    <title>feudal lordling</title>  
    <titleShort>lordling</titleShort>  
    <description>[PAWN_nameDef] grew up on a feudal world that was part of a multi-planet empire. As the son of a high lord, [PAWN_pronoun] enjoyed many privileges.

[PAWN_pronoun] learned to maneuver in both the political landscape and in close quarters combat.</description>  
    <skillGains>
      <Melee>4</Melee>    
      <Social>3</Social>
    </skillGains>  
    <workDisables>ManualDumb</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>FeudalFarmBoy0</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>FeudalFarmBoy0</identifier>  
    <slot>Childhood</slot>  
    <title>feudal farm boy</title>  
    <titleShort>slave</titleShort>  
    <description>[PAWN_nameDef] was the son of a medieval farmer, and was expected to follow in [PAWN_possessive] footsteps. [PAWN_pronoun] was brought up as a kind, well trained farm boy.

[PAWN_pronoun] lived in an unusual feudal kingdom which co-existed with a midworld society which was itself well-known for genetic manipulation.</description>  
    <skillGains>
      <Plants>4</Plants>    
      <Social>2</Social>
    </skillGains>  
    <workDisables>Intellectual</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpaceFanboy22</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>SpaceFanboy22</identifier>  
    <slot>Childhood</slot>  
    <title>space fanboy</title>  
    <titleShort>fanboy</titleShort>  
    <description>[PAWN_nameDef] dreamed of space travel. [PAWN_pronoun] built [PAWN_possessive] own pretend spaceship from an old police box and went to see the great unknown.</description>  
    <skillGains>
      <Intellectual>2</Intellectual>    
      <Crafting>3</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>GlitterworldNerd20</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>GlitterworldNerd20</identifier>  
    <slot>Childhood</slot>  
    <title>glitterworld nerd</title>  
    <titleShort>nerd</titleShort>  
    <description>[PAWN_nameDef] loved technology from the day [PAWN_pronoun] was born. [PAWN_pronoun] had a mechanoid companion which [PAWN_pronoun] tried to modify.

[PAWN_possessive] obsession with technology meant that [PAWN_pronoun] never appreciated arts or culture.</description>  
    <skillGains>
      <Intellectual>5</Intellectual>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>ManualDumb, Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>WarChild24</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>WarChild24</identifier>  
    <slot>Childhood</slot>  
    <title>war child</title>  
    <titleShort>war child</titleShort>  
    <description>[PAWN_nameDef]'s parents were drafted into a civil war. Growing up surrounded by fighting and death, [PAWN_pronoun] became well-accustomed to tragedy. [PAWN_possessive] expressive side, however, never developed.</description>  
    <skillGains>
      <Shooting>5</Shooting>    
      <Melee>3</Melee>
    </skillGains>  
    <workDisables>Caring, Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>UpperUrbworlder12</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>UpperUrbworlder12</identifier>  
    <slot>Childhood</slot>  
    <title>upper urbworlder</title>  
    <titleShort>high urber</titleShort>  
    <description>[PAWN_nameDef] had a privileged childhood on a major urbworld. Living in an apartment far above the dense poverty of the city, [PAWN_pronoun] spent most of [PAWN_possessive] time reading or drawing the cityscape [PAWN_pronoun] saw through [PAWN_possessive] window.</description>  
    <skillGains>
      <Construction>-3</Construction>    
      <Intellectual>3</Intellectual>    
      <Artistic>2</Artistic>    
      <Crafting>-3</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>IdealisticCadet64</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>IdealisticCadet64</identifier>  
    <slot>Childhood</slot>  
    <title>idealistic cadet</title>  
    <titleShort>cadet</titleShort>  
    <description>Raised in the military traditions of [PAWN_possessive] forefathers, [PAWN_nameDef] was taught from a young age that [PAWN_pronoun] would be a great leader and the hero of [PAWN_possessive] family.

[PAWN_pronoun] excelled at [PAWN_possessive] studies and graduated from the academy with honors.</description>  
    <skillGains>
      <Shooting>4</Shooting>    
      <Melee>3</Melee>    
      <Social>-2</Social>
    </skillGains>  
    <workDisables>Mining</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ChildOfGlass61</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ChildOfGlass61</identifier>  
    <slot>Childhood</slot>  
    <title>child of glass</title>  
    <titleShort>survivor</titleShort>  
    <description>[PAWN_nameDef] was raised underground on a "marble" planet - a wasteland of radioactive asphalt and toxic fallout.

To survive on a planet devoid of life, [PAWN_pronoun] had to learn how to farm underground and how to fight without wasting bullets.</description>  
    <skillGains>
      <Plants>3</Plants>    
      <Shooting>4</Shooting>    
      <Artistic>-3</Artistic>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>Social, Animals</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <ShootingAccuracy>1</ShootingAccuracy>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>AmateurBotanist79</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>AmateurBotanist79</identifier>  
    <slot>Childhood</slot>  
    <title>amateur botanist</title>  
    <titleShort>botanist</titleShort>  
    <description>Spending many summers crawling through dirt, [PAWN_nameDef] found [PAWN_pronoun] had quite the green thumb. Instead of learning to cook the food [PAWN_pronoun] grew, [PAWN_pronoun] just grew more and more.</description>  
    <skillGains>
      <Plants>4</Plants>    
      <Artistic>2</Artistic>
    </skillGains>  
    <workDisables>Cooking</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ChildSlave58</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ChildSlave58</identifier>  
    <slot>Childhood</slot>  
    <title>child slave</title>  
    <titleShort>slave</titleShort>  
    <description>[PAWN_nameDef] was born to a poor family on a rimworld. [PAWN_possessive] father sold [PAWN_objective] into slavery at a young age. [PAWN_pronoun] was traded many times. A tycoon bought [PAWN_objective] and sent [PAWN_objective] down into mines to look after machines and to work alongside them.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Mining>3</Mining>    
      <Social>-2</Social>
    </skillGains>  
    <workDisables>Intellectual</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>AdventuringChild30</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>AdventuringChild30</identifier>  
    <slot>Childhood</slot>  
    <title>adventuring child</title>  
    <titleShort>adventurer</titleShort>  
    <description>[PAWN_nameDef] was raised to become an engineer. For years, [PAWN_pronoun] planned to stow away on a cargo ship and start a new, more adventuresome life.

One night, after [PAWN_possessive] parents fell asleep, [PAWN_pronoun] managed to sneak on board a cargo ship just before it left port.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Crafting>3</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>VideoGamer55</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>VideoGamer55</identifier>  
    <slot>Childhood</slot>  
    <title>video gamer</title>  
    <titleShort>gamer</titleShort>  
    <description>[PAWN_nameDef] was raised in a glitterworld. Everything [PAWN_pronoun] wanted came to him. In school [PAWN_pronoun] was a social butterfly. At home, [PAWN_pronoun] spent [PAWN_possessive] time looking up the newest gadgets and playing [PAWN_possessive] favourite video games.</description>  
    <skillGains>
      <Intellectual>3</Intellectual>    
      <Social>3</Social>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>DreadedBaby56</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>DreadedBaby56</identifier>  
    <slot>Childhood</slot>  
    <title>dreaded baby</title>  
    <titleShort>baby dude</titleShort>  
    <description>[PAWN_nameDef] was found as a baby in a crashed spacecraft. According to the ship's records, [PAWN_pronoun] invented the nuclear device that powered [PAWN_possessive] ship. Apparently, [PAWN_pronoun] also invented the sky and the hamburger, and is said to have fathered a race of tasty golden kittens.</description>  
    <skillGains>
      <Shooting>1</Shooting>    
      <Cooking>3</Cooking>    
      <Artistic>3</Artistic>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Tinkerer79</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Tinkerer79</identifier>  
    <slot>Childhood</slot>  
    <title>tinkerer</title>  
    <titleShort>tinkerer</titleShort>  
    <description>[PAWN_nameDef] grew up on a scrap-heap of a planet - the garbage dump of other planets in the system. [PAWN_pronoun] learned to make new machines from the dead parts of old ones. With some friends, [PAWN_pronoun] eventually escaped on a salvaged and repaired ship.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Crafting>4</Crafting>
    </skillGains>  
    <workDisables>PlantWork</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>DisplacedNoble67</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>DisplacedNoble67</identifier>  
    <slot>Childhood</slot>  
    <title>displaced noble</title>  
    <titleShort>noble</titleShort>  
    <description>Born on an urbworld to the ruler of three megacities, [PAWN_nameDef]'s prospects looked good - until a violent insurrection overthrew [PAWN_possessive] family. Exile introduced [PAWN_nameDef] to hard work and combat, but some high-born squeamishness remains.</description>  
    <skillGains>
      <Intellectual>1</Intellectual>    
      <Shooting>3</Shooting>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>ManualDumb</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ChessMaster67</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ChessMaster67</identifier>  
    <slot>Childhood</slot>  
    <title>chess master</title>  
    <titleShort>chesshead</titleShort>  
    <description>[PAWN_nameDef] loved to play chess. [PAWN_pronoun] even earned the nickname [PAWN_nameDef] for some of [PAWN_possessive] craftier moves.

[PAWN_pronoun] never got into trouble - mostly because [PAWN_pronoun] was good at not getting caught.</description>  
    <skillGains>
      <Intellectual>2</Intellectual>    
      <Social>4</Social>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Orphan15</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Orphan15</identifier>  
    <slot>Childhood</slot>  
    <title>orphan</title>  
    <titleShort>orphan</titleShort>  
    <description>Born on a deep space station, [PAWN_nameDef] was orphaned at a young age. In the orphanage, [PAWN_pronoun] stood up for the weaker kids and fought off the bullies.</description>  
    <skillGains>
      <Melee>4</Melee>    
      <Medicine>2</Medicine>
    </skillGains>  
    <workDisables>Crafting</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Kind>0</Kind>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Straggler71</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Straggler71</identifier>  
    <slot>Childhood</slot>  
    <title>straggler</title>  
    <titleShort>straggler</titleShort>  
    <description>Despite being blessed with parental love, [PAWN_nameDef] hardly knew [PAWN_possessive] parents.

[PAWN_pronoun] was frequently left to [PAWN_possessive] own devices which meant more time devoted to exploring [PAWN_possessive] interests... and getting into trouble.</description>  
    <skillGains>
      <Intellectual>-3</Intellectual>    
      <Melee>2</Melee>    
      <Artistic>2</Artistic>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>Caring, Social</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>BrutalThief59</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>BrutalThief59</identifier>  
    <slot>Childhood</slot>  
    <title>brutal thief</title>  
    <titleShort>thief</titleShort>  
    <description>Growing up in a gang, [PAWN_nameDef] learned the brutality of the streets.

Knowing that showing weakness could be fatal, [PAWN_pronoun] closed [PAWN_objective]self off from others.</description>  
    <skillGains>
      <Plants>-2</Plants>    
      <Shooting>2</Shooting>    
      <Melee>4</Melee>    
      <Social>-2</Social>
    </skillGains>  
    <workDisables>Caring, Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Psychopath>0</Psychopath>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>BoySoldier14</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>BoySoldier14</identifier>  
    <slot>Childhood</slot>  
    <title>boy soldier</title>  
    <titleShort>soldier</titleShort>  
    <description>War may never change - but the cast of characters does.

Born on a violent urbworld, [PAWN_nameDef] was trained from a young age to fight the wars of others, and became rather good at it.</description>  
    <skillGains>
      <Shooting>4</Shooting>    
      <Melee>2</Melee>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ApocalypseChild23</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ApocalypseChild23</identifier>  
    <slot>Childhood</slot>  
    <title>apocalypse child</title>  
    <titleShort>apocalypse</titleShort>  
    <description>[PAWN_nameDef] spent [PAWN_possessive] childhood on a post-apocalyptic world. [PAWN_pronoun] fought for survival from a young age, on a planet where trust did not exist.

[PAWN_pronoun] told everyone that [PAWN_pronoun] had no luck.</description>  
    <skillGains>
      <Shooting>2</Shooting>    
      <Melee>3</Melee>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>Intellectual</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>OrphanOfWar60</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>OrphanOfWar60</identifier>  
    <slot>Childhood</slot>  
    <title>orphan of war</title>  
    <titleShort>orphan</titleShort>  
    <description>[PAWN_nameDef] lost [PAWN_possessive] family and home early in life. [PAWN_possessive] life became emotionally hollow, but [PAWN_possessive] painful memories drove [PAWN_objective] to survive.

[PAWN_pronoun] took several jobs, but never achieved more than a basic education.</description>  
    <skillGains>
      <Construction>3</Construction>    
      <Intellectual>-3</Intellectual>    
      <Mining>3</Mining>    
      <Cooking>3</Cooking>
    </skillGains>  
    <workDisables>Social</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ImperialStudent49</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ImperialStudent49</identifier>  
    <slot>Childhood</slot>  
    <title>imperial student</title>  
    <titleShort>student</titleShort>  
    <description>[PAWN_nameDef] studied on an imperial midworld where guns were banned and self-defense study was encouraged. The relative safety allowed [PAWN_objective] to focus on [PAWN_possessive] botanical research.</description>  
    <skillGains>
      <Plants>2</Plants>    
      <Intellectual>3</Intellectual>    
      <Shooting>-3</Shooting>    
      <Melee>2</Melee>
    </skillGains>  
    <workDisables>Crafting, Firefighting, Cleaning</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>PowerMadScholar30</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>PowerMadScholar30</identifier>  
    <slot>Childhood</slot>  
    <title>power-mad scholar</title>  
    <titleShort>scholar</titleShort>  
    <description>"Knowledge is power." These words sparked something within [PAWN_nameDef], and came to form [PAWN_possessive] core drive. When not entombed within libraries, [PAWN_pronoun] would take things apart to see first-hand how they worked, with little regard for the border between mechanical and organic.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Intellectual>3</Intellectual>    
      <Social>-3</Social>    
      <Crafting>3</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <FastLearner>0</FastLearner>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>TechHead8</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>TechHead8</identifier>  
    <slot>Childhood</slot>  
    <title>tech-head</title>  
    <titleShort>tech-head</titleShort>  
    <description>Standing on the ship hull with [PAWN_possessive] father in an child-size EVA suit, [PAWN_nameDef] loved space from a young age.

[PAWN_pronoun] was put in a Civil Academics Program. While pretending to be like [PAWN_possessive] father, [PAWN_pronoun] spent [PAWN_possessive] time taking care of plants in [PAWN_possessive] messy room. [PAWN_possessive] nickname was given for [PAWN_possessive] love of circles.</description>  
    <skillGains>
      <Plants>2</Plants>    
      <Intellectual>6</Intellectual>    
      <Social>-1</Social>
    </skillGains>  
    <workDisables>Cleaning</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Orphan25</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Orphan25</identifier>  
    <slot>Childhood</slot>  
    <title>orphan</title>  
    <titleShort>orphan</titleShort>  
    <description>Left alone in the world without parents, [PAWN_nameDef] did the best [PAWN_pronoun] could to adapt.

Having to take care both of [PAWN_objective]self and younger kids at the orphanage, [PAWN_pronoun] learned a lot about humans and how to interact with them.</description>  
    <skillGains>
      <Social>6</Social>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Abductee43</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Abductee43</identifier>  
    <slot>Childhood</slot>  
    <title>abductee</title>  
    <titleShort>abductee</titleShort>  
    <description>[PAWN_nameDef] was abducted at a young age and forced to do menial labor on a pirate ship.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Melee>2</Melee>    
      <Cooking>2</Cooking>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>BlackjackPlayer76</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>BlackjackPlayer76</identifier>  
    <slot>Childhood</slot>  
    <title>blackjack player</title>  
    <titleShort>gambler</titleShort>  
    <description>The only useful skill [PAWN_nameDef] learned from [PAWN_possessive] tough military school was card-counting. [PAWN_pronoun] amassed many enemies by cheating at casinos, becoming ever more edgy and violent as the threat of retaliation grew.

When [PAWN_pronoun] was finally caught, they burned half [PAWN_possessive] skin off. [PAWN_pronoun] could never face fire or violence again.</description>  
    <skillGains>
      <Intellectual>5</Intellectual>    
      <Social>4</Social>
    </skillGains>  
    <workDisables>Violent, Firefighting</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>AcademyStudent58</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>AcademyStudent58</identifier>  
    <slot>Childhood</slot>  
    <title>academy student</title>  
    <titleShort>student</titleShort>  
    <description>[PAWN_nameDef] went to school like a good space cadet would. [PAWN_pronoun] was very tall and was known for [PAWN_possessive] good looks.

Though [PAWN_pronoun] worried more about [PAWN_possessive] hairstyle than [PAWN_possessive] grades, [PAWN_pronoun] still somehow passed the final exams.</description>  
    <skillGains>
      <Social>3</Social>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>Cleaning</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>AspiringEngineer58</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>AspiringEngineer58</identifier>  
    <slot>Childhood</slot>  
    <title>aspiring engineer</title>  
    <titleShort>tinkerer</titleShort>  
    <description>[PAWN_nameDef] had a fascination with gadgets and gizmos. [PAWN_pronoun] took apart and put together almost anything [PAWN_pronoun] could find.

[PAWN_pronoun] got used to repairing toys brought to [PAWN_objective] by other children, and even fixed a few devices from [PAWN_possessive] elders.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Intellectual>4</Intellectual>    
      <Mining>2</Mining>    
      <Social>-2</Social>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>MedicalStudent96</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>MedicalStudent96</identifier>  
    <slot>Childhood</slot>  
    <title>medical student</title>  
    <titleShort>student</titleShort>  
    <description>[PAWN_nameDef] grew up on a glitterworld in a family of doctors and chose to maintain the tradition.

[PAWN_pronoun] had few friends, but got along with [PAWN_possessive] siblings very well.</description>  
    <skillGains>
      <Social>1</Social>    
      <Medicine>6</Medicine>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>HighBaroness37</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>HighBaroness37</identifier>  
    <slot>Childhood</slot>  
    <title>high baroness</title>  
    <titleShort>noble</titleShort>  
    <description>[PAWN_nameDef] was born as low-ranking royalty in a large imperial family.

[PAWN_pronoun] was placed In charge of running [PAWN_possessive] home planet from an early age, and learned important political and cultural skills in that role.</description>  
    <skillGains>
      <Intellectual>2</Intellectual>    
      <Social>7</Social>    
      <Artistic>2</Artistic>
    </skillGains>  
    <workDisables>ManualDumb</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>WorldSlider42</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>WorldSlider42</identifier>  
    <slot>Childhood</slot>  
    <title>world slider</title>  
    <titleShort>slider</titleShort>  
    <description>[PAWN_nameDef] was raised by a world-hopping band of criminals. [PAWN_pronoun] learned not to leave evidence of [PAWN_possessive] presence in case the group had to leave in a hurry. [PAWN_pronoun] also learned about medicine and hand-to-hand combat from [PAWN_possessive] fellow criminals.</description>  
    <skillGains>
      <Construction>-2</Construction>    
      <Plants>-2</Plants>    
      <Melee>3</Melee>    
      <Medicine>4</Medicine>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>MedicalStudent38</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>MedicalStudent38</identifier>  
    <slot>Childhood</slot>  
    <title>medical student</title>  
    <titleShort>student</titleShort>  
    <description>Born into a long line of doctors, [PAWN_nameDef] was tutored in all fields of medicine, from herbal ointments to the modern synthetic drugs. [PAWN_pronoun] also picked up a healthy disdain for lower class work.</description>  
    <skillGains>
      <Construction>-2</Construction>    
      <Social>3</Social>    
      <Medicine>3</Medicine>
    </skillGains>  
    <workDisables>Hauling, Mining</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>
    <possessions>
      <TextBook>1</TextBook>
    </possessions>
  </BackstoryDef>

  <BackstoryDef>
    <defName>AspergersRebel13</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>AspergersRebel13</identifier>  
    <slot>Childhood</slot>  
    <title>aspergers rebel</title>  
    <titleShort>rebel</titleShort>  
    <description>[PAWN_nameDef] grew up as a rebel on an formerly-advanced rimworld devastated by war.

[PAWN_possessive] Aspergers syndrome meant [PAWN_pronoun] struggled with social situations and was incapable of caring, but [PAWN_pronoun] learned hard skills like research and shooting very quickly.</description>  
    <skillGains>
      <Intellectual>5</Intellectual>    
      <Shooting>2</Shooting>    
      <Social>-3</Social>
    </skillGains>  
    <workDisables>Caring</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ShootingComa59</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ShootingComa59</identifier>  
    <slot>Childhood</slot>  
    <title>shooting coma</title>  
    <titleShort>coma child</titleShort>  
    <description>[PAWN_nameDef] fell into a coma at a young age and spent years dreaming about shooting. When [PAWN_pronoun] awoke, [PAWN_pronoun] was useless at most tasks - but [PAWN_pronoun] could aim pretty well.</description>  
    <skillGains>
      <Shooting>8</Shooting>
    </skillGains>  
    <workDisables>ManualDumb, ManualSkilled, Intellectual, Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>PsychologyStudent15</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>PsychologyStudent15</identifier>  
    <slot>Childhood</slot>  
    <title>psychology student</title>  
    <titleShort>student</titleShort>  
    <description>[PAWN_nameDef] researched new religions and traditions and often dreamt of distant stars. 

[PAWN_possessive] dreams prompted [PAWN_objective] to ask what lay beyond the lights in the sky.</description>  
    <skillGains>
      <Social>-3</Social>    
      <Artistic>4</Artistic>
    </skillGains>  
    <workDisables>Violent</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpaceSmuggler6</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>SpaceSmuggler6</identifier>  
    <slot>Childhood</slot>  
    <title>space smuggler</title>  
    <titleShort>smuggler</titleShort>  
    <description>[PAWN_nameDef]'s parents used [PAWN_objective] to help smuggle contraband between a cluster of rimworlds.

Aided by [PAWN_possessive] small size, [PAWN_nameDef] became very adept at carrying out [PAWN_possessive] parents' missions, learning weaponry, negotiations, and most of all smuggling.</description>  
    <skillGains>
      <Plants>-2</Plants>    
      <Intellectual>-2</Intellectual>    
      <Shooting>3</Shooting>    
      <Melee>3</Melee>    
      <Social>3</Social>    
      <Medicine>2</Medicine>    
      <Artistic>-2</Artistic>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Abductee7</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Abductee7</identifier>  
    <slot>Childhood</slot>  
    <title>abductee</title>  
    <titleShort>abductee</titleShort>  
    <description>[PAWN_nameDef] was abducted by xenohumans when [PAWN_pronoun] was still a baby. They experimented on [PAWN_objective] to understand [PAWN_possessive] genetic structure.

As [PAWN_pronoun] grew up, [PAWN_nameDef] grew a little bit too big and strong for [PAWN_possessive] captors and escaped.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Mining>4</Mining>    
      <Melee>4</Melee>    
      <Social>-2</Social>
    </skillGains>  
    <workDisables>Intellectual</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>BlessedChild46</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>BlessedChild46</identifier>  
    <slot>Childhood</slot>  
    <title>blessed child</title>  
    <titleShort>blessed</titleShort>  
    <description>[PAWN_nameDef] was born under auspicious circumstances to a midworld spiritual group and held in reverence throughout [PAWN_possessive] childhood.

[PAWN_pronoun] learned to take care of the poor and give succor to the faithful from an early age.</description>  
    <skillGains>
      <Social>3</Social>    
      <Medicine>2</Medicine>    
      <Artistic>1</Artistic>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>GlitterworldKid85</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>GlitterworldKid85</identifier>  
    <slot>Childhood</slot>  
    <title>glitterworld kid</title>  
    <titleShort>glit kid</titleShort>  
    <description>The son of a genetically-engineered "perfect mate" on a glitterworld, [PAWN_nameDef] was much more shy, withdrawn and nervous than [PAWN_possessive] parents. [PAWN_pronoun] kept mostly to [PAWN_objective]self, studying science and medicine and taking on gardening as a hobby. 

In [PAWN_possessive] teens, [PAWN_pronoun] ran away from home, seeking a quieter life.</description>  
    <skillGains>
      <Plants>4</Plants>    
      <Intellectual>2</Intellectual>    
      <Social>-3</Social>    
      <Medicine>1</Medicine>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>FeralChild85</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>FeralChild85</identifier>  
    <slot>Childhood</slot>  
    <title>feral child</title>  
    <titleShort>survivor</titleShort>  
    <description>[PAWN_nameDef] and [PAWN_possessive] family lived a hard but prosperous life along with their fellow colonists on an outlying rimworld.

One day, a group of mechanoids attacked, killing everyone aside from [PAWN_nameDef]. [PAWN_pronoun] then lived alone in the desert until a group of raiders found him.</description>  
    <skillGains>
      <Plants>2</Plants>    
      <Melee>4</Melee>    
      <Crafting>1</Crafting>
    </skillGains>  
    <workDisables>Social</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Scout6</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Scout6</identifier>  
    <slot>Childhood</slot>  
    <title>scout</title>  
    <titleShort>scout</titleShort>  
    <description>[PAWN_nameDef] loved being outdoors. [PAWN_possessive] parents enrolled [PAWN_objective] in a program that taught military scouting skills. [PAWN_pronoun] thrived when [PAWN_pronoun] was left alone in the wilderness.

Unfortunately, [PAWN_nameDef] did not learn the basic technological skills that are taken for granted by many others.</description>  
    <skillGains>
      <Construction>1</Construction>    
      <Shooting>3</Shooting>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>Intellectual, Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>TraineeAlchemist64</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>TraineeAlchemist64</identifier>  
    <slot>Childhood</slot>  
    <title>trainee alchemist</title>  
    <titleShort>alchemist</titleShort>  
    <description>[PAWN_nameDef] grew up on a medieval world. [PAWN_pronoun] learned the basics of alchemy and medicine in one the few monastic schools on [PAWN_possessive] planet.</description>  
    <skillGains>
      <Intellectual>3</Intellectual>    
      <Cooking>4</Cooking>    
      <Medicine>3</Medicine>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>MilitaryCadet16</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>MilitaryCadet16</identifier>  
    <slot>Childhood</slot>  
    <title>military cadet</title>  
    <titleShort>cadet</titleShort>  
    <description>Orphaned as a child, [PAWN_nameDef] was sent to a secret military school on a harsh deadworld.

Taking quickly to firearms and survival training, [PAWN_pronoun] graduated with honor, transitioning into a leadership position in covert operations.</description>  
    <skillGains>
      <Plants>-2</Plants>    
      <Mining>2</Mining>    
      <Shooting>3</Shooting>    
      <Melee>2</Melee>    
      <Medicine>2</Medicine>    
      <Animals>-3</Animals>
    </skillGains>  
    <workDisables>Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Pickpocket61</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Pickpocket61</identifier>  
    <slot>Childhood</slot>  
    <title>pickpocket</title>  
    <titleShort>thief</titleShort>  
    <description>At a young age, [PAWN_nameDef] witnessed [PAWN_possessive] parents' murder. With no guidance, [PAWN_pronoun] had to fend for [PAWN_objective]self. Joining a small group of misfits, [PAWN_pronoun] did whatever was necessary to survive.</description>  
    <skillGains>
      <Intellectual>-2</Intellectual>    
      <Melee>3</Melee>    
      <Social>-1</Social>    
      <Crafting>3</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpaceCadet77</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>SpaceCadet77</identifier>  
    <slot>Childhood</slot>  
    <title>space cadet</title>  
    <titleShort>cadet</titleShort>  
    <description>Growing up on a rural farm-world leaves little opportunity for excitement and adventure, so when the Royal Fleet began a recruiting program on [PAWN_possessive] home planet, [PAWN_nameDef] threw down [PAWN_possessive] hoe and cattle prod to took up arms for the crown.

[PAWN_pronoun] served on the interplanetary super-destroyer HMS Thunder-Child.</description>  
    <skillGains>
      <Shooting>4</Shooting>    
      <Melee>2</Melee>
    </skillGains>  
    <workDisables>PlantWork</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Naturalist14</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Naturalist14</identifier>  
    <slot>Childhood</slot>  
    <title>naturalist</title>  
    <titleShort>naturalist</titleShort>  
    <description>[PAWN_nameDef] grew up on a backwater planet with minimal education. [PAWN_pronoun] had to hunt and grow food to survive.

[PAWN_possessive] family's home was frequently raided for food by scavengers. This made [PAWN_nameDef] very suspicious and slow to trust anyone [PAWN_pronoun] doesn't know.</description>  
    <skillGains>
      <Plants>3</Plants>    
      <Melee>2</Melee>    
      <Cooking>2</Cooking>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>Social, Intellectual</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Drudge9</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Drudge9</identifier>  
    <slot>Childhood</slot>  
    <title>drudge</title>  
    <titleShort>drudge</titleShort>  
    <description>[PAWN_nameDef] was never sure what [PAWN_pronoun] wanted to do with [PAWN_possessive] life. [PAWN_pronoun] spent most of [PAWN_possessive] time doing low-wage jobs and wondering what [PAWN_possessive] future would be like.

[PAWN_pronoun] entertained [PAWN_objective]self with space sims and survival games, dreaming of one day being somewhere more interesting.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Mining>2</Mining>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>BunkerKid41</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>BunkerKid41</identifier>  
    <slot>Childhood</slot>  
    <title>bunker kid</title>  
    <titleShort>bunker kid</titleShort>  
    <description>[PAWN_nameDef] grew up in an underground shelter, waiting for radioactive fallout to subside. [PAWN_possessive] rich family had gained access to a luxury bunker city, while billions died on the surface.

Determined to one day seek revenge against the hated enemy, [PAWN_nameDef] spent most of [PAWN_possessive] time practicing shooting in [PAWN_possessive] private training facility.</description>  
    <skillGains>
      <Shooting>4</Shooting>    
      <Melee>4</Melee>    
      <Social>-2</Social>
    </skillGains>  
    <workDisables>Artistic, PlantWork</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ChildOfDrifters18</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ChildOfDrifters18</identifier>  
    <slot>Childhood</slot>  
    <title>child of drifters</title>  
    <titleShort>drifter</titleShort>  
    <description>Ever since [PAWN_pronoun] was very small, [PAWN_nameDef] never knew a home beyond [PAWN_possessive] parent's ship. Their nomadic meandering provided a constant change of scenery. [PAWN_possessive] life was often perilous but never dull, and [PAWN_pronoun] inherited [PAWN_possessive] parents' wanderlust.</description>  
    <skillGains>
      <Shooting>4</Shooting>    
      <Melee>-2</Melee>    
      <Social>-3</Social>    
      <Crafting>6</Crafting>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>Firefighting, PlantWork, Mining</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>TribalThunderer45</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>TribalThunderer45</identifier>  
    <slot>Childhood</slot>  
    <title>tribal thunderer</title>  
    <titleShort>thunderer</titleShort>  
    <description>Years ago, [PAWN_nameDef]'s tribe stumbled upon an abandoned gun vault. Believing the weapons to be sacred gifts, the shaman would select a child born under superstitious circumstances and assign them the honour of bearing one to defend the village. [PAWN_nameDef] is such a child.</description>  
    <skillGains>
      <Shooting>5</Shooting>    
      <Medicine>2</Medicine>
    </skillGains>  
    <workDisables>Intellectual, Artistic, Crafting</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Gymnast48</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Gymnast48</identifier>  
    <slot>Childhood</slot>  
    <title>gymnast</title>  
    <titleShort>gymnast</titleShort>  
    <description>[PAWN_nameDef] was a professional gymnast at an early age. Thanks to [PAWN_possessive] skills, [PAWN_pronoun] was able to enroll in a glitterworld university.

Because of the lack of career opportunities in gymnastics, [PAWN_pronoun] left the university unemployed and carrying an enormous debt.</description>  
    <skillGains>
      <Melee>4</Melee>    
      <Social>2</Social>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>CorpBredStudent54</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>CorpBredStudent54</identifier>  
    <slot>Childhood</slot>  
    <title>corp-bred student</title>  
    <titleShort>student</titleShort>  
    <description>[PAWN_nameDef] was bred to become an executive for the space exploration corporation that owned [PAWN_possessive] home planet. [PAWN_possessive] family had been working for that corporation for four generations.

At age five, [PAWN_pronoun] enrolled at the corporate academy where [PAWN_pronoun] learned loyalty and ruthlessness. [PAWN_nameDef] excelled in finding diplomatic solutions where others would need violence.</description>  
    <skillGains>
      <Intellectual>5</Intellectual>    
      <Social>7</Social>    
      <Medicine>3</Medicine>    
      <Crafting>3</Crafting>
    </skillGains>  
    <workDisables>Violent, Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Colonist22</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Colonist22</identifier>  
    <slot>Childhood</slot>  
    <title>colonist</title>  
    <titleShort>colonist</titleShort>  
    <description>As a child, [PAWN_pronoun] was launched into space as part of an interstellar colonization program.</description>  
    <skillGains>
      <Plants>4</Plants>    
      <Cooking>3</Cooking>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>Artistic, Firefighting</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>MilitaryTrainee20</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>MilitaryTrainee20</identifier>  
    <slot>Childhood</slot>  
    <title>military trainee</title>  
    <titleShort>trainee</titleShort>  
    <description>[PAWN_nameDef] was raised on a military base in the Xennoa system. [PAWN_pronoun] was exposed to many different strenuous activities and grew strong at an early age. [PAWN_pronoun] also learned negotiation, medical, and military tactics.</description>  
    <skillGains>
      <Shooting>3</Shooting>    
      <Melee>2</Melee>    
      <Medicine>2</Medicine>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Bookworm3</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Bookworm3</identifier>  
    <slot>Childhood</slot>  
    <title>bookworm</title>  
    <titleShort>bookworm</titleShort>  
    <description>[PAWN_nameDef] spent [PAWN_possessive] youth in the library, reading every book [PAWN_pronoun] could about the technical marvels of space travel, the engineering ingenuity of the space-faring pioneers, and the horrible yet fascinating tales of mechanoid wars. [PAWN_pronoun] vowed that one day, [PAWN_pronoun] would see these things for [PAWN_objective]self.</description>  
    <skillGains>
      <Plants>2</Plants>    
      <Intellectual>2</Intellectual>    
      <Social>-2</Social>    
      <Medicine>2</Medicine>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ColonyKid47</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ColonyKid47</identifier>  
    <slot>Childhood</slot>  
    <title>colony kid</title>  
    <titleShort>colony kid</titleShort>  
    <description>[PAWN_nameDef] was born the child of two doctors in a small colony. Because of [PAWN_possessive] parents, [PAWN_pronoun] was always interested in science and medicine.

[PAWN_nameDef] never really got along with other children, and as a result became withdrawn and unsociable.</description>  
    <skillGains>
      <Intellectual>2</Intellectual>    
      <Social>-2</Social>    
      <Medicine>3</Medicine>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>CaravanChild53</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>CaravanChild53</identifier>  
    <slot>Childhood</slot>  
    <title>caravan child</title>  
    <titleShort>child</titleShort>  
    <description>[PAWN_nameDef] was born on a urbworld merchant ship to an entrepreneurial mother and an absent father. Born male, [PAWN_pronoun] disliked boy stuff, and got into [PAWN_possessive] mother's things all the time.

Eventually, [PAWN_pronoun] traveled with [PAWN_possessive] mother to a glitterworld and spent [PAWN_possessive] savings on a body [PAWN_pronoun] was happy with.</description>  
    <skillGains>
      <Social>2</Social>    
      <Artistic>2</Artistic>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ReEducatedYouth23</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ReEducatedYouth23</identifier>  
    <slot>Childhood</slot>  
    <title>re-educated youth</title>  
    <titleShort>reeducated</titleShort>  
    <description>[PAWN_nameDef] lived on a midworld where the government cared about the youth. 

[PAWN_pronoun] was one of the many children who were taken from their parents and forced into the new education programs.</description>  
    <skillGains>
      <Social>2</Social>
    </skillGains>  
    <workDisables>Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>FarmMechanic1</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>FarmMechanic1</identifier>  
    <slot>Childhood</slot>  
    <title>farm mechanic</title>  
    <titleShort>mechanic</titleShort>  
    <description>Growing up on a farming planet, [PAWN_nameDef] spent [PAWN_possessive] time learning about the automated machinery that grew and harvested the multitude of crops.</description>  
    <skillGains>
      <Construction>4</Construction>    
      <Plants>2</Plants>
    </skillGains>  
    <workDisables>Intellectual, Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>AccursedChild88</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>AccursedChild88</identifier>  
    <slot>Childhood</slot>  
    <title>accursed child</title>  
    <titleShort>cursed</titleShort>  
    <description>[PAWN_nameDef] was born at the peak of a lunar eclipse. The elders declared [PAWN_objective] a child of darkness and brought [PAWN_objective] under their care.

[PAWN_possessive] entire childhood was spent studying the lore and rituals of [PAWN_possessive] people - until the day of calling.</description>  
    <skillGains>
      <Construction>-1</Construction>    
      <Intellectual>5</Intellectual>    
      <Medicine>4</Medicine>
    </skillGains>  
    <workDisables>Firefighting, Cleaning, Hauling, Mining</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <spawnCategories>
      <li>Tribal</li>
      <li>Cult</li>
    </spawnCategories>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>AmateurAstronomer77</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>AmateurAstronomer77</identifier>  
    <slot>Childhood</slot>  
    <title>amateur astronomer</title>  
    <titleShort>astronomer</titleShort>  
    <description>[PAWN_nameDef] was fascinated with astronomy. [PAWN_pronoun] would spend hours gazing at planets and nebulae through [PAWN_possessive] telescope.

[PAWN_pronoun] is credited with discovering a small comet that would, years later, strike a nearby moon and disrupt the mining operations there.</description>  
    <skillGains>
      <Intellectual>6</Intellectual>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>FarmHand2</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>FarmHand2</identifier>  
    <slot>Childhood</slot>  
    <title>farm hand</title>  
    <titleShort>farm hand</titleShort>  
    <description>[PAWN_nameDef] was born on a farm on a rimworld.

Everything was taken care of by the few members of the farm, including [PAWN_nameDef]. This helped [PAWN_objective] build independence, spirit, and a well-rounded character.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Plants>4</Plants>    
      <Intellectual>-2</Intellectual>    
      <Melee>2</Melee>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>EnergeticPopIdol61</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>EnergeticPopIdol61</identifier>  
    <slot>Childhood</slot>  
    <title>energetic pop idol</title>  
    <titleShort>pop idol</titleShort>  
    <description>[PAWN_nameDef] grew up on a glitterworld, training in rapier arts and singing. [PAWN_pronoun] spent several years as an energetic teen pop idol.

However, [PAWN_pronoun] decided that the music and modeling industries were not paying [PAWN_objective] enough attention, so [PAWN_pronoun] left.</description>  
    <skillGains>
      <Intellectual>-2</Intellectual>    
      <Melee>3</Melee>    
      <Social>2</Social>    
      <Medicine>-2</Medicine>    
      <Artistic>5</Artistic>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Beauty>2</Beauty>    
      <Greedy>0</Greedy>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>IcePlanetChild95</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>IcePlanetChild95</identifier>  
    <slot>Childhood</slot>  
    <title>ice planet child</title>  
    <titleShort>ice child</titleShort>  
    <description>Growing up on the frozen wastes of an ocean moon, [PAWN_nameDef] only had animals and a few hard-bitten sailors as companions.

The lack of social interaction made [PAWN_objective] develop a interest in engineering - but [PAWN_pronoun] never developed any great fondness for humans.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Intellectual>4</Intellectual>
    </skillGains>  
    <workDisables>Caring, Social, Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Scrounger25</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Scrounger25</identifier>  
    <slot>Childhood</slot>  
    <title>scrounger</title>  
    <titleShort>scrounger</titleShort>  
    <description>[PAWN_nameDef] would dive into junk mounds in search for valuables and items of interest.

[PAWN_pronoun] was never bothered by the stink, or dangers that could leave [PAWN_objective] wounded for weeks.</description>  
    <skillGains>
      <Mining>2</Mining>    
      <Social>-2</Social>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>Cleaning</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ToxicChild96</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ToxicChild96</identifier>  
    <slot>Childhood</slot>  
    <title>toxic child</title>  
    <titleShort>toxic</titleShort>  
    <description>Growing up on a an industrial planet, [PAWN_nameDef] started working in a chemical plant at age of six. Years of exposure to industrial toxins left [PAWN_objective] mentally scarred.

[PAWN_pronoun] eventually blew up the facility and escaped the planet in a stolen cargo ship.</description>  
    <skillGains>
      <Construction>3</Construction>    
      <Crafting>3</Crafting>
    </skillGains>  
    <workDisables>Firefighting, PlantWork</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Nerves>-1</Nerves>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>BoyScout42</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>BoyScout42</identifier>  
    <slot>Childhood</slot>  
    <title>boy scout</title>  
    <titleShort>scout</titleShort>  
    <description>[PAWN_nameDef] was in a boy scout troop on a midworld.

[PAWN_pronoun] learned many survival skills including how to thrive in the outdoors and how to tend to basic wounds.</description>  
    <skillGains>
      <Plants>2</Plants>    
      <Shooting>2</Shooting>    
      <Medicine>2</Medicine>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ScienceProdigy65</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ScienceProdigy65</identifier>  
    <slot>Childhood</slot>  
    <title>science prodigy</title>  
    <titleShort>prodigy</titleShort>  
    <description>[PAWN_nameDef] was top of [PAWN_possessive] class in chemistry, physics, exobiology, astronomy and opera.

An unfortunate lab accident left [PAWN_objective] with a deathly fear of fire.</description>  
    <skillGains>
      <Intellectual>3</Intellectual>    
      <Medicine>3</Medicine>    
      <Artistic>2</Artistic>
    </skillGains>  
    <workDisables>Violent, Firefighting</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpeederRacer71</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>SpeederRacer71</identifier>  
    <slot>Childhood</slot>  
    <title>speeder racer</title>  
    <titleShort>racer</titleShort>  
    <description>[PAWN_nameDef] was the son of a fern farmer in the towering forests of Khalderia.

[PAWN_pronoun] found [PAWN_possessive] first love in speeder racing, zipping in and out of the massive canopies and gorgeous vistas of [PAWN_possessive] homeworld. The seedy underworld of racing also forced [PAWN_objective] to learn to defend [PAWN_objective]self with a quick word or a quicker shot.</description>  
    <skillGains>
      <Shooting>3</Shooting>    
      <Social>3</Social>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>MercenaryRecruit18</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>MercenaryRecruit18</identifier>  
    <slot>Childhood</slot>  
    <title>mercenary recruit</title>  
    <titleShort>recruit</titleShort>  
    <description>Born to a long line of off-world mercenaries, [PAWN_nameDef] grew up in a busy trading hub. At a young age, [PAWN_pronoun] was recruited into one of the mercenary crews.</description>  
    <skillGains>
      <Shooting>3</Shooting>    
      <Crafting>3</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>MarineCadet73</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>MarineCadet73</identifier>  
    <slot>Childhood</slot>  
    <title>marine cadet</title>  
    <titleShort>cadet</titleShort>  
    <description>[PAWN_nameDef] was in a planetary marine cadet program.

[PAWN_pronoun] was kicked out for shooting an officer. It was never established whether this was an accident.</description>  
    <skillGains>
      <Shooting>3</Shooting>    
      <Melee>3</Melee>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>YoungPsychologist58</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>YoungPsychologist58</identifier>  
    <slot>Childhood</slot>  
    <title>young psychologist</title>  
    <titleShort>psych</titleShort>  
    <description>[PAWN_nameDef] was born and raised on a poor midworld, in the front passenger seat of [PAWN_possessive] mother's cab.

Watching pedestrians and listening to the taxi passengers all day long, [PAWN_pronoun] soon became very good at figuring out someone's real purpose.</description>  
    <skillGains>
      <Social>4</Social>    
      <Medicine>2</Medicine>
    </skillGains>  
    <workDisables>Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Tinkerer11</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Tinkerer11</identifier>  
    <slot>Childhood</slot>  
    <title>tinkerer</title>  
    <titleShort>tinkerer</titleShort>  
    <description>[PAWN_nameDef] spent [PAWN_possessive] spare time creating things from [PAWN_possessive] imagination out of scrap metal and discarded machinery.

[PAWN_pronoun] created simple robots and small tools, for both for fun and for utility.</description>  
    <skillGains>
      <Intellectual>3</Intellectual>    
      <Melee>-2</Melee>    
      <Crafting>3</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>LabGrownChild22</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>LabGrownChild22</identifier>  
    <slot>Childhood</slot>  
    <title>lab-grown child</title>  
    <titleShort>lab-grown</titleShort>  
    <description>[PAWN_nameDef] was born in a laboratory as part of a altruistic but failed attempt to create a new class of human.

[PAWN_possessive] childhood was filled with books and one-on-one tutoring. [PAWN_pronoun] was awkward, shy, and naive to the true nature of humanity.</description>  
    <skillGains>
      <Plants>2</Plants>    
      <Intellectual>2</Intellectual>    
      <Artistic>2</Artistic>
    </skillGains>  
    <workDisables>Animals</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Orphan11</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Orphan11</identifier>  
    <slot>Childhood</slot>  
    <title>orphan</title>  
    <titleShort>orphan</titleShort>  
    <description>Born in a brothel on a harsh world, [PAWN_nameDef] never really had a childhood. [PAWN_pronoun] did odd jobs to survive, and became distant from others.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Cooking>2</Cooking>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>Social, Animals</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>TechScholar75</defName>
    <identifier>TechScholar75</identifier>
    <slot>Childhood</slot>
    <title>farm kid</title>
    <titleShort>farm kid</titleShort>
    <description>[PAWN_nameDef] hung around a group of local luddite farmers on [PAWN_possessive] midworld. They helped [PAWN_objective] practice shooting every single day, and passed on some basic farming skills. They also taught [PAWN_nameDef] how to focus on working hard with your hands instead of getting distracted by highfalutin' ideas and clever machines.</description>
    <workDisables>Intellectual</workDisables>
    <skillGains>
      <Plants>3</Plants>
      <Shooting>3</Shooting>
      <Animals>1</Animals>
    </skillGains>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
      <li>Farmer</li>
    </spawnCategories>
    <shuffleable>false</shuffleable>
  </BackstoryDef>

  <BackstoryDef>
    <defName>PitGladiator19</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>PitGladiator19</identifier>  
    <slot>Childhood</slot>  
    <title>pit gladiator</title>  
    <titleShort>gladiator</titleShort>  
    <description>[PAWN_nameDef] was enslaved as a child and forced to fight creatures and other people in an underground fighting arena.

[PAWN_pronoun] showed an affinity for the sport, and eventually bought [PAWN_possessive] own freedom. However, [PAWN_possessive] time in the pits never brought [PAWN_objective] much intellectual stimulation.</description>  
    <skillGains>
      <Shooting>4</Shooting>    
      <Melee>4</Melee>
    </skillGains>  
    <workDisables>Intellectual</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>FactoryDrone58</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>FactoryDrone58</identifier>  
    <slot>Childhood</slot>  
    <title>factory drone</title>  
    <titleShort>worker</titleShort>  
    <description>[PAWN_nameDef] grew up in a large factory city on an industrial world.

Since poverty was rampant and food scarce, [PAWN_pronoun] worked for whatever wages [PAWN_pronoun] could get. This wasn't an easy life, but [PAWN_nameDef] never shied away from a hard day's work.</description>  
    <skillGains>
      <Construction>3</Construction>    
      <Intellectual>-2</Intellectual>    
      <Crafting>3</Crafting>
    </skillGains>  
    <workDisables>Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Industriousness>1</Industriousness>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>RichBoy9</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>RichBoy9</identifier>  
    <slot>Childhood</slot>  
    <title>rich boy</title>  
    <titleShort>rich boy</titleShort>  
    <description>[PAWN_nameDef] grew up on a midworld with loving parents, a cute pet and a lot of money - but no friends.

[PAWN_pronoun] was always told by [PAWN_possessive] father that 'those peasants just want your money'. So, [PAWN_pronoun] spent most of [PAWN_possessive] time alone, or fighting other kids.</description>  
    <skillGains>
      <Melee>3</Melee>    
      <Artistic>3</Artistic>
    </skillGains>  
    <workDisables>Caring, Social</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Athlete47</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Athlete47</identifier>  
    <slot>Childhood</slot>  
    <title>athlete</title>  
    <titleShort>athlete</titleShort>  
    <description>[PAWN_nameDef] was professional athlete at early age. Thanks to [PAWN_possessive] skills, [PAWN_pronoun] was able to leave [PAWN_possessive] homeworld and enroll in a glitterworld university of science and technology.

Because of [PAWN_possessive] demanding schedule and introverted nature, [PAWN_pronoun] became socially inept.</description>  
    <skillGains>
      <Intellectual>3</Intellectual>    
      <Melee>4</Melee>
    </skillGains>  
    <workDisables>Social, Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>SoleSurvivor63</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>SoleSurvivor63</identifier>  
    <slot>Childhood</slot>  
    <title>sole survivor</title>  
    <titleShort>survivor</titleShort>  
    <description>[PAWN_nameDef]'s entire tribe was wiped out by a mysterious disease.

Found alone and adopted by another band, [PAWN_pronoun] never became close with the other tribe members. [PAWN_pronoun] preferred to stay away from [PAWN_possessive] new home, wandering the woods and tending the animals.</description>  
    <skillGains>
      <Plants>2</Plants>    
      <Shooting>2</Shooting>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>MechanoidHacker93</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>MechanoidHacker93</identifier>  
    <slot>Childhood</slot>  
    <title>mechanoid hacker</title>  
    <titleShort>mechacker</titleShort>  
    <description>The only son of a well respected mechanoid inventor, [PAWN_nameDef] had access to the materials to subvert and modify [PAWN_possessive] father's creations.

[PAWN_pronoun] used drugs to increase [PAWN_possessive] productivity. Unfortunately, the side-effects included persistent delusions of being mechanized, which limited [PAWN_possessive] social life.</description>  
    <skillGains>
      <Construction>3</Construction>    
      <Intellectual>3</Intellectual>    
      <Social>-3</Social>    
      <Crafting>3</Crafting>
    </skillGains>  
    <workDisables>Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <DrugDesire>1</DrugDesire>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ProjectSubject99</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ProjectSubject99</identifier>  
    <slot>Childhood</slot>  
    <title>project subject</title>  
    <titleShort>subject</titleShort>  
    <description>[PAWN_nameDef] was picked by government agents for the mysterious "Frame Project".

Due to memory blockages, however, [PAWN_pronoun] remembers very little about this project or its true agenda - only that there were few survivors.</description>  
    <skillGains>
      <Shooting>4</Shooting>    
      <Melee>4</Melee>
    </skillGains>  
    <workDisables>Cleaning</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>HexCellArtist91</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>HexCellArtist91</identifier>  
    <slot>Childhood</slot>  
    <title>hex-cell artist</title>  
    <titleShort>artist</titleShort>  
    <description>[PAWN_nameDef] crafted sculptures from spent hex-cells and traded them to a local museum for food.

After [PAWN_possessive] popularity grew, [PAWN_pronoun] was approached by a pirate and abducted. The pirate kept [PAWN_objective] prisoner and forced [PAWN_objective] to create sculptures for sale.</description>  
    <skillGains>
      <Artistic>3</Artistic>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>Intellectual</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>PlagueProdigy12</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>PlagueProdigy12</identifier>  
    <slot>Childhood</slot>  
    <title>plague prodigy</title>  
    <titleShort>prodigy</titleShort>  
    <description>[PAWN_nameDef]'s childhood was cut short when a mysterious plague hit [PAWN_possessive] homeworld.

[PAWN_pronoun] watched [PAWN_possessive] friends die, and [PAWN_possessive] compassion for humanity died with them. In its place a new thirst for knowledge emerged.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Intellectual>4</Intellectual>    
      <Social>-2</Social>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>Caring, Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>PoorKid18</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>PoorKid18</identifier>  
    <slot>Childhood</slot>  
    <title>poor kid</title>  
    <titleShort>poor kid</titleShort>  
    <description>Abandoned by [PAWN_possessive] parents, [PAWN_nameDef] learned how to survive by [PAWN_objective]self before [PAWN_pronoun] was ten years old.</description>  
    <skillGains>
      <Melee>4</Melee>    
      <Social>1</Social>
    </skillGains>  
    <workDisables>Caring</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Dreamer79</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Dreamer79</identifier>  
    <slot>Childhood</slot>  
    <title>dreamer</title>  
    <titleShort>dreamer</titleShort>  
    <description>[PAWN_nameDef] was an expressive child. [PAWN_pronoun] sang often, and made friends very easily, be they young or old, male or female.

[PAWN_pronoun] hated fighting, but when made angry, [PAWN_pronoun] could be dangerous. [PAWN_possessive] temper was quick.</description>  
    <skillGains>
      <Melee>2</Melee>    
      <Social>4</Social>    
      <Artistic>3</Artistic>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Kind>0</Kind>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>UrbworldUrchin6</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>UrbworldUrchin6</identifier>  
    <slot>Childhood</slot>  
    <title>urbworld urchin</title>  
    <titleShort>urchin</titleShort>  
    <description>[PAWN_nameDef] was born in the darkest slums of [PAWN_possessive] urban homeworld. Neglected by [PAWN_possessive] mother, [PAWN_pronoun] learned to survive on [PAWN_possessive] own, battling for every scrap of food [PAWN_pronoun] saw.

Eventually, as [PAWN_possessive] talents exceeded [PAWN_possessive] peers, [PAWN_pronoun] ascended from the depths to the surface streets and left [PAWN_possessive] mother behind.</description>  
    <skillGains>
      <Melee>4</Melee>    
      <Social>-2</Social>    
      <Crafting>2</Crafting>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>PlantWork</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>MilitaryRecruit49</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>MilitaryRecruit49</identifier>  
    <slot>Childhood</slot>  
    <title>military recruit</title>  
    <titleShort>recruit</titleShort>  
    <description>[PAWN_nameDef] was born on a midworld run by an intensely militaristic dictatorship.

From a young age, [PAWN_pronoun] was trained to be a good soldier. They taught [PAWN_objective] how to use a gun and how to fight with melee weapons.</description>  
    <skillGains>
      <Shooting>4</Shooting>    
      <Melee>3</Melee>
    </skillGains>  
    <workDisables>Caring</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>StreetUrchin53</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>StreetUrchin53</identifier>  
    <slot>Childhood</slot>  
    <title>street urchin</title>  
    <titleShort>urchin</titleShort>  
    <description>Born to a poor family on an urbworld, [PAWN_nameDef] was abandoned on the streets at a young age.

[PAWN_pronoun] learned to steal and kill to survive the ruthless streets, gaining skills in melee and ranged combat. There was little time for talk in [PAWN_possessive] life.</description>  
    <skillGains>
      <Shooting>3</Shooting>    
      <Melee>3</Melee>
    </skillGains>  
    <workDisables>Social</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>KidScientist53</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>KidScientist53</identifier>  
    <slot>Childhood</slot>  
    <title>kid scientist</title>  
    <titleShort>scientist</titleShort>  
    <description>Born into an influential family, [PAWN_nameDef] received the best education money could buy. [PAWN_possessive] intellect soon proved to be superior to many. However, [PAWN_possessive] lack of empathy quickly put [PAWN_objective] at odds with [PAWN_possessive] teachers and [PAWN_possessive] parents.</description>  
    <skillGains>
      <Intellectual>4</Intellectual>    
      <Medicine>4</Medicine>    
      <Artistic>-3</Artistic>
    </skillGains>  
    <workDisables>ManualDumb, Social</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Psychopath>0</Psychopath>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>HelpDeskWorker31</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>HelpDeskWorker31</identifier>  
    <slot>Childhood</slot>  
    <title>help desk worker</title>  
    <titleShort>help desk</titleShort>  
    <description>Due to [PAWN_possessive] affinity for technology and desire to help others, [PAWN_nameDef] jumped at the chance to take a job at a help desk.

Unfortunately, the job's soul-crushing after-effects lasted longer than [PAWN_possessive] enthusiasm.</description>  
    <skillGains>
      <Intellectual>3</Intellectual>
    </skillGains>  
    <workDisables>Caring</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>MedievalThief74</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>MedievalThief74</identifier>  
    <slot>Childhood</slot>  
    <title>medieval thief</title>  
    <titleShort>thief</titleShort>  
    <description>[PAWN_nameDef] grew up poor and hungry on a medieval planet, learning to fend for [PAWN_objective]self.

At first, [PAWN_pronoun] only took what [PAWN_pronoun] needed. Then [PAWN_pronoun] learned to take what [PAWN_pronoun] wanted.</description>  
    <skillGains>
      <Shooting>-3</Shooting>    
      <Melee>4</Melee>    
      <Social>-2</Social>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>TraumatizedYouth87</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>TraumatizedYouth87</identifier>  
    <slot>Childhood</slot>  
    <title>traumatized youth</title>  
    <titleShort>trauma</titleShort>  
    <description>[PAWN_nameDef] lived on a pleasant midworld.

[PAWN_possessive] body purist mother fell sick with heart disease. In desperation, [PAWN_nameDef]'s father abducted innocent people and harvested their hearts. [PAWN_nameDef]'s father was arrested, but the child remained scarred from the experience.</description>  
    <skillGains>
      <Melee>2</Melee>    
      <Medicine>4</Medicine>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>Social</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>YouthSoldier99</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>YouthSoldier99</identifier>  
    <slot>Childhood</slot>  
    <title>youth soldier</title>  
    <titleShort>soldier</titleShort>  
    <description>Born to a long line of soldiers, [PAWN_nameDef] joined a military training program.

[PAWN_pronoun] excelled in physical training, and even passed most of the intellectual exams. While [PAWN_pronoun] was never particularly bright or nimble, [PAWN_nameDef] learned the value of hard work and suffering.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Shooting>2</Shooting>    
      <Melee>2</Melee>
    </skillGains>  
    <workDisables>Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Industriousness>1</Industriousness>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>GalacticPage37</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>GalacticPage37</identifier>  
    <slot>Childhood</slot>  
    <title>galactic page</title>  
    <titleShort>page</titleShort>  
    <description>[PAWN_nameDef] served the high admiral of a space fleet. [PAWN_pronoun] learned the ways of court, including etiquette and speechcraft.

In [PAWN_possessive] feudal society, it was considered a great honor to serve a man of such prestige.</description>  
    <skillGains>
      <Social>6</Social>    
      <Artistic>2</Artistic>
    </skillGains>  
    <workDisables>ManualDumb, ManualSkilled</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ApprenticeSmith37</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ApprenticeSmith37</identifier>  
    <slot>Childhood</slot>  
    <title>apprentice smith</title>  
    <titleShort>apprentice</titleShort>  
    <description>[PAWN_nameDef] grew up helping in [PAWN_possessive] father's smithy.

They were some of the last smiths on the planet that to use traditional smithing methods. They even collect some materials themselves.

Sometimes, after work, [PAWN_nameDef] would practice using the swords [PAWN_pronoun] forged.</description>  
    <skillGains>
      <Melee>2</Melee>    
      <Crafting>5</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>CloneFarmed21</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>CloneFarmed21</identifier>  
    <slot>Childhood</slot>  
    <title>clone-farmed</title>  
    <titleShort>disposable</titleShort>  
    <description>Clone children are seeded into nutrient-rich womb-vats and rapidly grown in a simmed universe. They're harvested later, sometimes for food, sometimes for organs, sometimes for workers - but they're always disposable.</description>  
    <skillGains>
      <Intellectual>4</Intellectual>    
      <Shooting>-3</Shooting>    
      <Artistic>3</Artistic>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>GangMember16</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>GangMember16</identifier>  
    <slot>Childhood</slot>  
    <title>gang member</title>  
    <titleShort>gang kid</titleShort>  
    <description>[PAWN_nameDef] grew up without parents. [PAWN_possessive] whole life was spent alone, fighting for survival on the streets.

No matter how hard it became to survive, [PAWN_pronoun] never gave up.</description>  
    <skillGains>
      <Shooting>3</Shooting>    
      <Melee>4</Melee>    
      <Medicine>3</Medicine>
    </skillGains>  
    <workDisables>Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Nerves>2</Nerves>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>MilitantChild49</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>MilitantChild49</identifier>  
    <slot>Childhood</slot>  
    <title>militant child</title>  
    <titleShort>soldier</titleShort>  
    <description>[PAWN_nameDef]'s military family forced [PAWN_objective] to train in hand-to-hand combat and fighting tactics.

In [PAWN_possessive] own time, [PAWN_pronoun] built small inventions. This sharpened [PAWN_possessive] mind.</description>  
    <skillGains>
      <Intellectual>2</Intellectual>    
      <Shooting>3</Shooting>    
      <Melee>3</Melee>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Killer41</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Killer41</identifier>  
    <slot>Childhood</slot>  
    <title>killer</title>  
    <titleShort>killer</titleShort>  
    <description>[PAWN_nameDef] was a bloodlusting child. Villagers thought [PAWN_objective] the product of a snake demon and human coupling.

After watching how [PAWN_possessive] human father betrayed and murdered [PAWN_possessive] mother, [PAWN_pronoun] longed to see how many more humans [PAWN_pronoun] could get to fall into [PAWN_possessive] hands.</description>  
    <skillGains>
      <Melee>4</Melee>    
      <Medicine>2</Medicine>
    </skillGains>  
    <workDisables>Social</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Bloodlust>0</Bloodlust>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ProfessionalGamer76</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ProfessionalGamer76</identifier>  
    <slot>Childhood</slot>  
    <title>professional gamer</title>  
    <titleShort>pro gamer</titleShort>  
    <description>[PAWN_nameDef] was obsessed with video games since [PAWN_possessive] hands were big enough to grip a joystick.

[PAWN_pronoun] achieved middling finishes in several local tournaments during the 16-bit era. Nobody noticed when [PAWN_pronoun] retired early in [PAWN_possessive] teenage years.</description>  
    <skillGains>
      <Shooting>4</Shooting>    
      <Melee>-3</Melee>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>DataDecoder39</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>DataDecoder39</identifier>  
    <slot>Childhood</slot>  
    <title>data decoder</title>  
    <titleShort>decoder</titleShort>  
    <description>Born on a glitterworld, [PAWN_nameDef] was very interested in the process of documenting information, and quickly became senior glitterpedia recorder.\n\n[PAWN_pronoun] was effective at self-study, but [PAWN_pronoun] lacked social experience.</description>
    <skillGains>
      <Intellectual>2</Intellectual>
    </skillGains>  
    <workDisables>Social</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <FastLearner>0</FastLearner>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>WolfPackMember26</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>WolfPackMember26</identifier>  
    <slot>Childhood</slot>  
    <title>wolf pack member</title>  
    <titleShort>feral</titleShort>  
    <description>[PAWN_nameDef] was discarded by [PAWN_possessive] family. Raised by wolves, [PAWN_pronoun] ran, hunted, and defended [PAWN_possessive] pack.

[PAWN_pronoun] learned to fight efficiently by tooth and claw. [PAWN_pronoun] also learned brutally effective primal hunting strategies.</description>  
    <skillGains>
      <Melee>8</Melee>    
      <Social>-3</Social>
    </skillGains>  
    <workDisables>Firefighting</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>RebelStudent14</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>RebelStudent14</identifier>  
    <slot>Childhood</slot>  
    <title>rebel student</title>  
    <titleShort>student</titleShort>  
    <description>[PAWN_nameDef] grew up in a modest but privileged family who encouraged [PAWN_objective] to play chess and shoot skeet at a young age.

As a bored student, [PAWN_pronoun] found joy in challenging [PAWN_possessive] teachers about the contradictions between [PAWN_possessive] planet's official values and their government's policies.</description>  
    <skillGains>
      <Intellectual>3</Intellectual>    
      <Shooting>2</Shooting>
    </skillGains>  
    <workDisables>ManualDumb</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Abrasive>0</Abrasive>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Cadet96</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Cadet96</identifier>  
    <slot>Childhood</slot>  
    <title>cadet</title>  
    <titleShort>cadet</titleShort>  
    <description>Born on the planet New China, [PAWN_nameDef]'s father was a police chief and kept extremely strict watch over [PAWN_possessive] son.

[PAWN_nameDef] had to do military-style drill exercises every day and keep [PAWN_possessive] room spotlessly clean, always ready for inspection. [PAWN_pronoun] came to abhor violence.</description>  
    <skillGains>
      <Construction>3</Construction>    
      <Intellectual>3</Intellectual>    
      <Crafting>3</Crafting>
    </skillGains>  
    <workDisables>Violent</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ShipTechnician0</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ShipTechnician0</identifier>  
    <slot>Childhood</slot>  
    <title>ship technician</title>  
    <titleShort>technician</titleShort>  
    <description>[PAWN_nameDef] was an apprentice technician on trade ships plying their routes between the stations and planets of [PAWN_possessive] home system. In this job, [PAWN_pronoun] learned to fix and improve many machines.

Sometimes, [PAWN_possessive] ship was assigned to short exploratory jaunts into dangerous regions of space.</description>  
    <skillGains>
      <Construction>3</Construction>    
      <Plants>-3</Plants>    
      <Intellectual>3</Intellectual>    
      <Crafting>3</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>FarmersDaughter81</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>FarmersDaughter81</identifier>  
    <slot>Childhood</slot>  
    <title>farmer's daughter</title>  
    <titleShort>farm girl</titleShort>  
    <description>[PAWN_nameDef] grew up on [PAWN_possessive] parents' vineyard. [PAWN_pronoun] spent [PAWN_possessive] youth exploring the land and making friends with various bugs and insects.

Having heard stories about all the fascinating things out there in the universe, [PAWN_pronoun] always dreamed of venturing out to see it for [PAWN_objective]self.</description>  
    <skillGains>
      <Social>-1</Social>    
      <Cooking>2</Cooking>    
      <Artistic>3</Artistic>    
      <Crafting>3</Crafting>
    </skillGains>  
    <workDisables>Violent</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>BlacksmithsSon73</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>BlacksmithsSon73</identifier>  
    <slot>Childhood</slot>  
    <title>blacksmith's son</title>  
    <titleShort>blacksmith</titleShort>  
    <description>[PAWN_nameDef]'s father owned the blacksmith shop in an run-down old city district. [PAWN_nameDef] would help [PAWN_possessive] father whenever [PAWN_pronoun] could.

Later, [PAWN_possessive] father took [PAWN_objective] as a blacksmith's apprentice and raised [PAWN_objective] as a man. [PAWN_nameDef] also took on shooting as a hobby.</description>  
    <skillGains>
      <Construction>3</Construction>    
      <Shooting>3</Shooting>    
      <Artistic>-3</Artistic>    
      <Crafting>3</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>JoywireAddict76</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>JoywireAddict76</identifier>  
    <slot>Childhood</slot>  
    <title>joywire addict</title>  
    <titleShort>addict</titleShort>  
    <description>[PAWN_nameDef]'s wealthy parents provided everything [PAWN_pronoun] ever wanted. After discovering joywires, [PAWN_pronoun] became obsessed. Once [PAWN_possessive] parents realized what happened, they cut [PAWN_objective] off.

Uncaring and often violent, [PAWN_pronoun] sought [PAWN_possessive] next joywire fix by any means possible.</description>  
    <skillGains>
      <Shooting>4</Shooting>    
      <Melee>6</Melee>    
      <Social>-3</Social>
    </skillGains>  
    <workDisables>Intellectual, Cleaning</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ChildKnave69</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ChildKnave69</identifier>  
    <slot>Childhood</slot>  
    <title>child-knave</title>  
    <titleShort>knave</titleShort>  
    <description>[PAWN_nameDef] was a child-knave of King Loteric. [PAWN_pronoun] enjoyed the training with wooden sticks and engaging others in close combat. [PAWN_pronoun] adapted quickly to the heat of battle and was good at spreading fire.

[PAWN_pronoun] served [PAWN_possessive] lord well, until the king died in an unfortunate accident.</description>  
    <skillGains>
      <Shooting>-3</Shooting>    
      <Melee>4</Melee>    
      <Social>3</Social>
    </skillGains>  
    <workDisables>Firefighting</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Brawler>0</Brawler>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>WastelandWanderer81</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>WastelandWanderer81</identifier>  
    <slot>Childhood</slot>  
    <title>wasteland wanderer</title>  
    <titleShort>wanderer</titleShort>  
    <description>Born into a wealthy family on a desert world, [PAWN_nameDef] was cast out after a hired attack squad destroyed [PAWN_possessive] ancestral home.

The boy managed to hide in a cooling duct beneath [PAWN_possessive] family's villa. Vowing revenge against the killers and whomever had hired them, [PAWN_nameDef] struck out into the wasteland, alone.</description>  
    <skillGains>
      <Shooting>3</Shooting>    
      <Social>3</Social>    
      <Medicine>5</Medicine>    
      <Crafting>4</Crafting>
    </skillGains>  
    <workDisables>Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>SocialPariah3</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>SocialPariah3</identifier>  
    <slot>Childhood</slot>  
    <title>social pariah</title>  
    <titleShort>pariah</titleShort>  
    <description>[PAWN_nameDef]'s family never fit in. They were oddballs who couldn't conform to their regimented society. Because of this, they were forced into cryptosleep and sent to different rimworlds, never to see each other again.</description>  
    <skillGains>
      <Crafting>2</Crafting>    
      <Animals>2</Animals>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>PyroAssistant82</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>PyroAssistant82</identifier>  
    <slot>Childhood</slot>  
    <title>pyro assistant</title>  
    <titleShort>assistant</titleShort>  
    <description>On an industrial world, [PAWN_nameDef] learned early that if [PAWN_pronoun] wanted to eat, [PAWN_pronoun] had to work. So work [PAWN_pronoun] did. Kids fit in places adults can't, and where [PAWN_pronoun] was from, the safety laws were quite flexible.

[PAWN_pronoun] was always a bit of a pyromaniac, and was banned from the kitchen after an unfortunate incident.</description>  
    <skillGains>
      <Construction>4</Construction>    
      <Medicine>3</Medicine>
    </skillGains>  
    <workDisables>Cooking, Firefighting</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Pyromaniac>0</Pyromaniac>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>StreetUrchin74</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>StreetUrchin74</identifier>  
    <slot>Childhood</slot>  
    <title>street urchin</title>  
    <titleShort>grifter</titleShort>  
    <description>After running away from home, [PAWN_nameDef] had to live on the streets.

[PAWN_pronoun] grew up fast, and learned early that [PAWN_pronoun] had to figure out how to fool people into connecting with [PAWN_objective].</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Plants>-2</Plants>    
      <Shooting>2</Shooting>    
      <Melee>3</Melee>    
      <Social>2</Social>
    </skillGains>  
    <workDisables>Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>RitualChild20</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>RitualChild20</identifier>  
    <slot>Childhood</slot>  
    <title>ritual child</title>  
    <titleShort>sacrifice</titleShort>  
    <description>Born with a special mark on [PAWN_possessive] neck, [PAWN_nameDef]'s tribe chose to sacrifice [PAWN_objective] in a blood ritual. They left [PAWN_objective] in the forest to die. A pack of arctic wolves found and adopted [PAWN_objective].

Over time, [PAWN_pronoun] learned to make tools to hunt with the pack.</description>  
    <skillGains>
      <Intellectual>-3</Intellectual>    
      <Melee>3</Melee>    
      <Crafting>3</Crafting>    
      <Animals>4</Animals>
    </skillGains>  
    <workDisables>Social</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>BrothelGofer84</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>BrothelGofer84</identifier>  
    <slot>Childhood</slot>  
    <title>brothel gofer</title>  
    <titleShort>gofer</titleShort>  
    <description>Born on a steam-powered midworld wracked by a century of economic crises, [PAWN_nameDef] went to work at a young age. Scrounging, shoe-polishing, sewing - [PAWN_pronoun] did it all. But [PAWN_pronoun] was most known as a brothel gofer, delivering all manner of aphrodisiacs and chemicals to the girls' rooms.</description>  
    <skillGains>
      <Social>2</Social>    
      <Cooking>2</Cooking>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>LaborCampOrphan91</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>LaborCampOrphan91</identifier>  
    <slot>Childhood</slot>  
    <title>labor camp orphan</title>  
    <titleShort>orphan</titleShort>  
    <description>[PAWN_nameDef] lost [PAWN_possessive] parents in an industrial accident and grew up in an orphans' labor camp. The camp overseers worked their charges mercilessly - but at least [PAWN_pronoun] was clothed and fed.</description>  
    <skillGains>
      <Construction>4</Construction>    
      <Mining>4</Mining>
    </skillGains>  
    <workDisables>Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>GameFanatic86</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>GameFanatic86</identifier>  
    <slot>Childhood</slot>  
    <title>game fanatic</title>  
    <titleShort>gamer</titleShort>  
    <description>[PAWN_nameDef] was a fanatical gamer who learned to survive in virtual reality games.

Once, while attempting to prove [PAWN_possessive] skills in the real world, [PAWN_pronoun] was abducted by a criminal scientist and experimented on. [PAWN_pronoun] escaped, but the experience stayed with him.</description>  
    <skillGains>
      <Intellectual>3</Intellectual>    
      <Shooting>3</Shooting>
    </skillGains>  
    <workDisables>Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ClassClown96</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ClassClown96</identifier>  
    <slot>Childhood</slot>  
    <title>class clown</title>  
    <titleShort>clowny kid</titleShort>  
    <description>In the ultra-competitive environs of [PAWN_possessive] glitterworld school, [PAWN_nameDef] always offered to play the class clown to diffuse tension.

[PAWN_pronoun] developed a sense of social desperation from this, as well as an appreciation for the artistic side of comedy.</description>  
    <skillGains>
      <Social>-3</Social>    
      <Artistic>4</Artistic>
    </skillGains>  
    <workDisables>Violent</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>CrimeBossChild39</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>CrimeBossChild39</identifier>  
    <slot>Childhood</slot>  
    <title>crime boss' child</title>  
    <titleShort>crime kid</titleShort>  
    <description>[PAWN_nameDef] was adopted child of a notorious crime boss. [PAWN_pronoun] was taught all facets of the family business, from smuggling to inventive ways of killing.

When the family was attacked, [PAWN_possessive] father ordered [PAWN_objective] to hide in a secret cryptosleep vault.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Shooting>2</Shooting>    
      <Melee>2</Melee>
    </skillGains>  
    <workDisables>Caring, Artistic, Cooking</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>SentimentalChild55</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>SentimentalChild55</identifier>  
    <slot>Childhood</slot>  
    <title>sentimental child</title>  
    <titleShort>nice kid</titleShort>  
    <description>[PAWN_nameDef] was a nice child. When anybody had a problem, [PAWN_pronoun] tried to fix it. But, being a child, [PAWN_pronoun] did not always succeed. This hurt [PAWN_possessive] sensitive soul.

[PAWN_pronoun] dreamed of being a soldier who fights raiders and aliens.</description>  
    <skillGains>
      <Social>4</Social>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <PsychicSensitivity>1</PsychicSensitivity>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>DustyFarmHand75</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>DustyFarmHand75</identifier>  
    <slot>Childhood</slot>  
    <title>dusty farm hand</title>  
    <titleShort>farm hand</titleShort>  
    <description>From the time [PAWN_nameDef] could walk, [PAWN_pronoun] helped take care of the animals and crops that [PAWN_possessive] people tended in their arid homeland.

The work toughened [PAWN_objective], but left little time for intellectual activities.</description>  
    <skillGains>
      <Plants>2</Plants>    
      <Medicine>1</Medicine>    
      <Animals>2</Animals>
    </skillGains>  
    <workDisables>Intellectual, Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Tough>0</Tough>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>HackerKid98</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>HackerKid98</identifier>  
    <slot>Childhood</slot>  
    <title>hacker kid</title>  
    <titleShort>hacker</titleShort>  
    <description>Born on a high-tech world, [PAWN_nameDef] learned to hack computers at a young age. 

Spending many hours tinkering alone made [PAWN_objective] very good with machines, but very bad with humans.</description>  
    <skillGains>
      <Construction>3</Construction>    
      <Crafting>3</Crafting>
    </skillGains>  
    <workDisables>Social</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>VideoGamer16</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>VideoGamer16</identifier>  
    <slot>Childhood</slot>  
    <title>video gamer</title>  
    <titleShort>gamer</titleShort>  
    <description>[PAWN_nameDef] grew up on a midworld, mostly locked in [PAWN_possessive] room, playing video games. [PAWN_pronoun] was warm-hearted, with a deep fondness for animals.

As [PAWN_pronoun] grew up, [PAWN_pronoun] turned to drugs to enhance [PAWN_possessive] reality. During these trying times [PAWN_pronoun] found passion and refuge in the gym.</description>  
    <skillGains>
      <Intellectual>3</Intellectual>    
      <Shooting>2</Shooting>    
      <Melee>1</Melee>
    </skillGains>  
    <workDisables>Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <DrugDesire>2</DrugDesire>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Student65</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Student65</identifier>  
    <slot>Childhood</slot>  
    <title>student</title>  
    <titleShort>student</titleShort>  
    <description>[PAWN_nameDef] studied chemistry and quantum mechanics for entertainment.

[PAWN_pronoun] was very good at convincing others to do [PAWN_possessive] physical work, and made it a goal in life to avoid manual labor.</description>  
    <skillGains>
      <Intellectual>3</Intellectual>    
      <Mining>-2</Mining>    
      <Social>2</Social>    
      <Medicine>3</Medicine>
    </skillGains>  
    <workDisables>Cleaning, Hauling</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>SuperSoldier99</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>SuperSoldier99</identifier>  
    <slot>Childhood</slot>  
    <title>super soldier</title>  
    <titleShort>soldier</titleShort>  
    <description>[PAWN_nameDef]'s government ran an experimental super-soldier development program. They kidnapped thousands of infants, conditioned their minds with drugs and machines, and trained them to fight.

Most infants died, but [PAWN_nameDef] did not. [PAWN_pronoun] killed [PAWN_possessive] first man at age six, and was fighting in field operations not long after.</description>  
    <skillGains>
      <Plants>-3</Plants>    
      <Shooting>3</Shooting>    
      <Melee>3</Melee>    
      <Medicine>2</Medicine>
    </skillGains>  
    <workDisables>Social, Animals, Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>HeadjackAddict4</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>HeadjackAddict4</identifier>  
    <slot>Childhood</slot>  
    <title>headjack addict</title>  
    <titleShort>headjacker</titleShort>  
    <description>Plugged in to the computer via head-jack for days at a time, [PAWN_nameDef] found [PAWN_pronoun] preferred the company of computers to that of people or pets.</description>  
    <skillGains>
      <Plants>-3</Plants>    
      <Intellectual>4</Intellectual>    
      <Shooting>2</Shooting>
    </skillGains>  
    <workDisables>Social, Animals</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>UrbworldArmyBrat84</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>UrbworldArmyBrat84</identifier>  
    <slot>Childhood</slot>  
    <title>urbworld army brat</title>  
    <titleShort>army brat</titleShort>  
    <description>[PAWN_nameDef] grew up on an urbworld military base. [PAWN_pronoun] was too young to fight, but [PAWN_pronoun] learned to make [PAWN_objective]self useful in other ways.

[PAWN_pronoun] became familiar with guns and military equipment, but never really got in touch with the natural world.</description>  
    <skillGains>
      <Construction>3</Construction>    
      <Plants>-3</Plants>    
      <Shooting>3</Shooting>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>PlagueChild44</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>PlagueChild44</identifier>  
    <slot>Childhood</slot>  
    <title>plague child</title>  
    <titleShort>child</titleShort>  
    <description>Born on a world wracked by plague, both of [PAWN_nameDef]'s parents were doctors. 

[PAWN_nameDef] was raised in reverse-quarantine, under the Hippocratic oath. [PAWN_pronoun] experienced little social interaction. However, [PAWN_pronoun] gained a lot of medical experience assisting in treatments.</description>  
    <skillGains>
      <Intellectual>1</Intellectual>    
      <Social>-3</Social>    
      <Medicine>5</Medicine>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>Violent, Animals, Artistic, Cooking</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>TechnicalKid1</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>TechnicalKid1</identifier>  
    <slot>Childhood</slot>  
    <title>technical kid</title>  
    <titleShort>tech kid</titleShort>  
    <description>[PAWN_nameDef] loved nothing more then to forget the world around [PAWN_objective] and focus on the task at hand.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Medicine>3</Medicine>    
      <Crafting>2</Crafting>    
      <Animals>2</Animals>
    </skillGains>  
    <workDisables>Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>YoungMaster23</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>YoungMaster23</identifier>  
    <slot>Childhood</slot>  
    <title>young master</title>  
    <titleShort>master</titleShort>  
    <description>Born to two medics, [PAWN_nameDef] longed for power. [PAWN_possessive] keen intellect and charisma helped [PAWN_objective] gain the respect of [PAWN_possessive] peers.

Deciding that the best way to gain power was to climb the ranks of the imperial military, [PAWN_pronoun] joined at the youngest possible age.</description>  
    <skillGains>
      <Intellectual>2</Intellectual>    
      <Shooting>1</Shooting>    
      <Melee>2</Melee>    
      <Social>2</Social>
    </skillGains>  
    <workDisables>ManualDumb</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>CaravanTraveler6</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>CaravanTraveler6</identifier>  
    <slot>Childhood</slot>  
    <title>caravan traveler</title>  
    <titleShort>traveler</titleShort>  
    <description>Born to a family of traveling merchants, [PAWN_nameDef] was mentored by [PAWN_possessive] father in the ways of being a trader. [PAWN_pronoun] was often tasked with caring for the pack animals. Due to [PAWN_possessive] nomadic lifestyle, [PAWN_pronoun] hunted and bartered for [PAWN_possessive] food.</description>  
    <skillGains>
      <Plants>-3</Plants>    
      <Shooting>1</Shooting>    
      <Social>2</Social>    
      <Animals>3</Animals>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>MidworldLoner80</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>MidworldLoner80</identifier>  
    <slot>Childhood</slot>  
    <title>midworld loner</title>  
    <titleShort>loner</titleShort>  
    <description>[PAWN_nameDef] was born on a peaceful, unimportant midworld.

While [PAWN_pronoun] had a few friends, [PAWN_pronoun] preferred to spend [PAWN_possessive] time in solitude, tinkering on [PAWN_possessive] autocycle while listening to netcasts, or plinking at plastic soldiers with a new rifle [PAWN_pronoun] had just put together.</description>  
    <skillGains>
      <Construction>3</Construction>    
      <Shooting>3</Shooting>    
      <Social>-2</Social>    
      <Crafting>5</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>JungleKid35</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>JungleKid35</identifier>  
    <slot>Childhood</slot>  
    <title>jungle kid</title>  
    <titleShort>jungle kid</titleShort>  
    <description>[PAWN_nameDef] was abandoned as a newborn, and grew up among the animals of [PAWN_possessive] homeworld's dense jungles.

When [PAWN_pronoun] was a teenager, a traveling doctor found [PAWN_objective] injured near a road, hissing and meowing. [PAWN_pronoun] rescued [PAWN_objective] and took [PAWN_objective] on as an apprentice.</description>  
    <skillGains>
      <Intellectual>-2</Intellectual>    
      <Melee>2</Melee>    
      <Cooking>2</Cooking>    
      <Animals>4</Animals>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>FarmersSon20</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>FarmersSon20</identifier>  
    <slot>Childhood</slot>  
    <title>farmer's son</title>  
    <titleShort>farmer</titleShort>  
    <description>[PAWN_nameDef] spent much of [PAWN_possessive] childhood learning biome farming. In [PAWN_possessive] biome they kept animals to investigate which would cope best in the alien environment.

Due to this, the travel restrictions and oxygen rationing system, [PAWN_pronoun] rarely got to meet anyone from outside [PAWN_possessive] family.</description>  
    <skillGains>
      <Plants>2</Plants>    
      <Social>-3</Social>    
      <Cooking>1</Cooking>    
      <Animals>4</Animals>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <spawnCategories>
      <li>Farmer</li>
    </spawnCategories>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>WealthyStudent59</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>WealthyStudent59</identifier>  
    <slot>Childhood</slot>  
    <title>wealthy student</title>  
    <titleShort>student</titleShort>  
    <description>[PAWN_nameDef] was born on a wealthy glitterworld. [PAWN_pronoun] studied at the Caspian school of Engineering, and excelled in mathematics and computer programming.

[PAWN_nameDef] grew up in clean, sterile environments. [PAWN_pronoun] never saw a farm or livestock.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Plants>-2</Plants>    
      <Intellectual>4</Intellectual>    
      <Mining>-2</Mining>    
      <Cooking>-2</Cooking>    
      <Crafting>3</Crafting>
    </skillGains>  
    <workDisables>Animals, Cleaning</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>StreetPeddler66</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>StreetPeddler66</identifier>  
    <slot>Childhood</slot>  
    <title>street peddler</title>  
    <titleShort>peddler</titleShort>  
    <description>Growing up on an industrial midworld where robberies and muggings weren't uncommon, [PAWN_nameDef] helped support [PAWN_possessive] family by making small trinkets and novelties to sell to passers-by.

[PAWN_pronoun] learned early on that it wasn't always what [PAWN_pronoun] knew, but who [PAWN_pronoun] knew that would get [PAWN_objective] far in life.</description>  
    <skillGains>
      <Melee>2</Melee>    
      <Social>3</Social>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>BodyguardTrainee54</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>BodyguardTrainee54</identifier>  
    <slot>Childhood</slot>  
    <title>bodyguard trainee</title>  
    <titleShort>bodyguard</titleShort>  
    <description>[PAWN_nameDef] was born into a famously skilled family of bodyguards. [PAWN_possessive] mothers Elena and Victoria forced [PAWN_objective] into many dangerous situations, training [PAWN_objective] to put [PAWN_possessive] client's well-being over [PAWN_possessive] own.

[PAWN_nameDef] was taught about many herbs and natural poisons to help [PAWN_objective] prevent clients from being poisoned.</description>  
    <skillGains>
      <Plants>2</Plants>    
      <Shooting>3</Shooting>    
      <Melee>2</Melee>    
      <Medicine>2</Medicine>
    </skillGains>  
    <workDisables>Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpoiledChild80</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>SpoiledChild80</identifier>  
    <slot>Childhood</slot>  
    <title>spoiled child</title>  
    <titleShort>spoiled</titleShort>  
    <description>[PAWN_nameDef] was the child of an industrious space engineer. [PAWN_possessive] parents forbade most manual and dangerous tasks, so [PAWN_pronoun] learned art and music instead.

[PAWN_pronoun] loved animals, but [PAWN_possessive] parents never let [PAWN_objective] have a pet - though [PAWN_pronoun] played with other people's pets a lot.</description>  
    <skillGains>
      <Intellectual>3</Intellectual>    
      <Artistic>2</Artistic>    
      <Animals>3</Animals>
    </skillGains>  
    <workDisables>ManualDumb</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Philosopher82</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Philosopher82</identifier>  
    <slot>Childhood</slot>  
    <title>philosopher</title>  
    <titleShort>thinker</titleShort>  
    <description>[PAWN_nameDef] was a student on a glitterworld. [PAWN_pronoun] had a hard time in school, since the other kids thought [PAWN_objective] rather strange.

[PAWN_pronoun] always maintained a distance from these events, being continuously surprised by the many interesting and awkward ways life can go.</description>  
    <skillGains>
      <Intellectual>4</Intellectual>    
      <Social>3</Social>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>Violent</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>CaveChild17</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>CaveChild17</identifier>  
    <slot>Childhood</slot>  
    <title>cave child</title>  
    <titleShort>cave kid</titleShort>  
    <description>[PAWN_nameDef] grew up in a cave on an tundra planet, and was adopted by a traveling group of entertainers known only as the Wizards. Once a starfaring crew, the Wizards told [PAWN_objective] wondrous stories about the universe.

One day, the Wizards did not return from scavenging. After years of waiting, [PAWN_nameDef] set off into space to find them.</description>  
    <skillGains>
      <Plants>2</Plants>    
      <Mining>2</Mining>    
      <Shooting>-2</Shooting>    
      <Melee>-2</Melee>    
      <Cooking>2</Cooking>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Undergrounder>0</Undergrounder>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>RangerChild57</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>RangerChild57</identifier>  
    <slot>Childhood</slot>  
    <title>ranger child</title>  
    <titleShort>ranger kid</titleShort>  
    <description>[PAWN_nameDef] was raised in a forest. [PAWN_possessive] father taught [PAWN_objective] to hunt and live off the land, so that [PAWN_pronoun] could live without the need of others when father died.

Accustomed to manual labor and with an intuitive mind, [PAWN_nameDef] could survive weeks alone.</description>  
    <skillGains>
      <Mining>-2</Mining>    
      <Shooting>2</Shooting>    
      <Cooking>2</Cooking>    
      <Animals>4</Animals>
    </skillGains>  
    <workDisables>Firefighting, PlantWork</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>GlitterworldRoyal24</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>GlitterworldRoyal24</identifier>  
    <slot>Childhood</slot>  
    <title>glitterworld royal</title>  
    <titleShort>royalty</titleShort>  
    <description>[PAWN_nameDef] grew up in a glitterworld royal household. [PAWN_pronoun] was groomed from a young age to marry into another planet's royal family.

Unfortunately, [PAWN_possessive] crooked teeth made [PAWN_objective] undesirable to suitors, so [PAWN_pronoun] became bitter and resentful.</description>  
    <skillGains>
      <Social>2</Social>    
      <Cooking>2</Cooking>    
      <Artistic>2</Artistic>
    </skillGains>  
    <workDisables>ManualDumb</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Beauty>-1</Beauty>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>PlagueSurvivor39</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>PlagueSurvivor39</identifier>  
    <slot>Childhood</slot>  
    <title>plague survivor</title>  
    <titleShort>survivor</titleShort>  
    <description>[PAWN_nameDef] watched as a mysterious plague spread through [PAWN_possessive] town, killing [PAWN_possessive] family and friends.

[PAWN_pronoun] learned some medicine from watching the plague doctors, but was mentally scarred by the ordeal.</description>  
    <skillGains>
      <Social>-2</Social>    
      <Cooking>2</Cooking>    
      <Medicine>3</Medicine>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Nerves>-1</Nerves>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>OffworldRecruit91</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>OffworldRecruit91</identifier>  
    <slot>Childhood</slot>  
    <title>offworld recruit</title>  
    <titleShort>recruit</titleShort>  
    <description>[PAWN_nameDef] was born an raised in an offworld soldier growth facility. From a very young age, [PAWN_pronoun] was taught close-quarters combat tactics, aggression, and how to survive on distant planets.

One of the best, [PAWN_nameDef] was eventually chose to enter the orbital-deployment shock troop corps.</description>  
    <skillGains>
      <Shooting>2</Shooting>    
      <Melee>2</Melee>    
      <Medicine>2</Medicine>
    </skillGains>  
    <workDisables>Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ProdigalStudent67</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ProdigalStudent67</identifier>  
    <slot>Childhood</slot>  
    <title>prodigal student</title>  
    <titleShort>student</titleShort>  
    <description>[PAWN_nameDef] was a faithful student, and was dedicated to learning anything and everything [PAWN_pronoun] could about humanity and its creations.

While [PAWN_pronoun] was shunned as a nerd, [PAWN_pronoun] didn't mind. [PAWN_pronoun] hoped for a brighter future.</description>  
    <skillGains>
      <Intellectual>3</Intellectual>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>Animals</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>AnimalCaretaker20</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>AnimalCaretaker20</identifier>  
    <slot>Childhood</slot>  
    <title>animal caretaker</title>  
    <titleShort>caretaker</titleShort>  
    <description>Born on a medieval farm, [PAWN_nameDef] was tasked with caring for domestic animals. [PAWN_pronoun] grew to love them.

With time, [PAWN_pronoun] learned to tame wild animals. [PAWN_pronoun] dreamed of one day meeting a thrumbo and taming it.</description>  
    <skillGains>
      <Plants>2</Plants>    
      <Animals>4</Animals>
    </skillGains>  
    <workDisables>Intellectual</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>AdventurousYouth70</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>AdventurousYouth70</identifier>  
    <slot>Childhood</slot>  
    <title>adventurous youth</title>  
    <titleShort>adventurer</titleShort>  
    <description>[PAWN_nameDef] explored all around [PAWN_possessive] family's large estate, uncovering little natural wonders hidden in nearby catacombs, rivers, and caves.

During [PAWN_possessive] teenage years, [PAWN_nameDef] made numerous enemies</description>  
    <skillGains>
      <Melee>2</Melee>    
      <Social>2</Social>    
      <Cooking>2</Cooking>
    </skillGains>  
    <workDisables>Cleaning</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>PilotFan16</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>PilotFan16</identifier>  
    <slot>Childhood</slot>  
    <title>pilot fan</title>  
    <titleShort>pilot fan</titleShort>  
    <description>[PAWN_nameDef]'s father was a starfighter pilot, and [PAWN_pronoun] always idolized the old man.

From a young age, [PAWN_pronoun] collected pilot memorabilia and studied pilot books, preparing to join the deep space navy and follow in [PAWN_possessive] father's footsteps.</description>  
    <skillGains>
      <Intellectual>3</Intellectual>    
      <Shooting>3</Shooting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>MedicalHelper27</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>MedicalHelper27</identifier>  
    <slot>Childhood</slot>  
    <title>medical helper</title>  
    <titleShort>med helper</titleShort>  
    <description>[PAWN_nameDef] traveled between rimworlds with [PAWN_possessive] family. [PAWN_nameDef]'s mother, a renowned doctor, often delivered lectures from the hull of their retrofitted cargo/medical ship.

Sometimes, the family took on difficult long-term medical work with especially needy patients, and [PAWN_nameDef] helped where [PAWN_pronoun] could.</description>  
    <skillGains>
      <Social>2</Social>    
      <Medicine>4</Medicine>
    </skillGains>  
    <workDisables>Firefighting</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>CountryChild95</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>CountryChild95</identifier>  
    <slot>Childhood</slot>  
    <title>country child</title>  
    <titleShort>hick kid</titleShort>  
    <description>[PAWN_nameDef] was raised up to fear God and love [PAWN_possessive] country. [PAWN_pronoun] was a genuine backwoods kid. [PAWN_pronoun] always worked hard to earn respect rather than demanding it.</description>  
    <skillGains>
      <Cooking>2</Cooking>    
      <Animals>4</Animals>
    </skillGains>  
    <workDisables>Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Industriousness>1</Industriousness>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>RebelWriter82</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>RebelWriter82</identifier>  
    <slot>Childhood</slot>  
    <title>rebel writer</title>  
    <titleShort>writer</titleShort>  
    <description>Early on, [PAWN_nameDef] developed a talent for writing. [PAWN_pronoun] was soon writing a popular online journal - anonymously, to hide [PAWN_possessive] age - on topics ranging from bioethics to political theory and xenosociology.</description>  
    <skillGains>
      <Intellectual>7</Intellectual>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ExiledPrince58</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ExiledPrince58</identifier>  
    <slot>Childhood</slot>  
    <title>exiled prince</title>  
    <titleShort>prince</titleShort>  
    <description>A member of a royal family, [PAWN_nameDef] was exiled for shaming [PAWN_possessive] family.

Living in a foreign land, mostly alone, [PAWN_pronoun] spent most of [PAWN_possessive] time making huts and houses to live in. [PAWN_pronoun] found little time in an average day for enjoyment, and lost all [PAWN_possessive] friends and family.</description>  
    <skillGains>
      <Construction>3</Construction>    
      <Artistic>-2</Artistic>    
      <Crafting>2</Crafting>    
      <Animals>1</Animals>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>Caring</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>NewAgeDuelist27</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>NewAgeDuelist27</identifier>  
    <slot>Childhood</slot>  
    <title>new age duelist</title>  
    <titleShort>duelist</titleShort>  
    <description>[PAWN_nameDef] was fascinated with combat. [PAWN_possessive] parents traveled often, so [PAWN_pronoun] was able to sample many different fighting styles, from 76th-wave jujutsu to the infamous 'urbworld-style' karate.

A polite child, most fighters accepted [PAWN_possessive] requests for training - but moving around often without finishing a tutelage made [PAWN_objective] lazy.</description>  
    <skillGains>
      <Shooting>-2</Shooting>    
      <Melee>4</Melee>    
      <Animals>-2</Animals>
    </skillGains>  
    <workDisables>Cleaning</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Industriousness>-1</Industriousness>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpoiledBrat59</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>SpoiledBrat59</identifier>  
    <slot>Childhood</slot>  
    <title>spoiled brat</title>  
    <titleShort>brat</titleShort>  
    <description>Born in a rich family, [PAWN_nameDef] was given everything, and never developed basic work ethic or the foundations of a non-dependent personality.

[PAWN_pronoun] was expected to become one of the best doctors in the world.</description>  
    <skillGains>
      <Medicine>2</Medicine>
    </skillGains>  
    <workDisables>ManualSkilled, Social, Intellectual, Cooking</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>CowFarmer39</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>CowFarmer39</identifier>  
    <slot>Childhood</slot>  
    <title>cow farmer</title>  
    <titleShort>farmer</titleShort>  
    <description>[PAWN_nameDef] grew up on a rimworld, under the tyranny of [PAWN_possessive] father, who forced [PAWN_objective] to work every day on the farm. [PAWN_pronoun] dreamed of escaping [PAWN_possessive] father and exploring the universe.

One day, when the trade ship was overhead, [PAWN_pronoun] stowed away among the goods in the launch pods, and escaped into space.</description>  
    <skillGains>
      <Plants>4</Plants>    
      <Animals>3</Animals>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>DiscardedYouth93</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>DiscardedYouth93</identifier>  
    <slot>Childhood</slot>  
    <title>discarded youth</title>  
    <titleShort>discarded</titleShort>  
    <description>[PAWN_nameDef] grew up isolated on a trash planet. A dumping ground for surrounding glitterworlds, it was a harsh home. As a baby, [PAWN_pronoun] sucked on the tap of a discarded nutrient paste dispenser for comfort.

In these desperate circumstances, against all odds, [PAWN_pronoun] survived - and thrived.</description>  
    <skillGains>
      <Construction>3</Construction>    
      <Crafting>3</Crafting>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>Artistic, Cleaning, PlantWork</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>DisasterSurvivor29</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>DisasterSurvivor29</identifier>  
    <slot>Childhood</slot>  
    <title>disaster survivor</title>  
    <titleShort>survivor</titleShort>  
    <description>[PAWN_nameDef]'s planet collided with a small moon. Everything [PAWN_pronoun] knew as home was destroyed, and [PAWN_possessive] entire family died. [PAWN_pronoun] was left in the ruins to fend for [PAWN_objective]self.</description>  
    <skillGains>
      <Construction>3</Construction>    
      <Cooking>2</Cooking>    
      <Crafting>1</Crafting>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>Animals</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>RebelChild8</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>RebelChild8</identifier>  
    <slot>Childhood</slot>  
    <title>rebel child</title>  
    <titleShort>rebel</titleShort>  
    <description>[PAWN_nameDef] was born under authoritarian rule. Despite intense oppression from family and community, [PAWN_pronoun] never gave up fighting for [PAWN_possessive] dignity and [PAWN_possessive] freedom.

[PAWN_pronoun] was often put into hopeless situations where even family would try to terrorize [PAWN_objective] into propriety. [PAWN_pronoun] learned to trust no one.</description>  
    <skillGains>
      <Melee>2</Melee>    
      <Social>3</Social>
    </skillGains>  
    <workDisables>Caring</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>MidworldCadet83</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>MidworldCadet83</identifier>  
    <slot>Childhood</slot>  
    <title>midworld cadet</title>  
    <titleShort>cadet</titleShort>  
    <description>Growing up on a high-tech midworld with a flourishing space transit industry, [PAWN_nameDef] wished to leave for the stars and live among the growing spacer class.

[PAWN_pronoun] excelled in [PAWN_possessive] studies, and gained entry to [PAWN_possessive] homeworld's most prestigious naval academy.</description>  
    <skillGains>
      <Intellectual>3</Intellectual>    
      <Social>3</Social>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ForestChild83</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ForestChild83</identifier>  
    <slot>Childhood</slot>  
    <title>forest child</title>  
    <titleShort>forest kid</titleShort>  
    <description>When [PAWN_pronoun] was a baby, [PAWN_nameDef]'s mother went insane and left [PAWN_objective] in the woods.

Raised by wild people, [PAWN_nameDef] was known for both loving and killing animals.</description>  
    <skillGains>
      <Melee>2</Melee>    
      <Animals>2</Animals>
    </skillGains>  
    <workDisables>Intellectual</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <spawnCategories>
      <li>Tribal</li>
    </spawnCategories>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>VatgrownScientist91</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>VatgrownScientist91</identifier>  
    <slot>Childhood</slot>  
    <title>vatgrown scientist</title>  
    <titleShort>scientist</titleShort>  
    <description>[PAWN_nameDef] was grown to be a perfect scientist, with a mind perfectly tuned for physics and chemistry.

[PAWN_possessive] creators used [PAWN_objective] as a teaching aid on mind design, hoping [PAWN_pronoun] would eventually grow to be a great mind and continue their research.</description>  
    <skillGains>
      <Intellectual>5</Intellectual>    
      <Medicine>2</Medicine>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>MusicIdol50</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>MusicIdol50</identifier>  
    <slot>Childhood</slot>  
    <title>music idol</title>  
    <titleShort>music idol</titleShort>  
    <description>[PAWN_nameDef] was the only child of a powerful company president. [PAWN_possessive] beautiful appearance led to a career as a music idol.

When [PAWN_possessive] mother died, [PAWN_nameDef] could not bear [PAWN_possessive] father's loneliness, and became his sexual companion.</description>  
    <skillGains>
      <Intellectual>2</Intellectual>    
      <Mining>-2</Mining>    
      <Melee>-2</Melee>    
      <Social>3</Social>    
      <Artistic>2</Artistic>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Beauty>2</Beauty>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>SicklyLiar3</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>SicklyLiar3</identifier>  
    <slot>Childhood</slot>  
    <title>sickly liar</title>  
    <titleShort>liar</titleShort>  
    <description>It is not uncommon for a sickly child to be cast away. What is uncommon is for that child to survive and even thrive with such a weak body.

Using [PAWN_possessive] quick wit and silver tongue, [PAWN_nameDef] was able to get out of almost any situation - most of the time at the cost others around him.</description>  
    <skillGains>
      <Construction>-2</Construction>    
      <Shooting>-2</Shooting>    
      <Melee>-2</Melee>    
      <Social>6</Social>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>Caring</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Immunity>-1</Immunity>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>IceworldSurvivor75</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>IceworldSurvivor75</identifier>  
    <slot>Childhood</slot>  
    <title>iceworld survivor</title>  
    <titleShort>iceborn</titleShort>  
    <description>[PAWN_nameDef] was born on an iceworld. Survival depended on staying together and building with nothing.

[PAWN_pronoun] got used to the cold. [PAWN_pronoun] never got used to dealing with those strange green things called plants.</description>  
    <skillGains>
      <Construction>3</Construction>    
      <Social>1</Social>    
      <Crafting>1</Crafting>
    </skillGains>  
    <workDisables>PlantWork</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>TragicLoner87</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>TragicLoner87</identifier>  
    <slot>Childhood</slot>  
    <title>tragic loner</title>  
    <titleShort>loner</titleShort>  
    <description>[PAWN_nameDef] was involved in a traumatic accident that resulted in [PAWN_objective] killing [PAWN_possessive] own family.

Cast out from society, [PAWN_pronoun] was left with only [PAWN_possessive] animal companions and several engineering textbooks to keep [PAWN_objective] company.</description>  
    <skillGains>
      <Intellectual>4</Intellectual>    
      <Artistic>-2</Artistic>    
      <Animals>2</Animals>
    </skillGains>  
    <workDisables>Social</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>SchoolyardOutcast11</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>SchoolyardOutcast11</identifier>  
    <slot>Childhood</slot>  
    <title>schoolyard outcast</title>  
    <titleShort>outcast</titleShort>  
    <description>[PAWN_nameDef] had a modest but proper upbringing. Despite a good family background and plenty of support, [PAWN_pronoun] struggled to make friends and was often bullied. [PAWN_pronoun] learned to work or play alone, and avoided socializing to avoid conflict.

When conflict did arise, [PAWN_nameDef] would retaliate violently.</description>  
    <skillGains>
      <Intellectual>2</Intellectual>    
      <Melee>2</Melee>    
      <Social>-3</Social>    
      <Artistic>2</Artistic>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>AntisocialChild83</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>AntisocialChild83</identifier>  
    <slot>Childhood</slot>  
    <title>antisocial child</title>  
    <titleShort>antisocial</titleShort>  
    <description>[PAWN_nameDef] lacked typical social skills, and avoided social interaction.

[PAWN_pronoun] gained satisfaction only from steady work. [PAWN_pronoun] especially disliked speaking with overly creative or outgoing individuals.</description>  
    <skillGains>
      <Intellectual>3</Intellectual>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>Social, Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Industriousness>1</Industriousness>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>YoungPirate71</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>YoungPirate71</identifier>  
    <slot>Childhood</slot>  
    <title>young pirate</title>  
    <titleShort>pirate</titleShort>  
    <description>[PAWN_nameDef] was born to pirates, in a dirty metal room that smelled of motor oil.

[PAWN_pronoun] was never exactly taught anything, but [PAWN_pronoun] still learned the ways of survival, barter, and combat.</description>  
    <skillGains>
      <Shooting>2</Shooting>    
      <Social>2</Social>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>SoldierExperiment50</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>SoldierExperiment50</identifier>  
    <slot>Childhood</slot>  
    <title>soldier experiment</title>  
    <titleShort>experiment</titleShort>  
    <description>[PAWN_nameDef] grew up believing [PAWN_pronoun] was a normal, albeit slightly sheltered child.

In truth, [PAWN_pronoun] was part of an elaborate experiment. Researchers were conditioning [PAWN_objective] using social interactions and mechanite injections to try to produce an elite soldier.</description>  
    <skillGains>
      <Shooting>3</Shooting>    
      <Melee>3</Melee>    
      <Social>-3</Social>    
      <Medicine>2</Medicine>
    </skillGains>  
    <workDisables>Cooking, Cleaning</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>DedicatedStudent12</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>DedicatedStudent12</identifier>  
    <slot>Childhood</slot>  
    <title>dedicated student</title>  
    <titleShort>student</titleShort>  
    <description>The child of a wealthy manufacturer, [PAWN_nameDef] was pampered from an early age. [PAWN_pronoun] developed an affinity for reading and art, and a distaste for menial chores.

A revolution brought [PAWN_possessive] father's businesses under state control. Penniless, [PAWN_nameDef] worked hard to complete [PAWN_possessive] education.</description>  
    <skillGains>
      <Intellectual>2</Intellectual>    
      <Artistic>2</Artistic>
    </skillGains>  
    <workDisables>Animals, Cooking, Cleaning</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Industriousness>1</Industriousness>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Infantry99</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Infantry99</identifier>  
    <slot>Childhood</slot>  
    <title>infantry</title>  
    <titleShort>infantry</titleShort>  
    <description>[PAWN_nameDef] was raised on a world wracked by war.

At an early age, [PAWN_pronoun] was shown how to use guns and cruelty to project [PAWN_possessive] will. [PAWN_pronoun] later distinguished [PAWN_objective]self by committing atrocities with more enthusiasm than anyone else.</description>  
    <skillGains>
      <Shooting>2</Shooting>    
      <Melee>1</Melee>    
      <Social>2</Social>
    </skillGains>  
    <workDisables>Caring</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Bloodlust>0</Bloodlust>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>AngryStudent88</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>AngryStudent88</identifier>  
    <slot>Childhood</slot>  
    <title>angry student</title>  
    <titleShort>student</titleShort>  
    <description>[PAWN_nameDef] trained towards [PAWN_possessive] dream of working for [PAWN_possessive] hometown's local council.

For reasons [PAWN_pronoun] never quite understood, [PAWN_pronoun] was bullied relentlessly by [PAWN_possessive] classmates. [PAWN_pronoun] chose to repress [PAWN_possessive] rage.</description>  
    <skillGains>
      <Intellectual>4</Intellectual>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>TransferStudent10</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>TransferStudent10</identifier>  
    <slot>Childhood</slot>  
    <title>transfer student</title>  
    <titleShort>student</titleShort>  
    <description>A midworld child, [PAWN_nameDef]'s wealthy parents sent [PAWN_objective] to a nearby glitterworld to receive a better education.

[PAWN_nameDef] was a talented student. However, due to [PAWN_possessive] family's wealth, [PAWN_pronoun] never had to do dumb labor.</description>  
    <skillGains>
      <Intellectual>3</Intellectual>    
      <Social>2</Social>
    </skillGains>  
    <workDisables>ManualDumb</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>DesertRat47</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>DesertRat47</identifier>  
    <slot>Childhood</slot>  
    <title>desert rat</title>  
    <titleShort>desert rat</titleShort>  
    <description>[PAWN_nameDef] was born to a tribe of desert ascetics who wandered the endless wastes.

They sought a mythical substance they believed could liberate them from ignorance through psychedelic revelation - and usher in a new period of interstellar peace.</description>  
    <skillGains>
      <Intellectual>2</Intellectual>    
      <Melee>2</Melee>    
      <Medicine>2</Medicine>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <forcedTraits>
      <Ascetic>0</Ascetic>
    </forcedTraits>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ArtisticWeirdo56</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ArtisticWeirdo56</identifier>  
    <slot>Childhood</slot>  
    <title>artistic weirdo</title>  
    <titleShort>weirdo</titleShort>  
    <description>[PAWN_nameDef] grew up in a house near a bustling midworld metropolis.

[PAWN_pronoun] was talented at art and creative work, but often acted strangely, and thus had few friends.</description>  
    <skillGains>
      <Cooking>1</Cooking>    
      <Artistic>3</Artistic>    
      <Crafting>2</Crafting>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>Caring, Social</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ApprenticeOracle83</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ApprenticeOracle83</identifier>  
    <slot>Childhood</slot>  
    <title>apprentice oracle</title>  
    <titleShort>oracle</titleShort>  
    <description>[PAWN_nameDef] was chosen at an early age by the village elders to keep the sacred rituals of the Oracle.

An irrepressibly curious child, [PAWN_nameDef] caused a religious crisis for [PAWN_possessive] tribe when [PAWN_pronoun] accidentally flipped a switch to open the "tombs" of the Gods - cryptosleep caskets inhabited by some very confused ancestors.</description>  
    <skillGains>
      <Intellectual>2</Intellectual>    
      <Social>3</Social>    
      <Medicine>2</Medicine>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>JunkyardMechanic51</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>JunkyardMechanic51</identifier>  
    <slot>Childhood</slot>  
    <title>junkyard mechanic</title>  
    <titleShort>mechanic</titleShort>  
    <description>Left for dead at a young age, [PAWN_nameDef] was rescued by an old man who ran an urbworld junkyard. The old man forced [PAWN_nameDef] to do dangerous, demanding work.

[PAWN_nameDef] became very familiar with machines, but [PAWN_pronoun] grew up with almost no other experiences.</description>  
    <skillGains>
      <Construction>2</Construction>    
      <Intellectual>2</Intellectual>    
      <Social>-3</Social>    
      <Crafting>4</Crafting>
    </skillGains>  
    <workDisables>Animals</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Empath47</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Empath47</identifier>  
    <slot>Childhood</slot>  
    <title>empath</title>  
    <titleShort>empath</titleShort>  
    <description>[PAWN_nameDef] was an incredibly empathetic child, so much that [PAWN_pronoun] was totally overwhelmed by simple social interactions. Once, [PAWN_pronoun] stepped on a bug and felt so guilty [PAWN_pronoun] cried for an hour.

[PAWN_nameDef] did try to use a gun one time, but it went very poorly, as the loud noise made [PAWN_objective] flinch uncontrollably.</description>  
    <skillGains>
      <Intellectual>2</Intellectual>    
      <Artistic>2</Artistic>
    </skillGains>  
    <workDisables>Violent, Social, Animals</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>AnimalLabTech39</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>AnimalLabTech39</identifier>  
    <slot>Childhood</slot>  
    <title>animal lab tech</title>  
    <titleShort>lab tech</titleShort>  
    <description>[PAWN_nameDef] worked as a technician in a lab studying animals.

[PAWN_pronoun] spent [PAWN_possessive] days taking care of the animals and cleaning up after them. [PAWN_pronoun] dreamed of having [PAWN_possessive] own lab one day.</description>  
    <skillGains>
      <Intellectual>1</Intellectual>    
      <Shooting>-2</Shooting>    
      <Medicine>2</Medicine>    
      <Animals>3</Animals>
    </skillGains>  
    <workDisables>PlantWork</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ShipChild46</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ShipChild46</identifier>  
    <slot>Childhood</slot>  
    <title>ship child</title>  
    <titleShort>ship child</titleShort>  
    <description>[PAWN_nameDef]'s family were space traders. One day, [PAWN_nameDef]'s cryptosleep pod failed in transit. [PAWN_pronoun] had to spend years awake on the ship as the other slept.

[PAWN_pronoun] filled [PAWN_possessive] days creating beautiful contraptions from the ship's cargo - but [PAWN_pronoun] didn't learn a lot of basic planetary living skills.</description>  
    <skillGains>
      <Melee>-3</Melee>    
      <Social>-3</Social>    
      <Artistic>4</Artistic>    
      <Crafting>4</Crafting>
    </skillGains>  
    <workDisables>Animals, Cooking, PlantWork, Mining</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>PetKeeper7</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>PetKeeper7</identifier>  
    <slot>Childhood</slot>  
    <title>pet keeper</title>  
    <titleShort>pet keeper</titleShort>  
    <description>[PAWN_nameDef] spent most of [PAWN_possessive] childhood tending the animals [PAWN_pronoun] had bought or rescued.

[PAWN_pronoun] saw beauty in natural things more than fabricated objects, and chose the company of [PAWN_possessive] animals over that of [PAWN_possessive] peers.</description>  
    <skillGains>
      <Medicine>1</Medicine>    
      <Animals>6</Animals>
    </skillGains>  
    <workDisables>Social, Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ChildResearcher32</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ChildResearcher32</identifier>  
    <slot>Childhood</slot>  
    <title>child researcher</title>  
    <titleShort>researcher</titleShort>  
    <description>A frightfully intelligent child, [PAWN_nameDef] was kidnapped by a glitterworld corporation and forced to research technologies for weaponizing anti-matter.

[PAWN_pronoun] vowed never to invent anything again.</description>  
    <skillGains>
      <Intellectual>7</Intellectual>    
      <Shooting>-2</Shooting>    
      <Melee>-2</Melee>
    </skillGains>  
    <workDisables>Crafting</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>VidtubeStar98</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>VidtubeStar98</identifier>  
    <slot>Childhood</slot>  
    <title>vidtube star</title>  
    <titleShort>vidtuber</titleShort>  
    <description>[PAWN_nameDef]'s hobby was making videos for the popular video sharing service Vidtube. [PAWN_pronoun] made everything from video game reviews to pasta cooking guides.

[PAWN_pronoun] gained over a million followers.</description>  
    <skillGains>
      <Social>2</Social>    
      <Cooking>2</Cooking>    
      <Artistic>2</Artistic>    
      <Crafting>1</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>CorporateSlave22</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>CorporateSlave22</identifier>  
    <slot>Childhood</slot>  
    <title>corporate slave</title>  
    <titleShort>soldier</titleShort>  
    <description>[PAWN_nameDef] grew up in a colony owned by a large glitterworld corporation. All children had virtual reality devices attached by force, and were raised in a brutal digital world.

Constantly forced to fight others for the entertainment of the wealthy, gunplay and tactics became the center of [PAWN_nameDef]'s universe.</description>  
    <skillGains>
      <Plants>-2</Plants>    
      <Shooting>4</Shooting>    
      <Social>4</Social>    
      <Animals>-2</Animals>
    </skillGains>  
    <workDisables>Cooking, Cleaning</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>AspiringPhysicist47</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>AspiringPhysicist47</identifier>  
    <slot>Childhood</slot>  
    <title>aspiring physicist</title>  
    <titleShort>student</titleShort>  
    <description>The daughter of an engineer and doctor, [PAWN_nameDef]'s early life was intellectually rich. After visiting a physics lab, [PAWN_pronoun] decided [PAWN_pronoun] wanted to be a quantum physicist.

Bitten by a camel, kicked off a horse, and chased by dogs, [PAWN_nameDef] developed a fear of animals.</description>  
    <skillGains>
      <Construction>1</Construction>    
      <Intellectual>3</Intellectual>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>Caring, Animals, Cooking</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>QuietNerd97</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>QuietNerd97</identifier>  
    <slot>Childhood</slot>  
    <title>quiet nerd</title>  
    <titleShort>nerd</titleShort>  
    <description>[PAWN_nameDef] was raised by doting parents on a midworld. Instead of playing with other children, [PAWN_pronoun] read books voraciously.\n\n[PAWN_pronoun] was not particularly rugged, and struggled with physical labor.</description>  
    <skillGains>
      <Intellectual>2</Intellectual>    
      <Social>-2</Social>    
      <Artistic>1</Artistic>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>ManualDumb</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
    <possessions>
      <TextBook>1</TextBook>
    </possessions>
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpacerOrphan77</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>SpacerOrphan77</identifier>  
    <slot>Childhood</slot>  
    <title>spacer orphan</title>  
    <titleShort>orphan</titleShort>  
    <description>Born among stars to a spacefaring family, the [PAWN_nameDef] was the only survivor when [PAWN_possessive] family's ship was destroyed.

[PAWN_pronoun] was forced to find a way to survive, and seek the joy of knowing another family.</description>  
    <skillGains>
      <Shooting>2</Shooting>    
      <Melee>4</Melee>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>Artistic, Mining</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>SmallTownKid41</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>SmallTownKid41</identifier>  
    <slot>Childhood</slot>  
    <title>small town kid</title>  
    <titleShort>town kid</titleShort>  
    <description>[PAWN_nameDef] grew up in an isolated midworld village, surrounded by opticows and countryside.

[PAWN_possessive] parents taught [PAWN_objective] a wide range of useful domestic skills, but a peaceful and secure childhood left [PAWN_objective] with little understanding of the harder parts of life.</description>  
    <skillGains>
      <Construction>-3</Construction>    
      <Plants>3</Plants>    
      <Mining>-3</Mining>    
      <Cooking>3</Cooking>    
      <Medicine>3</Medicine>    
      <Animals>3</Animals>
    </skillGains>  
    <workDisables>Violent</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>SteamworldTinker38</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>SteamworldTinker38</identifier>  
    <slot>Childhood</slot>  
    <title>steamworld tinker</title>  
    <titleShort>tinker</titleShort>  
    <description>In the slums of a steamworld, [PAWN_nameDef] developed a fascination with machines and contraptions. [PAWN_pronoun] found [PAWN_pronoun] preferred their company to that of other people. \n\n[PAWN_possessive] remarkable dexterity earned [PAWN_objective] a steady stream of pocket change, and [PAWN_pronoun] spent every penny on spare parts and tools to play with.</description>  
    <skillGains>
      <Construction>4</Construction>    
      <Social>-3</Social>    
      <Crafting>3</Crafting>
    </skillGains>  
    <workDisables>Artistic</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>
    <possessions>
      <Apparel_PackTurret MayRequire="Ludeon.RimWorld.Anomaly">1</Apparel_PackTurret>
    </possessions>
  </BackstoryDef>

  <BackstoryDef>
    <defName>AbandonedOrphan61</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>AbandonedOrphan61</identifier>  
    <slot>Childhood</slot>  
    <title>abandoned orphan</title>  
    <titleShort>orphan</titleShort>  
    <description>Abandoned at birth, young [PAWN_nameDef] started [PAWN_possessive] life in an orphanage.

A rascal and a scoundrel, [PAWN_pronoun] became a clever troublemaker.</description>  
    <skillGains>
      <Plants>-3</Plants>    
      <Shooting>2</Shooting>    
      <Melee>3</Melee>    
      <Social>2</Social>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpaceNerd97</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>SpaceNerd97</identifier>  
    <slot>Childhood</slot>  
    <title>space nerd</title>  
    <titleShort>space nerd</titleShort>  
    <description>[PAWN_nameDef] just wanted to be cool like [PAWN_possessive] favorite superhero All-Might. [PAWN_pronoun] dreamed of going on space adventures and exploring new worlds. [PAWN_pronoun] also dreamed of chocolate... space chocolate.</description>  
    <skillGains>
      <Intellectual>4</Intellectual>    
      <Social>-3</Social>    
      <Cooking>2</Cooking>    
      <Crafting>2</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ChildSpy84</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ChildSpy84</identifier>  
    <slot>Childhood</slot>  
    <title>child spy</title>  
    <titleShort>spy</titleShort>  
    <description>Children are often presumed innocent, and so make ideal spies. [PAWN_nameDef] was trained in the arts of infiltration and information-gathering when [PAWN_pronoun] was very young.

[PAWN_possessive] spent years behind enemy lines, gathering intel in a brutal war. During this time, [PAWN_pronoun] had limited opportunity for education.</description>  
    <skillGains>
      <Shooting>3</Shooting>    
      <Social>3</Social>    
      <Medicine>1</Medicine>
    </skillGains>  
    <workDisables>PlantWork</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>TurtleHerder41</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>TurtleHerder41</identifier>  
    <slot>Childhood</slot>  
    <title>turtle herder</title>  
    <titleShort>herder</titleShort>  
    <description>Born to settler folk, [PAWN_nameDef]'s family had a beloved herd of turtles, who protected them by absorbing shots from raiders.

At a young age, [PAWN_pronoun] was entrusted with the turtles. [PAWN_pronoun] bred them, successfully - too successfully. The massive turtle herds consumed every food source and destroyed the local ecosystem.</description>  
    <skillGains>
      <Shooting>2</Shooting>    
      <Social>1</Social>    
      <Artistic>-2</Artistic>    
      <Animals>5</Animals>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ComputerGeek62</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ComputerGeek62</identifier>  
    <slot>Childhood</slot>  
    <title>computer geek</title>  
    <titleShort>geek</titleShort>  
    <description>[PAWN_nameDef] spent most of [PAWN_possessive] childhood on [PAWN_possessive] computer, typing away, never knowing when to get up and eat.

[PAWN_pronoun] had a very narrow interest: hacking cryptosleep safety protocols.</description>  
    <skillGains>
      <Construction>3</Construction>    
      <Intellectual>3</Intellectual>    
      <Shooting>-2</Shooting>    
      <Animals>2</Animals>
    </skillGains>  
    <workDisables>Social, Cooking, PlantWork</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Freethinker38</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Freethinker38</identifier>  
    <slot>Childhood</slot>  
    <title>freethinker</title>  
    <titleShort>thinker</titleShort>  
    <description>[PAWN_nameDef] grew up on a toxic world with tyrannical leaders. While the rest of the population was high on the planet's narcotic exports, [PAWN_pronoun] joined a rebel militia.

After a risky operation against the regime failed, [PAWN_pronoun] fled the planet, staying alive by threatening mutual annihilation with a planetcracker antimatter bomb.</description>  
    <skillGains>
      <Intellectual>2</Intellectual>    
      <Shooting>3</Shooting>    
      <Social>2</Social>    
      <Artistic>2</Artistic>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>NavyPathfinder53</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>NavyPathfinder53</identifier>  
    <slot>Childhood</slot>  
    <title>navy pathfinder</title>  
    <titleShort>pathfinder</titleShort>  
    <description>[PAWN_nameDef] was raised and trained by a group of military explorers dedicated to charting pathways through deep space and on remote planets.</description>  
    <skillGains>
      <Intellectual>3</Intellectual>    
      <Shooting>2</Shooting>    
      <Social>3</Social>    
      <Cooking>-2</Cooking>    
      <Animals>4</Animals>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>HunterScavenger64</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>HunterScavenger64</identifier>  
    <slot>Childhood</slot>  
    <title>hunter scavenger</title>  
    <titleShort>scavenger</titleShort>  
    <description>Born to a family of raiders in the mudlands of Vinna, [PAWN_nameDef] survived by scavenging food and resources from nearby settlements with [PAWN_possessive] gang. [PAWN_pronoun] was the most ferocious of the bunch.</description>  
    <skillGains>
      <Social>2</Social>    
      <Cooking>2</Cooking>    
      <Medicine>2</Medicine>    
      <Artistic>3</Artistic>
    </skillGains>  
    <workDisables>Animals</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Computer80</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Computer80</identifier>  
    <slot>Childhood</slot>  
    <title>computer</title>  
    <titleShort>computer</titleShort>  
    <description>As a child, [PAWN_nameDef] crafted complex algorithms. [PAWN_pronoun] was used as a "human computer" for most of [PAWN_possessive] childhood. This left [PAWN_objective] socially awkward and rather scrawny.

        Despite the solitary confinement, [PAWN_nameDef] developed a great imagination to occupy [PAWN_possessive] idle time.</description>  
    <skillGains>
      <Construction>-2</Construction>    
      <Plants>-3</Plants>    
      <Intellectual>4</Intellectual>    
      <Artistic>1</Artistic>    
      <Crafting>3</Crafting>
    </skillGains>  
    <workDisables>Social</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>CommonerHeir5</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>CommonerHeir5</identifier>  
    <slot>Childhood</slot>  
    <title>commoner heir</title>  
    <titleShort>heir</titleShort>  
    <description>[PAWN_nameDef] was a street urchin on a feudal steamworld.

        After [PAWN_pronoun] tried to steal food from the palace, the emperor, desperate to secure an heir, took [PAWN_objective] in and groomed [PAWN_objective] to be next in line for the throne.</description>  
    <skillGains>
      <Intellectual>1</Intellectual>    
      <Melee>2</Melee>    
      <Crafting>3</Crafting>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>RebelliousStudent74</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>RebelliousStudent74</identifier>  
    <slot>Childhood</slot>  
    <title>rebellious student</title>  
    <titleShort>student</titleShort>  
    <description>[PAWN_nameDef] had a shot at an education, but [PAWN_pronoun] rebelled against the injustices [PAWN_pronoun] saw and was expelled from three schools. This left [PAWN_objective] with a sense of integrity, but a lack of social skills.

        [PAWN_nameDef] sought self-actualization. [PAWN_pronoun] spent [PAWN_possessive] days meditating and practicing katana and staff fighting techniques.</description>  
    <skillGains>
      <Intellectual>2</Intellectual>    
      <Melee>4</Melee>    
      <Social>-3</Social>    
      <Artistic>2</Artistic>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>SpaceyachtPilot89</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>SpaceyachtPilot89</identifier>  
    <slot>Childhood</slot>  
    <title>spaceyacht pilot</title>  
    <titleShort>pilot</titleShort>  
    <description>[PAWN_nameDef] ferried wealthy businessmen and politicians to and from [PAWN_possessive] home world.

        Witnessing the depths of hedonism these people descended to changed [PAWN_objective] in ways not even [PAWN_pronoun] understood.</description>  
    <skillGains>
      <Melee>2</Melee>    
      <Social>4</Social>
    </skillGains>  
    <workDisables>Cooking</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>ShunnedGirl30</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>ShunnedGirl30</identifier>  
    <slot>Childhood</slot>  
    <title>shunned girl</title>  
    <titleShort>shunned</titleShort>  
    <description>Freya was born into a small, poor family. She was bullied relentlessly, beaten and hated. As she grew up she developed an   interest in guns. She usually studied from books, but occasionally managed to get some practice time with the real thing. Freya later   discovered a talent for art and began to teach herself how to draw and paint.</description>  
    <skillGains>
      <Shooting>2</Shooting>    
      <Social>-2</Social>
    </skillGains>
    <workDisables>None</workDisables>
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>
    <possessions>
      <TextBook>1</TextBook>
    </possessions>
  </BackstoryDef>

  <BackstoryDef>
    <defName>MedievalNomad12</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>MedievalNomad12</identifier>  
    <slot>Childhood</slot>  
    <title>medieval nomad</title>  
    <titleShort>nomad</titleShort>  
    <description>Born to a nomadic family in a feudal society, [PAWN_nameDef] was instructed in the arts of speechcraft, sleight of hand, horse riding.

        [PAWN_pronoun] was always running around the camp doing odd jobs to help [PAWN_possessive] extended family.</description>  
    <skillGains>
      <Social>2</Social>    
      <Cooking>2</Cooking>    
      <Animals>2</Animals>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>OrphanedAcrobat84</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>OrphanedAcrobat84</identifier>  
    <slot>Childhood</slot>  
    <title>orphaned acrobat</title>  
    <titleShort>acrobat</titleShort>  
    <description>Born a quiet urbworld orphan, [PAWN_nameDef] was invited to join a traveling circus. [PAWN_possessive] small size and observant nature helped [PAWN_nameDef] become a noted acrobat.

Unfortunately, [PAWN_possessive] violent tendencies brought [PAWN_possessive] acrobatic career to an early end.</description>  
    <skillGains>
      <Melee>2</Melee>    
      <Social>3</Social>    
      <Animals>1</Animals>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>Caring</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>CuriousChild33</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>CuriousChild33</identifier>  
    <slot>Childhood</slot>  
    <title>curious child</title>  
    <titleShort>curious</titleShort>  
    <description>[PAWN_nameDef] was more interested in the systems and patterns of the world rather than its inhabitants. [PAWN_possessive] social skills suffered.

[PAWN_nameDef] came to understand that all things are part of the whole, and to destroy part would be to destroy all.</description>  
    <skillGains>
      <Intellectual>4</Intellectual>    
      <Social>-3</Social>    
      <Mining>-2</Mining>
    </skillGains>
    <spawnCategories>
      <li>Cult</li>
    </spawnCategories>
    <workDisables>Violent</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>StewardsAssisant28</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>StewardsAssisant28</identifier>  
    <slot>Childhood</slot>  
    <title>Steward's assisant</title>  
    <titleShort>Steward</titleShort>  
    <description>A steward on medieval merchant ships, [PAWN_nameDef] was well-traveled on [PAWN_possessive] homeworld by [PAWN_possessive] early teens.

The constant orders from [PAWN_possessive] captain, however, fostered a defiant streak, as well as a general disaste for anything [PAWN_pronoun] considered to be busywork.</description>  
    <skillGains>
      <Melee>5</Melee>    
      <Cooking>5</Cooking>    
      <Artistic>-2</Artistic>
    </skillGains>  
    <workDisables>Hauling</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>MonkeyChild67</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>MonkeyChild67</identifier>  
    <slot>Childhood</slot>  
    <title>Monkey child</title>  
    <titleShort>Monkey kid</titleShort>  
    <description>When [PAWN_pronoun] was a baby, [PAWN_nameDef]'s parents traded their child to a troop of monkeys. Little is known of [PAWN_possessive] life among the primates.</description>  
    <skillGains>
      <Plants>6</Plants>    
      <Mining>4</Mining>    
      <Social>-3</Social>    
      <Animals>6</Animals>
    </skillGains>  
    <workDisables>Artistic, Cleaning</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>OldMoneyHeir46</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>OldMoneyHeir46</identifier>  
    <slot>Childhood</slot>  
    <title>Old money heir</title>  
    <titleShort>Heir</titleShort>  
    <description>[PAWN_nameDef] grew up a sheltered and spoiled child in one of the many manors of [PAWN_possessive] old-money industrialist family.

While attending an elite private school, [PAWN_nameDef] received an extensive education in the arts and sciences, but never saw the horrors of the real world.</description>  
    <skillGains>
      <Intellectual>3</Intellectual>    
      <Social>4</Social>    
      <Artistic>2</Artistic>
    </skillGains>  
    <workDisables>Cleaning</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>MusicalKid14</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>MusicalKid14</identifier>  
    <slot>Childhood</slot>  
    <title>Musical kid</title>  
    <titleShort>Music kid</titleShort>  
    <description>[PAWN_nameDef] was born with an almost magic voice. [PAWN_pronoun] learned to sing as a baby. [PAWN_possessive] voice also had a special timbre to it which calmed down everyone who listened to it.</description>  
    <skillGains>
      <Social>3</Social>    
      <Artistic>3</Artistic>
    </skillGains>  
    <workDisables>Intellectual</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

  <BackstoryDef>
    <defName>Punk30</defName>  
    <ignoreIllegalLabelCharacterConfigError>False</ignoreIllegalLabelCharacterConfigError>  
    <identifier>Punk30</identifier>  
    <slot>Childhood</slot>  
    <title>punk</title>  
    <titleShort>punk</titleShort>  
    <description>[PAWN_nameDef] spent [PAWN_possessive] childhood selling knockoff cigarettes to 15-year-olds. The cigarettes were often full of grass clippings.</description>  
    <skillGains>
      <Intellectual>-2</Intellectual>    
      <Social>-2</Social>
    </skillGains>  
    <workDisables>None</workDisables>  
    <requiredWorkTags>None</requiredWorkTags>  
    <shuffleable>False</shuffleable>  
  </BackstoryDef>

</Defs>
