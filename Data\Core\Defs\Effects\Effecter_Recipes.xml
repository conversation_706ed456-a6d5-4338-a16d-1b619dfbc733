﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>



  <EffecterDef>
    <defName>ButcherFlesh</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <scale>0.3~0.7</scale>
        <ticksBetweenMotes>20</ticksBetweenMotes>
        <positionLerpFactor>0.6</positionLerpFactor>
        <positionRadius>0.6</positionRadius>
        <moteDef>Mote_FoodBitMeat</moteDef>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>ButcherMechanoid</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <scale>0.3~0.7</scale>
        <ticksBetweenMotes>20</ticksBetweenMotes>
        <positionLerpFactor>0.6</positionLerpFactor>
        <positionRadius>0.6</positionRadius>
        <moteDef>Mote_StoneBit</moteDef>
      </li>
    </children>
  </EffecterDef>



  <EffecterDef>
    <defName>Surgery</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <scale>0.3~0.7</scale>
        <ticksBetweenMotes>20</ticksBetweenMotes>
        <positionLerpFactor>0.6</positionLerpFactor>
        <positionRadius>0.6</positionRadius>
        <moteDef>Mote_FoodBitMeat</moteDef>
      </li>
    </children>
  </EffecterDef>



  <EffecterDef>
    <defName>CutStone</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <scale>0.3~0.7</scale>
        <ticksBetweenMotes>20</ticksBetweenMotes>
        <positionLerpFactor>0.6</positionLerpFactor>
        <positionRadius>0.6</positionRadius>
        <moteDef>Mote_StoneBit</moteDef>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>Tailor</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <scale>0.3~0.7</scale>
        <ticksBetweenMotes>20</ticksBetweenMotes>
        <positionLerpFactor>0.6</positionLerpFactor>
        <positionRadius>0.6</positionRadius>
        <moteDef>Mote_StoneBit</moteDef>
      </li>
    </children>
  </EffecterDef>



  <EffecterDef>
    <defName>Smith</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <scale>0.3~0.7</scale>
        <ticksBetweenMotes>20</ticksBetweenMotes>
        <positionLerpFactor>0.6</positionLerpFactor>
        <positionRadius>0.6</positionRadius>
        <moteDef>Mote_StoneBit</moteDef>
      </li>
    </children>
  </EffecterDef>




  <EffecterDef>
    <defName>Sculpt</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <scale>0.3~0.7</scale>
        <ticksBetweenMotes>20</ticksBetweenMotes>
        <positionLerpFactor>0.6</positionLerpFactor>
        <positionRadius>0.6</positionRadius>
        <moteDef>Mote_StoneBit</moteDef>
      </li>
    </children>
  </EffecterDef>
  


  <EffecterDef>
    <defName>MakeWoodPlanks_Hand</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <scale>0.3~0.7</scale>
        <ticksBetweenMotes>20</ticksBetweenMotes>
        <positionLerpFactor>0.6</positionLerpFactor>
        <positionRadius>0.6</positionRadius>
        <moteDef>Mote_StoneBit</moteDef>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>MakeWoodPlanks_Electric</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <scale>0.3~0.7</scale>
        <ticksBetweenMotes>5</ticksBetweenMotes>
        <positionLerpFactor>0.6</positionLerpFactor>
        <positionRadius>0.6</positionRadius>
        <moteDef>Mote_StoneBit</moteDef>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>Smelt</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <scale>0.3~0.7</scale>
        <ticksBetweenMotes>5</ticksBetweenMotes>
        <positionLerpFactor>0.6</positionLerpFactor>
        <positionRadius>0.6</positionRadius>
        <moteDef>Mote_StoneBit</moteDef>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>Cook</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <scale>0.3~0.7</scale>
        <ticksBetweenMotes>20</ticksBetweenMotes>
        <positionLerpFactor>0.6</positionLerpFactor>
        <positionRadius>0.6</positionRadius>
        <moteDef>Mote_CookBit</moteDef>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Cremate</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <scale>0.3~0.7</scale>
        <ticksBetweenMotes>20</ticksBetweenMotes>
        <positionLerpFactor>0.6</positionLerpFactor>
        <positionRadius>0.6</positionRadius>
        <moteDef>Mote_StoneBit</moteDef>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>BurnDrug</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <scale>0.3~0.7</scale>
        <ticksBetweenMotes>20</ticksBetweenMotes>
        <positionLerpFactor>0.6</positionLerpFactor>
        <positionRadius>0.6</positionRadius>
        <moteDef>Mote_CookBit</moteDef>
      </li>
    </children>
  </EffecterDef>


</Defs>
