﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>
  
  <ThingDef Abstract="True" Name="MysteriousSphereEntityBase" ParentName="BasePawn">
    <soundImpactDefault>BulletImpact_Metal</soundImpactDefault>
    <hideStats>true</hideStats>
    <hiddenWhileUndiscovered>true</hiddenWhileUndiscovered>
    <statBases>
      <ComfyTemperatureMin>-100</ComfyTemperatureMin>
      <ComfyTemperatureMax>250</ComfyTemperatureMax>
      <PsychicSensitivity>1</PsychicSensitivity>
      <ArmorRating_Heat>2.00</ArmorRating_Heat>
      <ArmorRating_Blunt>1.00</ArmorRating_Blunt>
      <ArmorRating_Sharp>1.00</ArmorRating_Sharp>
      <ToxicResistance>1.00</ToxicResistance>
      <EMPResistance>0.50</EMPResistance>
      <Flammability>0</Flammability>
      <MeatAmount>0</MeatAmount>
      <MoveSpeed>0</MoveSpeed>
      <Mass>100</Mass>
      <PainShockThreshold>100</PainShockThreshold>
      <InjuryHealingFactor>0</InjuryHealingFactor>
    </statBases>
    <tradeability>None</tradeability>
    <race>
      <alwaysAwake>true</alwaysAwake>
      <fleshType>EntityMechanical</fleshType>
      <overrideShouldHaveAbilityTracker>true</overrideShouldHaveAbilityTracker>
      <doesntMove>true</doesntMove>
      <foodType>None</foodType>
      <hasCorpse>false</hasCorpse>
      <needsRest>false</needsRest>
      <hasGenders>false</hasGenders>
      <lifeExpectancy>5000</lifeExpectancy>
      <gestationPeriodDays>10</gestationPeriodDays>
      <soundMeleeDodge>Pawn_MeleeDodge</soundMeleeDodge>
      <disableIgniteVerb>true</disableIgniteVerb>
      <trainability>None</trainability>
      <lifeStageAges>
        <li>
          <def>EntityFullyFormed</def>
        </li>
      </lifeStageAges>
      <hediffGiverSets>
        <li>AnomalyEntity</li>
      </hediffGiverSets>
    </race>
    <comps>
      <li Class="CompProperties_HoldingPlatformTarget">
        <baseEscapeIntervalMtbDays>-1</baseEscapeIntervalMtbDays>
        <canBeExecuted>false</canBeExecuted>
        <hasAnimation>false</hasAnimation>
      </li>
    </comps>
  </ThingDef>
  
  <ThingDef Name="Nociosphere" ParentName="MysteriousSphereEntityBase">
    <defName>Nociosphere</defName>
    <label>nociosphere</label>
    <description>A dark metallic sphere covered with curved and jagged grooves. The sphere emanates sensations of pain. It hurts to be near it.\n\nThe sphere thrums with a mysterious energy. It appears to be increasing in activity. It's not clear what will happen when it reaches its full capacity. However, if you capture it, you can decrease its activity by suppressing it.</description>
    <size>(2,2)</size>
    <statBases>
      <Mass>500</Mass>
    </statBases>
    <uiIconPath>Things/Pawn/Nociosphere/Nociosphere_MenuIcon</uiIconPath>
    <race>
      <body>Nociosphere</body>
      <renderTree>Nociosphere</renderTree>
      <thinkTreeMain>Nociosphere</thinkTreeMain>
      <thinkTreeConstant>NociosphereConstant</thinkTreeConstant>
      <baseHealthScale>10</baseHealthScale>
      <baseBodySize>3.6</baseBodySize>
      <intelligence>ToolUser</intelligence>
      <lifeStageAges Inherit="False">
        <li>
          <def>EntityFullyFormed</def>
          <soundCall>Pawn_Nociosphere_Call</soundCall>
          <soundWounded>Pawn_Nociosphere_Wounded</soundWounded>
          <soundAttack>Pawn_Nociosphere_Attack</soundAttack>
        </li>
      </lifeStageAges>
    </race>
    <inspectorTabs>
      <li>ITab_StudyNotes</li>
    </inspectorTabs>
    <comps>
      <li Class="CompProperties_ProducesBioferrite">
        <bioferriteDensity>2</bioferriteDensity>
      </li>
      <li Class="CompProperties_Studiable">
        <frequencyTicks>120000</frequencyTicks>
        <knowledgeCategory>Advanced</knowledgeCategory>
        <anomalyKnowledge>2.5</anomalyKnowledge>
        <minMonolithLevelForStudy>1</minMonolithLevelForStudy>
        <requiresHoldingPlatform>true</requiresHoldingPlatform>
      </li>
      <li Class="CompProperties_Pushable">
        <offsetDistance>1.75</offsetDistance>
      </li>
      <li Class="CompProperties_CauseHediff_AoE">
        <hediff>PainField</hediff>
        <range>5.9</range>
        <canTargetSelf>false</canTargetSelf>
        <drawLines>false</drawLines>
        <ignoreMechs>true</ignoreMechs>
      </li>
      <li Class="CompProperties_Activity">
        <startingRange>0.2~0.4</startingRange>
        <changePerDayOutside>0.05</changePerDayOutside>
        <workerClass>NociosphereActivityWorker</workerClass>
        <showLetterOnActivated>true</showLetterOnActivated>
        <showLetterOnManualActivation>false</showLetterOnManualActivation>
        <letterDef>ThreatBig</letterDef>
        <letterTitle>Nociosphere activated</letterTitle>
        <letterDesc>The nociosphere's activity level has reached 100% and it has activated. It has begun an onslaught of pain and destruction.\n\nIt will eventually depart - if you can survive long enough.</letterDesc> 
        <activityResearchFactorCurve>
          <points>
            <li>0, 0.5</li>
            <li>0.5, 1</li>
            <li>0.99, 2</li>
          </points>
        </activityResearchFactorCurve>
        <damagedActivityMultiplierCurve>
          <points>
            <li>0.25, 100</li>
            <li>0.5, 10</li>
            <li>0.75, 2</li>
            <li>1, 1</li>
          </points>
        </damagedActivityMultiplierCurve>
      </li>
      <li Class="CompProperties_StudyUnlocks">
        <studyNotes>
          <li>
            <thresholdRange>2~3</thresholdRange>
            <label>Nociosphere study progress</label>
            <text>{PAWN_nameDef} has learned more about the inner workings of the nociosphere. It seems to have a single purpose - to inflict pain on those around it.\n\n{PAWN_nameDef} thinks {PAWN_pronoun} may be able to weaponize the nociosphere but you must study it more first.</text>
          </li>
          <li>
            <thresholdRange>5~6</thresholdRange>
            <label>Nociosphere study progress</label>
            <text>By studying the nociosphere, you've learned how to intentionally activate it.\n\nWhen activated, the nociosphere will teleport to a remote location of your choice and unleash a barrage of pain on anyone nearby. The nociosphere will continue to hunt down anything nearby until it is destroyed or it chooses to return. Select the nociosphere to activate it.</text>
          </li>
          <li>
            <thresholdRange>8~9</thresholdRange>
            <label>Nociosphere study complete</label>
            <text>{PAWN_nameDef} has made an alarming discovery about the nociosphere. The entity is showing early signs of instability.\n\nOnce the nociosphere starts to become unstable, you can dispose of it by sending it to attack a hostile threat. For a short period, the nociosphere will hunt down anything nearby - friend or foe.\n\nOnce its onslaught is complete, it will permanently depart. If it becomes fully unstable, it will no longer respond to suppression.</text>
          </li>
        </studyNotes>
      </li>
      <li Class="CompProperties_Nociosphere">
        <attacks>
          <li>
            <ability>Heatspikes</ability>
          </li>
          <li>
            <ability>AgonyPulse</ability>
          </li>
          <li>
            <ability>FleshmelterBolt</ability>
          </li>
        </attacks>
        
        <activityOnRoofCollapsed>0.4</activityOnRoofCollapsed>
        <minOnslaughtTicks>1800</minOnslaughtTicks>
        <cooldownTicks>120000</cooldownTicks> <!-- 2 days -->
        <onCooldownString>Nociosphere onslaught cooldown</onCooldownString> 
        <sentOnslaughtDurationSeconds>90~120</sentOnslaughtDurationSeconds>
        <onslaughtInspectText>Onslaught mode: {0} seconds left</onslaughtInspectText>
        <becomingUnstableInspectText>Becoming unstable</becomingUnstableInspectText>
        <unstableInspectText>Unstable: cannot be suppressed</unstableInspectText> 
        <unstableWarning>Warning: the nociosphere will resist suppression in {0} days.</unstableWarning>
        <leftLetterLabel>Nociosphere departed</leftLetterLabel>
        <leftLetterText>The nociosphere has disappeared, ending its onslaught.\n\nIt's unclear where the nociosphere has gone or if it will return.</leftLetterText>
        <onslaughtEndedMessage>The nociosphere has ended its onslaught and has returned to its initial location.</onslaughtEndedMessage> 
        <departingMessage>The nociosphere is departing.</departingMessage>

        <!-- Interactable -->
        
        <activeTicks>1</activeTicks>
        <ticksToActivate>180</ticksToActivate>
        <jobString>send nociosphere</jobString>
        <activateLabelString>Send the nociosphere...</activateLabelString>
        <activateDescString>Teleport the nociosphere to a nearby location. It will then unleash an onslaught of murder and pain on everyone nearby for a short time. The nociosphere will hunt down any creature, including allies and other entities.\n\nThe nociosphere can be sent multiple times.</activateDescString>
        <activateTexPath>UI/Commands/SendNociosphere</activateTexPath>
        <activatingString>sending {0}: {1}s</activatingString>
        <targetingParameters>
          <canTargetBuildings>false</canTargetBuildings>
          <canTargetAnimals>false</canTargetAnimals>
          <canTargetMechs>false</canTargetMechs>
          <onlyTargetControlledPawns>true</onlyTargetControlledPawns>
        </targetingParameters>
      </li>
      <li Class="CompProperties_AttachPoints">
        <points>
          <li>
            <offset>(-.71, 0, .71)</offset>
            <type>PlatformRestraint0</type>
          </li>
          <li>
            <offset>(.71, 0, .71)</offset>
            <type>PlatformRestraint1</type>
          </li>
          <li>
            <offset>(.71, 0, -.71)</offset>
            <type>PlatformRestraint2</type>
          </li>
          <li>
            <offset>(-.71, 0, -.71)</offset>
            <type>PlatformRestraint3</type>
          </li>
        </points>
      </li>
    </comps>
    <killedLeavings>
      <Shard>2</Shard>
      <Steel>200</Steel>
      <Plasteel>100</Plasteel>
      <ChunkSlagSteel>4</ChunkSlagSteel>
      <Bioferrite>35</Bioferrite>
    </killedLeavings>
  </ThingDef>

  <ThingDef ParentName="BasePawn">
    <defName>Metalhorror</defName>
    <label>metalhorror</label>
    <description>A horrific shifting mass of metal filaments, blades, and instruments. This metal is maintained and regenerated by a circulating dark fluid.\n\nWhile dormant, fluid metalhorrors are hidden in a host body. They use their host to infect others, creating more metalhorrors. If detected or endangered, the metalhorror will form a jagged exoskeleton and cut its way out of the host's flesh.\n\nAn emerged metalhorror will eventually enter a low-energy hibernating state if left undisturbed.</description>
    <drawOffscreen>true</drawOffscreen>
    <hideStats>true</hideStats>
    <hiddenWhileUndiscovered>true</hiddenWhileUndiscovered>
    <soundImpactDefault>BulletImpact_Metal</soundImpactDefault>
    <statBases>
      <ComfyTemperatureMin>-100</ComfyTemperatureMin>
      <ComfyTemperatureMax>250</ComfyTemperatureMax>
      <ArmorRating_Sharp>0.5</ArmorRating_Sharp>
      <ArmorRating_Blunt>0.5</ArmorRating_Blunt>
      <ArmorRating_Heat>0</ArmorRating_Heat>
      <Flammability>2</Flammability>
      <MeatAmount>0</MeatAmount>
      <MoveSpeed>5.5</MoveSpeed>
      <PsychicSensitivity>1.5</PsychicSensitivity>
      <MinimumContainmentStrength>110</MinimumContainmentStrength>
      <ToxicResistance>1</ToxicResistance>
    </statBases>
    <tradeability>None</tradeability>
    <race>
      <body>Metalhorror</body>
      <fleshType>EntityMechanical</fleshType>
      <thinkTreeMain>Metalhorror</thinkTreeMain>
      <thinkTreeConstant>MetalhorrorConstant</thinkTreeConstant>
      <intelligence>ToolUser</intelligence>
      <foodType>None</foodType>
      <needsRest>false</needsRest>
      <hasGenders>false</hasGenders>
      <bloodDef IsNull="True"/>
      <hasMeat>false</hasMeat>
      <lifeExpectancy>250</lifeExpectancy>
      <baseHealthScale>0.6</baseHealthScale>
      <gestationPeriodDays>10</gestationPeriodDays>
      <soundMeleeDodge>Pawn_MeleeDodge</soundMeleeDodge>
      <disableIgniteVerb>true</disableIgniteVerb>
      <trainability>None</trainability>
      <lifeStageAges>
        <li>
          <def>MetalhorrorLarva</def>
        </li>
        <li>
          <def>MetalhorrorJuvenile</def>
          <minAge>0.016667</minAge>
        </li>
        <li>
          <def>MetalhorrorMature</def>
          <minAge>0.05</minAge>
        </li>
      </lifeStageAges>
      <hediffGiverSets>
        <li>AnomalyEntity</li>
      </hediffGiverSets>
      <hasCorpse>false</hasCorpse>
    </race>
    <comps>
      <li Class="CompProperties_HoldingPlatformTarget">
        <baseEscapeIntervalMtbDays>60</baseEscapeIntervalMtbDays>
      </li>
      <li Class="CompProperties_EffecterOnDeath">
        <effecterDef>MetalhorrorDeath</effecterDef>
      </li>
      <li Class="CompProperties_AttachPoints">
        <points>
          
          <li>
            <offset>(-.2, 0, .4)</offset>
            <type>PlatformRestraint0</type>
          </li>
          <li>
            <offset>(.2, 0, .4)</offset>
            <type>PlatformRestraint1</type>
          </li>
          <li>
            <offset>(.3, 0, -.3)</offset>
            <type>PlatformRestraint2</type>
          </li>
          <li>
            <offset>(-.3, 0, -.3)</offset>
            <type>PlatformRestraint3</type>
          </li>
        </points>
      </li>
      <li Class="CompProperties_ProducesBioferrite">
        <bioferriteDensity>4</bioferriteDensity>
      </li>
      <li Class="CompProperties_Studiable">
        <frequencyTicks>120000</frequencyTicks>
        <knowledgeCategory>Advanced</knowledgeCategory>
        <anomalyKnowledge>4</anomalyKnowledge>
        <minMonolithLevelForStudy>1</minMonolithLevelForStudy>
        <requiresHoldingPlatform>true</requiresHoldingPlatform>
      </li>
      <li Class="CompProperties_Metalhorror" />
      <li Class="CompProperties_CanBeDormant">
        <maxDistAwakenByOther>3</maxDistAwakenByOther>
        <wakeUpDelayRange>100~200</wakeUpDelayRange>
        <jobDormancy>true</jobDormancy>
        <dormantStateLabelKey>MetalhorrorDormant</dormantStateLabelKey>
        <dontShowDevGizmos>true</dontShowDevGizmos>
      </li>
      <li Class="CompProperties_WakeUpDormant">
        <wakeUpOnDamage>true</wakeUpOnDamage>
        <wakeUpCheckRadius>2.9</wakeUpCheckRadius>
        <wakeUpIfAnyTargetClose>true</wakeUpIfAnyTargetClose>
        <wakeUpWithDelay>true</wakeUpWithDelay>
        <wakeUpOnThingConstructedRadius>0</wakeUpOnThingConstructedRadius>
        <wakeUpTargetingParams>
          <canTargetBuildings>false</canTargetBuildings>
          <canTargetAnimals>false</canTargetAnimals>
          <canTargetMechs>false</canTargetMechs>
          <canTargetHumans>true</canTargetHumans>
        </wakeUpTargetingParams>
      </li>
    </comps>
    <tools>
      <li>
        <label>left blade</label>
        <labelNoLocation>blade</labelNoLocation>
        <capacities>
          <li>Cut</li>
          <li>Stab</li>
        </capacities>
        <power>20</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>LeftBlade</linkedBodyPartsGroup>
        <alwaysTreatAsWeapon>true</alwaysTreatAsWeapon>
      </li>
      <li>
        <label>right blade</label>
        <labelNoLocation>blade</labelNoLocation>
        <capacities>
          <li>Cut</li>
          <li>Stab</li>
        </capacities>
        <power>20</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>RightBlade</linkedBodyPartsGroup>
        <alwaysTreatAsWeapon>true</alwaysTreatAsWeapon>
      </li>
      <li>
        <label>head</label>
        <capacities>
          <li>Blunt</li>
        </capacities>
        <power>9</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>HeadAttackTool</linkedBodyPartsGroup>
        <ensureLinkedBodyPartsGroupAlwaysUsable>true</ensureLinkedBodyPartsGroupAlwaysUsable>
        <chanceFactor>0.2</chanceFactor>
      </li>
    </tools>
    <killedLeavingsRanges>
      <Bioferrite>10~20</Bioferrite>
      <Shard>0~1</Shard>
    </killedLeavingsRanges>
  </ThingDef>
  
  <ThingDef Name="Revenant" ParentName="BasePawn">
    <defName>Revenant</defName>
    <label>revenant</label>
    <description>A spindly figure of desiccated skin stretched over an impossibly tall humanoid frame. The revenant's name comes from its pattern of returning to hypnotize new people each night.\n\nOutlander folklore describes an invisible spectre that captures the minds of sinners, placing them in a living hell. In the story, the victims can only be returned to life by hunting the creature as it sleeps and killing it.\n\nA hidden revenant can be revealed with disruptor flares, explosives, EMP, firefoam, or fire.</description>
    <hiddenWhileUndiscovered>true</hiddenWhileUndiscovered>
    <statBases>
      <MoveSpeed>3.2</MoveSpeed>
      <Flammability>0</Flammability>
      <ComfyTemperatureMin>-40</ComfyTemperatureMin>
      <ComfyTemperatureMax>60</ComfyTemperatureMax>
      <PsychicSensitivity>2</PsychicSensitivity>
      <MinimumContainmentStrength>80</MinimumContainmentStrength>
      <StaggerDurationFactor>0</StaggerDurationFactor>
      <ToxicResistance>1</ToxicResistance>
    </statBases>
    <uiIconPath>Things/Pawn/Revenant/Revenant_MenuIcon</uiIconPath>
    <tradeability>None</tradeability>
    <hideStats>true</hideStats>
    <onlyShowInspectString>true</onlyShowInspectString>
    <hideMainDesc>true</hideMainDesc>
    <race>
      <thinkTreeMain>Revenant</thinkTreeMain>
      <thinkTreeConstant>RevenantConstant</thinkTreeConstant>
      <body>Human</body>
      <baseBodySize>1</baseBodySize>
      <baseHealthScale>10</baseHealthScale>
      <intelligence>ToolUser</intelligence>
      <needsRest>false</needsRest>
      <hasGenders>false</hasGenders>
      <fleshType>EntityMechanical</fleshType>
      <foodType>None</foodType>
      <hasCorpse>false</hasCorpse>
      <lifeExpectancy>1000</lifeExpectancy>
      <soundMeleeDodge>Pawn_MeleeDodge</soundMeleeDodge>
      <renderTree>Revenant</renderTree>
      <startingAnimation>RevenantSpasm</startingAnimation>
      <soundCallIntervalRange>120~240</soundCallIntervalRange>
      <disableIgniteVerb>true</disableIgniteVerb>
      <trainability>None</trainability>
      <lifeStageAges>
        <li>
          <def>EntityFullyFormed</def>
          <soundCall>Pawn_Revenant_Call</soundCall>
          <soundWounded>Pawn_Revenant_Wounded</soundWounded>
          <soundDeath>Pawn_Revenant_Death</soundDeath>
        </li>
      </lifeStageAges>
      <hediffGiverSets>
        <li>AnomalyEntity</li>
      </hediffGiverSets>
    </race>
    <comps>
      <li>
        <compClass>CompRevenant</compClass>
      </li>
      <li Class="CompProperties_HoldingPlatformTarget">
        <baseEscapeIntervalMtbDays>45</baseEscapeIntervalMtbDays>
        <lookForTargetOnEscape>false</lookForTargetOnEscape>
      </li>
      <li Class="CompProperties_AttachPoints">
        <points>
          <li>
            <offset>(-.35, 0, .35)</offset>
            <type>PlatformRestraint0</type>
          </li>
          <li>
            <offset>(.35, 0, .35)</offset>
            <type>PlatformRestraint1</type>
          </li>
          <li>
            <offset>(.2, 0, -.71)</offset>
            <type>PlatformRestraint2</type>
          </li>
          <li>
            <offset>(-.2, 0, -.71)</offset>
            <type>PlatformRestraint3</type>
          </li>
        </points>
      </li>
      <li Class="CompProperties_ProducesBioferrite">
        <bioferriteDensity>4</bioferriteDensity>
      </li>
      <li Class="CompProperties_Studiable">
        <frequencyTicks>120000</frequencyTicks>
        <knowledgeCategory>Advanced</knowledgeCategory>
        <anomalyKnowledge>3</anomalyKnowledge>
        <minMonolithLevelForStudy>1</minMonolithLevelForStudy>
        <requiresHoldingPlatform>true</requiresHoldingPlatform>
      </li>
    </comps>
    <!-- Never actually used - revenant doesn't attack -->
    <tools>
      <li>
        <label>head</label>
        <capacities>
          <li>Blunt</li>
        </capacities>
        <power>3</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>HeadAttackTool</linkedBodyPartsGroup>
        <ensureLinkedBodyPartsGroupAlwaysUsable>true</ensureLinkedBodyPartsGroupAlwaysUsable>
        <chanceFactor>0.2</chanceFactor>
      </li>
    </tools>
  </ThingDef>

  <ThingDef ParentName="BasePawn">
    <defName>Sightstealer</defName>
    <label>sightstealer</label>
    <description>Emaciated and misshapen humanoids. Their arms end in sharp, curled claws formed from bioferrite. Sightstealers are fragile, but use psychic influence to render themselves invisible until they get close to their victims. They are known to emit haunting screams as they gather on their terrible hunts.</description>
    <hiddenWhileUndiscovered>true</hiddenWhileUndiscovered>
    <statBases>
      <MoveSpeed>4.83</MoveSpeed>
      <ComfyTemperatureMin>-40</ComfyTemperatureMin>
      <ComfyTemperatureMax>60</ComfyTemperatureMax>
      <PsychicSensitivity>2</PsychicSensitivity>
      <MinimumContainmentStrength>30</MinimumContainmentStrength>
      <LeatherAmount>20</LeatherAmount>
      <MeatAmount>70</MeatAmount>
      <ToxicResistance>0.5</ToxicResistance>
    </statBases>
    <tradeability>None</tradeability>
    <race>
      <thinkTreeMain>Sightstealer</thinkTreeMain>
      <thinkTreeConstant>SightstealerConstant</thinkTreeConstant>
      <body>Human</body>
      <baseBodySize>0.8</baseBodySize>
      <baseHealthScale>0.75</baseHealthScale>
      <intelligence>ToolUser</intelligence>
      <needsRest>false</needsRest>
      <bloodDef>Filth_DarkBlood</bloodDef>
      <bleedRateFactor>0.5</bleedRateFactor>
      <fleshType>EntityFlesh</fleshType>
      <isImmuneToInfections>true</isImmuneToInfections>
      <specificMeatDef>Meat_Twisted</specificMeatDef>
      <leatherDef>Leather_Dread</leatherDef>
      <hasGenders>false</hasGenders>
      <foodType>None</foodType>
      <lifeExpectancy>250</lifeExpectancy>
      <soundCallIntervalRange>500~1000</soundCallIntervalRange>
      <soundMeleeHitPawn>Pawn_Melee_SmallScratch_HitPawn</soundMeleeHitPawn>
      <soundMeleeHitBuilding>Pawn_Melee_SmallScratch_HitBuilding</soundMeleeHitBuilding>
      <soundMeleeMiss>Pawn_Melee_SmallScratch_Miss</soundMeleeMiss>
      <soundMeleeDodge>Pawn_MeleeDodge</soundMeleeDodge>
      <disableIgniteVerb>true</disableIgniteVerb>
      <trainability>None</trainability>
      <canBecomeShambler>true</canBecomeShambler>
      <lifeStageAges>
        <li>
          <def>EntityFullyFormed</def>
          <soundCall>Pawn_Sightstealer_Call</soundCall>
          <soundWounded>Pawn_Sightstealer_Wounded</soundWounded>
          <soundDeath>Pawn_Sightstealer_Death</soundDeath>
        </li>
      </lifeStageAges>
      <hediffGiverSets>
        <li>AnomalyEntity</li>
      </hediffGiverSets>
      <corpseHiddenWhileUndiscovered>true</corpseHiddenWhileUndiscovered>
    </race>
    <comps>
      <li>
        <compClass>CompSightstealer</compClass>
      </li>
      <li Class="CompProperties_HoldingPlatformTarget">
        <baseEscapeIntervalMtbDays>60</baseEscapeIntervalMtbDays>
      </li>
      <li Class="CompProperties_ProducesBioferrite">
        <bioferriteDensity>2</bioferriteDensity>
      </li>
      <li Class="CompProperties_Studiable">
        <frequencyTicks>120000</frequencyTicks>
        <knowledgeCategory>Basic</knowledgeCategory>
        <minMonolithLevelForStudy>1</minMonolithLevelForStudy>
        <anomalyKnowledge>1.5</anomalyKnowledge>
        <requiresHoldingPlatform>true</requiresHoldingPlatform>
      </li>
      <li Class="CompProperties_AttachPoints">
        <!-- This thing has alternateGraphics, and all of these points will need overrides in the PawnKindDef. -->
        <points>
          <li>
            <offset>(-.25, 0, .15)</offset>
            <type>PlatformRestraint0</type>
          </li>
          <li>
            <offset>(.35, 0, .45)</offset>
            <type>PlatformRestraint1</type>
          </li>
          <li>
            <offset>(.15, 0, -.6)</offset>
            <type>PlatformRestraint2</type>
          </li>
          <li>
            <offset>(-.15, 0, -.6)</offset>
            <type>PlatformRestraint3</type>
          </li>
        </points>
      </li>
    </comps>
    <tools>
      <li>
        <label>left claw</label>
        <labelNoLocation>claw</labelNoLocation>
        <capacities>
          <li>Scratch</li>
        </capacities>
        <power>7.0</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>LeftHand</linkedBodyPartsGroup>
        <alwaysTreatAsWeapon>true</alwaysTreatAsWeapon>
      </li>
      <li>
        <label>right claw</label>
        <labelNoLocation>claw</labelNoLocation>
        <capacities>
          <li>Scratch</li>
        </capacities>
        <power>7.0</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>RightHand</linkedBodyPartsGroup>
        <alwaysTreatAsWeapon>true</alwaysTreatAsWeapon>
      </li>
      <li>
        <label>head</label>
        <capacities>
          <li>Blunt</li>
        </capacities>
        <power>3</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>HeadAttackTool</linkedBodyPartsGroup>
        <ensureLinkedBodyPartsGroupAlwaysUsable>true</ensureLinkedBodyPartsGroupAlwaysUsable>
        <chanceFactor>0.1</chanceFactor>
      </li>
    </tools>
  </ThingDef>

  <ThingDef ParentName="Human">
    <defName>CreepJoiner</defName>
    <tradeability>Sellable</tradeability>
    <race>
      <lifeExpectancy>100</lifeExpectancy>
      <linkedCorpseKind>Human</linkedCorpseKind>
      <useMeatFrom>Human</useMeatFrom>
      <knowledgeCategory>Advanced</knowledgeCategory>
      <anomalyKnowledge>2</anomalyKnowledge>
    </race>
  </ThingDef>

  <ThingDef ParentName="BasePawn">
    <defName>Noctol</defName>
    <label>noctol</label>
    <description>A large, chittering creature covered with an oily black carapace and armed with claws formed from bioferrite. While it is deadly in the dark, exposure to light irritates its eyes and flesh, slowing it down considerably.</description>
    <hiddenWhileUndiscovered>true</hiddenWhileUndiscovered>
    <statBases>
      <MoveSpeed>8</MoveSpeed>
      <ComfyTemperatureMin>-40</ComfyTemperatureMin>
      <ComfyTemperatureMax>60</ComfyTemperatureMax>
      <LeatherAmount>20</LeatherAmount>
      <MeatAmount>70</MeatAmount>
      <PsychicSensitivity>1</PsychicSensitivity>
      <ArmorRating_Sharp>0.28</ArmorRating_Sharp>
      <ArmorRating_Blunt>0.18</ArmorRating_Blunt>
      <MinimumContainmentStrength>60</MinimumContainmentStrength>
      <ToxicEnvironmentResistance MayRequire="Ludeon.RimWorld.Biotech">1</ToxicEnvironmentResistance>
      <ToxicResistance>0.5</ToxicResistance>
    </statBases>
    <hideInspect>true</hideInspect>
    <tradeability>None</tradeability>
    <tools>
      <li>
        <label>left claw</label>
        <labelNoLocation>claw</labelNoLocation>
        <capacities>
          <li>Scratch</li>
        </capacities>
        <power>12.3</power>
        <cooldownTime>1.25</cooldownTime>
        <linkedBodyPartsGroup>FrontLeftClaw</linkedBodyPartsGroup>
      </li>
      <li>
        <label>right claw</label>
        <labelNoLocation>claw</labelNoLocation>
        <capacities>
          <li>Scratch</li>
        </capacities>
        <power>12.3</power>
        <cooldownTime>1.25</cooldownTime>
        <linkedBodyPartsGroup>FrontRightClaw</linkedBodyPartsGroup>
      </li>
      <li>
        <capacities>
          <li>Bite</li>
        </capacities>
        <power>15</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>Teeth</linkedBodyPartsGroup>
        <chanceFactor>0.7</chanceFactor>
      </li>
      <li>
        <label>head</label>
        <capacities>
          <li>Blunt</li>
        </capacities>
        <power>9</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>HeadAttackTool</linkedBodyPartsGroup>
        <ensureLinkedBodyPartsGroupAlwaysUsable>true</ensureLinkedBodyPartsGroupAlwaysUsable>
        <chanceFactor>0.2</chanceFactor>
      </li>
    </tools>
    <race>
      <thinkTreeMain>Noctol</thinkTreeMain>
      <thinkTreeConstant>NoctolConstant</thinkTreeConstant>
      <intelligence>ToolUser</intelligence>
      <lifeExpectancy>25</lifeExpectancy>
      <disableIgniteVerb>true</disableIgniteVerb>
      <fleshType>EntityFlesh</fleshType>
      <isImmuneToInfections>true</isImmuneToInfections>
      <leatherDef>Leather_Dread</leatherDef>
      <body>Noctol</body>
      <renderTree>Misc</renderTree>
      <baseBodySize>1</baseBodySize>
      <baseHealthScale>1.5</baseHealthScale>
      <specificMeatDef>Meat_Twisted</specificMeatDef>
      <trainability>None</trainability>
      <bloodDef>Filth_DarkBlood</bloodDef>
      <bleedRateFactor>0.5</bleedRateFactor>
      <canBecomeShambler>true</canBecomeShambler>
      <lifeStageAges>
        <li>
          <def>EntityFullyFormed</def>
          <soundCall>Pawn_Noctol_Call</soundCall>
          <soundWounded>Pawn_Noctol_Wounded</soundWounded>
          <soundDeath>Pawn_Noctol_Death</soundDeath>
        </li>
      </lifeStageAges>
      <soundMeleeDodge>Pawn_MeleeDodge</soundMeleeDodge>
      <needsRest>false</needsRest>
      <hasGenders>false</hasGenders>
      <foodType>None</foodType>
      <hediffGiverSets>
        <li>AnomalyEntity</li>
      </hediffGiverSets>
      <corpseHiddenWhileUndiscovered>true</corpseHiddenWhileUndiscovered>
      <headPosPerRotation>
        <li>(0, 0, 0)</li>
        <li>(.2, 0, 0)</li>
        <li>(0, 0, .25)</li>
        <li>(-.2, 0, 0)</li>
      </headPosPerRotation>
    </race>
    <comps>
      <li Class="CompProperties_HoldingPlatformTarget">
        <baseEscapeIntervalMtbDays>50</baseEscapeIntervalMtbDays>
      </li>
      <li Class="CompProperties_AttachPoints">
        <points>
          <li>
            <offset>(-.5, 0, 0)</offset>
            <type>PlatformRestraint0</type>
          </li>
          <li>
            <offset>(.5, 0, 0)</offset>
            <type>PlatformRestraint1</type>
          </li>
          <li>
            <offset>(.45, 0, -.8)</offset>
            <type>PlatformRestraint2</type>
          </li>
          <li>
            <offset>(-.3, 0, -.8)</offset>
            <type>PlatformRestraint3</type>
          </li>
        </points>
      </li>
      <li Class="CompProperties_ProducesBioferrite">
        <bioferriteDensity>3</bioferriteDensity>
      </li>
      <li Class="CompProperties_Studiable">
        <frequencyTicks>120000</frequencyTicks>
        <knowledgeCategory>Advanced</knowledgeCategory>
        <anomalyKnowledge>1</anomalyKnowledge>
        <minMonolithLevelForStudy>1</minMonolithLevelForStudy>
        <requiresHoldingPlatform>true</requiresHoldingPlatform>
      </li>
      <li Class="CompProperties_NoctolEyes">
        <moteDef>Mote_NoctolEyes</moteDef>
      </li>
    </comps>
  </ThingDef>
  
  <ThingDef ParentName="MysteriousSphereEntityBase">
    <defName>FleshmassNucleus</defName>
    <label>fleshmass nucleus</label>
    <description>The archotech core of a fleshmass heart. It is a chaotic lattice of fluid-smeared metal. Solid chunks of jagged metal appear here and there, packed with archotechnology. The whole structure slowly twists and bends under its own power.\n\nIt constantly grows meat on its surface, which occasionally erupts violently. If too much meat builds up, it will regrow into a fleshmass heart.</description>
    <uiIconPath>Things/Pawn/FleshmassNucleus/FleshmassNucleusA</uiIconPath>
    <hiddenWhileUndiscovered>true</hiddenWhileUndiscovered>
    <race>
      <body>FleshmassNucleus</body>
      <thinkTreeMain>FleshmassNucleus</thinkTreeMain>
      <thinkTreeConstant>FleshmassNucleusConstant</thinkTreeConstant>
      <baseHealthScale>1</baseHealthScale>
      <baseBodySize>1</baseBodySize>
    </race>
    <comps>
      <li Class="CompProperties_Studiable">
        <frequencyTicks>120000</frequencyTicks>
        <knowledgeCategory>Advanced</knowledgeCategory>
        <anomalyKnowledge>3</anomalyKnowledge>
        <minMonolithLevelForStudy>1</minMonolithLevelForStudy>
        <requiresHoldingPlatform>true</requiresHoldingPlatform>
      </li>
      <li Class="CompProperties_ProducesBioferrite">
        <bioferriteDensity>3</bioferriteDensity>
      </li>
      <li Class="CompProperties_Pushable">
        <offsetDistance>0.5</offsetDistance>
      </li>
      <li Class="CompProperties_Activity">
        <startingRange>0.2~0.4</startingRange>
        <changePerDayOutside>0.05</changePerDayOutside>
        <workerClass>PawnActivityWorker</workerClass>
        <showLetterOnActivated>true</showLetterOnActivated>
        <dirtyGraphicsOnActivityChange>true</dirtyGraphicsOnActivityChange>
        <changePerDamage>0</changePerDamage>
        <letterDef>ThreatBig</letterDef>
        <letterTitle>Fleshmass transformation</letterTitle>
        <letterDesc>The fleshmass nucleus has grown a critical mass of new flesh. It has developed into a new fleshmass heart!\n\nIn the process, the fleshmass heart has mutated and adopted a new biosignature.</letterDesc>
        <activityResearchFactorCurve>
          <points>
            <li>0, 0.5</li>
            <li>0.5, 1</li>
            <li>0.99, 2</li>
          </points>
        </activityResearchFactorCurve>
      </li>
      <li Class="CompProperties_FleshmassNucleus">
        <activityOnRoofCollapsed>0.4</activityOnRoofCollapsed>
        <activityMeatPerDayCurve>
          <points>
            <li>0.1, 20</li>
            <li>0.9, 120</li>
          </points>
        </activityMeatPerDayCurve>
      </li>
      <li Class="CompProperties_AttachPoints">
        <points>
          <li>
            <offset>(-.35, 0, .35)</offset>
            <type>PlatformRestraint0</type>
          </li>
          <li>
            <offset>(.35, 0, .35)</offset>
            <type>PlatformRestraint1</type>
          </li>
          <li>
            <offset>(.35, 0, -.35)</offset>
            <type>PlatformRestraint2</type>
          </li>
          <li>
            <offset>(-.35, 0, -.35)</offset>
            <type>PlatformRestraint3</type>
          </li>
        </points>
      </li>
    </comps>
    <killedLeavingsRanges>
      <Meat_Twisted>100~200</Meat_Twisted>
      <Shard>2</Shard>
    </killedLeavingsRanges>
  </ThingDef>
  
  <ThingDef ParentName="BasePawn">
    <defName>Gorehulk</defName>
    <label>gorehulk</label>
    <description>A monstrous creature that resembles a massive, painfully swollen human with randomly reconfigured body parts. Near its top is a stretched face that watches its victim with dead eyes.\n\nHundreds of sharp keratin spines protrude from the gorehulk's skin. It can launch these spines to spear its victims at a distance. However, its awkward fleshy body is less effective at causing harm up close.\n\nIt's hard to know whether this is a human that was horribly distorted by the influence of the void or a poor imitation of humanity created from scratch by some evil intelligence.</description>
    <hiddenWhileUndiscovered>true</hiddenWhileUndiscovered>
    <statBases>
      <MoveSpeed>3.25</MoveSpeed>
      <ComfyTemperatureMin>-40</ComfyTemperatureMin>
      <ComfyTemperatureMax>60</ComfyTemperatureMax>
      <PsychicSensitivity>2</PsychicSensitivity>
      <MinimumContainmentStrength>60</MinimumContainmentStrength>
      <LeatherAmount>20</LeatherAmount>
      <MeatAmount>70</MeatAmount>
    </statBases>
    <tradeability>None</tradeability>
    <race>
      <body>Gorehulk</body>
      <thinkTreeMain>Gorehulk</thinkTreeMain>
      <thinkTreeConstant>GorehulkConstant</thinkTreeConstant>
      <overrideShouldHaveAbilityTracker>true</overrideShouldHaveAbilityTracker>
      <alwaysViolent>true</alwaysViolent>
      <baseBodySize>2</baseBodySize>
      <baseHealthScale>1.25</baseHealthScale>
      <intelligence>ToolUser</intelligence>
      <needsRest>false</needsRest>
      <bloodDef>Filth_DarkBlood</bloodDef>
      <bleedRateFactor>0.5</bleedRateFactor>
      <fleshType>EntityFlesh</fleshType>
      <isImmuneToInfections>true</isImmuneToInfections>
      <specificMeatDef>Meat_Twisted</specificMeatDef>
      <leatherDef>Leather_Human</leatherDef>
      <hasGenders>false</hasGenders>
      <foodType>None</foodType>
      <lifeExpectancy>25</lifeExpectancy>
      <soundMeleeHitPawn>Pawn_Melee_SmallScratch_HitPawn</soundMeleeHitPawn>
      <soundMeleeHitBuilding>Pawn_Melee_SmallScratch_HitBuilding</soundMeleeHitBuilding>
      <soundMeleeMiss>Pawn_Melee_SmallScratch_Miss</soundMeleeMiss>
      <soundMeleeDodge>Pawn_MeleeDodge</soundMeleeDodge>
      <disableIgniteVerb>true</disableIgniteVerb>
      <trainability>None</trainability>
      <canBecomeShambler>true</canBecomeShambler>
      <lifeStageAges>
        <li>
          <def>EntityFullyFormed</def>
          <soundCall>Pawn_Gorehulk_Call</soundCall>
          <soundWounded>Pawn_Gorehulk_Wounded</soundWounded>
          <soundAttack>Pawn_Gorehulk_Attack</soundAttack>
          <soundDeath>Pawn_Gorehulk_Death</soundDeath>
        </li>
      </lifeStageAges>
      <hediffGiverSets>
        <li>AnomalyEntity</li>
      </hediffGiverSets>
      <corpseHiddenWhileUndiscovered>true</corpseHiddenWhileUndiscovered>
    </race>
    <comps>
      <li Class="CompProperties_HoldingPlatformTarget">
        <baseEscapeIntervalMtbDays>60</baseEscapeIntervalMtbDays>
      </li>
      <li Class="CompProperties_AttachPoints">
        <!-- This thing has alternateGraphics, and all of these points will need overrides in the PawnKindDef. -->
        <points>
          <li>
            <offset>(-.4, 0, .4)</offset>
            <type>PlatformRestraint0</type>
          </li>
          <li>
            <offset>(.4, 0, .5)</offset>
            <type>PlatformRestraint1</type>
          </li>
          <li>
            <offset>(.3, 0, -.6)</offset>
            <type>PlatformRestraint2</type>
          </li>
          <li>
            <offset>(-.2, 0, -.7)</offset>
            <type>PlatformRestraint3</type>
          </li>
        </points>
      </li>
      <li Class="CompProperties_ProducesBioferrite">
        <bioferriteDensity>1.5</bioferriteDensity>
      </li>
      <li Class="CompProperties_Studiable">
        <frequencyTicks>120000</frequencyTicks>
        <knowledgeCategory>Basic</knowledgeCategory>
        <minMonolithLevelForStudy>1</minMonolithLevelForStudy>
        <anomalyKnowledge>2</anomalyKnowledge>
        <requiresHoldingPlatform>true</requiresHoldingPlatform>
      </li>
    </comps>
    <tools>
      <li>
        <label>left flesh club</label>
        <labelNoLocation>flesh club</labelNoLocation>
        <capacities>
          <li>Blunt</li>
        </capacities>
        <power>3</power>
        <cooldownTime>1.5</cooldownTime>
        <linkedBodyPartsGroup>LeftFleshClub</linkedBodyPartsGroup>
        <alwaysTreatAsWeapon>true</alwaysTreatAsWeapon>
      </li>
      <li>
        <label>right flesh club</label>
        <labelNoLocation>flesh club</labelNoLocation>
        <capacities>
          <li>Blunt</li>
        </capacities>
        <power>3</power>
        <cooldownTime>1.5</cooldownTime>
        <linkedBodyPartsGroup>RightFleshClub</linkedBodyPartsGroup>
        <alwaysTreatAsWeapon>true</alwaysTreatAsWeapon>
      </li>
      <li>
        <label>head</label>
        <capacities>
          <li>Blunt</li>
        </capacities>
        <power>3</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>HeadAttackTool</linkedBodyPartsGroup>
        <ensureLinkedBodyPartsGroupAlwaysUsable>true</ensureLinkedBodyPartsGroupAlwaysUsable>
        <chanceFactor>0.1</chanceFactor>
      </li>
    </tools>
  </ThingDef>
  
  <ThingDef ParentName="BasePawn">
    <defName>Devourer</defName>
    <label>devourer</label>
    <description>A massive monster with a gaping toothed maw and a stubby, snake-like body. The devourer can leap several times its body length to swallow a person whole. Powerful stomach acids rapidly digest the still-living victim. The remains are regurgitated soon after.</description>
    <hiddenWhileUndiscovered>true</hiddenWhileUndiscovered>
    <statBases>
      <MoveSpeed>3.5</MoveSpeed>
      <ComfyTemperatureMin>-40</ComfyTemperatureMin>
      <ComfyTemperatureMax>60</ComfyTemperatureMax>
      <PsychicSensitivity>2</PsychicSensitivity>
      <MinimumContainmentStrength>90</MinimumContainmentStrength>
      <LeatherAmount>20</LeatherAmount>
      <MeatAmount>70</MeatAmount>
      <ArmorRating_Sharp>0.5</ArmorRating_Sharp>
      <ArmorRating_Blunt>0.3</ArmorRating_Blunt>
      <ArmorRating_Heat>0.2</ArmorRating_Heat>
      <ToxicResistance>0.5</ToxicResistance>
    </statBases>
    <tradeability>None</tradeability>
    <race>
      <body>Devourer</body>
      <renderTree>Devourer</renderTree>
      <thinkTreeMain>Devourer</thinkTreeMain>
      <thinkTreeConstant>DevourerConstant</thinkTreeConstant>
      <overrideShouldHaveAbilityTracker>true</overrideShouldHaveAbilityTracker>
      <alwaysViolent>true</alwaysViolent>
      <baseBodySize>4</baseBodySize>
      <baseHealthScale>7.2</baseHealthScale>
      <intelligence>ToolUser</intelligence>
      <needsRest>false</needsRest>
      <bloodDef>Filth_DarkBlood</bloodDef>
      <bleedRateFactor>0.5</bleedRateFactor>
      <fleshType>EntityFlesh</fleshType>
      <isImmuneToInfections>true</isImmuneToInfections>
      <specificMeatDef>Meat_Twisted</specificMeatDef>
      <leatherDef>Leather_Dread</leatherDef>
      <hasGenders>false</hasGenders>
      <foodType>None</foodType>
      <lifeExpectancy>200</lifeExpectancy>
      <soundMeleeHitPawn>Pawn_Melee_SmallScratch_HitPawn</soundMeleeHitPawn>
      <soundMeleeHitBuilding>Pawn_Melee_SmallScratch_HitBuilding</soundMeleeHitBuilding>
      <soundMeleeMiss>Pawn_Melee_SmallScratch_Miss</soundMeleeMiss>
      <soundMeleeDodge>Pawn_MeleeDodge</soundMeleeDodge>
      <disableIgniteVerb>true</disableIgniteVerb>
      <trainability>None</trainability>
      <canBecomeShambler>true</canBecomeShambler>
      <lifeStageAges>
        <li>
          <def>EntityFullyFormed</def>
          <soundCall>Pawn_Devourer_Call</soundCall>
          <soundWounded>Pawn_Devourer_Wounded</soundWounded>
          <soundAttack>Pawn_Devourer_Attack</soundAttack>
        </li>
      </lifeStageAges>
      <hediffGiverSets>
        <li>AnomalyEntity</li>
      </hediffGiverSets>
      <corpseHiddenWhileUndiscovered>true</corpseHiddenWhileUndiscovered>
    </race>
    <comps>
      <li Class="CompProperties_Devourer">
        <messageDigested>A devourer swallowed {PAWN_nameDef} whole! Kill the devourer before it digests {PAWN_nameDef}.</messageDigested>
        <messageEmerged>{PAWN_nameDef} emerged from the devourer.</messageEmerged>
        <messageEmergedCorpse>{PAWN_nameDef} emerged from the devourer's corpse.</messageEmergedCorpse>
        <messageDigestionCompleted>The devourer has regurgitated {PAWN_nameDef}.</messageDigestionCompleted>
        <digestingInspector>Digesting {PAWN_nameDef}: {SECONDS} seconds left</digestingInspector>
        <completeDigestionDamage>200</completeDigestionDamage>
        <bodySizeDigestTimeCurve>
          <points>
            <li>(0.2, 10)</li>
            <li>(1, 30)</li>
            <li>(3.5, 60)</li>
          </points>
        </bodySizeDigestTimeCurve>
        <timeDamageCurve>
          <points>
            <li>(0, 10)</li>
            <li>(60, 40)</li>
          </points>
        </timeDamageCurve>
      </li>
      <li Class="CompProperties_HoldingPlatformTarget">
        <baseEscapeIntervalMtbDays>60</baseEscapeIntervalMtbDays>
      </li>
      <li Class="CompProperties_AttachPoints">
        <points>
          <li>
            <offset>(-.50, 0, .65)</offset>
            <type>PlatformRestraint0</type>
          </li>
          <li> 
            <offset>(.50, 0, .65)</offset>
            <type>PlatformRestraint1</type>
          </li>
          <li> 
            <offset>(.65, 0, -.65)</offset>
            <type>PlatformRestraint2</type>
          </li>
          <li>
            <offset>(-.65, 0, -.65)</offset>
            <type>PlatformRestraint3</type>
          </li>
        </points>
      </li>
      <li Class="CompProperties_ProducesBioferrite">
        <bioferriteDensity>1</bioferriteDensity>
      </li>
      <li Class="CompProperties_Studiable">
        <frequencyTicks>120000</frequencyTicks>
        <knowledgeCategory>Advanced</knowledgeCategory>
        <minMonolithLevelForStudy>1</minMonolithLevelForStudy>
        <anomalyKnowledge>2</anomalyKnowledge>
        <requiresHoldingPlatform>true</requiresHoldingPlatform>
      </li>
    </comps>
    <tools>
      <li>
        <label>left fin</label>
        <labelNoLocation>fin</labelNoLocation>
        <capacities>
          <li>Blunt</li>
        </capacities>
        <power>8</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>LeftFin</linkedBodyPartsGroup>
        <alwaysTreatAsWeapon>true</alwaysTreatAsWeapon>
      </li>
      <li>
        <label>right fin</label>
        <labelNoLocation>fin</labelNoLocation>
        <capacities>
          <li>Blunt</li>
        </capacities>
        <power>8</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>RightFin</linkedBodyPartsGroup>
        <alwaysTreatAsWeapon>true</alwaysTreatAsWeapon>
      </li>
      <li>
        <label>teeth</label>
        <capacities>
          <li>Blunt</li>
        </capacities>
        <power>15</power>
        <armorPenetration>0.2</armorPenetration>
        <cooldownTime>2.5</cooldownTime>
        <linkedBodyPartsGroup>HeadAttackTool</linkedBodyPartsGroup>
        <ensureLinkedBodyPartsGroupAlwaysUsable>true</ensureLinkedBodyPartsGroupAlwaysUsable>
      </li>
    </tools>
  </ThingDef>

  <ThingDef ParentName="BasePawn">
    <defName>Chimera</defName>
    <label>chimera</label>
    <description>A huge creature that resembles a disfigured combination of a bear and other animals. It is vicious without limit.\n\nIt's not known if this is a combination of natural animals or a poor imitation of animal life created by an insane machine mind.</description>
    <tradeability>None</tradeability>
    <hiddenWhileUndiscovered>true</hiddenWhileUndiscovered>
    <race>
      <baseHealthScale>3</baseHealthScale>
      <body>QuadrupedAnimalWithPaws</body>
      <baseBodySize>2.15</baseBodySize>
      <leatherDef>Leather_Dread</leatherDef>
      <needsRest>false</needsRest>
      <fleshType>EntityFlesh</fleshType>
      <isImmuneToInfections>true</isImmuneToInfections>
      <specificMeatDef>Meat_Twisted</specificMeatDef>
      <foodType>None</foodType>
      <bloodDef>Filth_DarkBlood</bloodDef>
      <bleedRateFactor>0.5</bleedRateFactor>
      <thinkTreeMain>Chimera</thinkTreeMain>
      <thinkTreeConstant>ChimeraConstant</thinkTreeConstant>
      <trainability>None</trainability>
      <canBecomeShambler>true</canBecomeShambler>
      <lifeStageAges>
        <li>
          <def>EntityFullyFormed</def>
          <soundCall>Pawn_Chimera_Call</soundCall>
          <soundWounded>Pawn_Chimera_Wounded</soundWounded>
          <soundDeath>Pawn_Chimera_Death</soundDeath>
        </li>
      </lifeStageAges>
      <hediffGiverSets>
        <li>AnomalyEntity</li>
      </hediffGiverSets>
      <soundMeleeHitPawn>Pawn_Melee_BigBash_HitPawn</soundMeleeHitPawn>
      <soundMeleeHitBuilding>Pawn_Melee_BigBash_HitBuilding</soundMeleeHitBuilding>
      <soundMeleeMiss>Pawn_Melee_BigBash_Miss</soundMeleeMiss>
      <headPosPerRotation>
        <li>(0.0, 0, 0.2)</li>
        <li>(0.3, 0, 0.1)</li>
        <li>(0.0, 0, -0.06)</li>
        <li>(-0.3, 0, 0.1)</li>
      </headPosPerRotation>
      <hasGenders>false</hasGenders>
      <corpseHiddenWhileUndiscovered>true</corpseHiddenWhileUndiscovered>
    </race>
    <statBases>
      <MoveSpeed>3.6</MoveSpeed>
      <MinimumContainmentStrength>70</MinimumContainmentStrength>
      <ComfyTemperatureMin>-40</ComfyTemperatureMin>
      <ComfyTemperatureMax>60</ComfyTemperatureMax>
      <LeatherAmount>20</LeatherAmount>
      <MeatAmount>70</MeatAmount>
      <ToxicResistance>0.5</ToxicResistance>
    </statBases>
    <uiIconScale>1.15</uiIconScale>
    <tools>
      <li>
        <label>left claw</label>
        <labelNoLocation>claw</labelNoLocation>
        <capacities>
          <li>Scratch</li>
        </capacities>
        <power>17</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>FrontLeftPaw</linkedBodyPartsGroup>
        <surpriseAttack>
          <extraMeleeDamages>
            <li>
              <def>Stun</def>
              <amount>14</amount>
            </li>
          </extraMeleeDamages>
        </surpriseAttack>
      </li>
      <li>
        <label>right claw</label>
        <labelNoLocation>claw</labelNoLocation>
        <capacities>
          <li>Scratch</li>
        </capacities>
        <power>17</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>FrontRightPaw</linkedBodyPartsGroup>
        <surpriseAttack>
          <extraMeleeDamages>
            <li>
              <def>Stun</def>
              <amount>14</amount>
            </li>
          </extraMeleeDamages>
        </surpriseAttack>
      </li>
      <li>
        <capacities>
          <li>Bite</li>
        </capacities>
        <power>23.6</power>
        <cooldownTime>2.6</cooldownTime>
        <linkedBodyPartsGroup>Teeth</linkedBodyPartsGroup>
        <chanceFactor>0.5</chanceFactor>
        <surpriseAttack>
          <extraMeleeDamages>
            <li>
              <def>Stun</def>
              <amount>14</amount>
            </li>
          </extraMeleeDamages>
        </surpriseAttack>
      </li>
      <li>
        <label>head</label>
        <capacities>
          <li>Blunt</li>
        </capacities>
        <power>11</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>HeadAttackTool</linkedBodyPartsGroup>
        <ensureLinkedBodyPartsGroupAlwaysUsable>true</ensureLinkedBodyPartsGroupAlwaysUsable>
        <chanceFactor>0.2</chanceFactor>
      </li>
    </tools>
    <comps Inherit="False">
      <li>
        <compClass>CompAttachBase</compClass>
      </li>
      <li Class="CompProperties_Chimera">
        <simpleAnimalLabel>chimera</simpleAnimalLabel>
      </li>
      <li Class="CompProperties_Studiable">
        <frequencyTicks>120000</frequencyTicks>
        <knowledgeCategory>Advanced</knowledgeCategory>
        <minMonolithLevelForStudy>1</minMonolithLevelForStudy>
        <anomalyKnowledge>1</anomalyKnowledge>
        <requiresHoldingPlatform>true</requiresHoldingPlatform>
      </li>
      <li Class="CompProperties_HoldingPlatformTarget">
        <baseEscapeIntervalMtbDays>60</baseEscapeIntervalMtbDays>
      </li>
      
      <li Class="CompProperties_AttachPoints">
        <points>
          <li>
            <offset>(-.6, 0, .6)</offset>
            <type>PlatformRestraint0</type>
          </li>
          <li>
            <offset>(.6, 0, .6)</offset>
            <type>PlatformRestraint1</type>
          </li>
          <li>
            <offset>(.6, 0, -.7)</offset>
            <type>PlatformRestraint2</type>
          </li>
          <li>
            <offset>(-.6, 0, -.7)</offset>
            <type>PlatformRestraint3</type>
          </li>
        </points>
      </li>
      <li Class="CompProperties_ProducesBioferrite">
        <bioferriteDensity>1.5</bioferriteDensity>
      </li>
    </comps>
  </ThingDef>
  
</Defs>