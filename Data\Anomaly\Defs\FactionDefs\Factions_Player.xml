﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <FactionDef ParentName="PlayerFactionBase">
    <defName>ResearchExpedition</defName>
    <label>Research Expedition</label>
    <description>A team of scientists.</description>
    <isPlayer>true</isPlayer>
    <basicMemberKind>Researcher</basicMemberKind>
    <pawnSingular>colonist</pawnSingular>
    <pawnsPlural>colonists</pawnsPlural>
    <techLevel>Industrial</techLevel>
    <factionNameMaker>NamerFactionOutlander</factionNameMaker>
    <settlementNameMaker>NamerSettlementOutlander</settlementNameMaker>
    <allowedCultures><li>Astropolitan</li></allowedCultures>
    <backstoryFilters>
      <li>
        <categoriesChildhood>
          <li>Offworld</li>
        </categoriesChildhood>
        <categoriesAdulthood>
          <li>Researcher</li>
        </categoriesAdulthood>
      </li>
    </backstoryFilters>
    <factionIconPath>World/WorldObjects/Expanding/Town</factionIconPath>
    <startingResearchTags>
      <li>ClassicStart</li>
    </startingResearchTags>
    <startingTechprintsResearchTags>
      <li>ClassicStart</li>
      <li>ClassicStartTechprints</li>
    </startingTechprintsResearchTags>
    <apparelStuffFilter>
      <thingDefs>
        <li>Cloth</li>
      </thingDefs>
    </apparelStuffFilter>
  </FactionDef>
</Defs>