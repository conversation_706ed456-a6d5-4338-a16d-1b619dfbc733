<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <SoundDef>
    <defName>Birth</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Humanlike/Birth/Birth</clipFolderPath>
          </li>
        </grains>
        <distRange>10~40</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>LaborEarly</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <priorityMode>PrioritizeNearest</priorityMode>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Humanlike/Birth/LaborEarly</clipFolderPath>
          </li>
        </grains>
        <volumeRange>20</volumeRange>
        <muteWhenPaused>true</muteWhenPaused>
        <tempoAffectedByGameSpeed>false</tempoAffectedByGameSpeed>
        <gameSpeedRange>0~2</gameSpeedRange>
        <distRange>20~40</distRange>
        <sustainLoop>false</sustainLoop>
        <sustainIntervalRange>7~13</sustainIntervalRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>LaborLate</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <priorityMode>PrioritizeNearest</priorityMode>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Humanlike/Birth/LaborLate</clipFolderPath>
          </li>
        </grains>
        <muteWhenPaused>true</muteWhenPaused>
        <volumeRange>20</volumeRange>
        <tempoAffectedByGameSpeed>false</tempoAffectedByGameSpeed>
        <gameSpeedRange>0~2</gameSpeedRange>
        <distRange>20~40</distRange>
        <sustainLoop>false</sustainLoop>
        <sustainIntervalRange>3~5</sustainIntervalRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>HumanPregnancy</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <onCamera>True</onCamera>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Pawn/Humanlike/Human_Pregnancy_02c</clipPath>
          </li>
        </grains>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>BabyCrying</defName>
    <context>MapOnly</context>
    <maxSimultaneous>3</maxSimultaneous>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <volumeRange>10</volumeRange>
        <distRange>5~30</distRange>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipPath>Pawn/Humanlike/BabyCrying</clipPath>
          </li>
        </grains>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>BabyGiggling</defName>
    <context>MapOnly</context>
    <maxSimultaneous>3</maxSimultaneous>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <volumeRange>10</volumeRange>
        <distRange>5~30</distRange>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipPath>Pawn/Humanlike/BabyGiggling</clipPath>
          </li>
        </grains>
      </li>
    </subSounds>
  </SoundDef>

</Defs>