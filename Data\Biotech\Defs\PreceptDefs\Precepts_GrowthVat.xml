<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <IssueDef>
    <defName>GrowthVat</defName>
    <label>growth vats</label>
    <iconPath>UI/Issues/GrowthVats</iconPath>
  </IssueDef>

  <PreceptDef>
    <defName>GrowthVat_Essential</defName>
    <issue>GrowthVat</issue>
    <label>essential</label>
    <description>We have the technology for safer childbirth and faster childhoods. Believers will develop faster in growth vats but will be unhappy if their children are not in a vat.</description>
    <impact>Medium</impact>
    <growthVatSpeedFactor>1.3</growthVatSpeedFactor>
    <associatedMemes>
      <li MayRequire="Ludeon.RimWorld.Ideology">Transhumanist</li>
    </associatedMemes>
    <comps>
      <li Class="PreceptComp_SituationalThought">
        <thought>GrowthVat_Essential_Pregnant</thought>
        <description>Natural pregnancy</description>
      </li>
      <li Class="PreceptComp_SituationalThought">
        <thought>GrowthVat_Essential_ChildNotInGrowthVat</thought>
        <description>Child not in growth vat</description>
      </li>
    </comps>
  </PreceptDef>

  <PreceptDef>
    <defName>GrowthVat_Prohibited</defName>
    <issue>GrowthVat</issue>
    <label>prohibited</label>
    <description>Growth vats rob children of their childhood.</description>
    <impact>Medium</impact>
    <associatedMemes>
      <li MayRequire="Ludeon.RimWorld.Ideology">FleshPurity</li>
    </associatedMemes>
    <comps>
      <li Class="PreceptComp_UnwillingToDo_WithDef">
        <eventDef>BuildSpecificDef</eventDef>
        <buildingDef>GrowthVat</buildingDef>
        <description>Build growth vat</description>
      </li>
      <li Class="PreceptComp_SituationalThought">
        <thought>GrowthVat_Prohibited_GrowthVatInColony</thought>
        <description>Growth vat in colony</description>
      </li>
      <li Class="PreceptComp_SituationalThought">
        <thought>GrowthVat_Prohibited_ChildInGrowthVat</thought>
        <description>My child in a growth vat</description>
      </li>
      <li Class="PreceptComp_SituationalThought">
        <thought>GrowthVat_Prohibited_ChildNotInGrowthVat</thought>
        <description>Child not in growth vat</description>
      </li>
    </comps>
  </PreceptDef>

  <!-- Thoughts -->

  <ThoughtDef>
    <defName>GrowthVat_Essential_Pregnant</defName>
    <workerClass>ThoughtWorker_Precept_GrowthVatEssentialPregnant</workerClass>
    <doNotApplyToQuestLodgers>true</doNotApplyToQuestLodgers>
    <minExpectation>Moderate</minExpectation>
    <stages>
      <li>
        <label>natural pregnancy</label>
        <description>Childbirth is a terrifying thought. We should have vats to safely grow our children.</description>
        <baseMoodEffect>-5</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>GrowthVat_Essential_ChildNotInGrowthVat</defName>
    <workerClass>ThoughtWorker_Precept_ChildNotInGrowthVat</workerClass>
    <doNotApplyToQuestLodgers>true</doNotApplyToQuestLodgers>
    <stackLimit>3</stackLimit>
    <minExpectation>Moderate</minExpectation>
    <stages>
      <li>
        <label>child not in growth vat</label>
        <description>I don't feel safe with my child running around. They should be growing safely in a vat.</description>
        <baseMoodEffect>-2</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>GrowthVat_Prohibited_ChildNotInGrowthVat</defName>
    <workerClass>ThoughtWorker_Precept_ChildNotInGrowthVat</workerClass>
    <doNotApplyToQuestLodgers>true</doNotApplyToQuestLodgers>
    <stackLimit>3</stackLimit>
    <stages>
      <li>
        <label>child not in growth vat</label>
        <description>I'm happy to see my kids be kids. Their childhood is too precious to be wasted in a vat.</description>
        <baseMoodEffect>1</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>GrowthVat_Prohibited_GrowthVatInColony</defName>
    <workerClass>ThoughtWorker_Precept_GrowthVatInColony</workerClass>
    <doNotApplyToQuestLodgers>true</doNotApplyToQuestLodgers>
    <stages>
      <li>
        <label>growth vat</label>
        <description>I hate the thought of that awful growth vat being in our colony.</description>
        <baseMoodEffect>-3</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>GrowthVat_Prohibited_ChildInGrowthVat</defName>
    <workerClass>ThoughtWorker_Precept_ChildInGrowthVat</workerClass>
    <stackLimit>3</stackLimit>
    <nullifyingTraits>
      <li>Psychopath</li>
    </nullifyingTraits>
    <stages>
      <li>
        <label>my child is in a growth vat</label>
        <description>I can't stand the thought of my child floating in that awful machine.</description>
        <baseMoodEffect>-8</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

</Defs>