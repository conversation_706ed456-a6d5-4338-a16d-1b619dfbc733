<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <QuestScriptDef>
    <defName>MechanitorShip</defName>
    <rootSelectionWeight>0</rootSelectionWeight>
    <isRootSpecial>true</isRootSpecial>
    <questNameRules>
      <rulesStrings>
        <li>questName->[shipAdjective] [shipNoun]</li>
        <li>shipAdjective->mechanitor</li>
        <li>shipAdjective->the overseer's</li>
        <li>shipAdjective->ancient</li>
        <li>shipAdjective->mechanoid</li>
        <li>shipNoun->ship</li>
        <li>shipNoun->shuttle</li>
        <li>shipNoun->craft</li>
        <li>shipNoun->wreck</li>
        <li>shipNoun->wreckage</li>
        <li>shipNoun->transport</li>
      </rulesStrings>
    </questNameRules>
    <questDescriptionRules>
      <rulesStrings>
        <li>questDescription->You have decrypted an ancient transponder, revealing the location of a mechanoid ship in orbit. The ship is badly damaged but you can signal it to land nearby.\n\nThe ship contains the remains of a long-dead mechanitor. Mechanitors can create and control mechanoids for work and combat. By extracting the deceased mechanitor's mechlink, you can turn one of your own colonists into a mechanitor.\n\nBeware - the ship also contains hostile mechanoids. The mechanoid group is composed of:\n\n[mechList]</li>
      </rulesStrings>
    </questDescriptionRules>
    <questContentRules>
      <rulesStrings>
      
        <li>mechanitorShuttleCrashedLetterLabel->Mechanitor ship</li>
        <li>mechanitorShuttleCrashedLetterText->A mechanitor ship containing feral mechanoids has crashed nearby. The mechanoids will exit the crashed ship and prepare to fight.</li>
        
        <li>assaultBeginLetterLabel->Mechanoid attack</li>
        <li>assaultBeginLetterText->The feral mechanoids are coming to assault the colony!</li>

        <li>mechlinkAvailableLetterLabel->[mechanitor_nameDef]'s mechlink available</li>
        <li>mechlinkAvailableLetterText->The crashed ship contained the corpse of an ancient mechanitor.\n\nExtract [mechanitor_possessive] mechlink by selecting a colonist and right-clicking the corpse.</li>

      </rulesStrings>
    </questContentRules>
    <root Class="QuestNode_Root_MechanitorShip">
      <combatPointsFactor>0.5</combatPointsFactor>
      <mechGroups>
        <li>  
          <options>
            <li>
              <pawnKind>Mech_Scyther</pawnKind>
              <weight>1</weight>
              <requireOneOf>true</requireOneOf>
            </li>
            <li>
              <pawnKind>Mech_Militor</pawnKind>
              <weight>2</weight>
            </li>
          </options>
        </li>
        <li>
          <options>
            <li>
              <pawnKind>Mech_Tesseron</pawnKind>
              <weight>1</weight>
              <requireOneOf>true</requireOneOf>
            </li>
            <li>
              <pawnKind>Mech_Militor</pawnKind>
              <weight>2</weight>
            </li>
          </options>
        </li>
        <li>
          <options>
            <li>
              <pawnKind>Mech_Scorcher</pawnKind>
              <weight>1</weight>
              <requireOneOf>true</requireOneOf>
            </li>
            <li>
              <pawnKind>Mech_Militor</pawnKind>
              <weight>2</weight>
            </li>
          </options>
        </li>
      </mechGroups>
    </root>
  </QuestScriptDef>
  
</Defs>
