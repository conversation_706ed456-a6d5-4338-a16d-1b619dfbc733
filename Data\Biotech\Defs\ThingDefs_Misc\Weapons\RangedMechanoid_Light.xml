<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingDef Name="LightMechanoidGun" ParentName="BaseGun" Abstract="True">
    <tradeability>None</tradeability>
    <destroyOnDrop>true</destroyOnDrop>
    <relicChance>0</relicChance>
    <graphicData>
      <drawSize>0.65</drawSize>
    </graphicData>
    <equippedDistanceOffset >-0.14</equippedDistanceOffset >
    <tools>
      <li>
        <label>barrel</label>
        <capacities>
          <li>Blunt</li>
          <li>Poke</li>
        </capacities>
        <power>9</power>
        <cooldownTime>2.6</cooldownTime>
      </li>
    </tools>
  </ThingDef>

  <!-- Ranged -->
  <ThingDef Name="LightMechanoidGunRanged" ParentName="LightMechanoidGun" Abstract="True">
    <weaponClasses>
      <li>Ranged</li>
    </weaponClasses>
  </ThingDef>

  <ThingDef ParentName="LightMechanoidGunRanged">
    <defName>Gun_MiniShotgun</defName>
    <label>mini-shotgun</label>
    <description>A compact, short-range shotgun designed to be mounted on a light combat mechanoid.</description>
    <techLevel>Spacer</techLevel>
    <graphicData>
      <texPath>Things/Item/Equipment/WeaponRanged/MiniShotgun</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <statBases>
      <MarketValue>1000</MarketValue>
      <Mass>1.5</Mass>
      <AccuracyTouch>0.80</AccuracyTouch>
      <AccuracyShort>0.87</AccuracyShort>
      <AccuracyMedium>0.7</AccuracyMedium>
      <AccuracyLong>0.55</AccuracyLong>
      <RangedWeapon_Cooldown>1.7</RangedWeapon_Cooldown>
    </statBases>
    <weaponTags>
      <li>MechanoidGunShortRange</li>
    </weaponTags>
    <verbs>
      <li>
        <verbClass>Verb_Shoot</verbClass>
        <hasStandardCommand>true</hasStandardCommand>
        <defaultProjectile>Bullet_MiniShotgun</defaultProjectile>
        <warmupTime>1.2</warmupTime>
        <range>12.9</range>
        <burstShotCount>1</burstShotCount>
        <ticksBetweenBurstShots>10</ticksBetweenBurstShots>
        <soundCast>Shot_Shotgun</soundCast>
        <soundCastTail>GunTail_Heavy</soundCastTail>
        <muzzleFlashScale>6</muzzleFlashScale>
      </li>
    </verbs>
  </ThingDef>
  <ThingDef ParentName="BaseBullet">
    <defName>Bullet_MiniShotgun</defName>
    <label>shotgun blast</label>
    <graphicData>
      <texPath>Things/Projectile/Bullet_MiniShotgun</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <projectile>
      <damageDef>Bullet</damageDef>
      <damageAmountBase>10</damageAmountBase>
      <stoppingPower>2.5</stoppingPower>
      <armorPenetrationBase>0.12</armorPenetrationBase>
      <speed>55</speed>
    </projectile>
  </ThingDef>

  <!-- Obsolete: Kept for back-compat and modding -->
  <ThingDef ParentName="LightMechanoidGunRanged">
    <defName>Gun_Slugthrower</defName>
    <label>slugthrower</label>
    <description>A compact single-shot slug thrower designed to be mounted on a light combat mechanoid.</description>
    <graphicData>
      <texPath>Things/Item/Equipment/WeaponRanged/Slugthrower</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <statBases>
      <MarketValue>1000</MarketValue>
      <Mass>1.5</Mass>
      <AccuracyTouch>0.20</AccuracyTouch>
      <AccuracyShort>0.30</AccuracyShort>
      <AccuracyMedium>0.40</AccuracyMedium>
      <AccuracyLong>0.95</AccuracyLong>
      <RangedWeapon_Cooldown>4.0</RangedWeapon_Cooldown>      
    </statBases>
    <weaponTags>
      <li>MechanoidGunSlugthrower</li>
    </weaponTags>
    <verbs>
      <li>
        <verbClass>Verb_Shoot</verbClass>
        <hasStandardCommand>true</hasStandardCommand>
        <defaultProjectile>Bullet_Slugthrower</defaultProjectile>
        <warmupTime>0.3</warmupTime>
        <range>19.9</range>
        <ticksBetweenBurstShots>8</ticksBetweenBurstShots>
        <burstShotCount>1</burstShotCount>
        <soundCast>Shot_Slugthrower</soundCast>
        <soundCastTail>GunTail_Light</soundCastTail>
        <muzzleFlashScale>9</muzzleFlashScale>
      </li>
    </verbs>
    <tools>
      <li>
        <label>barrel</label>
        <capacities>
          <li>Blunt</li>
          <li>Poke</li>
        </capacities>
        <power>9</power>
        <cooldownTime>2.6</cooldownTime>
      </li>
    </tools>
  </ThingDef>
  <ThingDef ParentName="BaseBullet">
    <defName>Bullet_Slugthrower</defName>
    <label>slug</label>
    <graphicData>
      <texPath>Things/Projectile/Bullet_Small</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <projectile>
      <damageDef>Bullet</damageDef>
      <damageAmountBase>12</damageAmountBase>
      <speed>70</speed>
    </projectile>
  </ThingDef>

  <ThingDef ParentName="LightMechanoidGunRanged">
    <defName>Gun_Spiner</defName>
    <label>spiner</label>
    <description>A very small gun used by war urchins which quickly fires spine-like projectiles. It is short-ranged but deadly.</description>
    <graphicData>
      <texPath>Things/Item/Equipment/WeaponRanged/SpinerGun</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <statBases>
      <MarketValue>500</MarketValue>
      <Mass>1</Mass>
      <AccuracyTouch>0.20</AccuracyTouch>
      <AccuracyShort>0.30</AccuracyShort>
      <AccuracyMedium>0.40</AccuracyMedium>
      <AccuracyLong>0.95</AccuracyLong>
      <RangedWeapon_Cooldown>1.8</RangedWeapon_Cooldown>      
    </statBases>
    <weaponTags>
      <li>MechanoidGunSpiner</li>
    </weaponTags>
    <verbs>
      <li>
        <verbClass>Verb_Shoot</verbClass>
        <hasStandardCommand>true</hasStandardCommand>
        <defaultProjectile>Bullet_Spiner</defaultProjectile>
        <warmupTime>0.3</warmupTime>
        <range>6.9</range>
        <ticksBetweenBurstShots>8</ticksBetweenBurstShots>
        <burstShotCount>1</burstShotCount>
        <soundCast>Shot_Spiner</soundCast>
        <soundCastTail>GunTail_Light</soundCastTail>
        <muzzleFlashScale>9</muzzleFlashScale>
      </li>
    </verbs>
  </ThingDef>
  <ThingDef ParentName="BaseBullet">
    <defName>Bullet_Spiner</defName>
    <label>spine</label>
    <graphicData>
      <texPath>Things/Projectile/Bullet_Small</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <projectile>
      <damageDef>Bullet</damageDef>
      <damageAmountBase>12</damageAmountBase>
      <speed>70</speed>
    </projectile>
  </ThingDef>

  <ThingDef ParentName="LightMechanoidGunRanged">
    <defName>Gun_MiniFlameblaster</defName>
    <label>mini-flameblaster</label>
    <description>A small burst-oriented flamethrower used by mechanoids.</description>
    <graphicData>
      <texPath>Things/Item/Equipment/WeaponRanged/MiniFlameblaster</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <statBases>
      <MarketValue>1000</MarketValue>
      <Mass>1.5</Mass>
      <AccuracyTouch>0.40</AccuracyTouch>
      <AccuracyShort>0.48</AccuracyShort>
      <AccuracyMedium>0.35</AccuracyMedium>
      <AccuracyLong>0.26</AccuracyLong>
      <RangedWeapon_Cooldown>4.0</RangedWeapon_Cooldown>      
    </statBases>
    <weaponTags>
      <li>MechanoidGunMiniFlameblaster</li>
    </weaponTags>
    <verbs>
      <li>
        <verbClass>Verb_SpewFire</verbClass>
        <hasStandardCommand>true</hasStandardCommand>
        <warmupTime>0</warmupTime>
        <range>4.9</range>
        <ticksBetweenBurstShots>4</ticksBetweenBurstShots>
        <burstShotCount>10</burstShotCount>
        <soundCast>Shot_MiniFlameblaster</soundCast>
        <muzzleFlashScale>9</muzzleFlashScale>
      </li>
    </verbs>
  </ThingDef>

</Defs>