﻿<?xml version="1.0" encoding="utf-8"?>

<Defs>

  <ThingDef ParentName="ApparelNoQualityBase">
    <defName>Apparel_DisruptorFlarePack</defName>
    <label>disruptor flare pack</label>
    <description>A utility pack that shoots disruptor flares. On impact, the flare burns a capsule of bioferrite, which creates a bright flash and a local psychic disruption. This stuns psychically sensitive creatures, revealing those that are invisible, and reducing their consciousness and movement speed for a short while. Afterwards, the flare continues to burn, lighting up a wide area for some time.</description>
    <techLevel>Industrial</techLevel>
    <mergeVerbGizmos>false</mergeVerbGizmos>
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/DisruptorFlarePack/DisruptorFlarePack</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>(0.7,0.7)</drawSize>
    </graphicData>
    <comps>
      <li Class="CompProperties_DisruptorFlarePack">
        <checkInterval>60</checkInterval>
        <fieldRadius>8.9</fieldRadius>
        <durationSeconds>30</durationSeconds>
      </li>
      <li Class="CompProperties_ApparelReloadable">
        <hotKey>Misc4</hotKey>
        <soundReload>Standard_Reload</soundReload>
        <displayGizmoWhileUndrafted>false</displayGizmoWhileUndrafted>
        <maxCharges>6</maxCharges>
        <ammoDef>Bioferrite</ammoDef>
        <ammoCountPerCharge>5</ammoCountPerCharge>
        <baseReloadTicks>60</baseReloadTicks>
        <chargeNoun>flare</chargeNoun>
      </li>
    </comps>
    <verbs>
      <li>
        <verbClass>Verb_LaunchProjectileStaticPsychic</verbClass>
        <label>deploy flare</label>
        <defaultProjectile>Grenade_DisruptorFlare</defaultProjectile>
        <warmupTime>0.5</warmupTime>
        <range>22.9</range>
        <burstShotCount>1</burstShotCount>
        <onlyManualCast>true</onlyManualCast>
        <hasStandardCommand>true</hasStandardCommand>
        <targetable>true</targetable>
        <violent>false</violent>
        <targetParams>
          <canTargetPawns>false</canTargetPawns>
          <canTargetBuildings>false</canTargetBuildings>
          <canTargetLocations>true</canTargetLocations>
        </targetParams>
        <soundCast>FlareLaunch</soundCast>
        <rangedFireRulepack>Combat_RangedFire_Thrown</rangedFireRulepack>
        <canGoWild>false</canGoWild>
        <explosionRadiusRingColor>(0.8, 0.8, 0.4, 1)</explosionRadiusRingColor><!-- Keep in sync with PlaceWorker_GlowRadius.RingColor -->
      </li>
    </verbs>
    <recipeMaker>
      <unfinishedThingDef>UnfinishedPack</unfinishedThingDef>
      <researchPrerequisite>DisruptorFlares</researchPrerequisite>
      <recipeUsers>
        <li>BioferriteShaper</li>
      </recipeUsers>
      <useIngredientsForColor>false</useIngredientsForColor>
      <workSpeedStat>GeneralLaborSpeed</workSpeedStat>
      <workSkill>Crafting</workSkill>
      <effectWorking>Smith</effectWorking>
      <soundWorking>Recipe_Smith</soundWorking>
      <skillRequirements>
        <Crafting>4</Crafting>
      </skillRequirements>
      <displayPriority>30</displayPriority>
    </recipeMaker>
    <costList>
      <ComponentIndustrial>1</ComponentIndustrial>
      <Bioferrite>30</Bioferrite>
    </costList>
    <tickerType>Normal</tickerType>
    <statBases>
      <WorkToMake>7200</WorkToMake>
      <Mass>3</Mass>
      <Flammability>0.4</Flammability>
      <EquipDelay>2</EquipDelay>
    </statBases>
    <thingCategories>
      <li>ApparelUtility</li>
    </thingCategories>
    <apparel>
      <countsAsClothingForNudity>false</countsAsClothingForNudity>
      <careIfWornByCorpse>false</careIfWornByCorpse>
      <careIfDamaged>false</careIfDamaged>
      <wearPerDay>0</wearPerDay>
      <bodyPartGroups>
        <li>Waist</li>
      </bodyPartGroups>
      <layers>
        <li>Belt</li>
      </layers>
      <wornGraphicPath>Things/Pawn/Humanlike/Apparel/DisruptorFlarePack/DisruptorFlarePack</wornGraphicPath>
      <wornGraphicData>
        <renderUtilityAsPack>true</renderUtilityAsPack>
        <north>
          <offset>(0,-0.15)</offset>
        </north>
        <south>
          <offset>(0,-0.15)</offset>
        </south>
        <east>
          <offset>(-0.35,-0.1)</offset>
          <thin><offset>(0.05,0)</offset></thin>
          <hulk><offset>(-0.15,0)</offset></hulk>
          <fat> <offset>(-0.20,0)</offset></fat>
        </east>
        <west>
          <offset>(0.35,-0.1)</offset>
          <thin><offset>(-0.05,0)</offset></thin>
          <hulk><offset>(0.15,0)</offset></hulk>
          <fat> <offset>(0.20,0)</offset></fat>
        </west>
        <male>  <scale>(0.4,0.4)</scale></male>
        <female><scale>(0.4,0.4)</scale></female>
        <thin>  <scale>(0.35,0.35)</scale></thin>
        <hulk>  <scale>(0.45,0.45)</scale></hulk>
        <fat>   <scale>(0.45,0.45)</scale></fat>
      </wornGraphicData>
    </apparel>
    <allowedArchonexusCount>1</allowedArchonexusCount>
    <tradeTags>
      <li>ExoticMisc</li>
    </tradeTags>
  </ThingDef>

  <ThingDef ParentName="BaseBullet">
    <defName>Grenade_DisruptorFlare</defName>
    <label>disruptor flare</label>
    <graphicData>
      <texPath>Things/Projectile/DisruptorFlare</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <shaderType>TransparentPostLight</shaderType>
    </graphicData>
    <thingClass>Projectile_SpawnsThing</thingClass>
    <projectile>
      <speed>40</speed>
      <arcHeightFactor>1.0</arcHeightFactor>
      <spinRate>3</spinRate>
      <explosionRadius>7.9</explosionRadius> <!-- Must be kept in sync with radius of CompProperties_DisruptorFlare -->
      <landedEffecter>DisruptorFlareLanded</landedEffecter>
      <spawnsThingDef>DisruptorFlare</spawnsThingDef>
    </projectile>
  </ThingDef>

  <ThingDef>
    <defName>DisruptorFlare</defName>
    <label>disruptor flare</label>
    <graphicData>
      <graphicClass>Graphic_Single</graphicClass>
      <texPath>Things/Projectile/DisruptorFlare</texPath>
      <shaderType>Transparent</shaderType>
      <onGroundRandomRotateAngle>360</onGroundRandomRotateAngle>
    </graphicData>
    <selectable>false</selectable>
    <tickerType>Normal</tickerType>
    <thingClass>ThingWithComps</thingClass>
    <useHitPoints>false</useHitPoints>
    <rotatable>false</rotatable>
    <comps>
      <li Class="CompProperties_DisruptorFlare">
        <radius>7.9</radius>
        <effecterDef>DisruptorFlareAttached</effecterDef>
        <destroyWarningEffecterDef>DisruptorDestroyWarning</destroyWarningEffecterDef>
      </li>
      <li Class="CompProperties_DestroyAfterDelay">
        <delayTicks>14400</delayTicks>
        <displayCountdownOnLabel>true</displayCountdownOnLabel>
      </li>
      <li Class="CompProperties_Glower">
        <glowRadius>5</glowRadius>
        <glowColor>(255,117,108,0)</glowColor>
      </li>
    </comps>
  </ThingDef>

  <ThingDef ParentName="ApparelNoQualityBase">
    <defName>Apparel_PackTurret</defName>
    <label>turret pack</label>
    <description>A wearable pack that allows the user to deploy a battery-powered turret. The pack contains a propulsion device, letting the user launch the turret a short distance. These turrets are effective for flanking and distracting enemies. However, the turret's limited AI can't be directly controlled, so it can cause friendly fire incidents. The turret battery lasts for several hours.</description>
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/TurretPack/TurretPack</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>(0.7,0.7)</drawSize>
    </graphicData>
    <tickerType>Normal</tickerType>
    <techLevel>Industrial</techLevel>
    <statBases>
      <WorkToMake>10000</WorkToMake>
      <Mass>3</Mass>
      <Flammability>0.6</Flammability>
      <EquipDelay>2</EquipDelay>
    </statBases>
    <thingCategories>
      <li>ApparelUtility</li>
    </thingCategories>
    <costList>
      <Steel>70</Steel>
      <ComponentIndustrial>2</ComponentIndustrial>
    </costList>
    <recipeMaker>
      <unfinishedThingDef>UnfinishedPack</unfinishedThingDef>
      <researchPrerequisite>TurretPack</researchPrerequisite>
      <recipeUsers>
        <li>TableMachining</li>
      </recipeUsers>
      <useIngredientsForColor>false</useIngredientsForColor>
      <workSpeedStat>GeneralLaborSpeed</workSpeedStat>
      <workSkill>Crafting</workSkill>
      <effectWorking>Smith</effectWorking>
      <soundWorking>Recipe_Smith</soundWorking>
      <displayPriority>360</displayPriority>
    </recipeMaker>
    <mergeVerbGizmos>false</mergeVerbGizmos>
    <apparel>
      <countsAsClothingForNudity>false</countsAsClothingForNudity>
      <scoreOffset>4</scoreOffset>
      <careIfWornByCorpse>false</careIfWornByCorpse>
      <careIfDamaged>false</careIfDamaged>
      <wearPerDay>0</wearPerDay>
      <bodyPartGroups>
        <li>Waist</li>
      </bodyPartGroups>
      <layers>
        <li>Belt</li>
      </layers>
      <wornGraphicPath>Things/Pawn/Humanlike/Apparel/TurretPack/TurretPack</wornGraphicPath>
      <wornGraphicData>
        <renderUtilityAsPack>true</renderUtilityAsPack>
        <north>
          <offset>(0,-0.15)</offset>
        </north>
        <south>
          <offset>(0,0.1)</offset>
          <scale>(1.3,1)</scale><!--frame covered by pawn body unless we stretch-->
        </south>
        <east>
          <offset>(-0.35,-0.05)</offset>
          <thin>
            <offset>(0.05,0)</offset>
          </thin>
          <hulk>
            <offset>(-0.1,0)</offset>
          </hulk>
          <fat>
            <offset>(-0.15,0)</offset>
          </fat>
        </east>
        <west>
          <offset>(0.35,-0.05)</offset>
          <thin>
            <offset>(-0.05,0)</offset>
          </thin>
          <hulk>
            <offset>(0.1,0)</offset>
          </hulk>
          <fat>
            <offset>(0.15,0)</offset>
          </fat>
        </west>
        <male>
          <scale>(0.8,0.8)</scale>
        </male>
        <female>
          <scale>(0.75,0.75)</scale>
        </female>
        <thin>
          <scale>(0.7,0.7)</scale>
        </thin>
        <hulk>
          <scale>(0.8,0.8)</scale>
        </hulk>
        <fat>
          <scale>(0.8,0.8)</scale>
        </fat>
      </wornGraphicData>
    </apparel>
    <comps>
      <li Class="CompProperties_ApparelVerbOwnerCharged">
        <hotKey>Misc4</hotKey>
        <displayGizmoWhileUndrafted>true</displayGizmoWhileUndrafted>
        <maxCharges>1</maxCharges>
        <destroyOnEmpty>true</destroyOnEmpty>
      </li>
    </comps>
    <verbs>
      <li>
        <verbClass>Verb_LaunchProjectileStaticOneUse</verbClass>
        <label>deploy turret</label>
        <defaultProjectile>Grenade_TurretPack</defaultProjectile>
        <warmupTime>1.0</warmupTime>
        <range>22.9</range>
        <burstShotCount>1</burstShotCount>
        <onlyManualCast>true</onlyManualCast>
        <hasStandardCommand>true</hasStandardCommand>
        <targetable>true</targetable>
        <targetParams>
          <canTargetPawns>false</canTargetPawns>
          <canTargetBuildings>false</canTargetBuildings>
          <canTargetLocations>true</canTargetLocations>
        </targetParams>
        <soundCast>ThrowGrenade</soundCast>
        <rangedFireRulepack>Combat_RangedFire_Thrown</rangedFireRulepack>
        <canGoWild>false</canGoWild>
      </li>
    </verbs>
    <tradeTags>
      <li>Clothing</li>
    </tradeTags>
  </ThingDef>

  <ThingDef ParentName="BaseGrenadeProjectile">
    <defName>Grenade_TurretPack</defName>
    <label>turret pack capsule</label> 
    <thingClass>Projectile_SpawnsThing</thingClass>
    <graphicData>
      <texPath>Things/Building/TacticalTurret/TacticalTurret_Packed_Projectile</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <projectile>
      <explosionRadius>19.9</explosionRadius> <!-- Must be kept in sync with range of Gun_TacticalTurret -->
      <speed>41</speed>
      <spawnsThingDef>Turret_TacticalTurret</spawnsThingDef>
      <tryAdjacentFreeSpaces>true</tryAdjacentFreeSpaces>
    </projectile>
  </ThingDef>

  <ThingDef ParentName="ApparelNoQualityBase">
    <defName>Apparel_DeadlifePack</defName>
    <label>deadlife pack</label>
    <description>A single-use pack that launches a capsule which explodes into deadlife dust. The dust animates human and animal corpses as shamblers that will only attack your enemies.\n\nDeadlife dust is composed of microscopic machine-like archites which hang in the air. Where they touch a corpse, they induce a chaotic and violent resurrection.</description>
    <techLevel>Industrial</techLevel>
    <genericMarketSellable>false</genericMarketSellable>
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/DeadlifePack/DeadlifePack</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>(0.7,0.7)</drawSize>
    </graphicData>
    <tickerType>Normal</tickerType>
    <tradeTags>
      <li>UtilitySpecial</li>
    </tradeTags>
    <statBases>
      <WorkToMake>6500</WorkToMake>
      <Mass>3</Mass>
      <Flammability>0.6</Flammability>
      <EquipDelay>2</EquipDelay>
    </statBases>
    <thingCategories>
      <li>ApparelUtility</li>
    </thingCategories>
    <thingSetMakerTags>
      <li>RewardStandardLowFreq</li>
    </thingSetMakerTags>
    <costList>
      <Bioferrite>30</Bioferrite>
      <Shard>1</Shard>
    </costList>
    <recipeMaker>
      <unfinishedThingDef>UnfinishedPack</unfinishedThingDef>
      <researchPrerequisite>DeadlifeDust</researchPrerequisite>
      <recipeUsers>
        <li>BioferriteShaper</li>
      </recipeUsers>
      <useIngredientsForColor>false</useIngredientsForColor>
      <workSpeedStat>GeneralLaborSpeed</workSpeedStat>
      <workSkill>Crafting</workSkill>
      <effectWorking>Smith</effectWorking>
      <soundWorking>Recipe_Smith</soundWorking>
      <displayPriority>40</displayPriority>
      <skillRequirements>
        <Crafting>4</Crafting>
      </skillRequirements>
    </recipeMaker>
    <mergeVerbGizmos>false</mergeVerbGizmos>
    <apparel>
      <countsAsClothingForNudity>false</countsAsClothingForNudity>
      <scoreOffset>4</scoreOffset>
      <careIfWornByCorpse>false</careIfWornByCorpse>
      <careIfDamaged>false</careIfDamaged>
      <wearPerDay>0</wearPerDay>
      <bodyPartGroups>
        <li>Waist</li>
      </bodyPartGroups>
      <layers>
        <li>Belt</li>
      </layers>
      <wornGraphicPath>Things/Pawn/Humanlike/Apparel/DeadlifePack/DeadlifePack</wornGraphicPath>
      <wornGraphicData>
        <renderUtilityAsPack>true</renderUtilityAsPack>
        <north>
          <offset>(0,-0.15)</offset>
        </north>
        <south>
          <offset>(0,-0.15)</offset>
        </south>
        <east>
          <offset>(-0.35,-0.1)</offset>
          <thin><offset>(0.05,0)</offset></thin>
          <hulk><offset>(-0.15,0)</offset></hulk>
          <fat> <offset>(-0.20,0)</offset></fat>
        </east>
        <west>
          <offset>(0.35,-0.1))</offset>
          <thin><offset>(-0.05,0)</offset></thin>
          <hulk><offset>(0.15,0)</offset></hulk>
          <fat> <offset>(0.20,0)</offset></fat>
        </west>

        <male>  <scale>(0.6,0.6)</scale></male>
        <female><scale>(0.6,0.6)</scale></female>
        <thin>  <scale>(0.6,0.6)</scale></thin>
        <hulk>  <scale>(0.75,0.75)</scale></hulk>
        <fat>   <scale>(0.75,0.75)</scale></fat>
      </wornGraphicData>
    </apparel>
    <comps>
      <li Class="CompProperties_ApparelVerbOwnerCharged">
        <maxCharges>1</maxCharges>
        <displayGizmoWhileUndrafted>false</displayGizmoWhileUndrafted>
        <destroyOnEmpty>true</destroyOnEmpty>
        <hotKey>Misc4</hotKey>
      </li>
    </comps>
    <verbs>
      <li>
        <verbClass>Verb_LaunchProjectileStatic</verbClass>
        <label>deploy deadlife dust</label>
        <defaultProjectile>Grenade_Deadlife</defaultProjectile>
        <warmupTime>1.0</warmupTime>
        <range>23.9</range>
        <forcedMissRadius>0.1</forcedMissRadius>
        <burstShotCount>1</burstShotCount>
        <onlyManualCast>true</onlyManualCast>
        <violent>false</violent>
        <hasStandardCommand>true</hasStandardCommand>
        <targetable>true</targetable>
        <targetParams>
          <canTargetPawns>false</canTargetPawns>
          <canTargetBuildings>false</canTargetBuildings>
          <canTargetLocations>true</canTargetLocations>
        </targetParams>
        <soundCast>ThrowGrenade</soundCast>
        <rangedFireRulepack>Combat_RangedFire_Thrown</rangedFireRulepack>
        <canGoWild>false</canGoWild>
      </li>
    </verbs>
  </ThingDef>
  
  <ThingDef ParentName="BaseBullet">
    <defName>Grenade_Deadlife</defName>
    <label>deadlife capsule</label>
    <graphicData>
      <texPath>Things/Projectile/ShellDeadlife</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <shaderType>TransparentPostLight</shaderType>
    </graphicData>
    <thingClass>Projectile_Explosive</thingClass>
    <projectile>
      <damageDef>DeadlifeDust</damageDef>
      <speed>41</speed>
      <explosionRadius>0.1</explosionRadius>
      <soundExplode>ToxicShellLanded</soundExplode>
      <postExplosionSpawnThingDef>Shell_Deadlife_Releasing</postExplosionSpawnThingDef>
    </projectile>
  </ThingDef>
  
</Defs>