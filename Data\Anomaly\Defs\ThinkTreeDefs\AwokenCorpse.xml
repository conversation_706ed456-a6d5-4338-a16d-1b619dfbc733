﻿<?xml version="1.0" encoding="utf-8"?>

<Defs>
  <ThinkTreeDef>
    <defName>AwokenCorpse</defName>
    <thinkRoot Class="ThinkNode_Priority">
      <subNodes>
        <!-- Keep lying down if we have to -->
        <li Class="ThinkNode_ConditionalMustKeepLyingDown">
          <subNodes>
            <!-- Do a queued job if possible -->
            <li Class="ThinkNode_QueuedJob">
              <inBedOnly>true</inBedOnly>
            </li>

            <!-- Keep lying down -->
            <li Class="JobGiver_KeepLyingDown" />
          </subNodes>
        </li>

        <li Class="ThinkNode_Subtree">
          <treeDef>Downed</treeDef>
        </li>
        
        <!-- Escaping-->
        <li Class="ThinkNodeConditional_EscapingHoldingPlatform">
          <subNodes>
            <li Class="JobGiver_EscapingHoldingPlatform" />
          </subNodes>
        </li>

        <!-- Unnatural corpse behaviour -->
        <li Class="JobGiver_UnnaturalCorpseIdle" />
        <li Class="JobGiver_UnnaturalCorpseUnstuck" />
        <li Class="JobGiver_UnnaturalCorpseAttack" />
        <li Class="JobGiver_UnnaturalCorpseSkip" />
        <li Class="JobGiver_UnnaturalCorpseTowardVictimDirection" />
        <li Class="JobGiver_UnnaturalCorpseSkipBackup" />
        <li Class="JobGiver_WanderAnywhere">
          <maxDanger>Deadly</maxDanger>
          <ticksBetweenWandersRange>60~120</ticksBetweenWandersRange>
        </li>

        <li Class="JobGiver_IdleError"/>
      </subNodes>
    </thinkRoot>
  </ThinkTreeDef>

  <ThinkTreeDef>
    <defName>AwokenCorpseConstant</defName>
    <thinkRoot Class="ThinkNode_Priority">
      <subNodes>
        <!-- Despawned -->
        <li Class="ThinkNode_Subtree">
          <treeDef>Despawned</treeDef>
        </li>

        <li Class="ThinkNode_ConditionalCanDoLordJobNow">
          <subNodes>
            <!-- Lord directives -->
            <li Class="ThinkNode_Subtree">
              <treeDef>LordDutyConstant</treeDef>
            </li>
          </subNodes>
        </li>
      </subNodes>
    </thinkRoot>
  </ThinkTreeDef>
</Defs>