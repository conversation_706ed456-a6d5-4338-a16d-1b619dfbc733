﻿<?xml version="1.0" encoding="utf-8" ?>

<Defs>

  <BodyPartDef>
    <defName>Head</defName>
    <label>head</label>
    <hitPoints>25</hitPoints>
    <skinCovered>true</skinCovered>
    <canScarify>true</canScarify>
    <canSuggestAmputation>false</canSuggestAmputation>
    <bleedRate>2</bleedRate>
    <executionPartPriority>500</executionPartPriority>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Skull</defName>
    <label>skull</label>
    <hitPoints>25</hitPoints>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <skinCovered>false</skinCovered>
    <solid>true</solid>
    <canSuggestAmputation>false</canSuggestAmputation>
    <bleedRate>0</bleedRate>
    <destroyableByDamage>false</destroyableByDamage>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Brain</defName>
    <label>brain</label>
    <hitPoints>10</hitPoints>
    <permanentInjuryChanceFactor>9999999</permanentInjuryChanceFactor>
    <delicate>true</delicate>
    <skinCovered>false</skinCovered>
    <tags>
      <li>ConsciousnessSource</li>
    </tags>
    <canSuggestAmputation>false</canSuggestAmputation>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Eye</defName>
    <label>eye</label>
    <hitPoints>10</hitPoints>
    <permanentInjuryChanceFactor>15</permanentInjuryChanceFactor>
    <delicate>true</delicate>
    <skinCovered>false</skinCovered>
    <canScarify>true</canScarify>
    <socketed>true</socketed>
    <beautyRelated>true</beautyRelated>
    <tags>
      <li>SightSource</li>
    </tags>
    <hitChanceFactors>
      <li>
        <key>Blunt</key>
        <value>0</value>
      </li>
    </hitChanceFactors>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Ear</defName>
    <label>ear</label>
    <hitPoints>12</hitPoints>
    <frostbiteVulnerability>5</frostbiteVulnerability>
    <skinCovered>true</skinCovered>
    <beautyRelated>true</beautyRelated>
    <tags>
      <li>HearingSource</li>
    </tags>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Nose</defName>
    <label>nose</label>
    <hitPoints>10</hitPoints>
    <frostbiteVulnerability>5</frostbiteVulnerability>
    <skinCovered>true</skinCovered>
    <canScarify>true</canScarify>
    <beautyRelated>true</beautyRelated>
    <solid>true</solid>
    <bleedRate>0</bleedRate>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Jaw</defName>
    <label>jaw</label>
    <hitPoints>20</hitPoints>
    <frostbiteVulnerability>2</frostbiteVulnerability>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <skinCovered>false</skinCovered>
    <solid>true</solid>
    <beautyRelated>true</beautyRelated>
    <bleedRate>0</bleedRate>
    <tags>
      <li>EatingSource</li>
      <li>TalkingSource</li>
    </tags>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Neck</defName>
    <label>neck</label>
    <hitPoints>25</hitPoints>
    <bleedRate>4</bleedRate>
    <skinCovered>true</skinCovered>
    <tags>
      <li>BreathingPathway</li>
      <li>EatingPathway</li>
      <li>TalkingPathway</li>
    </tags>
    <canSuggestAmputation>false</canSuggestAmputation>
    <executionPartPriority>1000</executionPartPriority>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Ribcage</defName>
    <label>ribcage</label>
    <hitPoints>30</hitPoints>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <skinCovered>false</skinCovered>
    <solid>true</solid>
    <bleedRate>0</bleedRate>
    <tags>
      <li>BreathingSourceCage</li>
    </tags>
    <destroyableByDamage>false</destroyableByDamage>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Spine</defName>
    <label>spine</label>
    <hitPoints>25</hitPoints>
    <permanentInjuryChanceFactor>6</permanentInjuryChanceFactor>
    <skinCovered>false</skinCovered>
    <solid>true</solid>
    <bleedRate>0</bleedRate>
    <canSuggestAmputation>false</canSuggestAmputation>
    <tags>
      <li>Spine</li>
    </tags>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Leg</defName>
    <label>leg</label>
    <hitPoints>30</hitPoints>
    <skinCovered>true</skinCovered>
    <canScarify>true</canScarify>
    <tags>
      <li>MovingLimbCore</li>
    </tags>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Foot</defName>
    <label>foot</label>
    <hitPoints>25</hitPoints>
    <frostbiteVulnerability>0.5</frostbiteVulnerability>
    <skinCovered>true</skinCovered>
    <tags>
      <li>MovingLimbSegment</li>
    </tags>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Tongue</defName>
    <label>tongue</label>
    <skinCovered>false</skinCovered>
    <bleedRate>0</bleedRate>
    <beautyRelated>true</beautyRelated>
    <forceAlwaysRemovable>true</forceAlwaysRemovable>
    <removeRecipeLabelOverride>remove</removeRecipeLabelOverride>
    <tags>
      <li>Tongue</li>
    </tags>
  </BodyPartDef>

</Defs>