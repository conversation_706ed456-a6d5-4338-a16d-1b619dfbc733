﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <DamageDef>
    <defName>Deterioration</defName>
    <label>deterioration</label>
    <hasForcefulImpact>false</hasForcefulImpact>
    <makesBlood>false</makesBlood>
    <canInterruptJobs>false</canInterruptJobs>
  </DamageDef>

  <DamageDef>
    <defName>Mining</defName>
    <label>mining</label>
  </DamageDef>

  <DamageDef>
    <defName>Rotting</defName>
    <label>rotting</label>
    <hasForcefulImpact>false</hasForcefulImpact>
    <makesBlood>false</makesBlood>
    <canInterruptJobs>false</canInterruptJobs>
  </DamageDef>

  <DamageDef>
    <defName>Extinguish</defName>
    <label>extinguish</label>
    <workerClass>DamageWorker_Extinguish</workerClass>
    <canInterruptJobs>false</canInterruptJobs>
    <makesBlood>false</makesBlood>
    <defaultDamage>999999</defaultDamage>
    <hediff>CoveredInFirefoam</hediff>
    <explosionCellFleck>BlastExtinguisher</explosionCellFleck>
    <explosionColorEdge>(1, 1, 1, 0.05)</explosionColorEdge>
    <soundExplosion>Explosion_Stun</soundExplosion>
    <harmsHealth>false</harmsHealth>
    <combatLogRules>Damage_Extinguish</combatLogRules>
    <consideredHelpful>true</consideredHelpful>
  </DamageDef>

  <DamageDef Name="Bomb">
    <defName>Bomb</defName>
    <label>bomb</label>
    <workerClass>DamageWorker_AddInjury</workerClass>
    <externalViolence>true</externalViolence>
    <isExplosive>true</isExplosive>
    <deathMessage>{0} has died in an explosion.</deathMessage>
    <hediff>Shredded</hediff>
    <hediffSolid>Crack</hediffSolid>
    <harmAllLayersUntilOutside>true</harmAllLayersUntilOutside>
    <impactSoundType>Blunt</impactSoundType>
    <armorCategory>Sharp</armorCategory>
    <minDamageToFragment>15</minDamageToFragment>
    <defaultDamage>50</defaultDamage>
    <defaultStoppingPower>0.5</defaultStoppingPower>
    <defaultArmorPenetration>0.10</defaultArmorPenetration>
    <buildingDamageFactorImpassable>4</buildingDamageFactorImpassable>
    <buildingDamageFactorPassable>2</buildingDamageFactorPassable>
    <plantDamageFactor>4</plantDamageFactor>
    <corpseDamageFactor>0.5</corpseDamageFactor>
    <explosionAffectOutsidePartsOnly>false</explosionAffectOutsidePartsOnly>
    <explosionHeatEnergyPerCell>5</explosionHeatEnergyPerCell>
    <explosionCellFleck>BlastDry</explosionCellFleck>
    <explosionColorCenter>(1, 0.5, 0.3)</explosionColorCenter>
    <explosionColorEdge>(0.6, 0.5, 0.4)</explosionColorEdge>
    <soundExplosion>Explosion_Bomb</soundExplosion>
    <combatLogRules>Damage_Bomb</combatLogRules>
  </DamageDef>

    <DamageDef ParentName="Bomb">
    <defName>BombSuper</defName>
    <defaultDamage>550</defaultDamage>
    <defaultStoppingPower>2.0</defaultStoppingPower>
    <defaultArmorPenetration>1.30</defaultArmorPenetration>
  </DamageDef>

  <DamageDef>
    <defName>Smoke</defName>
    <label>smoke</label>
    <canInterruptJobs>false</canInterruptJobs>
    <makesBlood>false</makesBlood>
    <defaultDamage>0</defaultDamage>
    <explosionCellFleck>BlastExtinguisher</explosionCellFleck>
    <explosionColorEdge>(1, 1, 1, 0.05)</explosionColorEdge>
    <harmsHealth>false</harmsHealth>
    <soundExplosion>Explosion_Smoke</soundExplosion>
    <combatLogRules>Damage_Smoke</combatLogRules>
  </DamageDef>

  <DamageDef>
    <defName>Thump</defName>
    <label>thump</label>
    <workerClass>DamageWorker_AddInjury</workerClass>
    <externalViolence>true</externalViolence>
    <isExplosive>true</isExplosive>
    <deathMessage>{0} has been shot to death.</deathMessage>
    <hediff>Crush</hediff>
    <hediffSolid>Crack</hediffSolid>
    <harmAllLayersUntilOutside>true</harmAllLayersUntilOutside>
    <impactSoundType>Blunt</impactSoundType>
    <armorCategory>Sharp</armorCategory>
    <minDamageToFragment>15</minDamageToFragment>
    <defaultDamage>5</defaultDamage>
    <defaultArmorPenetration>0</defaultArmorPenetration>
    <buildingDamageFactorImpassable>15</buildingDamageFactorImpassable>
    <buildingDamageFactorPassable>7.5</buildingDamageFactorPassable>
    <plantDamageFactor>4</plantDamageFactor>
    <explosionAffectOutsidePartsOnly>false</explosionAffectOutsidePartsOnly>
    <explosionCellFleck>BlastDry</explosionCellFleck>
    <explosionColorCenter>(0.73, 0.93, 0.96)</explosionColorCenter>
    <explosionColorEdge>(0.71, 0.82, 0.90)</explosionColorEdge>
    <soundExplosion>Explosion_Thump</soundExplosion>
    <combatLogRules>Damage_Bomb</combatLogRules>
  </DamageDef>

  <DamageDef Name="Vaporize">
    <defName>Vaporize</defName>
    <label>vaporize</label>
    <workerClass>DamageWorker_Vaporize</workerClass>
    <externalViolence>true</externalViolence>
    <deathMessage>{0} has been vaporized by extreme heat.</deathMessage>
    <hediff>Burn</hediff>
    <harmAllLayersUntilOutside>true</harmAllLayersUntilOutside>
    <defaultDamage>800</defaultDamage>
    <defaultStoppingPower>1.5</defaultStoppingPower>
    <defaultArmorPenetration>1</defaultArmorPenetration>
    <buildingDamageFactorImpassable>4</buildingDamageFactorImpassable>
    <buildingDamageFactorPassable>2</buildingDamageFactorPassable>
    <plantDamageFactor>2</plantDamageFactor>
    <explosionHeatEnergyPerCell>20</explosionHeatEnergyPerCell>
    <explosionCellFleck>Fleck_Vaporize</explosionCellFleck>
    <armorCategory>Heat</armorCategory>
    <minDamageToFragment>15</minDamageToFragment>
    <makesAnimalsFlee>true</makesAnimalsFlee>
    <explosionColorCenter>(1, 1, 1)</explosionColorCenter>
    <explosionColorEdge>(0.6, 0.5, 0.4)</explosionColorEdge>
    <soundExplosion>Explosion_Vaporize</soundExplosion>
    <expolosionPropagationSpeed>0.3</expolosionPropagationSpeed>
    <hasForcefulImpact>true</hasForcefulImpact>
    <corpseDamageFactor>0.1</corpseDamageFactor>
  </DamageDef>

  <DamageDef ParentName="Flame">
    <defName>AcidBurn</defName>
    <label>acid burn</label>
    <workerClass>DamageWorker_AddInjury</workerClass>
    <armorCategory>Sharp</armorCategory>
    <hediff>AcidBurn</hediff>
    <scaleDamageToBuildingsBasedOnFlammability>false</scaleDamageToBuildingsBasedOnFlammability>
  </DamageDef>
  
  <DamageDef>
    <defName>Decayed</defName>
    <label>decayed organ</label>
    <workerClass>DamageWorker_AddInjury</workerClass>
    <deathMessage>{0} died from metabolic disruptions caused by organ decay.</deathMessage>
    <hediff>Decayed</hediff>
  </DamageDef>
  
</Defs>
