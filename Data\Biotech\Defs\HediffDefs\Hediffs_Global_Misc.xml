<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <HediffDef>
    <defName>PregnantHuman</defName>
    <label>pregnant</label>
    <labelNoun>pregnancy</labelNoun>
    <debugLabelExtra>human</debugLabelExtra>
    <description>A baby growing inside a woman, preparing to be born. In humans, pregnancy lasts for 18 days and is divided into 3 trimesters. As the pregnancy grows it will reduce the mother's mobility. For some mothers, pregnancy may induce nausea or mood swings that can be hard to deal with.</description>
    <hediffClass>Hediff_Pregnant</hediffClass>
    <defaultLabelColor>(0.7, 1.0, 0.7)</defaultLabelColor>
    <isBad>false</isBad>
    <initialSeverity>0.001</initialSeverity>
    <preventsPregnancy>true</preventsPregnancy>
    <pregnant>true</pregnant>
    <comps>
      <li Class="HediffCompProperties_MessageAfterTicks">
        <ticks>600</ticks>
        <letterLabel>{0_labelShort} pregnant</letterLabel>
        <letterText>{0_labelShort} is pregnant!</letterText>
        <letterType>HumanPregnancy</letterType>
      </li>
      <li>
        <compClass>HediffComp_PregnantHuman</compClass>
      </li>
    </comps>
    <stages>
      <li>
        <label>first trimester</label>
        <hungerRateFactorOffset>0.1</hungerRateFactorOffset>
      </li>
      <li>
        <label>second trimester</label>
        <minSeverity>0.333</minSeverity>
        <hungerRateFactorOffset>0.3</hungerRateFactorOffset>
        <capMods>
          <li>
            <capacity>Moving</capacity>
            <offset>-0.1</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.05</offset>
          </li>
        </capMods>
      </li>
      <li>
        <label>third trimester</label>
        <minSeverity>0.666</minSeverity>
        <hungerRateFactorOffset>0.5</hungerRateFactorOffset>
        <capMods>
          <li>
            <capacity>Moving</capacity>
            <offset>-0.5</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.1</offset>
          </li>
        </capMods>
      </li>
    </stages>
  </HediffDef>

  <HediffDef>
    <defName>MorningSickness</defName>
    <label>morning sickness</label>
    <description>This is a common side effect of pregnancy, and despite the name isn't limited to mornings. While this is ongoing, the person suffers from nausea, dizziness, and fuzzy thinking.</description>
    <hediffClass>HediffWithComps</hediffClass>
    <makesSickThought>true</makesSickThought>
    <comps>
      <li Class="HediffCompProperties_Disappears">
        <disappearsAfterTicks>10000~20000</disappearsAfterTicks>
        <showRemainingTime>true</showRemainingTime>
      </li>
    </comps>
    <stages>
      <li>
        <vomitMtbDays>0.075</vomitMtbDays>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <postFactor>0.8</postFactor>
          </li>
          <li>
            <capacity>Moving</capacity>
            <postFactor>0.9</postFactor>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <postFactor>0.9</postFactor>
          </li>
        </capMods>
      </li>
    </stages>
  </HediffDef>

  <HediffDef>
    <defName>PregnancyMood</defName>
    <label>pregnancy mood</label>
    <description>This common side effect of pregnancy represents a rising and falling mood caused by unbalanced hormones.</description>
    <hediffClass>HediffWithComps</hediffClass>
    <comps>
      <li Class="HediffCompProperties_Disappears">
        <disappearsAfterTicks>20000~40000</disappearsAfterTicks>
        <showRemainingTime>true</showRemainingTime>
      </li>
      <li Class="HediffCompProperties_GiveRandomSituationalThought">
        <thoughtDefs>
          <li>PregnancyMood_Collapse</li>
          <li>PregnancyMood_Down</li>
          <li>PregnancyMood_Up</li>
          <li>PregnancyMood_High</li>
        </thoughtDefs>
      </li>
    </comps>
  </HediffDef>

  <HediffDef>
    <defName>PregnancyLabor</defName>
    <label>labor (dilation)</label>
    <description>The condition of being about to give birth. Labor moves through multiple stages with different levels of pain and contractions. Each labor is different, and pain can rise and fall unpredictably over time. Labor ends when the baby is born.</description>
    <hediffClass>Hediff_Labor</hediffClass>
    <blocksSleeping>true</blocksSleeping>
    <preventsPregnancy>true</preventsPregnancy>
    <pregnant>true</pregnant>
    <comps>
      <li Class="HediffCompProperties_Disappears">
        <disappearsAfterTicks>1700~25000</disappearsAfterTicks>
      </li>
      <li Class="HediffCompProperties_RandomizeStageWithInterval">
        <ticksToRandomize>5000~15000</ticksToRandomize>
        <notifyMessage>{PAWN_labelShort}'s labor went from {1} to {2}.</notifyMessage>
      </li>
      <li Class="HediffCompProperties_Effecter">
        <stateEffecter>LaborEarly</stateEffecter>
      </li>
    </comps>
    <stages>
      <li>
        <label>mild</label>
        <minSeverity>0.01</minSeverity>
        <painOffset>0.25</painOffset>
        <capMods>
          <li>
            <capacity>Moving</capacity>
            <postFactor>0.4</postFactor>
          </li>
        </capMods>
      </li>
      <li>
        <label>intense</label>
        <minSeverity>0.3</minSeverity>
        <painOffset>0.5</painOffset>
        <capMods>
          <li>
            <capacity>Moving</capacity>
            <postFactor>0.2</postFactor>
          </li>
        </capMods>
      </li>
      <li>
        <label>debilitating</label>
        <minSeverity>0.6</minSeverity>
        <painOffset>0.85</painOffset>
        <capMods>
          <li>
            <capacity>Moving</capacity>
            <postFactor>0.0</postFactor>
          </li>
        </capMods>
      </li>
    </stages>
  </HediffDef>

  <HediffDef>
    <defName>PregnancyLaborPushing</defName>
    <label>labor (pushing)</label>
    <description>The baby is descending the birth canal and the mother has a strong urge to push. The birth is imminent.</description>
    <hediffClass>Hediff_LaborPushing</hediffClass>
    <blocksSleeping>true</blocksSleeping>
    <preventsCrawling>true</preventsCrawling>
    <preventsPregnancy>true</preventsPregnancy>
    <pregnant>true</pregnant>
    <comps>
      <li Class="HediffCompProperties_Disappears">
        <disappearsAfterTicks>800~5000</disappearsAfterTicks>
      </li>
      <li Class="HediffCompProperties_Effecter">
        <stateEffecter>LaborLate</stateEffecter>
      </li>
    </comps>
    <stages>
      <li>
        <painOffset>0.85</painOffset>
        <capMods>
          <li>
            <capacity>Moving</capacity>
            <postFactor>0.0</postFactor>
          </li>
        </capMods>
      </li>
    </stages>
  </HediffDef>

  <HediffDef>
    <defName>PostpartumExhaustion</defName>
    <label>postpartum exhaustion</label>
    <description>General after-effects of giving birth. This mother needs some time to physically recover.</description>
    <hediffClass>HediffWithComps</hediffClass>
    <initialSeverity>0.001</initialSeverity>
    <comps>
      <li Class="HediffCompProperties_Disappears">
        <showRemainingTime>true</showRemainingTime>
        <disappearsAfterTicks>40000</disappearsAfterTicks>
      </li>
      <li Class="HediffCompProperties_SeverityPerDay">
        <severityPerDay>0.66667</severityPerDay>
      </li>
    </comps>
    <stages>
      <li>
        <capMods>
          <li>
            <capacity>Moving</capacity>
            <postFactor>0.1</postFactor>
          </li>
        </capMods>
      </li>
      <li>
        <minSeverity>0.25</minSeverity>
        <capMods>
          <li>
            <capacity>Moving</capacity>
            <postFactor>0.3</postFactor>
          </li>
        </capMods>
      </li>
    </stages>
  </HediffDef>

  <HediffDef ParentName="DiseaseBase">
    <defName>Stillborn</defName>
    <label>stillborn</label>
    <description>This baby died due to non-specific complications during pregnancy.</description>
    <makesSickThought>true</makesSickThought>
    <lethalSeverity>0.0</lethalSeverity>
    <initialSeverity>0.0</initialSeverity>
  </HediffDef>

  <HediffDef ParentName="DiseaseBase">
    <defName>InfantIllness</defName>
    <label>infant illness</label>
    <descriptionShort>Effects of various birth complications</descriptionShort>
    <description>Effects of various birth complications. If the baby can survive for 10 days, they will be strong enough to recover on their own.\n\nThe illness will wax and wane in strength over time, requiring different levels of tending and medicine to recover.</description>
    <tendable>true</tendable>
    <makesSickThought>true</makesSickThought>
    <lethalSeverity>1.0</lethalSeverity>
    <initialSeverity>0.4</initialSeverity>
    <comps>
      <li Class="HediffCompProperties_RandomizeSeverityPhases">
        <ticksToRandomize>60000~180000</ticksToRandomize> <!-- 1-3 days -->
        <notifyMessage>{PAWN_labelShort}'s infant illness went from {1} to {2}.</notifyMessage>
        <phases>
          <li>
            <labelPrefix>grave</labelPrefix>
            <descriptionExtra>that are life threatening without exceptional medical care and glitterworld medicine.</descriptionExtra>
            <severityPerDay>0.5</severityPerDay>
          </li>
          <li>
            <labelPrefix>bad</labelPrefix>
            <descriptionExtra>that are life threatening without good medical care and standard medicine.</descriptionExtra>
            <severityPerDay>0.3</severityPerDay>
          </li>
          <li>
            <labelPrefix>common</labelPrefix>
            <descriptionExtra>that can be life threatening without basic medicine and tending.</descriptionExtra>
            <severityPerDay>0.1</severityPerDay>
          </li>
          <li>
            <labelPrefix>mild</labelPrefix>
            <descriptionExtra>which make the baby slightly ill but, at this rate, they will recover on their own.</descriptionExtra>
            <severityPerDay>-0.25</severityPerDay>
          </li>
        </phases>
      </li>
      <li Class="HediffCompProperties_TendDuration">
        <baseTendDurationHours>18</baseTendDurationHours>
        <tendOverlapHours>6</tendOverlapHours>
        <severityPerDayTended>-.35</severityPerDayTended>
      </li>
      <li Class="HediffCompProperties_Immunizable"> <!-- to provide some inevitability -->
        <immunityPerDaySick>.1</immunityPerDaySick>
        <severityPerDayImmune>-0.70</severityPerDayImmune>
        <immunityPerDayNotSick>-0.70</immunityPerDayNotSick>
        <hidden>true</hidden>
      </li>
    </comps>
    <stages>
      <li>
        <label>minor</label>
        <painOffset>0.05</painOffset>
        <vomitMtbDays>2.5</vomitMtbDays>
        <statOffsets>
          <RestFallRateFactor>1</RestFallRateFactor>
        </statOffsets>
      </li>
      <li>
        <label>major</label>
        <minSeverity>0.33</minSeverity>
        <painOffset>0.08</painOffset>
        <vomitMtbDays>1.0</vomitMtbDays>
        <statOffsets>
          <RestFallRateFactor>1</RestFallRateFactor>
        </statOffsets>
      </li>
      <li>
        <label>extreme</label>
        <minSeverity>0.78</minSeverity>
        <lifeThreatening>true</lifeThreatening>
        <painOffset>0.12</painOffset>
        <vomitMtbDays>0.75</vomitMtbDays>
        <statOffsets>
          <RestFallRateFactor>1</RestFallRateFactor>
        </statOffsets>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.05</offset>
          </li>
        </capMods>
      </li>
      <li>
        <label>extreme</label>
        <minSeverity>0.87</minSeverity>
        <lifeThreatening>true</lifeThreatening>
        <painOffset>0.85</painOffset>
        <vomitMtbDays>0.5</vomitMtbDays>
        <statOffsets>
          <RestFallRateFactor>1</RestFallRateFactor>
        </statOffsets>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <setMax>0.1</setMax>
          </li>
          <li>
            <capacity>Breathing</capacity>
            <offset>-0.05</offset>
          </li>
        </capMods>
      </li>
    </stages>
  </HediffDef>

  <HediffDef>
    <defName>Lactating</defName>
    <label>lactating</label>
    <description>The condition of being able to breastfeed a baby. The breasts are actively producing and storing milk. The lactating state begins when a mother gives birth, and continues indefinitely as long as she breastfeeds a baby at least once every 10 days.\n\nPeople who are lactating will have a harder time becoming pregnant.</description>
    <hediffClass>HediffWithComps</hediffClass>
    <initialSeverity>1</initialSeverity>
    <everCurableByItem>false</everCurableByItem>
    <comps>
      <li Class="HediffCompProperties_SeverityPerDay">
        <severityPerDay>-0.1</severityPerDay>
      </li>
      <li Class="HediffCompProperties_MessageOnRemoval">
        <messageOnNonZeroSeverity>false</messageOnNonZeroSeverity>
        <message>{0} is no longer lactating due to not breastfeeding for 10 days.</message>
        <messageType>SilentInput</messageType>
        <onlyMessageForColonistsOrPrisoners>true</onlyMessageForColonistsOrPrisoners>
      </li>
      <li Class="HediffCompProperties_Lactating">
        <ticksToFullCharge>15000</ticksToFullCharge> <!-- 6 hrs: 2 to feed a baby and 4 to recover. -->
        <initialCharge>0.125</initialCharge>
        <fullChargeAmount>0.125</fullChargeAmount>
        <labelInBrackets>milk fullness {CHARGEFACTOR_percentage}</labelInBrackets>
        <minChargeToActivate>0.031</minChargeToActivate> <!-- some hysteresis so when breastfeeding starts we're sure it can continue for a while. -->
      </li>
      <li Class="HediffCompProperties_RemoveIfOtherHediff">
        <hediffs>
          <li>Malnutrition</li>
        </hediffs>
        <stages>2~</stages>
        <mtbHours>4</mtbHours>
        <message>{0} is no longer lactating due to malnutrition.</message>
        <messageType>SilentInput</messageType>
        <onlyMessageForColonistsOrPrisoners>true</onlyMessageForColonistsOrPrisoners>
      </li>
    </comps>
    <stages>
      <li>
        <fertilityFactor>0.05</fertilityFactor>
      </li>
    </stages>
  </HediffDef>

  <HediffDef>
    <defName>OvumExtracted</defName>
    <label>ovum extracted</label>
    <description>This woman recently underwent an ovum extraction procedure. No more ova can be extracted until some time has passed.</description>
    <hediffClass>HediffWithComps</hediffClass>
    <initialSeverity>1</initialSeverity>
    <everCurableByItem>false</everCurableByItem>
    <comps>
      <li Class="HediffCompProperties_Disappears">
        <showRemainingTime>true</showRemainingTime>
        <disappearsAfterTicks>600000</disappearsAfterTicks><!-- 10 days -->
      </li>
    </comps>
    <tags>
      <li>OvumExtracted</li>
    </tags>
  </HediffDef>

  <HediffDef>
    <defName>PollutionStimulus</defName>
    <label>pollution stimulus</label>
    <description>This creature has been energized by exposure to pollution.\n\nThe pollution-stimulus response was originally engineered to help giant insects in their role as anti-mechanoid bioweapons. However, the gene has since been used to help people function in polluted areas as well.</description>
    <descriptionShort>This creature has been energized by exposure to pollution.</descriptionShort>
    <hediffClass>HediffWithComps</hediffClass>
    <defaultLabelColor>(0.7, 1.0, 0.7)</defaultLabelColor>
    <isBad>false</isBad>
    <makesSickThought>false</makesSickThought>
    <scenarioCanAdd>false</scenarioCanAdd>
    <maxSeverity>1</maxSeverity>
    <minSeverity>0</minSeverity>
    <initialSeverity>0.01</initialSeverity>
    <comps>
      <li Class="HediffCompProperties_Pollution">
        <pollutedSeverity>0.0333</pollutedSeverity>
        <unpollutedSeverity>-0.003</unpollutedSeverity>
        <interval>60</interval>
      </li>
    </comps>
    <stages>
      <li>
        <label>minor</label>
        <statFactors>
          <MoveSpeed>1.1</MoveSpeed>
        </statFactors>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>0.05</offset>
          </li>
        </capMods>
      </li>
      <li>
        <label>moderate</label>
        <minSeverity>0.20</minSeverity>
        <statFactors>
          <MoveSpeed>1.15</MoveSpeed>
        </statFactors>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>0.05</offset>
          </li>
        </capMods>
      </li>
      <li>
        <label>maximum</label>
        <minSeverity>0.50</minSeverity>
        <statFactors>
          <MoveSpeed>1.2</MoveSpeed>
        </statFactors>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>0.05</offset>
          </li>
        </capMods>
      </li>
    </stages>
  </HediffDef>


</Defs>