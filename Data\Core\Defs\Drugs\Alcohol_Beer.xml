﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingDef ParentName="DrugBase">
    <defName>Beer</defName>
    <label>beer</label>
    <description>The first beverage besides water ever consumed by mankind. Beer can taste good, but its main effect is intoxication. Excessive consumption can lead to alcohol blackouts and, over time, addiction.</description>
    <descriptionHyperlinks>
      <HediffDef>AlcoholHigh</HediffDef>
      <HediffDef>AlcoholTolerance</HediffDef>
      <HediffDef>Hangover</HediffDef>
      <HediffDef>AlcoholAddiction</HediffDef>
      <HediffDef>Cirrhosis</HediffDef>
      <HediffDef>ChemicalDamageModerate</HediffDef>
    </descriptionHyperlinks>
    <possessionCount>6</possessionCount>
    <graphicData>
      <texPath>Things/Item/Drug/Beer</texPath>
      <graphicClass>Graphic_StackCount</graphicClass>
    </graphicData>
    <equippedAngleOffset>-150</equippedAngleOffset>
    <rotatable>false</rotatable>
    <stackLimit>25</stackLimit>
    <statBases>
      <DeteriorationRate>0.5</DeteriorationRate>
      <MarketValue>12</MarketValue>
      <Mass>0.3</Mass>
      <Flammability>0.5</Flammability>
      <Nutrition>0.08</Nutrition>
    </statBases>
    <ingestible>
      <foodType>Fluid, Processed, Liquor</foodType>
      <joyKind>Chemical</joyKind>
      <joy>0.17</joy>
      <nurseable>true</nurseable>
      <drugCategory>Social</drugCategory>
      <ingestSound>Ingest_Beer</ingestSound>
      <ingestHoldOffsetStanding>
        <northDefault>
          <offset>(0.18,0,0)</offset>
        </northDefault>
      </ingestHoldOffsetStanding>
      <ingestCommandString>Drink {0}</ingestCommandString>
      <ingestReportString>Drinking {0}.</ingestReportString>
      <chairSearchRadius>25</chairSearchRadius>
      <canAutoSelectAsFoodForCaravan>false</canAutoSelectAsFoodForCaravan>
      <tableDesired>false</tableDesired>
      <outcomeDoers>
        <li Class="IngestionOutcomeDoer_GiveHediff">
          <hediffDef>AlcoholHigh</hediffDef>
          <severity>0.15</severity>
          <toleranceChemical>Alcohol</toleranceChemical>
        </li>
        <li Class="IngestionOutcomeDoer_GiveHediff">
          <hediffDef>AlcoholTolerance</hediffDef>
          <toleranceChemical>Alcohol</toleranceChemical>
          <severity>0.016</severity>
          <divideByBodySize>true</divideByBodySize>
          <multiplyByGeneToleranceFactors>true</multiplyByGeneToleranceFactors>
        </li>
      </outcomeDoers>
    </ingestible>
    <equipmentType>Primary</equipmentType>
    <techLevel>Neolithic</techLevel>
    <comps>
      <li Class="CompProperties_Drug">
        <chemical>Alcohol</chemical>
        <addictiveness>0.010</addictiveness>
        <minToleranceToAddict>0.25</minToleranceToAddict>
        <existingAddictionSeverityOffset>0.20</existingAddictionSeverityOffset>
        <needLevelOffset>0.9</needLevelOffset>
        <listOrder>10</listOrder>
      </li>
      <li>
        <compClass>CompEquippable</compClass>
      </li>
    </comps>
    <weaponClasses>
      <li>Melee</li>
      <li>MeleeBlunt</li>
    </weaponClasses>
    <tools>
      <li>
        <label>bottle</label>
        <capacities>
          <li>Blunt</li>
        </capacities>
        <power>9</power>
        <cooldownTime>2</cooldownTime>
      </li>
      <li>
        <label>neck</label>
        <capacities>
          <li>Poke</li>
        </capacities>
        <power>9</power>
        <cooldownTime>2</cooldownTime>
      </li>
    </tools>
    <allowedArchonexusCount>50</allowedArchonexusCount>
  </ThingDef>

  <RecipeDef>
    <defName>Make_Wort</defName>
    <label>make wort</label>
    <description>Make wort from hops.</description>
    <jobString>Making wort.</jobString>
    <workSpeedStat>DrugCookingSpeed</workSpeedStat>
    <workSkill>Cooking</workSkill>
    <effectWorking>Cook</effectWorking>
    <soundWorking>Recipe_Brewing</soundWorking>
    <allowMixingIngredients>true</allowMixingIngredients>
    <workAmount>1000</workAmount>
    <targetCountAdjustment>1</targetCountAdjustment>
    <ingredients>
      <li>
        <filter>
          <thingDefs>
            <li>RawHops</li>
          </thingDefs>
        </filter>
        <count>25</count>
      </li>
    </ingredients>
    <fixedIngredientFilter>
      <thingDefs>
        <li>RawHops</li>
      </thingDefs>
    </fixedIngredientFilter>
    <products>
      <Wort>5</Wort>
    </products>
  </RecipeDef>

  <ThingDef ParentName="ResourceBase">
    <defName>Wort</defName>
    <label>wort</label>
    <description>Un-fermented beer. This substance needs to ferment in a fermenting barrel before it becomes drinkable beer.</description>
    <graphicData>
      <texPath>Things/Item/Resource/Wort</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <healthAffectsPrice>false</healthAffectsPrice>
    <rotatable>false</rotatable>
    <stackLimit>25</stackLimit>
    <thingCategories>
      <li>Manufactured</li>
    </thingCategories>
    <statBases>
      <DeteriorationRate>1.0</DeteriorationRate>
      <MarketValue>2.5</MarketValue>
      <Mass>0.8</Mass>
      <Flammability>0.2</Flammability>
      <MaxHitPoints>60</MaxHitPoints>
    </statBases>
    <tickerType>Rare</tickerType>
    <comps>
      <li Class="CompProperties_Rottable">
        <daysToRotStart>5</daysToRotStart>
        <rotDestroys>true</rotDestroys>
      </li>
    </comps>
  </ThingDef>
  
  <HediffDef>
    <defName>AlcoholHigh</defName>
    <label>alcohol</label>
    <labelNoun>drunkenness</labelNoun>
    <description>Alcohol in the bloodstream. It makes people happy, but reduces capacities.</description>
    <hediffClass>Hediff_Alcohol</hediffClass>
    <defaultLabelColor>(1,0,0.5)</defaultLabelColor>
    <scenarioCanAdd>true</scenarioCanAdd>
    <isBad>false</isBad>
    <comps>
      <li Class="HediffCompProperties_Effecter">
        <stateEffecter>Drunk</stateEffecter>
        <severityIndices>3~5</severityIndices>
      </li>
      <li Class="HediffCompProperties_SeverityPerDay">
        <severityPerDay>-0.75</severityPerDay>
        <showHoursToRecover>true</showHoursToRecover>
      </li>
    </comps>
      <stages>
        <li>
          <label>warm</label>
          <painFactor>0.9</painFactor>
          <socialFightChanceFactor>1.5</socialFightChanceFactor>
            <capMods>
              <li>
                <capacity>Manipulation</capacity>
                <offset>-0.02</offset>
              </li>
            </capMods>
        </li>
        <li>
          <minSeverity>0.25</minSeverity>
          <label>tipsy</label>
          <painFactor>0.8</painFactor>
          <socialFightChanceFactor>2.5</socialFightChanceFactor>
            <capMods>
              <li>
                <capacity>Consciousness</capacity>
                <postFactor>0.9</postFactor>
              </li>
              <li>
                <capacity>Moving</capacity>
                <offset>-0.05</offset>
              </li>
            </capMods>
        </li>
        <li>
          <minSeverity>0.4</minSeverity>
          <label>drunk</label>
          <painFactor>0.5</painFactor>
          <socialFightChanceFactor>4.0</socialFightChanceFactor>
          <vomitMtbDays>0.75</vomitMtbDays>
          <tale>Drunk</tale>
            <capMods>
              <li>
                <capacity>Consciousness</capacity>
                <postFactor>0.65</postFactor>
              </li>
              <li>
                <capacity>Moving</capacity>
                <offset>-0.10</offset>
              </li>
            </capMods>
        </li>
        <li>
          <minSeverity>0.7</minSeverity>
          <label>hammered</label>
          <painFactor>0.3</painFactor>
          <socialFightChanceFactor>5.0</socialFightChanceFactor>
          <vomitMtbDays>0.2</vomitMtbDays>
          <painOffset>0.05</painOffset>
            <capMods>
              <li>
                <capacity>Consciousness</capacity>
                <postFactor>0.5</postFactor>
              </li>
              <li>
                <capacity>Moving</capacity>
                <offset>-0.10</offset>
              </li>
            </capMods>
        </li>
        <li>
          <minSeverity>0.9</minSeverity>
          <label>blackout</label>
          <painFactor>0.1</painFactor>
          <capMods>
            <li>
              <capacity>Consciousness</capacity>
              <setMax>0.1</setMax>
            </li>
          </capMods>
          <hediffGivers>
            <li Class="HediffGiver_Random">
              <hediff>ChemicalDamageModerate</hediff>
              <mtbDays>10</mtbDays>
              <partsToAffect>
                <li>Brain</li>
              </partsToAffect>
            </li>
          </hediffGivers>
        </li>
      </stages>
  </HediffDef>

  <HediffDef>
    <defName>Hangover</defName>
    <label>hangover</label>
    <labelNoun>a hangover</labelNoun>
    <description>An unpleasant delayed after-effect of alcohol intoxication.</description>
    <hediffClass>Hediff_Hangover</hediffClass>
    <initialSeverity>1</initialSeverity>
    <scenarioCanAdd>true</scenarioCanAdd>
    <comps>
      <li Class="HediffCompProperties_SeverityPerDay">
        <severityPerDay>-1</severityPerDay>
      </li>
    </comps>
    <stages>
      <li>
        <minSeverity>0</minSeverity>
        <label>slight</label>
          <capMods>
            <li>
              <capacity>Consciousness</capacity>
              <offset>-0.03</offset>
            </li>
          </capMods>
      </li>
      <li>
        <minSeverity>0.15</minSeverity>
        <label>strong</label>
          <capMods>
            <li>
              <capacity>Consciousness</capacity>
              <offset>-0.08</offset>
            </li>
          </capMods>
      </li>
      <!-- starts low because it isn't visible until alcohol hediff is gone-->
      <li>
        <minSeverity>0.40</minSeverity>
        <label>pounding</label>
        <vomitMtbDays>0.4</vomitMtbDays>
          <capMods>
            <li>
              <capacity>Consciousness</capacity>
              <offset>-0.18</offset>
            </li>
          </capMods>
      </li>
    </stages>
  </HediffDef>
  
  <ThoughtDef>
    <defName>Inebriated</defName>
    <workerClass>ThoughtWorker_Hediff</workerClass>
    <hediff>AlcoholHigh</hediff>
    <validWhileDespawned>true</validWhileDespawned>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
    <stages>
      <li>
        <label>alcohol warmth</label>
        <description>I just feel a bit more relaxed after that drink. That's good.</description>
        <baseMoodEffect>10</baseMoodEffect>
      </li>
      <li>
        <label>quite inebriated</label>
        <description>I'm a bit wobbly! And this is quite enjoyable.</description>
        <baseMoodEffect>14</baseMoodEffect>
      </li>     
      <li>
        <label>drunk</label>
        <description>I feel so uninhibited and unafraid! This is a great time!</description>
        <baseMoodEffect>20</baseMoodEffect>
      </li>
      <li>
        <label>hammered</label>
        <description>Wooo! What's going on?</description>
        <baseMoodEffect>26</baseMoodEffect>
      </li>   
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>Hungover</defName>
    <workerClass>ThoughtWorker_Hediff</workerClass>
    <hediff>Hangover</hediff>
    <validWhileDespawned>true</validWhileDespawned>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
    <stages>
      <li>
        <label>mild hangover</label>
        <description>The headache is almost gone.</description>
        <baseMoodEffect>-3</baseMoodEffect>
      </li>
      <li>
        <label>serious hangover</label>
        <description>My head hurts from all that liquor.</description>
        <baseMoodEffect>-6</baseMoodEffect>
      </li>
      <li>
        <label>pounding hangover</label>
        <description>It feels like a gaggle of geese are pecking at my skull.</description>
        <baseMoodEffect>-12</baseMoodEffect>
      </li>
      <li>
        <visible>false</visible>
      </li>
    </stages>
  </ThoughtDef>
  
  <!-- Alcohol addiction -->
  
  <ChemicalDef>
    <defName>Alcohol</defName>
    <label>alcohol</label>
    <addictionHediff>AlcoholAddiction</addictionHediff>
    <toleranceHediff>AlcoholTolerance</toleranceHediff>
    <onGeneratedAddictedToleranceChance>0.8</onGeneratedAddictedToleranceChance>
    <onGeneratedAddictedEvents>
      <li>
        <hediff>Cirrhosis</hediff>
        <chance>0.15</chance>
        <partsToAffect>
          <li>Liver</li>
        </partsToAffect>
      </li>
    </onGeneratedAddictedEvents>
    <geneToleranceBuildupFactorResist>0.5</geneToleranceBuildupFactorResist>
    <geneToleranceBuildupFactorImmune>0</geneToleranceBuildupFactorImmune>
  </ChemicalDef>
  
  <NeedDef ParentName="DrugAddictionNeedBase">
    <defName>Chemical_Alcohol</defName>
    <needClass>Need_Chemical</needClass>
    <label>alcohol</label>
    <description>Because of an alcohol addiction, this person needs to regularly consume alcohol to avoid withdrawal symptoms.</description>
    <listPriority>35</listPriority>
  </NeedDef>

  <HediffDef ParentName="DrugToleranceBase">
    <defName>AlcoholTolerance</defName>
    <label>alcohol tolerance</label>
    <description>A built-up tolerance to alcohol. The more severe this tolerance is, the more alcohol it takes to get the same effect.</description>
    <comps>
      <li Class="HediffCompProperties_SeverityPerDay">
        <severityPerDay>-0.016</severityPerDay>
      </li>
      <li Class="HediffCompProperties_DrugEffectFactor">
        <chemical>Alcohol</chemical>
      </li>
    </comps>
    <hediffGivers>
      <li Class="HediffGiver_RandomDrugEffect">
        <hediff>Cirrhosis</hediff>
        <severityToMtbDaysCurve>
          <points>
            <li>(0.45, 99999)</li>
            <li>(0.5, 60)</li>
            <li>(1, 45)</li>
          </points>
        </severityToMtbDaysCurve>
        <partsToAffect>
          <li>Liver</li>
        </partsToAffect>
      </li>
      <li Class="HediffGiver_RandomDrugEffect">
        <hediff>Carcinoma</hediff>
        <severityToMtbDaysCurve>
          <points>
            <li>(0.45, 99999)</li>
            <li>(0.5, 180)</li>
            <li>(1, 150)</li>
          </points>
        </severityToMtbDaysCurve>
        <partsToAffect>
          <li>Liver</li>
        </partsToAffect>
      </li>
    </hediffGivers>
  </HediffDef>

  <HediffDef ParentName="AddictionBase">
    <defName>AlcoholAddiction</defName>
    <label>alcohol addiction</label>
    <description>A chemical addiction to alcohol. Long-term presence of alcohol has caused neurological adaptations at the cellular level, so the brain can no longer function properly without the drug.
\nWithout regular doses of alcohol, withdrawal symptoms will begin. However, extended abstinence will force the brain to adapt back to its normal state, resolving the addiction.</description>
    <hediffClass>Hediff_Addiction</hediffClass>
    <causesNeed>Chemical_Alcohol</causesNeed>
    <comps>
      <li Class="HediffCompProperties_SeverityPerDay">
        <severityPerDay>-0.0333</severityPerDay>
        <showDaysToRecover>true</showDaysToRecover>
      </li>
    </comps>
    <stages>
      <li>
      </li>
      <li>
        <label>withdrawal</label>
        <socialFightChanceFactor>2.0</socialFightChanceFactor>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.20</offset>
          </li>
          <li>
            <capacity>Moving</capacity>
            <offset>-0.30</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <postFactor>0.5</postFactor>
          </li>
        </capMods>
        <mentalStateGivers>
          <li>
            <mentalState>Binging_DrugMajor</mentalState>
            <mtbDays>40</mtbDays>
          </li>
        </mentalStateGivers>
      </li>
    </stages>
  </HediffDef>

  <ThoughtDef>
    <defName>AlcoholWithdrawal</defName>
    <workerClass>ThoughtWorker_Hediff</workerClass>
    <hediff>AlcoholAddiction</hediff>
    <validWhileDespawned>true</validWhileDespawned>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
    <stages>
      <li>
        <visible>false</visible>
      </li>
      <li>
        <label>alcohol withdrawal</label>
        <description>Feeling shaky. Everything pisses me off. I keep thinking of drinking.</description>
        <baseMoodEffect>-35</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

</Defs>