﻿<?xml version="1.0" encoding="utf-8"?>

<Defs>

  <AnimationDef>
    <defName>DeathRefusalTwitches</defName>
    <durationTicks>180</durationTicks>
    <startOnRandomTick>false</startOnRandomTick>
    <playWhenDowned>true</playWhenDowned>
    <animationParts>
      <li>
        <key>Root</key>
        <value>
          <workerClass>AnimationWorker_Keyframes</workerClass>
          <keyframes>
            <li>
              <tick>0</tick>
              <angle>5</angle>
            </li>
            <li>
              <tick>6</tick>
              <angle>-5</angle>
            </li>
            <li>
              <tick>12</tick>
              <angle>4</angle>
            </li>
            <li>
              <tick>18</tick>
              <angle>-1</angle>
            </li>
            <li>
              <tick>24</tick>
              <angle>-6</angle>
            </li>
            <li>
              <tick>30</tick>
              <angle>6</angle>
            </li>
            <li>
              <tick>35</tick>
              <angle>4</angle>
            </li>
            <li>
              <tick>40</tick>
              <angle>-4</angle>
            </li>
            <li>
              <tick>45</tick>
              <angle>3</angle>
            </li>
            <li>
              <tick>50</tick>
              <angle>-3</angle>
            </li>
            <li>
              <tick>54</tick>
              <angle>3</angle>
            </li>
            <li>
              <tick>58</tick>
              <angle>-3</angle>
            </li>
            <li>
              <tick>65</tick>
              <angle>4</angle>
            </li>
            <li>
              <tick>69</tick>
              <angle>-1</angle>
            </li>
            <li>
              <tick>77</tick>
              <angle>3</angle>
            </li>
            <li>
              <tick>80</tick>
              <angle>-4</angle>
            </li>
            <li>
              <tick>85</tick>
              <angle>2</angle>
            </li>
            <li>
              <tick>90</tick>
              <angle>-1</angle>
            </li>
            <li>
              <tick>92</tick>
              <angle>1</angle>
            </li>
            <li>
              <tick>94</tick>
              <angle>-2</angle>
            </li>
            <li>
              <tick>96</tick>
              <angle>2</angle>
            </li>

            <li>
              <tick>105</tick>
              <angle>1</angle>
            </li>
            <li>
              <tick>107</tick>
              <angle>-1</angle>
            </li>
            <li>
              <tick>109</tick>
              <angle>2</angle>
            </li>

            <li>
              <tick>115</tick>
              <angle>-2</angle>
            </li>
            <li>
              <tick>118</tick>
              <angle>3</angle>
            </li>
            <li>
              <tick>121</tick>
              <angle>-1</angle>
            </li>
            <li>
              <tick>124</tick>
              <angle>1</angle>
            </li>
            <li>
              <tick>127</tick>
              <angle>-1</angle>
            </li>
            <li>
              <tick>129</tick>
              <angle>2</angle>
            </li>
            <li>
              <tick>131</tick>
              <angle>-1</angle>
            </li>
            <li>
              <tick>133</tick>
              <angle>1</angle>
            </li>
            <li>
              <tick>135</tick>
              <angle>-1</angle>
            </li>
            <li>
              <tick>137</tick>
              <angle>1</angle>
            </li>
            <li>
              <tick>139</tick>
              <angle>-1</angle>
            </li>
            <li>
              <tick>141</tick>
              <angle>4</angle>
            </li>
            <li>
              <tick>160</tick>
              <angle>2</angle>
            </li>
            <li>
              <tick>162</tick>
              <angle>-1</angle>
            </li>
            <li>
              <tick>164</tick>
              <angle>1</angle>
            </li>
            <li>
              <tick>166</tick>
              <angle>-1</angle>
            </li>
            <li>
              <tick>168</tick>
              <angle>3</angle>
            </li>
            <li>
              <tick>170</tick>
              <angle>1</angle>
            </li>
            <li>
              <tick>172</tick>
              <angle>-1</angle>
            </li>
            <li>
              <tick>173</tick>
              <angle>2</angle>
            </li>
            <li>
              <tick>174</tick>
              <angle>-1</angle>
            </li>
            <li>
              <tick>175</tick>
              <angle>2</angle>
            </li>
            <li>
              <tick>176</tick>
              <angle>-1</angle>
            </li>
            <li>
              <tick>177</tick>
              <angle>1</angle>
            </li>
            <li>
              <tick>178</tick>
              <angle>-2</angle>
            </li>
            <li>
              <tick>180</tick>
              <angle>1</angle>
            </li>
          </keyframes>
        </value>
      </li>
    </animationParts>
  </AnimationDef>

</Defs>