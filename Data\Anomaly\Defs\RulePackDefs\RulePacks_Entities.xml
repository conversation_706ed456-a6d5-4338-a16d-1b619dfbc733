<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <!-- Revenant heard message verbs -->

  <RulePackDef>
    <defName>RevenantNoises</defName>
    <rulePack>
      <rulesStrings>
        <li>verb->breathing</li>
        <li>verb->shuddering</li>
        <li>verb->shivering</li>
        <li>verb->creaking</li>
        <li>verb->rasping</li>
        <li>verb->scraping</li>
        <li>verb->hissing</li>
        <li>verb->groaning</li>
        <li>verb->grumbling</li>
        <li>verb->chattering</li>
        <li>verb->clicking</li>
        <li>verb->wheezing</li>
        <li>verb->rattling</li>
        <li>verb->twitching</li>
        <li>verb->gnashing</li>
        <li>verb->coughing</li>
      </rulesStrings>
    </rulePack>
  </RulePackDef>
  
  <RulePackDef>
    <defName>Event_UnnaturalCorpseAttack</defName>
    <rulePack>
      <rulesStrings>
        <li>r_logentry->[INITIATOR_definite] psychically incinerated [SUBJECT_definite]'s brain.</li>
      </rulesStrings>
    </rulePack>
  </RulePackDef>
  
  <RulePackDef>
    <defName>Event_DevourerConsumeLeap</defName>
    <rulePack>
      <rulesStrings>
        <li>r_logentry->[INITIATOR_definite] leaped onto [SUBJECT_definite] and began digesting [SUBJECT_objective].</li>
      </rulesStrings>
    </rulePack>
  </RulePackDef>
  
  <RulePackDef>
    <defName>Event_DevourerDigestionAborted</defName>
    <rulePack>
      <rulesStrings>
        <li>r_logentry->[INITIATOR_definite] vomited [SUBJECT_definite] up.</li>
      </rulesStrings>
    </rulePack>
  </RulePackDef>
  
  <RulePackDef>
    <defName>Event_DevourerDigestionCompleted</defName>
    <rulePack>
      <rulesStrings>
        <li>r_logentry->[INITIATOR_definite] digested [SUBJECT_definite].</li>
      </rulesStrings>
    </rulePack>
  </RulePackDef>

  <RulePackDef>
    <defName>Event_MetalhorrorEmerged</defName>
    <rulePack>
      <rulesStrings>
        <li>r_logentry->[INITIATOR_definite] emerged from [SUBJECT_definite].</li>
      </rulesStrings>
    </rulePack>
  </RulePackDef>

  <RulePackDef>
    <defName>Event_Hypnotized</defName>
    <rulePack>
      <rulesStrings>
        <li>r_logentry->[INITIATOR_definite] put [SUBJECT_definite] into a trance.</li>
      </rulesStrings>
    </rulePack>
  </RulePackDef>

</Defs>