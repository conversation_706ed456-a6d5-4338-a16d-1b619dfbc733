﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingDef Name="DrugBase" Abstract="True">
    <thingClass>ThingWithComps</thingClass>
    <category>Item</category>
    <drawerType>MapMeshOnly</drawerType>
    <useHitPoints>true</useHitPoints>
    <healthAffectsPrice>false</healthAffectsPrice>
    <selectable>true</selectable>
    <statBases>
      <MaxHitPoints>50</MaxHitPoints>
      <Flammability>1.0</Flammability>
      <DeteriorationRate>2</DeteriorationRate>
      <Beauty>-4</Beauty>
    </statBases>
    <altitudeLayer>Item</altitudeLayer>
    <stackLimit>150</stackLimit>
    <thingCategories>
      <li>Drugs</li>
    </thingCategories>
    <tradeTags>
      <li>Drugs</li>
    </tradeTags>
    <alwaysHaulable>true</alwaysHaulable>
    <comps>
      <li Class="CompProperties_Forbiddable" />
    </comps>
    <pathCost>14</pathCost>
    <burnableByRecipe>true</burnableByRecipe>
    <resourceReadoutPriority>Last</resourceReadoutPriority>
    <drawGUIOverlay>true</drawGUIOverlay>
    <allowedArchonexusCount>-1</allowedArchonexusCount>
    <ingestible>
      <preferability>NeverForNutrition</preferability>
      <maxNumToIngestAtOnce>1</maxNumToIngestAtOnce>
      <defaultNumToIngestAtOnce>1</defaultNumToIngestAtOnce>
      <chairSearchRadius>8</chairSearchRadius>
    </ingestible>
    <storedConceptLearnOpportunity>DrugPolicies</storedConceptLearnOpportunity>
    <orderedTakeGroup>Drug</orderedTakeGroup>
  </ThingDef>
  
  <ThingDef Name="MakeableDrugBase" ParentName="DrugBase" Abstract="True">
    <recipeMaker>
      <workSpeedStat>DrugSynthesisSpeed</workSpeedStat>
      <workSkill>Intellectual</workSkill>
      <effectWorking>Cook</effectWorking>
      <soundWorking>Recipe_CookMeal</soundWorking>
      <bulkRecipeCount>4</bulkRecipeCount>
    </recipeMaker>
  </ThingDef>

  <ThingDef Name="MakeableDrugPillBase" ParentName="MakeableDrugBase" Abstract="True">
    <ingestible>
      <foodType>Processed</foodType>
      <baseIngestTicks>100</baseIngestTicks>
      <chairSearchRadius>0</chairSearchRadius>
      <ingestSound>Ingest_Pill</ingestSound>
      <ingestHoldUsesTable>false</ingestHoldUsesTable>
      <ingestCommandString>Take {0}</ingestCommandString>
      <ingestReportString>Taking {0}.</ingestReportString>
    </ingestible>
    <socialPropernessMatters>true</socialPropernessMatters>
  </ThingDef>

  <ThingDef Name="DrugPillBase" ParentName="DrugBase" Abstract="True">
    <ingestible>
      <foodType>Processed</foodType>
      <baseIngestTicks>100</baseIngestTicks>
      <chairSearchRadius>0</chairSearchRadius>
      <ingestSound>Ingest_Pill</ingestSound>
      <ingestHoldUsesTable>false</ingestHoldUsesTable>
      <ingestCommandString>Take {0}</ingestCommandString>
      <ingestReportString>Taking {0}.</ingestReportString>
    </ingestible>
    <socialPropernessMatters>true</socialPropernessMatters>
  </ThingDef>
  
  <HediffDef Name="AddictionBase" Abstract="True">
    <defaultLabelColor>(1,0,0.5)</defaultLabelColor>
    <initialSeverity>0.5</initialSeverity>
    <maxSeverity>1.0</maxSeverity>
    <scenarioCanAdd>true</scenarioCanAdd>
    <removeOnRedressChanceByDaysCurve>
      <points>
        <li>(0, 0)</li>
        <li>(10, 0)</li>
        <li>(60, 0.25)</li>
      </points>
    </removeOnRedressChanceByDaysCurve>
  </HediffDef>

  <HediffDef Name="DrugToleranceBase" Abstract="True">
    <hediffClass>HediffWithComps</hediffClass>
    <defaultLabelColor>(1,0,0.5)</defaultLabelColor>
    <maxSeverity>1.0</maxSeverity>
    <scenarioCanAdd>true</scenarioCanAdd>
    <isBad>false</isBad>
    <stages>
      <li>
        <minSeverity>0</minSeverity>
        <label>small</label>
      </li>
      <li>
        <minSeverity>0.50</minSeverity>
        <label>large</label>
      </li>
      <li>
        <minSeverity>0.80</minSeverity>
        <label>massive</label>
      </li>
    </stages>
  </HediffDef> 
  
  <NeedDef Name="DrugAddictionNeedBase" Abstract="True">
    <onlyIfCausedByHediff>true</onlyIfCausedByHediff>
    <tutorHighlightTag>NeedAddiction</tutorHighlightTag>
    <showForCaravanMembers>true</showForCaravanMembers>
  </NeedDef>
  
</Defs>
