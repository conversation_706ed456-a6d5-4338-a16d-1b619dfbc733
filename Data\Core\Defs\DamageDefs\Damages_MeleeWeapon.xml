<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <DamageDef Name="CutBase">
    <defName>Cut</defName>
    <label>cut</label>
    <workerClass>DamageWorker_Cut</workerClass>
    <externalViolence>true</externalViolence>
    <deathMessage>{0} has been cut to death.</deathMessage>
    <hediff>Cut</hediff>
    <hediffSkin>Cut</hediffSkin>
    <hediffSolid>Crack</hediffSolid>
    <harmAllLayersUntilOutside>true</harmAllLayersUntilOutside>
    <impactSoundType>Slice</impactSoundType>
    <armorCategory>Sharp</armorCategory>
    <overkillPctToDestroyPart>0~0.1</overkillPctToDestroyPart>
    <cutExtraTargetsCurve>
      <points>
        <li>0, 0</li>
        <li>0.6, 1</li>
        <li>0.9, 2</li>
        <li>1, 3</li>
      </points>
    </cutExtraTargetsCurve>
    <cutCleaveBonus>1.4</cutCleaveBonus>
  </DamageDef>

  <DamageDef>
    <defName>Crush</defName>
    <label>crush</label>
    <workerClass>DamageWorker_AddInjury</workerClass>
    <externalViolence>true</externalViolence>
    <deathMessage>{0} has been crushed to death.</deathMessage>
    <hediff>Crush</hediff>
    <hediffSkin>Cut</hediffSkin>
    <hediffSolid>Crack</hediffSolid>
    <harmAllLayersUntilOutside>true</harmAllLayersUntilOutside>
    <impactSoundType>Blunt</impactSoundType>
    <armorCategory>Blunt</armorCategory>
    <overkillPctToDestroyPart>0.4~1.0</overkillPctToDestroyPart>
  </DamageDef>

  <DamageDef Name="BluntBase">
    <defName>Blunt</defName>
    <label>blunt</label>
    <workerClass>DamageWorker_Blunt</workerClass>
    <externalViolence>true</externalViolence>
    <deathMessage>{0} has been beaten to death.</deathMessage>
    <hediff>Crush</hediff>
    <hediffSkin>Bruise</hediffSkin>
    <hediffSolid>Crack</hediffSolid>
    <harmAllLayersUntilOutside>true</harmAllLayersUntilOutside>
    <impactSoundType>Blunt</impactSoundType>
    <armorCategory>Blunt</armorCategory>
    <overkillPctToDestroyPart>0.4~1.0</overkillPctToDestroyPart>
    <buildingDamageFactor>1.5</buildingDamageFactor>
    <bluntStunDuration>2.0</bluntStunDuration>
    <bluntInnerHitChance>0.4</bluntInnerHitChance>
    <bluntInnerHitDamageFractionToConvert>0.1~0.2</bluntInnerHitDamageFractionToConvert>
    <bluntInnerHitDamageFractionToAdd>0.2~0.35</bluntInnerHitDamageFractionToAdd>
    <bluntStunChancePerDamagePctOfCorePartToHeadCurve>
      <points>
        <li>(0.04, 0.20)</li>
        <li>(0.5, 1)</li>
      </points>
    </bluntStunChancePerDamagePctOfCorePartToHeadCurve>
    <bluntStunChancePerDamagePctOfCorePartToBodyCurve>
      <points>
        <li>(0.4, 0)</li>
        <li>(0.9, 0.15)</li>
      </points>
    </bluntStunChancePerDamagePctOfCorePartToBodyCurve>
  </DamageDef>

  <!-- Damage types are the same as Blunt, but behaves like stabbing; intended for things like rifle barrels -->
  <DamageDef ParentName="BluntBase">
    <defName>Poke</defName>
    <workerClass>DamageWorker_Stab</workerClass>
    <stabChanceOfForcedInternal>0.4</stabChanceOfForcedInternal>
  </DamageDef>

  <DamageDef ParentName="BluntBase">
    <defName>Demolish</defName>
    <buildingDamageFactor>10</buildingDamageFactor>
    <buildingDamageFactorImpassable>0.75</buildingDamageFactorImpassable>
  </DamageDef>

  <DamageDef>
    <defName>Stab</defName>
    <label>stab</label>
    <workerClass>DamageWorker_Stab</workerClass>
    <externalViolence>true</externalViolence>
    <deathMessage>{0} has been stabbed to death.</deathMessage>
    <hediff>Stab</hediff>
    <hediffSolid>Crack</hediffSolid>
    <impactSoundType>Slice</impactSoundType>
    <armorCategory>Sharp</armorCategory>
    <stabChanceOfForcedInternal>0.6</stabChanceOfForcedInternal>
    <overkillPctToDestroyPart>0.4~1.0</overkillPctToDestroyPart>
  </DamageDef>

  <DamageDef Name="Scratch">
    <defName>Scratch</defName>
    <label>scratch</label>
    <workerClass>DamageWorker_Scratch</workerClass>
    <externalViolence>true</externalViolence>
    <deathMessage>{0} has been torn to death.</deathMessage>
    <hediff>Scratch</hediff>
    <hediffSolid>Crack</hediffSolid>
    <harmAllLayersUntilOutside>true</harmAllLayersUntilOutside>
    <impactSoundType>Slice</impactSoundType>
    <armorCategory>Sharp</armorCategory>
    <overkillPctToDestroyPart>0~0.7</overkillPctToDestroyPart>
    <scratchSplitPercentage>0.67</scratchSplitPercentage>
  </DamageDef>

  <DamageDef ParentName="Scratch">
    <defName>ScratchToxic</defName>
    <label>toxic scratch</label>
    <additionalHediffs>
      <li>
        <hediff>ToxicBuildup</hediff>
        <severityPerDamageDealt>0.015</severityPerDamageDealt>
        <victimSeverityScaling>ToxicResistance</victimSeverityScaling>
        <inverseStatScaling>true</inverseStatScaling>
        <victimSeverityScalingByInvBodySize>true</victimSeverityScalingByInvBodySize>
      </li>
    </additionalHediffs>
    <applyAdditionalHediffsIfHuntingForFood>false</applyAdditionalHediffsIfHuntingForFood>
    <impactSoundType>Toxic</impactSoundType>
    <damageEffecter>Impact_Toxic</damageEffecter>
  </DamageDef>

  <DamageDef Name="Bite">
    <defName>Bite</defName>
    <label>bite</label>
    <workerClass>DamageWorker_Bite</workerClass>
    <externalViolence>true</externalViolence>
    <deathMessage>{0} has been bitten to death.</deathMessage>
    <hediff>Bite</hediff>
    <hediffSolid>Crack</hediffSolid>
    <harmAllLayersUntilOutside>true</harmAllLayersUntilOutside>
    <impactSoundType>Slice</impactSoundType>
    <armorCategory>Sharp</armorCategory>
    <overkillPctToDestroyPart>0~0.1</overkillPctToDestroyPart>
  </DamageDef>

  <DamageDef ParentName="Bite">
    <defName>ToxicBite</defName>
    <label>toxic bite</label>
    <additionalHediffs>
      <li>
        <hediff>ToxicBuildup</hediff>
        <severityPerDamageDealt>0.015</severityPerDamageDealt>
        <victimSeverityScaling>ToxicResistance</victimSeverityScaling>
        <inverseStatScaling>true</inverseStatScaling>
        <victimSeverityScalingByInvBodySize>true</victimSeverityScalingByInvBodySize>
      </li>
    </additionalHediffs>
    <applyAdditionalHediffsIfHuntingForFood>false</applyAdditionalHediffsIfHuntingForFood>
    <impactSoundType>Toxic</impactSoundType>
    <damageEffecter>Impact_Toxic</damageEffecter>
  </DamageDef>

</Defs>
