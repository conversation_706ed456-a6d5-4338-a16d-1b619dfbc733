<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <CreepJoinerDownsideDef>
    <defName>Nothing</defName>
    <label>nothing</label>
    <weight>1</weight>
  </CreepJoinerDownsideDef>
  
  <CreepJoinerDownsideDef>
    <defName>Leaves</defName>
    <label>leaves</label>
    <weight>3</weight>
    <workerType>CreepJoinerWorker_DepartDownside</workerType>
    <triggersAfterDays>9~30</triggersAfterDays>
    <hasLetter>true</hasLetter>
    <letterLabel>{PAWN_nameDef} departure</letterLabel>
    <letterDesc>{PAWN_nameDef} thanks you for your hospitality, but says that it is time for {PAWN_objective} to move on.</letterDesc>
    <letterDef>NeutralEvent</letterDef>
  </CreepJoinerDownsideDef>
  
  <CreepJoinerDownsideDef>
    <defName>Aggressive</defName>
    <label>aggressive</label>
    <workerType>CreepJoinerWorker_AggressiveDownside</workerType>
    <triggersAfterDays>9~30</triggersAfterDays>
    <canOccurWhenImprisoned>true</canOccurWhenImprisoned>
    <hasLetter>true</hasLetter>
    <letterLabel>{PAWN_nameDef} hostile</letterLabel>
    <letterDesc>{PAWN_nameDef}'s demeanor has suddenly changed. A menacing grin splits {PAWN_possessive} face.\n\n{PAWN_nameDef} has become hostile.</letterDesc>
    <letterDef>ThreatSmall</letterDef>
  </CreepJoinerDownsideDef>
  
  <CreepJoinerDownsideDef>
    <defName>Metalhorror</defName>
    <label>metalhorror</label>
    <minCombatPoints>300</minCombatPoints>
    <canOccurRandomly>false</canOccurRandomly>
    <workerType>CreepJoinerWorker_MetalhorrorDownside</workerType>
    <requires>
      <li>Assault</li>
    </requires>
    <excludes>
      <li>FleshbeastEmergence</li>
    </excludes>
  </CreepJoinerDownsideDef>
  
  <CreepJoinerDownsideDef>
    <defName>CrumblingMind</defName>
    <label>crumbling mind</label>
    <triggersAfterDays>1~3</triggersAfterDays>
    <canOccurWhenImprisoned>true</canOccurWhenImprisoned>
    <hediffs>
      <li>CrumblingMindUndiagnosedCreepjoiner</li>
    </hediffs>
  </CreepJoinerDownsideDef>
  
  <CreepJoinerDownsideDef>
    <defName>EntityJailbreaker</defName>
    <label>entity jailbreaker</label>
    <traits>
      <VoidFascination/>
    </traits>
  </CreepJoinerDownsideDef>
  
  <CreepJoinerDownsideDef>
    <defName>PsychicAgony</defName>
    <label>psychic agony</label>
    <workerType>CreepJoinerWorker_PsychicAgony</workerType>
    <repeats>true</repeats>
    <triggerMtbDays>30</triggerMtbDays>
    <triggerMinDays>5</triggerMinDays>
    <mustBeConscious>true</mustBeConscious>
    <canOccurWhileDowned>false</canOccurWhileDowned>
    <canOccurWhenImprisoned>true</canOccurWhenImprisoned>
    <hasLetter>true</hasLetter>
    <letterLabel>Psychic agony</letterLabel>
    <letterDesc>{PAWN_nameDef} has involuntarily released a wave of psychic suffering! {PAWN_pronoun} tried to prevent it, but was unable to do so.\n\nNearby creatures will experience intense pain. The effects will linger for several days.</letterDesc>
    <letterDef>NegativeEvent</letterDef>
    <surgicalInspectionLetterExtra>{SURGEON_nameDef} detected a faint but painful psychic presence emanating from {PAWN_nameDef} but failed to understand what was causing it.</surgicalInspectionLetterExtra>
  </CreepJoinerDownsideDef>
  
  <CreepJoinerDownsideDef>
    <defName>OrganDecay</defName>
    <label>organ decay</label>
    <triggersAfterDays>3~5</triggersAfterDays>
    <canOccurWhenImprisoned>true</canOccurWhenImprisoned>
    <hediffs>
      <li>OrganDecayUndiagnosedCreepjoiner</li>
    </hediffs>
  </CreepJoinerDownsideDef>
  
  <CreepJoinerDownsideDef>
    <defName>Disturbing</defName>
    <label>disturbing</label>
    <traits>
      <Disturbing/>
    </traits>
  </CreepJoinerDownsideDef>

</Defs>