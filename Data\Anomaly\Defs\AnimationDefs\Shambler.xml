<?xml version="1.0" encoding="utf-8"?>

<Defs>
  
  <AnimationDef>
    <defName>ShamblerSway</defName>
    <durationTicks>1080</durationTicks>
    <startOnRandomTick>true</startOnRandomTick>
    <animationParts>
      <li>
        <key>Root</key>
        <value>
          <workerClass>AnimationWorker_Keyframes</workerClass>
          <keyframes>
            <li>
              <tick>0</tick>
              <angle>-7</angle>
            </li>
            <li>
              <tick>240</tick>
              <angle>7</angle>
            </li>
            <li>
              <tick>540</tick>
              <angle>7</angle>
            </li>
            <li>
              <tick>780</tick>
              <angle>-7</angle>
            </li>
            <li>
              <tick>1080</tick>
              <angle>-7</angle>
            </li>
          </keyframes>
          <pivot>(0.5, 0)</pivot>
        </value>
      </li>
    </animationParts>
  </AnimationDef>
  
  <AnimationDef>
    <defName>ShamblerRise</defName>
    <durationTicks>120</durationTicks>
    <startOnRandomTick>true</startOnRandomTick>
    <playWhenDowned>true</playWhenDowned>
    <animationParts>
      <li>
        <key>Root</key>
        <value>
          <workerClass>AnimationWorker_Keyframes</workerClass>
          <keyframes>
            <li>
              <tick>0</tick>
              <angle>0</angle>
            </li>
            <li>
              <tick>5</tick>
              <angle>-10</angle>
            </li>
            <li>
              <tick>15</tick>
              <angle>10</angle>
            </li>
            <li>
              <tick>20</tick>
              <angle>-20</angle>
            </li>
            <li>
              <tick>40</tick>
              <angle>-20</angle>
            </li>
            <li>
              <tick>50</tick>
              <angle>0</angle>
            </li>
            <li>
              <tick>60</tick>
              <angle>10</angle>
            </li>
            <li>
              <tick>65</tick>
              <angle>-10</angle>
            </li>
            <li>
              <tick>75</tick>
              <angle>-20</angle>
            </li>
            <li>
              <tick>80</tick>
              <angle>0</angle>
            </li>
            <li>
              <tick>90</tick>
              <angle>10</angle>
            </li>
            <li>
              <tick>100</tick>
              <angle>10</angle>
            </li>
            <li>
              <tick>110</tick>
              <angle>20</angle>
            </li>
            <li>
              <tick>120</tick>
              <angle>0</angle>
            </li>
          </keyframes>
        </value>
      </li>
    </animationParts>
  </AnimationDef>
  
</Defs>