<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <BodyDef>
    <defName>Mech_Diabolus</defName>
    <label>diabolus</label>
    <corePart>
      <def>MechanicalDiabolusBodyFirstRing</def>
      <height>Middle</height>
      <depth>Outside</depth>
      <groups>
        <li>Torso</li>
      </groups>
      <parts>
        <li>
          <def>MechanicalHead</def>
          <coverage>0.1</coverage>
          <groups>
            <li>HeadAttackTool</li>
          </groups>
          <parts>
            <li>
              <def>ArtificialBrain</def>
              <coverage>0.10</coverage>
              <depth>Inside</depth>
            </li>
            <li>
              <def>SightSensor</def>
              <customLabel>left sight sensor</customLabel>
              <coverage>0.13</coverage>
            </li>
            <li>
              <def>SightSensor</def>
              <customLabel>right sight sensor</customLabel>
              <coverage>0.13</coverage>
            </li>
            <li>
              <def>HearingSensor</def>
              <customLabel>left hearing sensor</customLabel>
              <coverage>0.10</coverage>
            </li>
            <li>
              <def>HearingSensor</def>
              <customLabel>right hearing sensor</customLabel>
              <coverage>0.10</coverage>
            </li>
            <li>
              <def>SmellSensor</def>
              <coverage>0.10</coverage>
            </li>
          </parts>
        </li>
        <li>
          <def>MechanicalDiabolusBodySecondRing</def>
          <coverage>0.75</coverage>
          <parts>
            <li>
              <def>Reactor</def>
              <coverage>0.05</coverage>
              <depth>Inside</depth>
            </li>
            <li>
              <def>DiabolusCapacitor</def>
              <coverage>0.1</coverage>
              <depth>Outside</depth>
            </li>
            <li>
              <def>DiabolusCapacitor</def>
              <coverage>0.1</coverage>
              <depth>Outside</depth>
            </li>
            <li>
              <def>MechanicalDiabolusBodyThirdRing</def>
              <coverage>0.7</coverage>
              <parts>
                <li>
                  <def>MechanicalDiabolusBodyFourthRing</def>
                  <coverage>0.7</coverage>
                  <parts>
                    <li>
                      <def>MechanicalDiabolusBodyFifthRing</def>
                      <coverage>0.6</coverage>
                      <parts>
                        <li>
                          <def>MechanicalDiabolusBodySixthRing</def>
                          <coverage>0.5</coverage>
                          <parts>
                            <li>
                              <def>FluidReprocessor</def>
                              <coverage>0.05</coverage>
                              <depth>Inside</depth>
                            </li>
                          </parts>
                        </li>
                      </parts>
                    </li>
                  </parts>
                </li>
              </parts>
            </li>
          </parts>
        </li>
        <li>
          <def>BulbTurret</def>
          <customLabel>bulb turret</customLabel>
          <coverage>0.05</coverage>
          <groups>
            <li>BulbTurret</li>
          </groups>
        </li>
      </parts>
    </corePart>
  </BodyDef>

  <BodyDef>
    <defName>Mech_Centurion</defName>
    <label>centurion</label>
    <corePart>
      <def>MechanicalThoraxCanManipulate</def>
      <height>Middle</height>
      <depth>Outside</depth>
      <groups>
        <li>Torso</li>
      </groups>
      <parts>
        <li>
          <def>MechanicalNeck</def>
          <coverage>0.10</coverage>
          <height>Top</height>
          <parts>
            <li>
              <def>MechanicalHead</def>
              <coverage>0.80</coverage>
              <groups>
                <li>HeadAttackTool</li>
              </groups>
              <parts>
                <li>
                  <def>ArtificialBrain</def>
                  <coverage>0.10</coverage>
                  <depth>Inside</depth>
                </li>
                <li>
                  <def>SightSensor</def>
                  <customLabel>left sight sensor</customLabel>
                  <coverage>0.13</coverage>
                </li>
                <li>
                  <def>SightSensor</def>
                  <customLabel>right sight sensor</customLabel>
                  <coverage>0.13</coverage>
                </li>
                <li>
                  <def>HearingSensor</def>
                  <customLabel>left hearing sensor</customLabel>
                  <coverage>0.10</coverage>
                </li>
                <li>
                  <def>HearingSensor</def>
                  <customLabel>right hearing sensor</customLabel>
                  <coverage>0.10</coverage>
                </li>
                <li>
                  <def>SmellSensor</def>
                  <coverage>0.10</coverage>
                </li>
              </parts>
            </li>
          </parts>
        </li>
        <li>
          <def>MechanicalLeg</def>
          <customLabel>front left leg</customLabel>
          <coverage>0.16</coverage>
          <height>Bottom</height>
          <parts>
            <li>
              <def>MechanicalFoot</def>
              <customLabel>front left foot</customLabel>
              <coverage>0.5</coverage>
              <groups>
                <li>FrontLeftLeg</li>
              </groups>
            </li>
          </parts>
        </li>
        <li>
          <def>MechanicalLeg</def>
          <customLabel>front right leg</customLabel>
          <coverage>0.16</coverage>
          <height>Bottom</height>
          <parts>
            <li>
              <def>MechanicalFoot</def>
              <customLabel>front right foot</customLabel>
              <coverage>0.5</coverage>
              <groups>
                <li>FrontRightLeg</li>
              </groups>
            </li>
          </parts>
        </li>
        <li>
          <def>MechanicalLeg</def>
          <customLabel>rear left leg</customLabel>
          <coverage>0.16</coverage>
          <height>Bottom</height>
          <parts>
            <li>
              <def>MechanicalFoot</def>
              <customLabel>rear left foot</customLabel>
              <coverage>0.5</coverage>
            </li>
          </parts>
        </li>
        <li>
          <def>MechanicalLeg</def>
          <customLabel>rear right leg</customLabel>
          <coverage>0.16</coverage>
          <height>Bottom</height>
          <parts>
            <li>
              <def>MechanicalFoot</def>
              <customLabel>rear right foot</customLabel>
              <coverage>0.5</coverage>
            </li>
          </parts>
        </li>
        <li>
          <def>Reactor</def>
          <coverage>0.06</coverage>
          <depth>Inside</depth>
        </li>
        <li>
          <def>FluidReprocessor</def>
          <coverage>0.04</coverage>
          <depth>Inside</depth>
        </li>
        <li>
          <def>FluidReprocessor</def>
          <coverage>0.04</coverage>
          <depth>Inside</depth>
        </li>
        <li>
          <def>BulbTurret</def>
          <customLabel>bulb turret</customLabel>
          <coverage>0.05</coverage>
          <groups>
            <li>BulbTurret</li>
          </groups>
        </li>
      </parts>
    </corePart>
  </BodyDef>

  <BodyDef>
    <defName>Mech_Warqueen</defName>
    <label>war queen</label>
    <corePart>
      <def>MechanicalWarqueenBodyFirstRing</def>
      <height>Middle</height>
      <depth>Outside</depth>
      <groups>
        <li>Torso</li>
      </groups>
      <parts>
        <li>
          <def>MechanicalHead</def>
          <coverage>0.1</coverage>
          <groups>
            <li>HeadAttackTool</li>
          </groups>
          <parts>
            <li>
              <def>ArtificialBrain</def>
              <coverage>0.10</coverage>
              <depth>Inside</depth>
            </li>
            <li>
              <def>SightSensor</def>
              <customLabel>left sight sensor</customLabel>
              <coverage>0.13</coverage>
            </li>
            <li>
              <def>SightSensor</def>
              <customLabel>right sight sensor</customLabel>
              <coverage>0.13</coverage>
            </li>
            <li>
              <def>HearingSensor</def>
              <customLabel>left hearing sensor</customLabel>
              <coverage>0.10</coverage>
            </li>
            <li>
              <def>HearingSensor</def>
              <customLabel>right hearing sensor</customLabel>
              <coverage>0.10</coverage>
            </li>
            <li>
              <def>SmellSensor</def>
              <coverage>0.10</coverage>
            </li>
          </parts>
        </li>
        <li>
          <def>MechanicalWarqueenBodySecondRing</def>
          <coverage>0.75</coverage>
          <parts>
            <li>
              <def>Reactor</def>
              <coverage>0.05</coverage>
              <depth>Inside</depth>
            </li>
            <li>
              <def>MechanicalWarqueenBodyThirdRing</def>
              <coverage>0.8</coverage>
              <parts>
                <li>
                  <def>MechanicalWarqueenBodyFourthRing</def>
                  <coverage>0.7</coverage>
                  <parts>
                    <li>
                      <def>MechanicalWarqueenBodyFifthRing</def>
                      <coverage>0.6</coverage>
                      <parts>
                        <li>
                          <def>FluidReprocessor</def>
                          <coverage>0.05</coverage>
                          <depth>Inside</depth>
                        </li>
                        <li>
                          <def>MechanicalWarqueenFormingPod</def>
                          <coverage>0.5</coverage>
                          <height>Middle</height>
                          <depth>Inside</depth>
                        </li>
                      </parts>
                    </li>
                  </parts>
                </li>
              </parts>
            </li>
          </parts>
        </li>
        <li>
          <def>BulbTurret</def>
          <customLabel>bulb turret</customLabel>
          <coverage>0.05</coverage>
          <groups>
            <li>BulbTurret</li>
          </groups>
        </li>
      </parts>
    </corePart>
  </BodyDef>

</Defs>