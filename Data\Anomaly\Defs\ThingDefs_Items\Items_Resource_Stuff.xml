﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <ThingDef ParentName="ResourceBase">
    <defName>Bioferrite</defName>
    <label>bioferrite</label>
    <description>An exotic metal-like fibrous substance with both organic and metallic properties. Bioferrite is produced by microscopic mechanites as they process organic material. In cases of disordered mechanite behavior, this happens in a chaotic, cancer-like way.\n\nThe material is known to interact with some types of psychic flows. If pressurized under specific conditions, bioferrite becomes extremely flammable, making it a useful fuel.</description>
    <graphicData>
      <texPath>Things/Item/Resource/Bioferrite</texPath> <!-- placeholder - art queued -->
      <graphicClass>Graphic_StackCount</graphicClass>
    </graphicData>
    <genericMarketSellable>false</genericMarketSellable>
    <soundInteract>Metal_Drop</soundInteract>
    <soundDrop>Metal_Drop</soundDrop>
    <useHitPoints>false</useHitPoints>
    <healthAffectsPrice>false</healthAffectsPrice>
    <statBases>
      <MarketValue>0.75</MarketValue>
      <Mass>0.25</Mass>
      <StuffPower_Armor_Sharp>1.1</StuffPower_Armor_Sharp>
      <StuffPower_Armor_Blunt>0.5</StuffPower_Armor_Blunt>
      <StuffPower_Armor_Heat>0.5</StuffPower_Armor_Heat>
      <StuffPower_Insulation_Cold>2.5</StuffPower_Insulation_Cold>
      <StuffPower_Insulation_Heat>0</StuffPower_Insulation_Heat>
      <SharpDamageMultiplier>1.3</SharpDamageMultiplier>
      <BluntDamageMultiplier>0.9</BluntDamageMultiplier>
    </statBases>
    <thingCategories>
      <li>ResourcesRaw</li>
    </thingCategories>
    <burnableByRecipe>false</burnableByRecipe>
    <smeltable>true</smeltable>
    <stuffProps>
      <categories>
        <li>Metallic</li>
        <li>Bioferrite</li>
      </categories>
      <appearance>Metal</appearance>
      <commonality>0</commonality>
      <allowedInStuffGeneration>false</allowedInStuffGeneration>
      <constructEffect>ConstructMetal</constructEffect>
      <color>(100, 80, 80)</color>
      <soundImpactBullet>BulletImpact_Metal</soundImpactBullet>
      <soundMeleeHitSharp>MeleeHit_Metal_Sharp</soundMeleeHitSharp>
      <soundMeleeHitBlunt>MeleeHit_Metal_Blunt</soundMeleeHitBlunt>
      <soundImpactMelee>Pawn_Melee_Punch_HitBuilding_Metal</soundImpactMelee>
      <statOffsets>
        <Beauty>0</Beauty>
      </statOffsets>
      <statFactors>
        <MaxHitPoints>2</MaxHitPoints>
        <Beauty>0.25</Beauty>
        <Flammability>0.75</Flammability>
        <WorkToMake>2.5</WorkToMake>
        <WorkToBuild>2.5</WorkToBuild>
        <DoorOpenSpeed>1</DoorOpenSpeed>
        <BedRestEffectiveness>0.85</BedRestEffectiveness>
        <MeleeWeapon_CooldownMultiplier>1</MeleeWeapon_CooldownMultiplier>
      </statFactors>
      <statFactorsQuality>
        <li>
          <stat>PsychicSensitivityFactor</stat>
          <awful>1.06</awful>
          <poor>1.08</poor>
          <normal>1.1</normal>
          <good>1.13</good>
          <excellent>1.15</excellent>
          <masterwork>1.17</masterwork>
          <legendary>1.20</legendary>
        </li>
      </statFactorsQuality>
      <statOffsetsQuality>
        <li MayRequireAnyOf="Ludeon.RimWorld.Royalty,Ludeon.RimWorld.Biotech">
          <stat>PsychicEntropyMaxOffset</stat>
          <awful>4</awful>
          <poor>6</poor>
          <normal>8</normal>
          <good>11</good>
          <excellent>13</excellent>
          <masterwork>15</masterwork>
          <legendary>18</legendary>
        </li>
        <li MayRequireAnyOf="Ludeon.RimWorld.Royalty,Ludeon.RimWorld.Biotech">
          <stat>PsychicEntropyRecoveryRateOffset</stat>
          <awful>0.04</awful>
          <poor>0.04</poor>
          <normal>0.04</normal>
          <good>0.04</good>
          <excellent>0.04</excellent>
          <masterwork>0.04</masterwork>
          <legendary>0.04</legendary>
        </li>
      </statOffsetsQuality>
    </stuffProps>
    <terrainAffordanceNeeded>Medium</terrainAffordanceNeeded>
    <allowedArchonexusCount>80</allowedArchonexusCount>
  </ThingDef>
  
  <ThingDef ParentName="ResourceBase">
    <defName>LabyrinthMatter</defName>
    <label>gray</label>
    <description>An unnatural gray material of unknown origin.</description>
    <statBases>
      <StuffPower_Insulation_Cold>3</StuffPower_Insulation_Cold>
      <StuffPower_Insulation_Heat>0</StuffPower_Insulation_Heat>
      <SharpDamageMultiplier>0</SharpDamageMultiplier>
      <BluntDamageMultiplier>0</BluntDamageMultiplier>
    </statBases>
    <useHitPoints>false</useHitPoints>
    <alwaysHaulable>false</alwaysHaulable>
    <designateHaulable>false</designateHaulable>
    <selectable>false</selectable>
    <neverMultiSelect>true</neverMultiSelect>
    <scatterableOnMapGen>false</scatterableOnMapGen>
    <stuffProps>
      <appearance>Metal</appearance>
      <commonality>0</commonality>
      <constructEffect>ConstructMetal</constructEffect>
      <soundImpactBullet>BulletImpact_Metal</soundImpactBullet>
      <soundMeleeHitSharp>MeleeHit_Metal_Sharp</soundMeleeHitSharp>
      <soundMeleeHitBlunt>MeleeHit_Metal_Blunt</soundMeleeHitBlunt>
      <soundImpactMelee>Pawn_Melee_Punch_HitBuilding_Metal</soundImpactMelee>
      <statFactors>
        <MaxHitPoints>1</MaxHitPoints>
        <Beauty>1</Beauty>
        <Flammability>0</Flammability>
      </statFactors>
    </stuffProps>
    <terrainAffordanceNeeded>Medium</terrainAffordanceNeeded>
  </ThingDef>

  <ThingDef ParentName="LeatherBase">
    <defName>Leather_Dread</defName>
    <label>dread leather</label>
    <description>Thick, tough leather taken from some monstrous creature. The surface is covered in ugly scars and whorls, and smells faintly of rotten meat. Most people will be unhappy if made to wear apparel made of this.</description>
    <possessionCount>20</possessionCount>
    <hiddenWhileUndiscovered>true</hiddenWhileUndiscovered>
    <genericMarketSellable>false</genericMarketSellable>
    <graphicData>
      <color>(84,91,90)</color>
    </graphicData>
    <statBases>
      <MarketValue>3.5</MarketValue>
      <StuffPower_Armor_Sharp>1.27</StuffPower_Armor_Sharp>
      <StuffPower_Armor_Blunt>0.24</StuffPower_Armor_Blunt>
      <StuffPower_Armor_Heat>1.5</StuffPower_Armor_Heat>
      <StuffPower_Insulation_Cold>20</StuffPower_Insulation_Cold>
      <StuffPower_Insulation_Heat>12</StuffPower_Insulation_Heat>
    </statBases>
    <stuffProps>
      <color>(84,91,90)</color>
      <statFactors>
        <Beauty>1</Beauty>
      </statFactors>
    </stuffProps>
  </ThingDef>

</Defs>