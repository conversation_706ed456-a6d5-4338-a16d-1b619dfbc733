<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <EffecterDef>
    <defName>WarqueenWarUrchinsSpawned</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>WarqueenWarUrchinsSpawned</soundDef>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_WarQueenSpineGlow</moteDef>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <attachToSpawnThing>true</attachToSpawnThing>
        <absoluteAngle>true</absoluteAngle>
        <rotation>0~0</rotation>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>SubcoreSoftscanner_Start</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>SubcoreSoftscanner_Start</soundDef>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>SubcoreRipscanner_Start</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>SubcoreRipscanner_Start</soundDef>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>Radiotalking</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_Sustainer</subEffecterClass>
        <soundDef>Radiotalking</soundDef>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Floordrawing</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_Sustainer</subEffecterClass>
        <soundDef>Floordrawing</soundDef>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Breastfeeding</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_Sustainer</subEffecterClass>
        <soundDef>Breastfeeding</soundDef>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Birth</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>Birth</soundDef>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Fire_Spew</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.15</positionRadius>
        <fleckDef>FireSpew_A</fleckDef>
        <chancePerTick>0.9</chancePerTick>
        <speed>9.5</speed>
        <scale>0.8~1.2</scale>
        <angle>-15~15</angle>
        <positionLerpFactor>0.85</positionLerpFactor>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.1</positionRadius>
        <fleckDef>FireSpew_Base</fleckDef>
        <chancePerTick>0.1</chancePerTick>
        <speed>0.6</speed>
        <scale>0.8</scale>
        <angle>-9~9</angle>
        <positionLerpFactor>0.75</positionLerpFactor>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.1</positionRadius>
        <moteDef>Mote_SparkThrownFast</moteDef>
        <chancePerTick>0.5</chancePerTick>
        <speed>3.3~5</speed>
        <scale>0.1~0.2</scale>
        <angle>-12~12</angle>
        <positionLerpFactor>0.8</positionLerpFactor>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.2</positionRadius>
        <fleckDef>FireSpew_Glow</fleckDef>
        <chancePerTick>0.16</chancePerTick>
        <positionLerpFactor>0.65</positionLerpFactor>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Fire_SpewShort</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.15</positionRadius>
        <fleckDef>FireSpew_Short</fleckDef>
        <chancePerTick>0.9</chancePerTick>
        <speed>6.5</speed>
        <scale>0.8~1.2</scale>
        <angle>-15~15</angle>
        <positionLerpFactor>0.8</positionLerpFactor>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.1</positionRadius>
        <fleckDef>FireSpew_Base</fleckDef>
        <chancePerTick>0.1</chancePerTick>
        <speed>0.6</speed>
        <scale>0.65</scale>
        <angle>-9~9</angle>
        <positionLerpFactor>0.6</positionLerpFactor>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.1</positionRadius>
        <moteDef>Mote_SparkThrownFast</moteDef>
        <chancePerTick>0.5</chancePerTick>
        <speed>3.3~5</speed>
        <scale>0.1~0.2</scale>
        <angle>-12~12</angle>
        <positionLerpFactor>0.8</positionLerpFactor>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.2</positionRadius>
        <fleckDef>FireSpew_Glow</fleckDef>
        <chancePerTick>0.16</chancePerTick>
        <positionLerpFactor>0.65</positionLerpFactor>
        <scale>0.8</scale>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>GraserBeam_End</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>Smoke</fleckDef>
        <chancePerTick>0.4</chancePerTick>
        <scale>0.6~1.1</scale>
        <spawnLocType>OnSource</spawnLocType>
        <absoluteAngle>true</absoluteAngle>
        <angle>30~70</angle>
        <speed>0.2~0.35</speed>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.1</positionRadius>
        <moteDef>Mote_SparkThrownFast</moteDef>
        <spawnLocType>OnSource</spawnLocType>
        <chancePerTick>0.9</chancePerTick>
        <speed>3.3~5</speed>
        <scale>0.1~0.2</scale>
        <angle>88~104</angle>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.1</positionRadius>
        <spawnLocType>OnSource</spawnLocType>
        <fleckDef>Beam_Glow</fleckDef>
        <chancePerTick>0.5</chancePerTick>
        <scale>0.8</scale>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Fire_Burst</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.15</positionRadius>
        <fleckDef>FireSpew_A</fleckDef>
        <chancePerTick>1</chancePerTick>
        <speed>9.5</speed>
        <scale>0.8~1.2</scale>
        <angle>0~360</angle>
        <positionLerpFactor>0.85</positionLerpFactor>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.1</positionRadius>
        <fleckDef>FireSpew_Base</fleckDef>
        <chancePerTick>1</chancePerTick>
        <speed>0.6</speed>
        <scale>0.8</scale>
        <angle>0~360</angle>
        <positionLerpFactor>0.75</positionLerpFactor>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.2</positionRadius>
        <fleckDef>FireSpew_Glow</fleckDef>
        <chancePerTick>0.16</chancePerTick>
        <positionLerpFactor>0.65</positionLerpFactor>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.15</positionRadius>
        <fleckDef>FuelSpray</fleckDef>
        <chancePerTick>1</chancePerTick>
        <speed>6.5</speed>
        <scale>0.8~1.2</scale>
        <angle>0~360</angle>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Fire_Burst_Warmup</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.1</positionRadius>
        <moteDef>Mote_SparkThrownFast</moteDef>
        <chancePerTick>1</chancePerTick>
        <speed>3.3~5</speed>
        <scale>0.1~0.2</scale>
        <angle>0~360</angle>
        <positionLerpFactor>0.65</positionLerpFactor>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.15</positionRadius>
        <fleckDef>FuelSpray</fleckDef>
        <chancePerTick>1</chancePerTick>
        <speed>6.5</speed>
        <scale>0.8~1.2</scale>
        <angle>0~360</angle>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_Sustainer</subEffecterClass>
        <soundDef>FireBurst_Warmup</soundDef>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>CellPollution</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.2</positionRadius>
        <positionOffset>(0, 0, 0.12)</positionOffset>
        <fleckDef>Fleck_WastePackDissolutionCell</fleckDef>
        <chancePerTick>0.16</chancePerTick>
        <scale>0.8~1.2</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>PollutionSpreading</soundDef>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>CellPollution_Performant</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.2</positionRadius>
        <positionOffset>(0, 0, 0.12)</positionOffset>
        <fleckDef>Fleck_WastePackDissolutionCell_Performant</fleckDef>
        <chancePerTick>0.16</chancePerTick>
        <scale>0.8~1.2</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>PollutionSpreading</soundDef>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>RipScannerHeadGlow</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.13</positionRadius>
        <moteDef>Mote_RipscannerHeadGlow</moteDef>
        <chancePerTick>0.1</chancePerTick>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>WarUrchinSpawned</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_WarhUrchinSpawnElectricity</moteDef>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <attachToSpawnThing>true</attachToSpawnThing>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_WarUrchinSpawnFlash</moteDef>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <attachToSpawnThing>true</attachToSpawnThing>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>WarqueenWarUrchinsSpawned</soundDef>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>SanguophageMeeting</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_Sustainer</subEffecterClass>
        <soundDef>SanguophageMeeting</soundDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_SanguophageMeetingSmoke</moteDef>
        <burstCount>1</burstCount>
        <rotation>0</rotation>
        <spawnLocType>OnSource</spawnLocType>
        <positionOffset>(0, 0, 0.75)</positionOffset>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>SanguophageEyes</fleckDef>
        <burstCount>2~3</burstCount>
        <chancePerTick>0.015</chancePerTick>
        <rotation>132</rotation>
        <spawnLocType>OnSource</spawnLocType>
        <scale>0.8~1.1</scale>
        <positionDimensions>(4.5, 0, 1.7)</positionDimensions>
        <positionOffset>(0, 0, 4.5)</positionOffset>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>SanguophageEyes</fleckDef>
        <burstCount>2~3</burstCount>
        <chancePerTick>0.025</chancePerTick>
        <rotation>132</rotation>
        <spawnLocType>OnSource</spawnLocType>
        <scale>0.8~1.0</scale>
        <positionDimensions>(3.5, 0, 1.3)</positionDimensions>
        <positionOffset>(0, 0, -3.5)</positionOffset>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>GeneAssembler_Working</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_Sustainer</subEffecterClass>
        <soundDef>GeneAssembler_Working</soundDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>PlainFlash</fleckDef>
        <chancePerTick>0.02</chancePerTick>
        <positionRadius>0.15</positionRadius>
        <color>(96, 199, 231, 128)</color>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>TunnelerSlowedDown</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <ticksBetweenMotes>40</ticksBetweenMotes>
        <moteDef>Mote_MechTunnelerSlowedDown</moteDef>
        <rotation>0~0</rotation>
        <absoluteAngle>true</absoluteAngle>
        <attachToSpawnThing>true</attachToSpawnThing>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>MechUncontrolled</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <ticksBetweenMotes>200</ticksBetweenMotes>
        <moteDef>Mote_MechUncontrolled</moteDef>
        <rotation>0~0</rotation>
        <absoluteAngle>true</absoluteAngle>
        <attachToSpawnThing>true</attachToSpawnThing>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>MechResurrected</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>MechResurrectCast</soundDef>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MechResurrectGlow</moteDef>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <attachToSpawnThing>true</attachToSpawnThing>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MechResurrectFlash</moteDef>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <attachToSpawnThing>true</attachToSpawnThing>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Implant_Xenogerm</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <scale>0.3~0.7</scale>
        <ticksBetweenMotes>15</ticksBetweenMotes>
        <positionLerpFactor>0.6</positionLerpFactor>
        <positionRadius>0.6</positionRadius>
        <moteDef>Mote_FoodBitMeat</moteDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <fleckDef>FlashXenogerm</fleckDef>
        <ticksBetweenMotes>40</ticksBetweenMotes>
        <scale>1</scale>
        <positionOffset>(0, 0, 0.38)</positionOffset>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>BlastMechBandShockwave</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>Fleck_BlastMechBandShockwave</fleckDef>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <absoluteAngle>true</absoluteAngle>
        <rotation>0~0</rotation>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>Fleck_BlastMechBandRedLine</fleckDef>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <absoluteAngle>true</absoluteAngle>
        <rotation>0~0</rotation>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>FlashMechBand</fleckDef>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <absoluteAngle>true</absoluteAngle>
        <rotation>0~0</rotation>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_RedFlashStrong</moteDef>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <scale>24</scale>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>MechBandElectricityArc</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredChance</subEffecterClass>
        <moteDef>Mote_MechControlTakingSparkArch</moteDef>
        <burstCount>1</burstCount>
        <chancePerTick>1</chancePerTick>
        <rotation>-150~-50</rotation>
        <spawnLocType>OnTarget</spawnLocType>
        <scale>1.1~1.4</scale>
        <rotateTowardsTargetCenter>true</rotateTowardsTargetCenter>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Coagulate</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <moteDef>Mote_CoagulateA</moteDef>
        <burstCount>1</burstCount>
        <chancePerTick>0.125</chancePerTick>
        <scale>0.9~1.1</scale>
        <spawnLocType>RandomDrawPosOnTarget</spawnLocType>
        <attachToSpawnThing>true</attachToSpawnThing>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <moteDef>Mote_CoagulateB</moteDef>
        <burstCount>1</burstCount>
        <chancePerTick>0.125</chancePerTick>
        <scale>1~1.4</scale>
        <spawnLocType>RandomDrawPosOnTarget</spawnLocType>
        <attachToSpawnThing>true</attachToSpawnThing>
      </li>
    </children>
  </EffecterDef>

  
  <EffecterDef>
    <defName>CocoonDestroyed</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>CocoonDestroyed</soundDef>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>CocoonDestroyedSlime</fleckDef>
        <burstCount>5</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>1.3</positionRadius>
        <speed>0.6~1.1</speed>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_CocoonDestroyedMist</moteDef>
        <burstCount>12</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>1.3</positionRadius>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_CocoonDestroyedFlyingSlime</moteDef>
        <burstCount>3</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>0.6</positionRadius>
        <speed>0.8~1.2</speed>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_CocoonDestroyedFlyingPiece</moteDef>
        <burstCount>5</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>0.5</positionRadius>
        <speed>2~3</speed>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>CocoonWakingUp</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_Sustainer</subEffecterClass>
        <soundDef>CocoonWakingUp</soundDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <moteDef>Mote_CocoonBreaking</moteDef>
        <burstCount>7</burstCount>
        <initialDelayTicks>5</initialDelayTicks>
        <ticksBetweenMotes>43</ticksBetweenMotes>
        <rotation>-150~150</rotation>
        <positionRadius>0.66</positionRadius>
        <speed>0.6~1.1</speed>
        <spawnLocType>OnSource</spawnLocType>
        <maxMoteCount>3</maxMoteCount>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <fleckDef>CocoonPulse</fleckDef>
        <burstCount>1</burstCount>
        <ticksBetweenMotes>43</ticksBetweenMotes>
        <rotation>-150~150</rotation>
        <positionRadius>0</positionRadius>
        <speed>0</speed>
        <spawnLocType>OnSource</spawnLocType>
        <maxMoteCount>3</maxMoteCount>
      </li>
    </children>
  </EffecterDef>
  
  <EffecterDef>
    <defName>AncientExostriderRemainsPulse</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <fleckDef>AncientExostriderRedPulse</fleckDef>
        <ticksBetweenMotes>135</ticksBetweenMotes>
        <spawnLocType>OnSource</spawnLocType>
        <positionOffset>(0.2, 0, -0.4)</positionOffset>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>WastepackAtomizer_Operating</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <fleckDef>WastepackAtomizer_Forward</fleckDef>
        <spawnLocType>BetweenPositions</spawnLocType>
        <positionLerpFactor>0.6</positionLerpFactor>
        <positionOffset>(0, 0, 0.15)</positionOffset>
        <rotation>0</rotation>
        <ticksBetweenMotes>180</ticksBetweenMotes>
        <speed>-0.52</speed>
        <angle>0</angle>
        <useTargetBInitialRotation>true</useTargetBInitialRotation>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <fleckDef>WastepackAtomizer_Backward</fleckDef>
        <spawnLocType>BetweenPositions</spawnLocType>
        <positionLerpFactor>1.3</positionLerpFactor>
        <positionOffset>(0, 0, 0.15)</positionOffset>
        <rotation>0</rotation>
        <ticksBetweenMotes>180</ticksBetweenMotes>
        <initialDelayTicks>120</initialDelayTicks>
        <speed>1</speed>
        <angle>0</angle>
        <useTargetBInitialRotation>true</useTargetBInitialRotation>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Bloodfeed_Warmup</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <positionRadius>0.2</positionRadius>
        <positionOffset>(0, 0, 0.5)</positionOffset>
        <ticksBetweenMotes>25</ticksBetweenMotes>
        <fleckDef>BloodFeedSplash</fleckDef>
        <burstCount>1</burstCount>
        <color>(210, 50, 0)</color>
        <speed>-1.5</speed>
        <rotateTowardsTargetCenter>True</rotateTowardsTargetCenter>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>RandomDrawPosOnTarget</spawnLocType>
        <attachToSpawnThing>true</attachToSpawnThing>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Vat_Bubbles_South</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <positionDimensions>(0.32, 0, 0.2)</positionDimensions>
        <positionOffset>(0, 0, 0.35)</positionOffset>
        <ticksBetweenMotes>10</ticksBetweenMotes>
        <fleckDef>Fleck_VatBubble</fleckDef>
        <burstCount>1~2</burstCount>
        <speed>0.5</speed>
        <angle>0</angle>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Vat_Bubbles_North</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <positionDimensions>(0.32, 0, 0.2)</positionDimensions>
        <positionOffset>(0, 0, -0.6)</positionOffset>
        <ticksBetweenMotes>10</ticksBetweenMotes>
        <fleckDef>Fleck_VatBubble</fleckDef>
        <burstCount>1~2</burstCount>
        <speed>0.5</speed>
        <angle>0</angle>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Vat_Bubbles_West</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <positionDimensions>(0.7, 0, 0.15)</positionDimensions>
        <positionOffset>(0.5, 0, -0.2)</positionOffset>
        <ticksBetweenMotes>10</ticksBetweenMotes>
        <fleckDef>Fleck_VatBubble</fleckDef>
        <burstCount>1~2</burstCount>
        <speed>0.3</speed>
        <angle>0</angle>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Vat_Bubbles_East</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <positionDimensions>(0.7, 0, 0.15)</positionDimensions>
        <positionOffset>(-0.5, 0, -0.2)</positionOffset>
        <ticksBetweenMotes>10</ticksBetweenMotes>
        <fleckDef>Fleck_VatBubble</fleckDef>
        <burstCount>1~2</burstCount>
        <speed>0.3</speed>
        <angle>0</angle>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>ApocrionAttached</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <moteDef>Mote_MechSparkSimple</moteDef>
        <burstCount>1~2</burstCount>
        <chancePerTick>0.015</chancePerTick>
        <rotation>-150~-150</rotation>
        <spawnLocType>OnTarget</spawnLocType>
        <scale>1.0~1.2</scale>
        <positionRadius>0.7</positionRadius>
        <attachToSpawnThing>true</attachToSpawnThing>
        <rotateTowardsTargetCenter>true</rotateTowardsTargetCenter>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <moteDef>Mote_ApocritonPulse</moteDef>
        <burstCount>1</burstCount>
        <rotation>-150~150</rotation>
        <ticksBetweenMotes>76</ticksBetweenMotes>
        <positionRadius>0</positionRadius>
        <attachToSpawnThing>true</attachToSpawnThing>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>ApocrionAoeWarmup</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <moteDef>Mote_MechSparkSimple</moteDef>
        <burstCount>1</burstCount>
        <chancePerTick>0.05</chancePerTick>
        <rotation>-150~-150</rotation>
        <spawnLocType>OnTarget</spawnLocType>
        <scale>1.0~1.3</scale>
        <positionRadius>4.5</positionRadius>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_ApocritonWarmupArea</moteDef>
        <burstCount>1</burstCount>
        <rotation>-150~150</rotation>
        <positionRadius>0</positionRadius>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>ApocrionAoeResolve</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_MechSparkSimple</moteDef>
        <burstCount>15</burstCount>
        <rotation>-150~-150</rotation>
        <spawnLocType>OnTarget</spawnLocType>
        <scale>1.0~1.3</scale>
        <positionRadius>5</positionRadius>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>ApocritonResurrectFlashGrowing</fleckDef>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>
  
  <EffecterDef>
    <defName>JumpMechWarmupEffect</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <spawnLocType>OnSource</spawnLocType>
        <soundDef>JumpMechPreLaunch</soundDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <spawnLocType>OnSource</spawnLocType>
        <fleckDef>JumpWarmupSmoke</fleckDef>
        <initialDelayTicks>1</initialDelayTicks>
        <speed>0.6~0.8</speed>
        <angle>170~190</angle>
        <absoluteAngle>true</absoluteAngle>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>JumpMechFlightEffect</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <spawnLocType>OnSource</spawnLocType>
        <soundDef>JumpMechLaunch</soundDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <spawnLocType>OnSource</spawnLocType>
        <fleckDef>ShotFlash</fleckDef>
        <scale>6</scale>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <scale>0.5~0.6</scale>
        <spawnLocType>OnSource</spawnLocType>
        <positionOffset>(0,0,-0.5)</positionOffset>
        <fleckDef>JumpFlameMech</fleckDef>
        <ticksBetweenMotes>1</ticksBetweenMotes>
        <maxMoteCount>14</maxMoteCount>
        <speed>4~5</speed>
        <angle>170~190</angle>
        <absoluteAngle>true</absoluteAngle>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <scale>0.9~0.7</scale>
        <spawnLocType>OnSource</spawnLocType>
        <positionOffset>(0,0,-0.5)</positionOffset>
        <fleckDef>JumpFlameGlowMech</fleckDef>
        <ticksBetweenMotes>1</ticksBetweenMotes>
        <maxMoteCount>14</maxMoteCount>
        <speed>4~5</speed>
        <angle>160~200</angle>
        <absoluteAngle>true</absoluteAngle>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <scale>0.8~1.2</scale>
        <spawnLocType>OnSource</spawnLocType>
        <positionOffset>(0,0,-0.5)</positionOffset>
        <fleckDef>JumpSmoke</fleckDef>
        <ticksBetweenMotes>2</ticksBetweenMotes>
        <maxMoteCount>8</maxMoteCount>
        <speed>5~6</speed>
        <angle>170~190</angle>
        <absoluteAngle>true</absoluteAngle>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>AtomizerResolve</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <spawnLocType>OnSource</spawnLocType>
        <soundDef>WastepackAtomizer_Atomized</soundDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>AtomizerFlash</fleckDef>
        <positionRadius>0</positionRadius>
        <scale>2.5</scale>
        <positionOffset>(0, 0, 0.25)</positionOffset>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>AtomizerGlowShrinking</fleckDef>
        <positionRadius>0</positionRadius>
        <scale>1.8</scale>
        <positionOffset>(0, 0, 0.25)</positionOffset>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <fleckDef>AtomizerGlowFast</fleckDef>
        <burstCount>1</burstCount>
        <ticksBetweenMotes>100</ticksBetweenMotes>
        <positionRadius>0</positionRadius>
        <scale>0.8</scale>
        <positionOffset>(0, 0, -0.2)</positionOffset>
        <initialDelayTicks>8</initialDelayTicks>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <fleckDef>AtomizerFlashGrowing</fleckDef>
        <burstCount>1</burstCount>
        <ticksBetweenMotes>100</ticksBetweenMotes>
        <positionRadius>0</positionRadius>
        <scale>1</scale>
        <positionOffset>(0, 0, -0.2)</positionOffset>
        <initialDelayTicks>11</initialDelayTicks>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <fleckDef>AtomizerSpark</fleckDef>
        <burstCount>3</burstCount>
        <ticksBetweenMotes>2</ticksBetweenMotes>
        <positionRadius>0.5</positionRadius>
        <rotation>-150~150</rotation>
        <positionOffset>(0, 0, 0.25)</positionOffset>
        <spawnLocType>OnSource</spawnLocType>
        <initialDelayTicks>13</initialDelayTicks>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>WastepackAtomizer_Working</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_Sustainer</subEffecterClass>
        <spawnLocType>OnSource</spawnLocType>
        <soundDef>WastepackAtomizer_Working</soundDef>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>AnimalWarcall_Cast</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <spawnLocType>OnSource</spawnLocType>
        <soundDef>AnimalWarcall_Cast</soundDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_AnimalWarcallFlash</moteDef>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_AnimalWarcallDistortion</moteDef>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>AnimalWarcall_CastOnTarget</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_AnimalWarcallTarget</moteDef>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_AnimalWarcallTargetFlash</moteDef>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>AnimalWarcall_Warmup</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <spawnLocType>OnSource</spawnLocType>
        <soundDef>AnimalWarcall_Warmup</soundDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <moteDef>Mote_AnimalWarcallWarmup</moteDef>
        <initialDelayTicks>20</initialDelayTicks>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>



  <EffecterDef>
    <defName>AnimalWarcallMentalState</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <rotation>0</rotation>
        <moteDef>Mote_AnimalWarcallMentalState</moteDef>
        <attachToSpawnThing>true</attachToSpawnThing>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

</Defs>