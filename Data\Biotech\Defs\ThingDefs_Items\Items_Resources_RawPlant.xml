<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingDef ParentName="PlantFoodRawBase">
    <defName>RawToxipotato</defName>
    <label>toxipotatoes</label>
    <description>Raw toxipotatoes. Once harvested, toxipotatoes are very dietarily similar to potatoes. However, when eaten raw, they have a higher chance of causing food poisoning.</description>
    <soundInteract>Grain_Drop</soundInteract>
    <soundDrop>Grain_Drop</soundDrop>
    <graphicData>
      <texPath>Things/Item/Resource/PlantFoodRaw/Toxipotato</texPath>
    </graphicData>
    <statBases>
      <MarketValue>0.75</MarketValue>
      <FoodPoisonChanceFixedHuman>0.04</FoodPoisonChanceFixedHuman>
    </statBases>
    <ingestible>
      <foodType>VegetableOrFruit</foodType>
    </ingestible>
    <comps>
      <li Class="CompProperties_Rottable">
        <daysToRotStart>60</daysToRotStart>
        <rotDestroys>true</rotDestroys>
      </li>
    </comps>
  </ThingDef>

</Defs>