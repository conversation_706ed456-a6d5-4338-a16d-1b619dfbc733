﻿<?xml version="1.0" encoding="utf-8"?>

<Defs>
  <ThinkTreeDef>
    <defName>FleshmassNucleus</defName>
    <thinkRoot Class="ThinkNode_Priority">
      <subNodes>
        <!-- Despawned -->
        <li Class="ThinkNode_Subtree">
          <treeDef>Despawned</treeDef>
        </li>
        
        <!-- Downed -->
        <li Class="ThinkNode_Subtree">
          <treeDef>Downed</treeDef>
        </li>
        
        <!-- Idle fallback -->
        <li Class="JobGiver_Idle" />
      </subNodes>
    </thinkRoot>
  </ThinkTreeDef>

  <ThinkTreeDef>
    <defName>FleshmassNucleusConstant</defName>
    <thinkRoot Class="ThinkNode_Priority">
      <subNodes>
        <!-- Despawned -->
        <li Class="ThinkNode_Subtree">
          <treeDef>Despawned</treeDef>
        </li>
      </subNodes>
    </thinkRoot>
  </ThinkTreeDef>
</Defs>
