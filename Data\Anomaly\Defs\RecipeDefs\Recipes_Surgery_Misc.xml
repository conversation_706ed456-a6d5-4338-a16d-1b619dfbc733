﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <RecipeDef ParentName="SurgeryFlesh">
    <defName>BlissLobotomy</defName>
    <label>bliss lobotomy</label>
    <description>Remove part of a person's brain, placing them in a state of bliss while rendering them incapable of intellectual and skilled labor. They will also learn more slowly, and be less likely to rebel or attempt a prison break.</description>
    <workerClass>Recipe_AddHediff</workerClass>
    <addsHediff>BlissLobotomy</addsHediff>
    <jobString>Lobotomizing TargetA.</jobString>
    <workAmount>1200</workAmount>
    <hideBodyPartNames>true</hideBodyPartNames>
    <targetsBodyPart>false</targetsBodyPart>
    <isViolation>true</isViolation>
    <deathOnFailedSurgeryChance>0.1</deathOnFailedSurgeryChance>
    <developmentalStageFilter>Child, Adult</developmentalStageFilter>
    <skillRequirements>
      <Medicine>4</Medicine>
    </skillRequirements>
    <researchPrerequisite>
      <li>BlissLobotomy</li>
    </researchPrerequisite>
    <ingredients>
      <li>
        <filter>
          <categories>
            <li>Medicine</li>
          </categories>
        </filter>
        <count>2</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>Bioferrite</li>
          </thingDefs>
        </filter>
        <count>30</count>
      </li>
    </ingredients>
    <fixedIngredientFilter>
      <thingDefs>
        <li>Bioferrite</li>
      </thingDefs>
      <categories>
        <li>Medicine</li>
      </categories>
    </fixedIngredientFilter>
  </RecipeDef>

  <RecipeDef ParentName="SurgeryFlesh">
    <defName>GhoulInfusion</defName>
    <label>ghoul infusion</label>
    <description>Infuse a living person with an archotech shard to create a terrifying creature known as a ghoul. Ghouls are ferocious melee fighters and are incapable of work. They eat raw meat to survive but never sleep. Hungry ghouls are dangerous.\n\nGhouls created by this operation will be a part of your colony.</description>
    <workerClass>Recipe_GhoulInfusion</workerClass>
    <jobString>Ghoulizing TargetA.</jobString>
    <workAmount>2000</workAmount>
    <hideBodyPartNames>true</hideBodyPartNames>
    <targetsBodyPart>false</targetsBodyPart>
    <isViolation>true</isViolation>
    <anesthetize>false</anesthetize>
    <surgeryOutcomeEffect IsNull="True" /> <!-- Always succeeds -->
    <deathOnFailedSurgeryChance>0</deathOnFailedSurgeryChance>
    <skillRequirements>
      <Medicine>4</Medicine>
    </skillRequirements>
    <researchPrerequisite>
      <li>GhoulInfusion</li>
    </researchPrerequisite>
    <ingredients>
      <li>
        <filter>
          <thingDefs>
            <li>Shard</li>
          </thingDefs>
        </filter>
        <count>1</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>Bioferrite</li>
          </thingDefs>
        </filter>
        <count>30</count>
      </li>
    </ingredients>
    <fixedIngredientFilter>
      <thingDefs>
        <li>Bioferrite</li>
        <li>Shard</li>
      </thingDefs>
    </fixedIngredientFilter>
  </RecipeDef>

  <RecipeDef ParentName="SurgeryFlesh">
    <defName>SurgicalInspection</defName>
    <label>surgical inspection</label>
    <description>An invasive surgery to find any hidden abnormalities within a patient.</description>
    <workerClass>Recipe_SurgicalInspection</workerClass>
    <jobString>Surgically inspecting TargetA.</jobString>
    <workAmount>2000</workAmount>
    <hideBodyPartNames>true</hideBodyPartNames>
    <targetsBodyPart>false</targetsBodyPart>
    <isViolation>false</isViolation>
    <deathOnFailedSurgeryChance>0</deathOnFailedSurgeryChance>
    <surgerySuccessChanceFactor>1.5</surgerySuccessChanceFactor>
    <uiIconPath>UI/Icons/SurgicalInspection</uiIconPath>
    <skillRequirements>
      <Medicine>3</Medicine>
    </skillRequirements>
    <ingredients>
      <li>
        <filter>
          <categories>
            <li>Medicine</li>
          </categories>
        </filter>
        <count>2</count>
      </li>
    </ingredients>
    <fixedIngredientFilter>
      <categories>
        <li>Medicine</li>
      </categories>
    </fixedIngredientFilter>
    <surgeryOutcomeEffect>SurgeryOutcomeMinorFailure</surgeryOutcomeEffect>
  </RecipeDef>

</Defs>