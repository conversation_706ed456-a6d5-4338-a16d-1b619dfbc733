<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <FleckDef ParentName="FleckBase_RandomGraphic">
    <defName>FleckBabyGiggling</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fadeInTime>0.1</fadeInTime>
    <solidTime>0.5</solidTime>
    <fadeOutTime>0.35</fadeOutTime>
    <growthRate>0.4</growthRate>
    <randomGraphics>
      <li>
        <texPath>Things/Mote/Childcare/BabyGiggling/BabyGigglingA</texPath>
        <graphicClass>Graphic_Fleck</graphicClass>
        <shaderType>Mote</shaderType>
        <drawSize>0.5</drawSize>
      </li>
      <li>
        <texPath>Things/Mote/Childcare/BabyGiggling/BabyGigglingB</texPath>
        <graphicClass>Graphic_Fleck</graphicClass>
        <shaderType>Mote</shaderType>
        <drawSize>0.5</drawSize>
      </li>
      <li>
        <texPath>Things/Mote/Childcare/BabyGiggling/BabyGigglingC</texPath>
        <graphicClass>Graphic_Fleck</graphicClass>
        <shaderType>Mote</shaderType>
        <drawSize>0.5</drawSize>
      </li>
      <li>
        <texPath>Things/Mote/Childcare/BabyGiggling/BabyGigglingD</texPath>
        <graphicClass>Graphic_Fleck</graphicClass>
        <shaderType>Mote</shaderType>
        <drawSize>0.5</drawSize>
      </li>
      <li>
        <texPath>Things/Mote/Childcare/BabyGiggling/BabyGigglingE</texPath>
        <graphicClass>Graphic_Fleck</graphicClass>
        <shaderType>Mote</shaderType>
        <drawSize>0.5</drawSize>
      </li>
      <li>
        <texPath>Things/Mote/Childcare/BabyGiggling/BabyGigglingF</texPath>
        <graphicClass>Graphic_Fleck</graphicClass>
        <shaderType>Mote</shaderType>
        <drawSize>0.5</drawSize>
      </li>
      <li>
        <texPath>Things/Mote/Childcare/BabyGiggling/BabyGigglingG</texPath>
        <graphicClass>Graphic_Fleck</graphicClass>
        <shaderType>Mote</shaderType>
        <drawSize>0.5</drawSize>
      </li>
    </randomGraphics>
  </FleckDef>

  <EffecterDef>
    <defName>BabyGiggling</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>FleckBabyGiggling</fleckDef>
        <positionRadiusMin>0.4</positionRadiusMin>
        <positionRadius>0.4</positionRadius>
        <chancePerTick>0.05</chancePerTick>
        <speed>0.4</speed>
        <angle>0</angle>
        <scale>0.7~1.0</scale>
        <avoidLastPositionRadius>0.3</avoidLastPositionRadius>
        <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>BabyGiggling</soundDef>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>BabyCrying</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>BabyCrying</soundDef>
      </li>
    </children>
  </EffecterDef>

  <FleckDef ParentName="FleckBase_RandomGraphic">
    <defName>FleckBabyCrying</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <fleckSystemClass>FleckSystemThrown</fleckSystemClass>
    <fadeInTime>0.05</fadeInTime>
    <solidTime>0.35</solidTime>
    <fadeOutTime>0.1</fadeOutTime>
    <growthRate>0.4</growthRate>
    <rotateTowardsMoveDirection>true</rotateTowardsMoveDirection>
    <randomGraphics>
      <li>
        <texPath>Things/Mote/Childcare/BabyCrying/BabyCryingA</texPath>
        <graphicClass>Graphic_Fleck</graphicClass>
        <shaderType>Mote</shaderType>
        <drawSize>0.7</drawSize>
      </li>
      <li>
        <texPath>Things/Mote/Childcare/BabyCrying/BabyCryingB</texPath>
        <graphicClass>Graphic_Fleck</graphicClass>
        <shaderType>Mote</shaderType>
        <drawSize>0.7</drawSize>
      </li>
      <li>
        <texPath>Things/Mote/Childcare/BabyCrying/BabyCryingC</texPath>
        <graphicClass>Graphic_Fleck</graphicClass>
        <shaderType>Mote</shaderType>
        <drawSize>0.7</drawSize>
      </li>
    </randomGraphics>
  </FleckDef>

</Defs>