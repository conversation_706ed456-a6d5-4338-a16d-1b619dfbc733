﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <BiomeDef>
    <defName>TropicalRainforest</defName>
    <label>tropical rainforest</label>
    <description>A thick, moist jungle, buzzing with animal life and infested with disease. Despite its visual beauty, this is a very dangerous biome. Choking overgrowth, aggressive animals, and constant sickness are why some explorers call this the "green hell".</description>
    <workerClass>BiomeWorker_TropicalRainforest</workerClass>
    <animalDensity>5.4</animalDensity>
    <plantDensity>0.90</plantDensity>
    <settlementSelectionWeight>0.7</settlementSelectionWeight>
    <movementDifficulty>2</movementDifficulty>
    <texture>World/Biomes/TropicalRainforest</texture>
    <forageability>1</forageability>
    <foragedFood>RawBerries</foragedFood>
    <wildPlantRegrowDays>15</wildPlantRegrowDays>
    <soundsAmbient>
      <li>Ambient_NightInsects_Jungle</li>
      <li>Ambient_DayInsects_Jungle</li>
    </soundsAmbient>
    <diseaseMtbDays>35</diseaseMtbDays>
    <diseases>
      <li>
        <diseaseInc>Disease_Malaria</diseaseInc>
        <commonality>160</commonality>
      </li>
      <li>
        <diseaseInc>Disease_SleepingSickness</diseaseInc>
        <commonality>140</commonality>
      </li>
      <li>
        <diseaseInc>Disease_Flu</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_Plague</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_GutWorms</diseaseInc>
        <commonality>80</commonality>
      </li>
      <li>
        <diseaseInc>Disease_MuscleParasites</diseaseInc>
        <commonality>80</commonality>
      </li>
      <li>
        <diseaseInc>Disease_FibrousMechanites</diseaseInc>
        <commonality>30</commonality>
      </li>
      <li>
        <diseaseInc>Disease_SensoryMechanites</diseaseInc>
        <commonality>30</commonality>
      </li>

      <li>
        <diseaseInc>Disease_AnimalFlu</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_AnimalPlague</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_OrganDecay</diseaseInc>
        <commonality>10</commonality>
      </li>
    </diseases>
    <terrainsByFertility>
      <li>
        <terrain>Soil</terrain>
        <min>-999</min>
        <max>0.87</max>
      </li>
      <li>
        <terrain>SoilRich</terrain>
        <min>0.87</min>
        <max>999</max>
      </li>
    </terrainsByFertility>
    <terrainPatchMakers>
      <li>
        <perlinFrequency>0.04</perlinFrequency>
        <thresholds>
          <li>
            <terrain>MarshyTerrain</terrain>
            <min>0.7</min>
            <max>999</max>
          </li>
        </thresholds>
      </li>
      <li>
        <perlinFrequency>0.035</perlinFrequency>
        <thresholds>
          <li>
            <terrain>MarshyTerrain</terrain>
            <min>0.64</min>
            <max>0.87</max>
          </li>
          <li>
            <terrain>Marsh</terrain>
            <min>0.87</min>
            <max>999</max>
          </li>
        </thresholds>
      </li>
    </terrainPatchMakers>
    <baseWeatherCommonalities>
      <Clear>18</Clear>
      <Fog>1</Fog>
      <Rain>2</Rain>
      <DryThunderstorm>0.3</DryThunderstorm>
      <RainyThunderstorm>1.7</RainyThunderstorm>
      <FoggyRain>1</FoggyRain>
      <SnowGentle>4</SnowGentle>
      <SnowHard>4</SnowHard>
      <GrayPall MayRequire="Ludeon.RimWorld.Anomaly">1</GrayPall>
    </baseWeatherCommonalities>
    <wildPlants>
      <Plant_Grass>3.0</Plant_Grass>
      <Plant_TallGrass>2.0</Plant_TallGrass>
      <Plant_ShrubLow>2.0</Plant_ShrubLow>
      <Plant_Alocasia>0.8</Plant_Alocasia>
      <Plant_TreeCecropia>0.3</Plant_TreeCecropia>
      <Plant_TreePalm>0.3</Plant_TreePalm>
      <Plant_TreeBamboo>0.3</Plant_TreeBamboo>
      <Plant_TreeTeak>0.2</Plant_TreeTeak>
      <Plant_Bush>0.15</Plant_Bush>
      <Plant_Clivia>0.07</Plant_Clivia>
      <Plant_Berry>0.05</Plant_Berry>
      <Plant_Rafflesia>0.0008</Plant_Rafflesia>
      <Plant_GrayGrass MayRequire="Ludeon.RimWorld.Biotech">2</Plant_GrayGrass>
      <Plant_RatPalm MayRequire="Ludeon.RimWorld.Biotech">0.3</Plant_RatPalm>
    </wildPlants>
    <wildAnimals>
      <GuineaPig>0.25</GuineaPig>
      <Rat>1</Rat>
      <Monkey>1</Monkey>
      <Boomalope>0.5</Boomalope>
      <Capybara>0.5</Capybara>
      <Cassowary>0.5</Cassowary>
      <Tortoise>0.5</Tortoise>
      <Chinchilla>0.5</Chinchilla>
      <WildBoar>0.5</WildBoar>
      <Elephant>0.5</Elephant>
      <Rhinoceros>0.5</Rhinoceros>
      <Alpaca>0.5</Alpaca>
      <Cobra>0.15</Cobra>
      <Panther>0.15</Panther>
    </wildAnimals>
    <pollutionWildAnimals MayRequire="Ludeon.RimWorld.Biotech">
      <Toxalope MayRequire="Ludeon.RimWorld.Biotech">0.4</Toxalope>
      <Rat>1</Rat>
      <Raccoon>0.5</Raccoon>
      <Boomrat>0.4</Boomrat>
      <Boomalope>0.1</Boomalope>
      <Warg>0.05</Warg>
    </pollutionWildAnimals>
    <allowedPackAnimals>
      <li>Muffalo</li>
      <li>Elephant</li>
      <li>Alpaca</li>
    </allowedPackAnimals>
  </BiomeDef>

  <BiomeDef>
    <defName>TropicalSwamp</defName>
    <label>tropical swamp</label>
    <description>A plant-choked, steamy swamp seething with parasites and pathogens. Much of the land is too marshy to build on. Difficult movement, aggressive animals, and rampant disease make living here a nightmare.</description>
    <workerClass>BiomeWorker_TropicalSwamp</workerClass>
    <animalDensity>6.5</animalDensity>
    <plantDensity>0.99</plantDensity>
    <settlementSelectionWeight>0.4</settlementSelectionWeight>
    <campSelectionWeight>0.9</campSelectionWeight>
    <movementDifficulty>4</movementDifficulty>
    <texture>World/Biomes/TropicalSwamp</texture>
    <forageability>0.75</forageability>
    <foragedFood>RawBerries</foragedFood>
    <wildPlantRegrowDays>13</wildPlantRegrowDays>
    <soundsAmbient>
      <li>Ambient_NightInsects_Jungle</li>
      <li>Ambient_DayInsects_Jungle</li>
    </soundsAmbient>
    <diseaseMtbDays>30</diseaseMtbDays>
    <diseases>
      <li>
        <diseaseInc>Disease_Malaria</diseaseInc>
        <commonality>160</commonality>
      </li>
      <li>
        <diseaseInc>Disease_SleepingSickness</diseaseInc>
        <commonality>140</commonality>
      </li>
      <li>
        <diseaseInc>Disease_Flu</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_Plague</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_GutWorms</diseaseInc>
        <commonality>80</commonality>
      </li>
      <li>
        <diseaseInc>Disease_MuscleParasites</diseaseInc>
        <commonality>80</commonality>
      </li>
      <li>
        <diseaseInc>Disease_FibrousMechanites</diseaseInc>
        <commonality>30</commonality>
      </li>
      <li>
        <diseaseInc>Disease_SensoryMechanites</diseaseInc>
        <commonality>30</commonality>
      </li>

      <li>
        <diseaseInc>Disease_AnimalFlu</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_AnimalPlague</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_OrganDecay</diseaseInc>
        <commonality>10</commonality>
      </li>
    </diseases>
    <terrainsByFertility>
      <li>
        <terrain>Soil</terrain>
        <min>-999</min>
        <max>0.87</max>
      </li>
      <li>
        <terrain>SoilRich</terrain>
        <min>0.87</min>
        <max>999</max>
      </li>
    </terrainsByFertility>
    <terrainPatchMakers>
      <li>
        <perlinFrequency>0.04</perlinFrequency>
        <thresholds>
          <li>
            <terrain>MarshyTerrain</terrain>
            <min>0.35</min>
            <max>999</max>
          </li>
        </thresholds>
      </li>
      <li>
        <perlinFrequency>0.027</perlinFrequency>
        <thresholds>
          <li>
            <terrain>SoilRich</terrain>
            <min>-0.1</min>
            <max>0.32</max>
          </li>
          <li>
            <terrain>Mud</terrain>
            <min>0.32</min>
            <max>0.6</max>
          </li>
          <li>
            <terrain>WaterShallow</terrain>
            <min>0.6</min>
            <max>999</max>
          </li>
        </thresholds>
      </li>
      <li>
        <perlinFrequency>0.035</perlinFrequency>
        <thresholds>
          <li>
            <terrain>MarshyTerrain</terrain>
            <min>0.24</min>
            <max>0.48</max>
          </li>
          <li>
            <terrain>Marsh</terrain>
            <min>0.48</min>
            <max>999</max>
          </li>
        </thresholds>
      </li>
    </terrainPatchMakers>
    <baseWeatherCommonalities>
      <Clear>18</Clear>
      <Fog>1</Fog>
      <Rain>2</Rain>
      <DryThunderstorm>0.3</DryThunderstorm>
      <RainyThunderstorm>1.7</RainyThunderstorm>
      <FoggyRain>1</FoggyRain>
      <SnowGentle>4</SnowGentle>
      <SnowHard>4</SnowHard>
      <GrayPall MayRequire="Ludeon.RimWorld.Anomaly">1</GrayPall>
    </baseWeatherCommonalities>
    <wildPlants>
      <Plant_TallGrass>4.8</Plant_TallGrass>
      <Plant_ShrubLow>2.0</Plant_ShrubLow>
      <Plant_TreeWillow>1.72</Plant_TreeWillow>
      <Plant_TreeCypress>1.72</Plant_TreeCypress>
      <Plant_TreeMaple>1.72</Plant_TreeMaple>
      <Plant_Chokevine>0.8</Plant_Chokevine>
      <Plant_Alocasia>0.8</Plant_Alocasia>
      <Plant_Bush>0.15</Plant_Bush>
      <Plant_Clivia>0.07</Plant_Clivia>
      <Plant_Berry>0.05</Plant_Berry>
      <Plant_Rafflesia>0.0008</Plant_Rafflesia>
      <Plant_RatPalm MayRequire="Ludeon.RimWorld.Biotech">1.5</Plant_RatPalm>
    </wildPlants>
    <wildAnimals>
      <GuineaPig>0.18</GuineaPig>
      <Rat>1</Rat>
      <Monkey>1</Monkey>
      <Boomalope>0.5</Boomalope>
      <Capybara>0.5</Capybara>
      <Cassowary>0.5</Cassowary>
      <Tortoise>0.5</Tortoise>
      <Chinchilla>0.5</Chinchilla>
      <WildBoar>0.5</WildBoar>
      <Elephant>0.5</Elephant>
      <Rhinoceros>0.5</Rhinoceros>
      <Alpaca>0.5</Alpaca>
      <Cobra>0.15</Cobra>
      <Panther>0.15</Panther>
    </wildAnimals>
    <pollutionWildAnimals MayRequire="Ludeon.RimWorld.Biotech">
      <Toxalope MayRequire="Ludeon.RimWorld.Biotech">0.4</Toxalope>
      <Rat>1</Rat>
      <Raccoon>0.5</Raccoon>
      <Boomalope>0.1</Boomalope>
    </pollutionWildAnimals>
    <allowedPackAnimals>
      <li>Muffalo</li>
      <li>Elephant</li>
      <li>Alpaca</li>
    </allowedPackAnimals>
  </BiomeDef>

</Defs>
