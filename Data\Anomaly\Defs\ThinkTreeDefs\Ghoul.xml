﻿<?xml version="1.0" encoding="utf-8"?>

<Defs>
  <ThinkTreeDef>
    <defName>Ghoul</defName>
    <thinkRoot Class="ThinkNode_Priority">
      <subNodes>
        <!-- Keep lying down if we have to -->
        <li Class="ThinkNode_ConditionalMustKeepLyingDown">
          <subNodes>
            <!-- Do a queued job if possible -->
            <li Class="ThinkNode_QueuedJob">
              <inBedOnly>true</inBedOnly>
            </li>

            <!-- Keep lying down -->
            <li Class="JobGiver_KeepLyingDown" />
          </subNodes>
        </li>

        <li Class="ThinkNode_Subtree">
          <treeDef>Downed</treeDef>
        </li>
        <li Class="ThinkNode_Subtree">
          <treeDef>BurningResponse</treeDef>
        </li>
        <li Class="ThinkNode_Subtree">
          <treeDef>MentalStateCritical</treeDef>
        </li>

        <!-- Escaping-->
        <li Class="ThinkNodeConditional_EscapingHoldingPlatform">
          <subNodes>
            <li Class="JobGiver_EscapingHoldingPlatform" />
          </subNodes>
        </li>

        <!-- React to close melee threat -->
        <li Class="JobGiver_ReactToCloseMeleeThreat" />

        <!-- Do a queued job -->
        <li Class="ThinkNode_QueuedJob" />

        <!-- Mental state non critical -->
        <li Class="ThinkNode_Subtree">
          <treeDef>MentalStateNonCritical</treeDef>
        </li>

        <!-- Wait if drafted -->
        <li Class="ThinkNode_ConditionalOfPlayerFaction">
          <subNodes>
            <li Class="ThinkNode_Tagger">
              <tagToGive>DraftedOrder</tagToGive>
              <subNodes>
                <li Class="JobGiver_MoveToStandable" />
                <li Class="JobGiver_Orders" />
              </subNodes>
            </li>
          </subNodes>
        </li>

        <!-- Lord directives -->
        <li Class="ThinkNode_Subtree">
          <treeDef>LordDuty</treeDef>
        </li>

        <!-- Insertion hook for modders -->
        <li Class="ThinkNode_SubtreesByTag">
          <insertTag>Ghoul_PreMain</insertTag>
        </li>

        <!-- Seek allowed area -->
        <li Class="JobGiver_SeekAllowedArea" />
        <li Class="JobGiver_SeekSafeTemperature">
          <requiresInjury>false</requiresInjury>
          <waitInSafeTemp>false</waitInSafeTemp>
          <maxRadius>15</maxRadius>
        </li>

        <!-- Fight enemies -->
        <li Class="JobGiver_AIFightEnemies">
          <targetAcquireRadius>30</targetAcquireRadius>
          <targetKeepRadius>35</targetKeepRadius>
        </li>

        <!-- Resting for medical reasons -->
        <li Class="ThinkNode_ConditionalOfPlayerFaction">
          <subNodes>
            <li Class="ThinkNode_Tagger">
              <tagToGive>RestingForMedicalReasons</tagToGive>
              <subNodes>
                <li Class="JobGiver_PatientGoToBed">
                  <urgentOnly>true</urgentOnly>
                </li>
              </subNodes>
            </li>
          </subNodes>
        </li>

        <!-- Satisfy basic needs -->
        <li Class="ThinkNode_Subtree">
          <treeDef>SatisfyBasicNeeds</treeDef>
        </li>

        <!-- Insertion hook for modders -->
        <li Class="ThinkNode_SubtreesByTag">
          <insertTag>Ghoul_PreWander</insertTag>
        </li>

        <!-- Wander -->
        <li Class="ThinkNode_Tagger">
          <subNodes>
            <li Class="JobGiver_ShamblerWander">
              <wanderRadius>12</wanderRadius>
            </li>
          </subNodes>
        </li>

        <li Class="JobGiver_IdleError"/>
      </subNodes>
    </thinkRoot>
  </ThinkTreeDef>

  <ThinkTreeDef>
    <defName>GhoulConstant</defName>
    <thinkRoot Class="ThinkNode_Priority">
      <subNodes>
        <!-- Despawned -->
        <li Class="ThinkNode_Subtree">
          <treeDef>Despawned</treeDef>
        </li>

        <li Class="ThinkNode_ConditionalCanDoConstantThinkTreeJobNow">
          <subNodes>
            <!-- Join auto joinable caravan -->
            <li Class="ThinkNode_Subtree">
              <treeDef>JoinAutoJoinableCaravan</treeDef>
            </li>

            <!-- Hostility response -->
            <li Class="JobGiver_ConfigurableHostilityResponse" />
          </subNodes>
        </li>

        <li Class="ThinkNode_ConditionalCanDoLordJobNow">
          <subNodes>
            <!-- Lord directives -->
            <li Class="ThinkNode_Subtree">
              <treeDef>LordDutyConstant</treeDef>
            </li>
          </subNodes>
        </li>
      </subNodes>
    </thinkRoot>
  </ThinkTreeDef>
</Defs>