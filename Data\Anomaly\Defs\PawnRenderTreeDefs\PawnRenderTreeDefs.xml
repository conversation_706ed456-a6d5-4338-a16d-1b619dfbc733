﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <!-- Fleshbeasts -->

  <PawnRenderTreeDef>
    <defName>Bulbfreak</defName>
    <root Class="PawnRenderNodeProperties_Parent">
      <debugLabel>Root</debugLabel>
      <tagDef>Root</tagDef>
      <children>
        <li>
          <debugLabel>Body</debugLabel>
          <tagDef>Body</tagDef>
          <useRottenColor>true</useRottenColor>
          <nodeClass>PawnRenderNode_AnimalPart</nodeClass>
          <children>
            <li Class="PawnRenderNodeProperties_BulbfreakTentacle">
              <debugLabel>TentacleA</debugLabel>
              <texPath>Things/Pawn/Fleshbeast/Bulbfreak/Tentacles/BulbfreakTentacleA</texPath>
              <drawSize>1.1</drawSize>
              <useRottenColor>true</useRottenColor>
              <linkedBodyPartsGroup>FirstTentacle</linkedBodyPartsGroup>
              <drawData>
                <defaultData>
                  <pivot>(0.5, 0.95)</pivot>
                  <layer>-7</layer>
                </defaultData>
                <dataNorth><offset>(-0.2, 0, 1.15)</offset></dataNorth>
                <dataEast><offset>(0.6, 0, 0.15)</offset></dataEast>
                <dataSouth>
                  <offset>(0.2, 0, -0.15)</offset>
                  <layer>10</layer>
                </dataSouth>
              </drawData>
            </li>
            <li Class="PawnRenderNodeProperties_BulbfreakTentacle">
              <debugLabel>TentacleB</debugLabel>
              <texPath>Things/Pawn/Fleshbeast/Bulbfreak/Tentacles/BulbfreakTentacleB</texPath>
              <useRottenColor>true</useRottenColor>
              <linkedBodyPartsGroup>SecondTentacle</linkedBodyPartsGroup>
              <drawData>
                <defaultData>
                  <pivot>(0.5, 0.95)</pivot>
                  <layer>-8</layer>
                </defaultData>
                <dataNorth><offset>(-0.4, 0, 1.125)</offset></dataNorth>
                <dataEast><offset>(0.4, 0, 0)</offset></dataEast>
                <dataSouth>
                  <offset>(0.4, 0.02, -0.125)</offset>
                  <layer>13</layer>
                </dataSouth>
              </drawData>
            </li>
            <li Class="PawnRenderNodeProperties_BulbfreakTentacle">
              <debugLabel>TentacleC</debugLabel>
              <texPath>Things/Pawn/Fleshbeast/Bulbfreak/Tentacles/BulbfreakTentacleA</texPath>
              <useRottenColor>true</useRottenColor>
              <linkedBodyPartsGroup>ThirdTentacle</linkedBodyPartsGroup>
              <drawSize>1.1</drawSize>
              <drawData>
                <defaultData>
                  <pivot>(0.5, 0.95)</pivot>
                  <layer>-7</layer>
                </defaultData>
                <dataNorth><offset>(0.3, 0, 1.15)</offset></dataNorth>
                <dataEast><offset>(0.7, 0, 0.1)</offset></dataEast>
                <dataSouth>
                  <offset>(-0.3, 0, -0.15)</offset>
                  <layer>10</layer>
                </dataSouth>
              </drawData>
            </li>
            <li Class="PawnRenderNodeProperties_BulbfreakTentacle">
              <debugLabel>TentacleD</debugLabel>
              <texPath>Things/Pawn/Fleshbeast/Bulbfreak/Tentacles/BulbfreakTentacleB</texPath>
              <useRottenColor>true</useRottenColor>
              <drawSize>1.05</drawSize>
              <linkedBodyPartsGroup>FourthTentacle</linkedBodyPartsGroup>
              <drawData>
                <defaultData>
                  <pivot>(0.5, 0.95)</pivot>
                  <layer>-10</layer>
                  <flip>true</flip>
                </defaultData>
                <dataNorth><offset>(0.45, 0, 1.18)</offset></dataNorth>
                <dataEast><offset>(0.55, 0, -0.05)</offset></dataEast>
                <dataSouth>
                  <offset>(-0.45, 0, -0.08)</offset>
                  <layer>15</layer>
                </dataSouth>
              </drawData>
            </li>
            <li Class="PawnRenderNodeProperties_BulbfreakTentacle">
              <debugLabel>TentacleE</debugLabel>
              <texPath>Things/Pawn/Fleshbeast/Bulbfreak/Tentacles/BulbfreakTentacleC</texPath>
              <useRottenColor>true</useRottenColor>
              <linkedBodyPartsGroup>FifthTentacle</linkedBodyPartsGroup>
              <drawData>
                <defaultData>
                  <pivot>(0.5, 0.95)</pivot>
                  <layer>-9</layer>
                  <flip>true</flip>
                </defaultData>
                <dataNorth><offset>(0.25, 0, 1.1)</offset></dataNorth>
                <dataEast><offset>(0.53, 0, 0.17)</offset></dataEast>
                <dataSouth>
                  <offset>(-0.25, 0, -0.1)</offset>
                  <layer>14</layer>
                </dataSouth>
              </drawData>
            </li>
            <li Class="PawnRenderNodeProperties_BulbfreakTentacle">
              <debugLabel>TentacleF</debugLabel>
              <texPath>Things/Pawn/Fleshbeast/Bulbfreak/Tentacles/BulbfreakTentacleA</texPath>
              <useRottenColor>true</useRottenColor>
              <drawSize>1.2</drawSize>
              <linkedBodyPartsGroup>FirstTentacle</linkedBodyPartsGroup>
              <drawData>
                <defaultData>
                  <pivot>(0.5, 0.95)</pivot>
                  <layer>-9</layer>
                </defaultData>
                <dataNorth><offset>(0.5, 0, 1.1)</offset></dataNorth>
                <dataEast><offset>(0.51, 0, 0.3)</offset></dataEast>
                <dataSouth>
                  <offset>(-0.5, 0, -0.1)</offset>
                  <layer>14</layer>
                </dataSouth>
              </drawData>
            </li>
            <li Class="PawnRenderNodeProperties_BulbfreakTentacle">
              <debugLabel>TentacleG</debugLabel>
              <texPath>Things/Pawn/Fleshbeast/Bulbfreak/Tentacles/BulbfreakTentacleB</texPath>
              <useRottenColor>true</useRottenColor>
              <drawSize>1.05</drawSize>
              <linkedBodyPartsGroup>SecondTentacle</linkedBodyPartsGroup>
              <drawData>
                <defaultData>
                  <pivot>(0.5, 0.95)</pivot>
                  <layer>-7</layer>
                </defaultData>
                <dataNorth><offset>(0.05, 0, 1.18)</offset></dataNorth>
                <dataEast><offset>(0.55, 0, 0.4)</offset></dataEast>
                <dataSouth>
                  <offset>(-0.05, 0, -0.18)</offset>
                  <layer>10</layer>
                </dataSouth>
              </drawData>
            </li>
            <li Class="PawnRenderNodeProperties_BulbfreakTentacle">
              <debugLabel>TentacleH</debugLabel>
              <texPath>Things/Pawn/Fleshbeast/Bulbfreak/Tentacles/BulbfreakTentacleC</texPath>
              <useRottenColor>true</useRottenColor>
              <drawSize>1.05</drawSize>
              <linkedBodyPartsGroup>ThirdTentacle</linkedBodyPartsGroup>
              <drawData>
                <defaultData>
                  <pivot>(0.5, 0.95)</pivot>
                  <layer>-2</layer>
                  <flip>true</flip>
                </defaultData>
                <dataNorth><offset>(-0.35, 0, 1.2)</offset></dataNorth>
                <dataEast><offset>(0.05, 0, 0.36)</offset></dataEast>
                <dataSouth>
                  <offset>(0.35, 0, -0.2)</offset>
                  <layer>5</layer>
                </dataSouth>
              </drawData>
            </li>
            <li Class="PawnRenderNodeProperties_BulbfreakTentacle">
              <debugLabel>TentacleI</debugLabel>
              <texPath>Things/Pawn/Fleshbeast/Bulbfreak/Tentacles/BulbfreakTentacleA</texPath>
              <useRottenColor>true</useRottenColor>
              <linkedBodyPartsGroup>FourthTentacle</linkedBodyPartsGroup>
              <drawData>
                <defaultData>
                  <pivot>(0.5, 0.95)</pivot>
                  <layer>-7</layer>
                  <flip>true</flip>
                </defaultData>
                <dataNorth><offset>(-0.05, 0, 1.15)</offset></dataNorth>
                <dataEast><offset>(0.1, 0, 0.4)</offset></dataEast>
                <dataSouth>
                  <offset>(0.1, 0, -0.15)</offset>
                  <layer>10</layer>
                </dataSouth>
              </drawData>
            </li>
            <li Class="PawnRenderNodeProperties_BulbfreakTentacle">
              <debugLabel>TentacleJ</debugLabel>
              <texPath>Things/Pawn/Fleshbeast/Bulbfreak/Tentacles/BulbfreakTentacleB</texPath>
              <useRottenColor>true</useRottenColor>
              <linkedBodyPartsGroup>FifthTentacle</linkedBodyPartsGroup>
              <drawData>
                <defaultData>
                  <pivot>(0.5, 0.95)</pivot>
                  <layer>-10</layer>
                  <flip>true</flip>
                </defaultData>
                <dataNorth><offset>(-0.5, 0, 1.03)</offset></dataNorth>
                <dataEast><offset>(0.1, 0, 0.3)</offset></dataEast>
                <dataSouth>
                  <offset>(0.5, 0, -0.03)</offset>
                  <layer>15</layer>
                </dataSouth>
              </drawData>
            </li>
            <li>
              <debugLabel>Wounds</debugLabel>
              <nodeClass>PawnRenderNode_AnimalPart</nodeClass>
              <workerClass>PawnRenderNodeWorker_OverlayWounds</workerClass>
              <overlayLayer>Body</overlayLayer>
              <useGraphic>false</useGraphic>
              <baseLayer>10</baseLayer>
            </li>
            <li>
              <debugLabel>Firefoam</debugLabel>
              <nodeClass>PawnRenderNode_AnimalPart</nodeClass>
              <workerClass>PawnRenderNodeWorker_OverlayFirefoam</workerClass>
              <overlayLayer>Body</overlayLayer>
              <useGraphic>false</useGraphic>
              <baseLayer>20</baseLayer>
            </li>
          </children>
        </li>
        <li Class="PawnRenderNodeProperties_Carried">
          <debugLabel>Carried thing</debugLabel>
          <workerClass>PawnRenderNodeWorker_Carried</workerClass>
        </li>
      </children>
    </root>
  </PawnRenderTreeDef>

  <PawnRenderTreeDef>
    <defName>Toughspike</defName>
    <root Class="PawnRenderNodeProperties_Parent">
      <debugLabel>Root</debugLabel>
      <tagDef>Root</tagDef>
      <children>
        <li>
          <debugLabel>Body</debugLabel>
          <tagDef>Body</tagDef>
          <useRottenColor>true</useRottenColor>
          <nodeClass>PawnRenderNode_AnimalPart</nodeClass>
          <children>
            <li Class="PawnRenderNodeProperties_Spastic">
              <debugLabel>SpikeL</debugLabel>
              <linkedBodyPartsGroup>LeftSpike</linkedBodyPartsGroup>
              <texPath>Things/Pawn/Fleshbeast/Toughspike/Attachments/Toughspike_Spike</texPath>
              <rotDrawMode>Fresh, Rotting</rotDrawMode>
              <rotateFacing>false</rotateFacing>
              <rotationRange>-30~30</rotationRange>
              <durationTicksRange>10~35</durationTicksRange>
              <nextSpasmTicksRange>10~50</nextSpasmTicksRange>
              <drawSize>1.4</drawSize>
              <baseLayer>10</baseLayer>
              <useRottenColor>true</useRottenColor>
              <drawData>
                <dataNorth>
                  <offset>(-0.25, 0.01, 0.25)</offset>
                  <rotationOffset>-45</rotationOffset>
                </dataNorth>
                <dataEast>
                  <offset>(0, 0, 0.15)</offset>
                  <layer>-5</layer>
                </dataEast>
                <dataSouth>
                  <offset>(0.15, 0, 0.3)</offset>
                  <rotationOffset>45</rotationOffset>
                </dataSouth>
                <dataWest>
                  <offset>(0, 0, 0.15)</offset>
                  <flip>true</flip>
                </dataWest>
              </drawData>
            </li>
            <li Class="PawnRenderNodeProperties_Spastic">
              <debugLabel>SpikeR</debugLabel>
              <linkedBodyPartsGroup>RightSpike</linkedBodyPartsGroup>
              <texPath>Things/Pawn/Fleshbeast/Toughspike/Attachments/Toughspike_Spike</texPath>
              <rotDrawMode>Fresh, Rotting</rotDrawMode>
              <rotateFacing>false</rotateFacing>
              <rotationRange>-30~30</rotationRange>
              <durationTicksRange>10~35</durationTicksRange>
              <nextSpasmTicksRange>10~50</nextSpasmTicksRange>
              <drawSize>1.4</drawSize>
              <baseLayer>10</baseLayer>
              <useRottenColor>true</useRottenColor>
              <drawData>
                <dataNorth>
                  <offset>(0.25, 0.01, 0.25)</offset>
                  <rotationOffset>45</rotationOffset>
                  <flip>true</flip>
                </dataNorth>
                <dataEast>
                  <offset>(0, 0, 0.15)</offset>
                </dataEast>
                <dataSouth>
                  <offset>(-0.15, 0, 0.3)</offset>
                  <rotationOffset>-45</rotationOffset>
                  <flip>true</flip>
                </dataSouth>
                <dataWest>
                  <offset>(0, 0, 0.15)</offset>
                  <layer>-5</layer>
                  <flip>true</flip>
                </dataWest>
              </drawData>
            </li>
            <li>
              <debugLabel>Wounds</debugLabel>
              <nodeClass>PawnRenderNode_AnimalPart</nodeClass>
              <workerClass>PawnRenderNodeWorker_OverlayWounds</workerClass>
              <overlayLayer>Body</overlayLayer>
              <useGraphic>false</useGraphic>
              <baseLayer>10</baseLayer>
            </li>
            <li>
              <debugLabel>Firefoam</debugLabel>
              <nodeClass>PawnRenderNode_AnimalPart</nodeClass>
              <workerClass>PawnRenderNodeWorker_OverlayFirefoam</workerClass>
              <overlayLayer>Body</overlayLayer>
              <useGraphic>false</useGraphic>
              <baseLayer>20</baseLayer>
            </li>
          </children>
        </li>
        <li Class="PawnRenderNodeProperties_Carried">
          <debugLabel>Carried thing</debugLabel>
          <workerClass>PawnRenderNodeWorker_Carried</workerClass>
        </li>
      </children>
    </root>
  </PawnRenderTreeDef>

  <PawnRenderTreeDef>
    <defName>Trispike</defName>
    <root Class="PawnRenderNodeProperties_Parent">
      <debugLabel>Root</debugLabel>
      <tagDef>Root</tagDef>
      <children>
        <li>
          <debugLabel>Body</debugLabel>
          <tagDef>Body</tagDef>
          <useRottenColor>true</useRottenColor>
          <nodeClass>PawnRenderNode_AnimalPart</nodeClass>
          <children>
            <li Class="PawnRenderNodeProperties_Spastic">
              <debugLabel>SpikeL</debugLabel>
              <texPath>Things/Pawn/Fleshbeast/Trispike/Attachments/Side/Trispike_SideLimb</texPath>
              <linkedBodyPartsGroup>LeftSpike</linkedBodyPartsGroup>
              <rotDrawMode>Fresh, Rotting</rotDrawMode>
              <rotateFacing>false</rotateFacing>
              <rotationRange>-25~25</rotationRange>
              <scaleRange>0.9~1.1</scaleRange>
              <durationTicksRange>15~45</durationTicksRange>
              <nextSpasmTicksRange>5~35</nextSpasmTicksRange>
              <drawSize>1.4</drawSize>
              <baseLayer>10</baseLayer>
              <useRottenColor>true</useRottenColor>
              <drawData>
                <dataNorth>
                  <rotationOffset>-20</rotationOffset>
                  <offset>(-0.25, 0, 0.05)</offset>
                </dataNorth>
                <dataEast>
                  <rotationOffset>245</rotationOffset>
                  <layer>-5</layer>
                </dataEast>
                <dataSouth>
                  <rotationOffset>35</rotationOffset>
                  <offset>(0.25, 0, 0.05)</offset>
                </dataSouth>
                <dataWest>
                  <offset>(0, 0, 0.1)</offset>
                  <flip>true</flip>
                </dataWest>
              </drawData>
            </li>
            <li Class="PawnRenderNodeProperties_Spastic">
              <debugLabel>SpikeR</debugLabel>
              <texPath>Things/Pawn/Fleshbeast/Trispike/Attachments/Side/Trispike_SideLimb</texPath>
              <linkedBodyPartsGroup>RightSpike</linkedBodyPartsGroup>
              <rotDrawMode>Fresh, Rotting</rotDrawMode>
              <rotateFacing>false</rotateFacing>
              <rotationRange>-25~25</rotationRange>
              <scaleRange>0.9~1.1</scaleRange>
              <durationTicksRange>15~45</durationTicksRange>
              <nextSpasmTicksRange>5~35</nextSpasmTicksRange>
              <drawSize>1.45</drawSize>
              <baseLayer>10</baseLayer>
              <useRottenColor>true</useRottenColor>
              <drawData>
                <dataNorth>
                  <rotationOffset>160</rotationOffset>
                  <offset>(0.2, 0, 0.05)</offset>
                </dataNorth>
                <dataEast>
                  <offset>(0, 0, 0.1)</offset>
                </dataEast>
                <dataSouth>
                  <rotationOffset>180</rotationOffset>
                  <offset>(-0.25, 0, 0)</offset>
                </dataSouth>
                <dataWest>
                  <rotationOffset>120</rotationOffset>
                  <layer>-5</layer>
                  <flip>true</flip>
                </dataWest>
              </drawData>
            </li>
            <li Class="PawnRenderNodeProperties_Spastic">
              <debugLabel>SpikeTop</debugLabel>
              <texPath>Things/Pawn/Fleshbeast/Trispike/Attachments/Top/Trispike_TopLimb</texPath>
              <linkedBodyPartsGroup>MiddleSpike</linkedBodyPartsGroup>
              <rotDrawMode>Fresh, Rotting</rotDrawMode>
              <rotateFacing>false</rotateFacing>
              <rotationRange>-30~30</rotationRange>
              <scaleRange>0.95~1.05</scaleRange>
              <durationTicksRange>10~60</durationTicksRange>
              <nextSpasmTicksRange>20~60</nextSpasmTicksRange>
              <drawSize>1.4</drawSize>
              <baseLayer>10</baseLayer>
              <useRottenColor>true</useRottenColor>
              <drawData>
                <defaultData><offset>(0, 0, 0.3)</offset></defaultData>
                <dataNorth><rotationOffset>60</rotationOffset></dataNorth>
                <dataSouth><rotationOffset>-60</rotationOffset></dataSouth>
                <dataWest><flip>true</flip></dataWest>
              </drawData>
            </li>
            <li>
              <debugLabel>Wounds</debugLabel>
              <nodeClass>PawnRenderNode_AnimalPart</nodeClass>
              <workerClass>PawnRenderNodeWorker_OverlayWounds</workerClass>
              <overlayLayer>Body</overlayLayer>
              <useGraphic>false</useGraphic>
              <baseLayer>10</baseLayer>
            </li>
            <li>
              <debugLabel>Firefoam</debugLabel>
              <nodeClass>PawnRenderNode_AnimalPart</nodeClass>
              <workerClass>PawnRenderNodeWorker_OverlayFirefoam</workerClass>
              <overlayLayer>Body</overlayLayer>
              <useGraphic>false</useGraphic>
              <baseLayer>20</baseLayer>
            </li>
          </children>
        </li>
        <li Class="PawnRenderNodeProperties_Carried">
          <debugLabel>Carried thing</debugLabel>
          <workerClass>PawnRenderNodeWorker_Carried</workerClass>
        </li>
      </children>
    </root>
  </PawnRenderTreeDef>

  <PawnRenderTreeDef>
    <defName>Fingerspike</defName>
    <root Class="PawnRenderNodeProperties_Parent">
      <debugLabel>Root</debugLabel>
      <tagDef>Root</tagDef>
      <children>
        <li>
          <debugLabel>Body</debugLabel>
          <tagDef>Body</tagDef>
          <useRottenColor>true</useRottenColor>
          <nodeClass>PawnRenderNode_AnimalPart</nodeClass>
          <children>
            <li Class="PawnRenderNodeProperties_Spastic">
              <debugLabel>Spike</debugLabel>
              <texPath>Things/Pawn/Fleshbeast/Fingerspike/Attachments/Fingerspike_TopLimb</texPath>
              <linkedBodyPartsGroup>MiddleSpike</linkedBodyPartsGroup>
              <rotDrawMode>Fresh, Rotting</rotDrawMode>
              <rotateFacing>false</rotateFacing>
              <rotationRange>-18~18</rotationRange>
              <durationTicksRange>15~45</durationTicksRange>
              <nextSpasmTicksRange>5~30</nextSpasmTicksRange>
              <scaleRange>0.95~1.05</scaleRange>
              <drawSize>1.4</drawSize>
              <baseLayer>10</baseLayer>
              <useRottenColor>true</useRottenColor>
              <drawData>
                <dataNorth>
                  <rotationOffset>-15</rotationOffset>
                  <offset>(0, 0, 0.3)</offset>
                </dataNorth>
                <dataEast><offset>(0.25, 0, 0.1)</offset></dataEast>
                <dataSouth>
                  <rotationOffset>15</rotationOffset>
                  <offset>(0, 0, -0.2)</offset>
                  <flip>true</flip>
                </dataSouth>
                <dataWest><flip>true</flip></dataWest>
              </drawData>
            </li>
            <li>
              <debugLabel>Wounds</debugLabel>
              <nodeClass>PawnRenderNode_AnimalPart</nodeClass>
              <workerClass>PawnRenderNodeWorker_OverlayWounds</workerClass>
              <overlayLayer>Body</overlayLayer>
              <baseLayer>10</baseLayer>
            </li>
            <li>
              <debugLabel>Firefoam</debugLabel>
              <nodeClass>PawnRenderNode_AnimalPart</nodeClass>
              <workerClass>PawnRenderNodeWorker_OverlayFirefoam</workerClass>
              <overlayLayer>Body</overlayLayer>
              <useGraphic>false</useGraphic>
              <baseLayer>20</baseLayer>
            </li>
          </children>
        </li>
        <li Class="PawnRenderNodeProperties_Carried">
          <debugLabel>Carried thing</debugLabel>
          <workerClass>PawnRenderNodeWorker_Carried</workerClass>
        </li>
      </children>
    </root>
  </PawnRenderTreeDef>

  <PawnRenderTreeDef>
    <defName>Dreadmeld</defName>
    <root Class="PawnRenderNodeProperties_Parent">
      <debugLabel>Root</debugLabel>
      <tagDef>Root</tagDef>
      <children>
        <li>
          <debugLabel>Body</debugLabel>
          <tagDef>Body</tagDef>
          <useRottenColor>true</useRottenColor>
          <nodeClass>PawnRenderNode_AnimalPart</nodeClass>
          <children>
            <li Class="PawnRenderNodeProperties_BulbfreakTentacle">
              <debugLabel>TentacleA</debugLabel>
              <texPath>Things/Pawn/Fleshbeast/Bulbfreak/Tentacles/BulbfreakTentacleA</texPath>
              <linkedBodyPartsGroup>FirstTentacle</linkedBodyPartsGroup>
              <drawSize>1.2</drawSize>
              <useRottenColor>true</useRottenColor>
              <drawData>
                <defaultData>
                  <pivot>(0.5, 0.95)</pivot>
                  <layer>-7</layer>
                  <rotationOffset>180</rotationOffset>
                </defaultData>
                <dataWest><offset>(1.1, 0, 0.4)</offset></dataWest>
                <dataNorth>
                  <offset>(0.2, 0, 0.15)</offset>
                  <layer>10</layer>
                </dataNorth>
              </drawData>
            </li>
            <li Class="PawnRenderNodeProperties_BulbfreakTentacle">
              <debugLabel>TentacleB</debugLabel>
              <texPath>Things/Pawn/Fleshbeast/Bulbfreak/Tentacles/BulbfreakTentacleB</texPath>
              <drawSize>1.2</drawSize>
              <useRottenColor>true</useRottenColor>
              <linkedBodyPartsGroup>SecondTentacle</linkedBodyPartsGroup>
              <drawData>
                <defaultData>
                  <pivot>(0.5, 0.95)</pivot>
                  <layer>-8</layer>
                  <rotationOffset>180</rotationOffset>
                </defaultData>
                <dataWest><offset>(0.9, 0, 0.6)</offset></dataWest>
                <dataNorth>
                  <offset>(0.4, 0.02, 0.125)</offset>
                  <layer>13</layer>
                </dataNorth>
              </drawData>
            </li>
            <li Class="PawnRenderNodeProperties_BulbfreakTentacle">
              <debugLabel>TentacleC</debugLabel>
              <texPath>Things/Pawn/Fleshbeast/Bulbfreak/Tentacles/BulbfreakTentacleA</texPath>
              <drawSize>1.2</drawSize>
              <useRottenColor>true</useRottenColor>
              <linkedBodyPartsGroup>ThirdTentacle</linkedBodyPartsGroup>
              <drawData>
                <defaultData>
                  <pivot>(0.5, 0.95)</pivot>
                  <layer>-7</layer>
                  <rotationOffset>180</rotationOffset>
                </defaultData>
                <dataWest><offset>(1.05, 0, 0.4)</offset></dataWest>
                <dataNorth>
                  <offset>(-0.3, 0, 0.15)</offset>
                  <layer>10</layer>
                </dataNorth>
              </drawData>
            </li>
            <li Class="PawnRenderNodeProperties_BulbfreakTentacle">
              <debugLabel>TentacleD</debugLabel>
              <texPath>Things/Pawn/Fleshbeast/Bulbfreak/Tentacles/BulbfreakTentacleB</texPath>
              <drawSize>1.2</drawSize>
              <useRottenColor>true</useRottenColor>
              <linkedBodyPartsGroup>FirstTentacle</linkedBodyPartsGroup>
              <drawData>
                <defaultData>
                  <pivot>(0.5, 0.95)</pivot>
                  <layer>-10</layer>
                  <flip>true</flip>
                  <rotationOffset>180</rotationOffset>
                </defaultData>
                <dataWest><offset>(1, 0, 0.5)</offset></dataWest>
                <dataNorth>
                  <offset>(-0.45, 0, 0.08)</offset>
                  <layer>15</layer>
                </dataNorth>
              </drawData>
            </li>
            <li Class="PawnRenderNodeProperties_BulbfreakTentacle">
              <debugLabel>TentacleE</debugLabel>
              <texPath>Things/Pawn/Fleshbeast/Bulbfreak/Tentacles/BulbfreakTentacleC</texPath>
              <drawSize>1.2</drawSize>
              <useRottenColor>true</useRottenColor>
              <linkedBodyPartsGroup>SecondTentacle</linkedBodyPartsGroup>
              <drawData>
                <defaultData>
                  <pivot>(0.5, 0.95)</pivot>
                  <layer>-9</layer>
                  <flip>true</flip>
                  <rotationOffset>180</rotationOffset>
                </defaultData>
                <dataWest><offset>(0.95, 0, 0.4)</offset></dataWest>
                <dataNorth>
                  <offset>(-0.25, 0, 0.1)</offset>
                  <layer>14</layer>
                </dataNorth>
              </drawData>
            </li>
            <li Class="PawnRenderNodeProperties_Spastic">
              <debugLabel>ToughSpikeL</debugLabel>
              <texPath>Things/Pawn/Fleshbeast/Toughspike/Attachments/Toughspike_Spike</texPath>
              <rotDrawMode>Fresh, Rotting</rotDrawMode>
              <rotateFacing>false</rotateFacing>
              <rotationRange>-30~30</rotationRange>
              <durationTicksRange>10~35</durationTicksRange>
              <nextSpasmTicksRange>10~50</nextSpasmTicksRange>
              <drawSize>1.4</drawSize>
              <baseLayer>10</baseLayer>
              <useRottenColor>true</useRottenColor>
              <linkedBodyPartsGroup>LeftSpike</linkedBodyPartsGroup>
              <drawData>
                <dataNorth>
                  <layer>-5</layer>
                </dataNorth>
                <dataEast>
                  <offset>(1, 0, 0.9)</offset>
                  <layer>-5</layer>
                </dataEast>
                <dataSouth>
                  <offset>(0.2, 0, -0.2)</offset>
                  <rotationOffset>45</rotationOffset>
                </dataSouth>
                <dataWest>
                  <flip>true</flip>
                </dataWest>
              </drawData>
            </li>
            <li Class="PawnRenderNodeProperties_Spastic">
              <debugLabel>ToughSpikeR</debugLabel>
              <texPath>Things/Pawn/Fleshbeast/Toughspike/Attachments/Toughspike_Spike</texPath>
              <rotDrawMode>Fresh, Rotting</rotDrawMode>
              <rotateFacing>false</rotateFacing>
              <rotationRange>-30~30</rotationRange>
              <durationTicksRange>10~35</durationTicksRange>
              <nextSpasmTicksRange>10~50</nextSpasmTicksRange>
              <drawSize>1.4</drawSize>
              <baseLayer>10</baseLayer>
              <useRottenColor>true</useRottenColor>
              <linkedBodyPartsGroup>RightSpike</linkedBodyPartsGroup>
              <drawData>
                <dataNorth>
                  <layer>-5</layer>
                </dataNorth>
                <dataEast>
                  <offset>(1.2, 0, 0.6)</offset>
                  <rotationOffset>41</rotationOffset>
                </dataEast>
                <dataSouth>
                  <offset>(-0.2, 0, -0.2)</offset>
                  <rotationOffset>-45</rotationOffset>
                  <flip>true</flip>
                </dataSouth>
                <dataWest>
                  <rotationOffset>-41</rotationOffset>
                  <flip>true</flip>
                </dataWest>
              </drawData>
            </li>
            <li Class="PawnRenderNodeProperties_Spastic">
              <debugLabel>SpikeL</debugLabel>
              <texPath>Things/Pawn/Fleshbeast/Trispike/Attachments/Side/Trispike_SideLimb</texPath>
              <rotDrawMode>Fresh, Rotting</rotDrawMode>
              <rotateFacing>false</rotateFacing>
              <rotationRange>-25~25</rotationRange>
              <scaleRange>0.9~1.1</scaleRange>
              <durationTicksRange>15~45</durationTicksRange>
              <nextSpasmTicksRange>5~35</nextSpasmTicksRange>
              <drawSize>1.4</drawSize>
              <baseLayer>10</baseLayer>
              <useRottenColor>true</useRottenColor>
              <drawData>
                <dataNorth>
                  <rotationOffset>263</rotationOffset>
                  <offset>(-0.4, 0, 0.4)</offset>
                </dataNorth>
                <dataEast>
                  <rotationOffset>245</rotationOffset>
                  <layer>-5</layer>
                </dataEast>
                <dataSouth>
                  <rotationOffset>2</rotationOffset>
                  <offset>(0.5, 0, 1.2)</offset>
                </dataSouth>
                <dataWest>
                  <rotationOffset>-245</rotationOffset>
                  <layer>-5</layer>
                  <flip>true</flip>
                </dataWest>
              </drawData>
            </li>
            <li Class="PawnRenderNodeProperties_Spastic">
              <debugLabel>SpikeR</debugLabel>
              <texPath>Things/Pawn/Fleshbeast/Trispike/Attachments/Side/Trispike_SideLimb</texPath>
              <rotDrawMode>Fresh, Rotting</rotDrawMode>
              <rotateFacing>false</rotateFacing>
              <rotationRange>-25~25</rotationRange>
              <scaleRange>0.9~1.1</scaleRange>
              <durationTicksRange>15~45</durationTicksRange>
              <nextSpasmTicksRange>5~35</nextSpasmTicksRange>
              <drawSize>1.45</drawSize>
              <baseLayer>10</baseLayer>
              <useRottenColor>true</useRottenColor>
              <drawData>
                <dataNorth>
                  <offset>(0.5, 0, 1)</offset>
                  <rotationOffset>85</rotationOffset>
                </dataNorth>
                <dataEast>
                  <offset>(-0.4, 0, 0.5)</offset>
                </dataEast>
                <dataSouth>
                  <rotationOffset>171</rotationOffset>
                  <offset>(0.8, 0, 0.6)</offset>
                  <flip>true</flip>
                </dataSouth>
                <dataWest>
                  <flip>true</flip>
                </dataWest>
              </drawData>
            </li>
            <li Class="PawnRenderNodeProperties_Spastic">
              <debugLabel>SpikeTop</debugLabel>
              <texPath>Things/Pawn/Fleshbeast/Trispike/Attachments/Top/Trispike_TopLimb</texPath>
              <rotDrawMode>Fresh, Rotting</rotDrawMode>
              <rotateFacing>false</rotateFacing>
              <rotationRange>-30~30</rotationRange>
              <scaleRange>0.95~1.05</scaleRange>
              <durationTicksRange>10~60</durationTicksRange>
              <nextSpasmTicksRange>20~60</nextSpasmTicksRange>
              <drawSize>1.4</drawSize>
              <baseLayer>10</baseLayer>
              <useRottenColor>true</useRottenColor>
              <drawData>
                <dataNorth>
                  <offset>(-0.5, 0, 1)</offset>
                  <rotationOffset>323</rotationOffset>
                </dataNorth>
                <dataEast>
                  <offset>(0.5, 0, 1)</offset>
                  <rotationOffset>91</rotationOffset>
                </dataEast>
                <dataSouth>
                  <rotationOffset>-60</rotationOffset>
                  <offset>(-0.7, 0, 0.9)</offset>
                </dataSouth>
                <dataWest>
                  <rotationOffset>-91</rotationOffset>
                  <flip>true</flip>
                </dataWest>
              </drawData>
            </li>
            <li>
              <debugLabel>Wounds</debugLabel>
              <nodeClass>PawnRenderNode_AnimalPart</nodeClass>
              <workerClass>PawnRenderNodeWorker_OverlayWounds</workerClass>
              <overlayLayer>Body</overlayLayer>
              <baseLayer>10</baseLayer>
            </li>
            <li>
              <debugLabel>Firefoam</debugLabel>
              <nodeClass>PawnRenderNode_AnimalPart</nodeClass>
              <workerClass>PawnRenderNodeWorker_OverlayFirefoam</workerClass>
              <overlayLayer>Body</overlayLayer>
              <useGraphic>false</useGraphic>
              <baseLayer>20</baseLayer>
            </li>
          </children>
        </li>
        <li Class="PawnRenderNodeProperties_Carried">
          <debugLabel>Carried thing</debugLabel>
          <workerClass>PawnRenderNodeWorker_Carried</workerClass>
        </li>
      </children>
    </root>
  </PawnRenderTreeDef>


  <!-- Misc -->

  <PawnRenderTreeDef>
    <defName>Revenant</defName>
    <root Class="PawnRenderNodeProperties_Parent">
      <debugLabel>Root</debugLabel>
      <tagDef>Root</tagDef>
      <children>
        <li>
          <debugLabel>Body</debugLabel>
          <tagDef>Body</tagDef>
          <useRottenColor>true</useRottenColor>
          <nodeClass>PawnRenderNode_AnimalPart</nodeClass>
          <children>
            <li>
              <debugLabel>Head</debugLabel>
              <tagDef>Head</tagDef>
              <texPath>Things/Pawn/Revenant/RevenantHead</texPath>
              <drawSize>1.4</drawSize>
              <baseLayer>10</baseLayer>
              <useRottenColor>true</useRottenColor>
              <drawData>
                <dataNorth>
                  <offset>(0, 0, 0.55)</offset>
                  <layer>-5</layer>
                </dataNorth>
                <dataEast><offset>(0.2, 0, 0.55)</offset></dataEast>
                <dataSouth><offset>(0, 0, 0.55)</offset></dataSouth>
                <dataWest><offset>(-0.2, 0, 0.55)</offset></dataWest>
              </drawData>
            </li>
            <li>
              <debugLabel>Firefoam</debugLabel>
              <nodeClass>PawnRenderNode_AnimalPart</nodeClass>
              <workerClass>PawnRenderNodeWorker_OverlayFirefoam</workerClass>
              <overlayLayer>Body</overlayLayer>
              <useGraphic>false</useGraphic>
              <baseLayer>20</baseLayer>
            </li>
          </children>
        </li>
        <li Class="PawnRenderNodeProperties_Carried">
          <debugLabel>Carried thing</debugLabel>
          <workerClass>PawnRenderNodeWorker_Carried</workerClass>
        </li>
      </children>
    </root>
  </PawnRenderTreeDef>

  <PawnRenderTreeDef>
    <defName>Devourer</defName>
    <root Class="PawnRenderNodeProperties_Parent">
      <debugLabel>Root</debugLabel>
      <tagDef>Root</tagDef>
      <children>
        <li>
          <debugLabel>Body</debugLabel>
          <tagDef>Body</tagDef>
          <useRottenColor>true</useRottenColor>
          <nodeClass>PawnRenderNode_Devourer</nodeClass>
          <children>
            <li>
              <debugLabel>Wounds</debugLabel>
              <nodeClass>PawnRenderNode_AnimalPart</nodeClass>
              <workerClass>PawnRenderNodeWorker_OverlayWounds</workerClass>
              <overlayLayer>Body</overlayLayer>
              <useGraphic>false</useGraphic>
              <baseLayer>3</baseLayer>
            </li>
            <li>
              <debugLabel>Firefoam</debugLabel>
              <nodeClass>PawnRenderNode_AnimalPart</nodeClass>
              <workerClass>PawnRenderNodeWorker_OverlayFirefoam</workerClass>
              <overlayLayer>Body</overlayLayer>
              <useGraphic>false</useGraphic>
              <baseLayer>10</baseLayer>
            </li>
          </children>
        </li>
        <li Class="PawnRenderNodeProperties_Carried">
          <debugLabel>Carried thing</debugLabel>
          <workerClass>PawnRenderNodeWorker_Carried</workerClass>
        </li>
      </children>
    </root>
  </PawnRenderTreeDef>

  <PawnRenderTreeDef>
    <defName>Nociosphere</defName>
    <root Class="PawnRenderNodeProperties_Parent">
      <debugLabel>Root</debugLabel>
      <tagDef>Root</tagDef>
      <children>
        <li>
          <debugLabel>Body</debugLabel>
          <tagDef>Body</tagDef>
          <nodeClass>PawnRenderNode_AnimalPart</nodeClass>
          <children>
            <li Class="PawnRenderNodeProperties_NociosphereSegment">
              <debugLabel>Shell_A</debugLabel>
              <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellA</texPath>
              <maskPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellA_Mask</maskPath>
              <baseLayer>4</baseLayer>
              <offset>0.5,1.75</offset>
              <children>
                <li Class="PawnRenderNodeProperties_NociosphereSegmentOutline">
                  <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellA_Outline</texPath>
                </li>
              </children>
            </li>
            <li Class="PawnRenderNodeProperties_NociosphereSegment">
              <debugLabel>Shell_B</debugLabel>
              <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellB</texPath>
              <maskPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellB_Mask</maskPath>
              <baseLayer>4</baseLayer>
              <offset>-0.5,1.75</offset>
              <children>
                <li Class="PawnRenderNodeProperties_NociosphereSegmentOutline">
                  <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellB_Outline</texPath>
                </li>
              </children>
            </li>
            <li Class="PawnRenderNodeProperties_NociosphereSegment">
              <debugLabel>Shell_C</debugLabel>
              <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellC</texPath>
              <maskPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellC_Mask</maskPath>
              <baseLayer>6</baseLayer>
              <offset>-0.25,0.75</offset>
              <children>
                <li Class="PawnRenderNodeProperties_NociosphereSegmentOutline">
                  <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellC_Outline</texPath>
                </li>
              </children>
            </li>
            <li Class="PawnRenderNodeProperties_NociosphereSegment">
              <debugLabel>Shell_D</debugLabel>
              <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellD</texPath>
              <maskPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellD_Mask</maskPath>
              <baseLayer>6</baseLayer>
              <offset>0.25,0.75</offset>
              <children>
                <li Class="PawnRenderNodeProperties_NociosphereSegmentOutline">
                  <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellD_Outline</texPath>
                </li>
              </children>
            </li>
            <li Class="PawnRenderNodeProperties_NociosphereSegment">
              <debugLabel>Shell_E</debugLabel>
              <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellE</texPath>
              <maskPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellE_Mask</maskPath>
              <baseLayer>3</baseLayer>
              <offset>-2.25,-1</offset>
              <children>
                <li Class="PawnRenderNodeProperties_NociosphereSegmentOutline">
                  <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellE_Outline</texPath>
                </li>
              </children>
            </li>
            <li Class="PawnRenderNodeProperties_NociosphereSegment">
              <debugLabel>Shell_F</debugLabel>
              <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellF</texPath>
              <maskPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellF_Mask</maskPath>
              <baseLayer>3</baseLayer>
              <offset>2.25,-1</offset>
              <children>
                <li Class="PawnRenderNodeProperties_NociosphereSegmentOutline">
                  <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellF_Outline</texPath>
                </li>
              </children>
            </li>
            <li Class="PawnRenderNodeProperties_NociosphereSegment">
              <debugLabel>Shell_G</debugLabel>
              <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellG</texPath>
              <maskPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellG_Mask</maskPath>
              <baseLayer>4</baseLayer>
              <offset>-1,-0.6</offset>
              <children>
                <li Class="PawnRenderNodeProperties_NociosphereSegmentOutline">
                  <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellG_Outline</texPath>
                </li>
              </children>
            </li>
            <li Class="PawnRenderNodeProperties_NociosphereSegment">
              <debugLabel>Shell_H</debugLabel>
              <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellH</texPath>
              <maskPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellH_Mask</maskPath>
              <baseLayer>4</baseLayer>
              <offset>1,-0.6</offset>
              <children>
                <li Class="PawnRenderNodeProperties_NociosphereSegmentOutline">
                  <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellH_Outline</texPath>
                </li>
              </children>
            </li>
            <li Class="PawnRenderNodeProperties_NociosphereSegment">
              <debugLabel>Shell_I</debugLabel>
              <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellI</texPath>
              <maskPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellI_Mask</maskPath>
              <baseLayer>10</baseLayer>
              <offset>0,-0.5</offset>
              <children>
                <li Class="PawnRenderNodeProperties_NociosphereSegmentOutline">
                  <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellI_Outline</texPath>
                </li>
              </children>
            </li>
            <li Class="PawnRenderNodeProperties_NociosphereSegment">
              <debugLabel>Shell_J</debugLabel>
              <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellJ</texPath>
              <maskPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellJ_Mask</maskPath>
              <baseLayer>2</baseLayer>
              <offset>-1.25,-1.55</offset>
              <children>
                <li Class="PawnRenderNodeProperties_NociosphereSegmentOutline">
                  <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellJ_Outline</texPath>
                </li>
              </children>
            </li>
            <li Class="PawnRenderNodeProperties_NociosphereSegment">
              <debugLabel>Shell_K</debugLabel>
              <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellK</texPath>
              <maskPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellK_Mask</maskPath>
              <baseLayer>2</baseLayer>
              <offset>1.25,-1.55</offset>
              <children>
                <li Class="PawnRenderNodeProperties_NociosphereSegmentOutline">
                  <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellK_Outline</texPath>
                </li>
              </children>
            </li>
            <li Class="PawnRenderNodeProperties_NociosphereSegment">
              <debugLabel>Shell_L</debugLabel>
              <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellL</texPath>
              <maskPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellL_Mask</maskPath>
              <baseLayer>2</baseLayer>
              <offset>-0.25,-1.65</offset>
              <children>
                <li Class="PawnRenderNodeProperties_NociosphereSegmentOutline">
                  <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellL_Outline</texPath>
                </li>
              </children>
            </li>
            <li Class="PawnRenderNodeProperties_NociosphereSegment">
              <debugLabel>Shell_M</debugLabel>
              <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellM</texPath>
              <maskPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellM_Mask</maskPath>
              <baseLayer>2</baseLayer>
              <offset>0.25,-1.65</offset>
              <children>
                <li Class="PawnRenderNodeProperties_NociosphereSegmentOutline">
                  <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellM_Outline</texPath>
                </li>
              </children>
            </li>
            <li Class="PawnRenderNodeProperties_NociosphereSegment">
              <debugLabel>Shell_N</debugLabel>
              <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellN</texPath>
              <baseLayer>0</baseLayer>
              <offset>0,-1.75</offset>
              <children>
                <li Class="PawnRenderNodeProperties_NociosphereSegmentOutline">
                  <texPath>Things/Pawn/Nociosphere/Shell/Nociosphere_ShellN_Outline</texPath>
                </li>
              </children>
            </li>
          </children>
        </li>
      </children>
    </root>
  </PawnRenderTreeDef>

</Defs>
