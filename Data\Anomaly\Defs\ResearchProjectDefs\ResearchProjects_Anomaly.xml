﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <!-- Bioferrite content -->

  <ResearchProjectDef>
    <defName>BioferriteExtraction</defName>
    <label>bioferrite extraction</label>
    <description>Extract bioferrite directly from captured entities. Use it to craft weapons and apparel at a crafting spot or bioferrite shaper.</description>
    <tab>Anomaly</tab>
    <knowledgeCost>5</knowledgeCost>
    <knowledgeCategory>Basic</knowledgeCategory>
    <researchViewX>0.00</researchViewX>
    <researchViewY>3.40</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->bioferrite extraction</li>

        <li>subject_story->extracted an organic compound from a rapidly mutating flesh sample</li>
        <li>subject_story->stripped and boiled the flesh of a harbinger tree</li>
        <li>subject_story->investigated reports of an archotech phenomenon</li>
        <li>subject_story->discovered a novel organic metal</li>

        <li>subject_gerund->extracting bioferrite</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>BioferriteHarvesting</defName>
    <label>bioferrite harvester</label>
    <description>Build bioferrite harvesters that automatically extract bioferrite from captured entities. Produces more bioferrite than manual extraction.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>EntityContainment</li>
      <li>BioferriteExtraction</li>
    </prerequisites>
    <knowledgeCost>10</knowledgeCost>
    <knowledgeCategory>Basic</knowledgeCategory>
    <researchViewX>1.00</researchViewX>
    <researchViewY>5.10</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->bioferrite</li>

        <li>subject_story->investigated an exotic metal-like fibrous substance</li>
        <li>subject_story->vivisected a ghoulish corpse</li>
        <li>subject_story->discovered a cache of organic metals</li>
        <li>subject_story->observed strange metal-infested wildlife</li>

        <li>subject_gerund->harvesting bioferrite</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>BioferriteShaping</defName>
    <label>bioferrite shaping</label>
    <description>Shape and re-form bioferrite into useful tools, weapons, flooring, and more.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>BioferriteExtraction</li>
      <li>Electricity</li>
    </prerequisites>
    <knowledgeCost>20</knowledgeCost>
    <knowledgeCategory>Basic</knowledgeCategory>
    <researchViewX>1.00</researchViewX>
    <researchViewY>3.40</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->bioferrite shaping</li>

        <li>subject_story->learned unique techniques for molding and sculpting</li>
        <li>subject_story->trained in heat-shaping</li>
        <li>subject_story->worked in an exotic materials foundry</li>
        <li>subject_story->aided an archotech materials scientist </li>
        <li>subject_story->studied the formation of archotech artefacts</li>

        <li>subject_gerund->shaping bioferrite</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>BioferriteIgnition</defName>
    <label>bioferrite weaponry</label>
    <description>Create advanced weapons that use bioferrite as a fuel source.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>BioferriteShaping</li>
      <li>PrecisionRifling</li>
    </prerequisites>
    <knowledgeCost>60</knowledgeCost>
    <knowledgeCategory>Basic</knowledgeCategory>
    <researchViewX>3.00</researchViewX>
    <researchViewY>4.00</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->bioferrite combustion</li>
        <li>subject->bioferrite weaponry</li>

        <li>subject_story->worked for an extraction rig on an archotech asteroid</li>
        <li>subject_story->researched the combustion points of exotic materials</li>
        <li>subject_story->joined the research arm of a milword weapons corp</li>
        <li>subject_story->cleared biomines in an archotech desert</li>

        <li>subject_gerund->building bioferrite-based weapons</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>BioferriteGenerator</defName>
    <label>bioferrite generator</label>
    <description>Build power generators that burn bioferrite as a fuel.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>Electroharvester</li>
    </prerequisites>
    <knowledgeCost>40</knowledgeCost>
    <knowledgeCategory>Advanced</knowledgeCategory>
    <researchViewX>5.50</researchViewX>
    <researchViewY>5.70</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->bioferrite power generation</li>

        <li>subject_story->was trained in the use of innovative power sources</li>
        <li>subject_story->explored the use of metal- and fibre-based fuels</li>
        <li>subject_story->discovered a love of fire and things that burn</li>
        <li>subject_story->worked as a technician, maintaining orbital energy systems</li>
        <li>subject_story->rendered the wildlife of a system down for fuel</li>

        <li>subject_gerund->constructing bioferrite-powered generators</li>
        <li>subject_gerund->powering generators using bioferrite</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>VoidSculptures</defName>
    <label>void sculptures</label>
    <description>Craft special void sculptures from bioferrite to enhance the quality of psychic rituals.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>BioferriteExtraction</li>
      <li>BasicPsychicRituals</li>
    </prerequisites>
    <knowledgeCost>25</knowledgeCost>
    <knowledgeCategory>Basic</knowledgeCategory>
    <researchViewX>1.00</researchViewX>
    <researchViewY>1.90</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->void sculptures</li>
        <li>subject->bioferrite sculpture</li>

        <li>subject_story->computer-modelled archotech possibility spaces</li>
        <li>subject_story->was employed by a sibyl to sculpt her dreams</li>
        <li>subject_story->wanted to bring sculptures to life</li>
        <li>subject_story->dreamed of an endless ocean, shaped by human hands</li>
        <li>subject_story->observed Imperial psychics at work</li>

        <li>subject_gerund->sculpting with bioferrite</li>
        <li>subject_gerund->crafting bioferrite ritual sculptures</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>InsanityWeaponry</defName>
    <label>insanity weaponry</label>
    <description>Restructure archotech shards into limited-use weapons that shock, manipulate, and destroy enemies using archotechnology.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>BioferriteShaping</li>
    </prerequisites>
    <knowledgeCost>30</knowledgeCost>
    <knowledgeCategory>Advanced</knowledgeCategory>
    <researchViewX>4.50</researchViewX>
    <researchViewY>4.00</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->archotech insanity weaponry</li>

        <li>subject_story->explored psychological warfare for [ANYPAWN_possessive] dissertation</li>
        <li>subject_story->watched an Imperial battalion rout and learned the value of fear</li>
        <li>subject_story->worked in an asylum of archotech victims</li>
        <li>subject_story->vivisected archite-infested wildlife to understand the effects on the mammalian brain</li>

        <li>subject_gerund->restructuring archotech into insanity-inducing weaponry</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>MutationWeaponry</defName>
    <label>mutation weaponry</label>
    <description>Restructure archotech shards into limited-use weapons that twist living targets into terrifying creatures.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>BioferriteShaping</li>
    </prerequisites>
    <knowledgeCost>35</knowledgeCost>
    <knowledgeCategory>Advanced</knowledgeCategory>
    <researchViewX>5.50</researchViewX>
    <researchViewY>4.00</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->mutation induction</li>
        <li>subject->archotech mutation weaponry</li>


        <li>subject_story->dreamed of new life, infused with archites</li>
        <li>subject_story->witnessed archotech warping plant life</li>
        <li>subject_story->experimented with new weapons</li>
        <li>subject_story->studied new life on transcendent worlds</li>
        <li>subject_story->grew archite-based life in the laboratory</li>

        <li>subject_gerund->inducing mutations</li>
        <li>subject_gerund->restructuring archotech into mutation-inducing weaponry</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>AtmosphericHeater</defName>
    <label>atmospheric heater</label>
    <description>Build bioferrite-powered heaters that produce enough heat to warm an entire region.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>BioferriteGenerator</li>
    </prerequisites>
    <knowledgeCost>60</knowledgeCost>
    <knowledgeCategory>Advanced</knowledgeCategory>
    <researchViewX>6.50</researchViewX>
    <researchViewY>5.70</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->bioferrite heating</li>

        <li>subject_story->upgraded archaic geothermal plants</li>
        <li>subject_story->studied the heat output of biometals</li>
        <li>subject_story->researched the impossible capacity of archotech energy storage</li>
        <li>subject_story->examined the hypocaust systems on densely occupied iceworlds</li>

        <li>subject_gerund->heating with bioferrite</li>
        <li>subject_gerund->constructing bioferrite-fueled heaters</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>FrenzyInducer</defName>
    <label>frenzy inducer</label>
    <description>Build shard-powered structures that generate a frenetic psychic field to make anyone nearby move and work faster. However, the unstable emotional energy creates a tendency towards violence, irritating anyone nearby.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>SleepSuppressor</li>
    </prerequisites>
    <knowledgeCost>60</knowledgeCost>
    <knowledgeCategory>Basic</knowledgeCategory>
    <researchViewX>2.00</researchViewX>
    <researchViewY>4.00</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->archotech frenzy inducers</li>

        <li>subject_story->worked as a motivational therapist</li>
        <li>subject_story->beat the drums on a feralworld galley</li>
        <li>subject_story->researched the effects of varied archofields on human consciousness</li>
        <li>subject_story->managed a trance station in an urbworld underclub</li>

        <li>subject_gerund->inducing frenzy</li>
        <li>subject_gerund->constructing frenzy inducers from archotech</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>SleepSuppressor</defName>
    <label>sleep suppressor</label>
    <description>Build shard-powered structures that stimulate those nearby to prevent fatigue. The device will irritate anyone nearby.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>BioferriteExtraction</li>
      <li>EntityContainment</li>
    </prerequisites>
    <knowledgeCost>30</knowledgeCost>
    <knowledgeCategory>Basic</knowledgeCategory>
    <researchViewX>1.00</researchViewX>
    <researchViewY>4.00</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->sleep suppression</li>

        <li>subject_story->studied sleep deprivation in factory workers</li>
        <li>subject_story->researched stimulant use among Imperial troopers</li>
        <li>subject_story->researched the effects of varied archofields on human consciousness</li>
        <li>subject_story->worked in a glitterworld eversleep lab, where the wealthy dreamed their lives away</li>
        <li>subject_story->managed the reveille at a prisonworld work camp</li>

        <li>subject_gerund->constructing sleep suppressors from archotech</li>
        <li>subject_gerund->suppressing sleep with archotech</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <!-- Containment content -->
  
  <ResearchProjectDef>
    <defName>EntityContainment</defName>
    <label>entity containment</label>
    <description>Build holding platforms and inhibitors to better contain entities.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>Electricity</li>
    </prerequisites>
    <knowledgeCost>10</knowledgeCost>
    <knowledgeCategory>Basic</knowledgeCategory>
    <researchViewX>0.00</researchViewX>
    <researchViewY>5.40</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->entity containment</li>

        <li>subject_story->worked in a prisonworld research facility</li>
        <li>subject_story->was held captive on a prisonworld</li>
        <li>subject_story->worked with a guerilla movement to breach holding facilities</li>
        <li>subject_story->constructed meatpacking plants in the dinoworld chains</li>

        <li>subject_gerund->containing entities</li>

        <li>subject_gerund->construction entity containment facilities</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>Electroharvester</defName>
    <label>electroharvester</label>
    <description>Build a generator that draws electricity from contained entities. This process aggravates entities, making them harder to contain.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>EntityContainment</li>
    </prerequisites>
    <knowledgeCost>30</knowledgeCost>
    <knowledgeCategory>Basic</knowledgeCategory>
    <researchViewX>2.00</researchViewX>
    <researchViewY>5.70</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->entity-derived power generation</li>

        <li>subject_story->researched ion pumps in cell membranes</li>
        <li>subject_story->studied the life of the rare Terran electric eel</li>
        <li>subject_story->provoked archites and studied their thermal output</li>
        <li>subject_story->theorized about the energy potential of archotech</li>

        <li>subject_gerund->harvesting from entities</li>

        <li>subject_gerund->constructing electroharvesters</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>DisruptorFlares</defName>
    <label>disruptor flares</label>
    <description>Build bioferrite-based flare packs that reveal invisible creatures and light up an area.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>EntityContainment</li>
      <li>BioferriteShaping</li>
    </prerequisites>
    <knowledgeCost>30</knowledgeCost>
    <knowledgeCategory>Basic</knowledgeCategory>
    <researchViewX>2.00</researchViewX>
    <researchViewY>5.10</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->bioferrite flares</li>
        <li>subject->short-term lighting</li>

        <li>subject_story->studied the non-visual electromagnetic spectrum</li>
        <li>subject_story->disabled the visual centres of test subjects</li>
        <li>subject_story->studied the bioluminescence techniques of nightworld fauna</li>
        <li>subject_story->narrowed the electromagnetic spectrum of archite energy releases</li>

        <li>subject_gerund->boosting visibility temporarily</li>

        <li>subject_gerund->crafting bioferrite flares</li>

      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>ProximityDetector</defName>
    <label>proximity detector</label>
    <description>Build a device that can detect invisible creatures.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>EntityContainment</li>
    </prerequisites>
    <knowledgeCost>15</knowledgeCost>
    <knowledgeCategory>Basic</knowledgeCategory>
    <researchViewX>1.00</researchViewX>
    <researchViewY>5.70</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->proximity detectors</li>
        <li>subject->motion sensors</li>

        <li>subject_story->watched ancient entertainment reels and was inspired</li>
        <li>subject_story->prototyped simple psi-hardened devices</li>
        <li>subject_story->maintained the early warning systems of a deathworld outpost</li>
        <li>subject_story->studied multiband scanners for cross-system pattern detection</li>

        <li>subject_gerund->revealing the unseen</li>

        <li>subject_gerund->detecting invisible creatures</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <!-- Psychic rituals -->

  <ResearchProjectDef>
    <defName>BasicPsychicRituals</defName>
    <label>void provocation</label>
    <description>Build a psychic ritual spot and perform psychic rituals. Use the void provocation ritual to discover new entities.</description>
    <tab>Anomaly</tab>
    <knowledgeCost>5</knowledgeCost>
    <knowledgeCategory>Basic</knowledgeCategory>
    <researchViewX>0.00</researchViewX>
    <researchViewY>1.05</researchViewY>
    <teachConcept>VoidProvocation</teachConcept>
    <generalRules>
      <rulesStrings>
        <li>subject->psychic rituals</li>

        <li>subject_story->studied the rituals of Horax cultists</li>
        <li>subject_story->reconstructed primitive altars</li>
        <li>subject_story->established the basics of psychic communication</li>
        <li>subject_story->watched a failed void provocation ritual on a deathworld</li>
        <li>subject_story->communed with an archotech shard atop a desert pillar</li>

        <li>subject_gerund->conducting psychic rituals</li>

        <li>subject_gerund->establishing ritual spots</li>

        <li>subject_gerund->provoking void entities</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>SummonAnimals</defName>
    <label>draw animals</label>
    <description>Perform a psychic ritual which will draw a herd of animals.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>BasicPsychicRituals</li>
    </prerequisites>
    <knowledgeCost>20</knowledgeCost>
    <knowledgeCategory>Basic</knowledgeCategory>
    <researchViewX>1.00</researchViewX>
    <researchViewY>1.30</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->stampede invocation</li>

        <li>subject_story->worked as a shepherd amidst the mountains of a primitive world</li>
        <li>subject_story->learned to commune with the beasts of the fields</li>
        <li>subject_story->experimented with the effects of archite on different animal breeds</li>
        <li>subject_story->watched a nature cult commune with their herds</li>

        <li>subject_gerund->summoning animal herds</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>SummonShamblers</defName>
    <label>draw shamblers</label>
    <description>Perform a psychic ritual which will draw a horde of animated corpses. While hostile, the corpses won't attack your colony directly. They can be captured for study.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>SummonAnimals</li>
    </prerequisites>
    <knowledgeCost>25</knowledgeCost>
    <knowledgeCategory>Basic</knowledgeCategory>
    <researchViewX>2.00</researchViewX>
    <researchViewY>1.30</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->shambler summoning rituals</li>

        <li>subject_story->studied the necrocults of the deadworlds, from the safety of a university library</li>
        <li>subject_story->collected stories of shambler attacks</li>
        <li>subject_story->studied archite thread formation in deceased organisms</li>
        <li>subject_story->saw all sorts of strange things as a deathworld curator</li>

        <li>subject_gerund->summoning shamblers</li>

        <li>subject_gerund->calling shamblers through ritual</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>Chronophagy</defName>
    <label>chronophagy</label>
    <description>Perform a psychic ritual that causes a target to age rapidly. The ritual invoker will become younger in the process. The target will suffer brain damage as a result, which may be lethal for elderly targets.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>Psychophagy</li>
    </prerequisites>
    <knowledgeCost>30</knowledgeCost>
    <knowledgeCategory>Basic</knowledgeCategory>
    <researchViewX>2.00</researchViewX>
    <researchViewY>0.10</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->chronophagy</li>

        <li>subject_story->studied age-related degeneration</li>
        <li>subject_story->destructively replicated healthy structures from one organism to another</li>
        <li>subject_story->theorized about the various rituals that imbued archotech can enable</li>
        <li>subject_story->observed wealthy longevity treatments in glitterworld clinics</li>

        <li>subject_gerund->stealing youth</li>

        <li>subject_gerund->invoking chronophagy</li>

        <li>subject_gerund->performing a chronophagy ritual</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>Psychophagy</defName>
    <label>psychophagy</label>
    <description>Perform a psychic ritual which deadens a target's psychic sensitivity and temporarily boosts the ritual invoker's psychic sensitivity. The target will suffer brain damage as a side-effect.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>BasicPsychicRituals</li>
    </prerequisites>
    <knowledgeCost>20</knowledgeCost>
    <knowledgeCategory>Basic</knowledgeCategory>
    <researchViewX>1.00</researchViewX>
    <researchViewY>0.10</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->psychophagy</li>

        <li>subject_story->studied managed desensitization to psychic effects</li>
        <li>subject_story->managed archite transfer from deceased subjects</li>
        <li>subject_story->collated rumours of psychic transference rituals</li>
        <li>subject_story->witnessed a Horax cult sap the power of an Imperial noble</li>

        <li>subject_gerund->stealing psychic abilities</li>

        <li>subject_gerund->performing a psychophagy ritual</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>SkipAbductionPlayer</defName>
    <label>skip abduction</label>
    <description>Perform a psychic ritual that abducts a random hostile person from anywhere in the world, putting them in a short-term coma for capture.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>BasicPsychicRituals</li>
    </prerequisites>
    <knowledgeCost>40</knowledgeCost>
    <knowledgeCategory>Basic</knowledgeCategory>
    <researchViewX>1.00</researchViewX>
    <researchViewY>0.70</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->psychic abduction</li>
        <li>subject->skip abduction</li>

        <li>subject_story->witnessed an abduction attempt on an Imperial noble</li>
        <li>subject_story->was ransomed by dirtmole scavengers</li>
        <li>subject_story->observed a failed translocation ritual performed by a cult leader</li>
        <li>subject_story->researched transpatial transfer mediated by archotech</li>

        <li>subject_gerund->abducting enemies through psychic ritual</li>

        <li>subject_gerund->performing a ritual of skip abduction</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>SummonFleshbeastsPlayer</defName>
    <label>summon fleshbeasts</label>
    <description>Perform a psychic ritual which provokes fleshbeasts to emerge from the ground near enemies, attacking anyone nearby.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>SummonAnimals</li>
    </prerequisites>
    <knowledgeCost>30</knowledgeCost>
    <knowledgeCategory>Basic</knowledgeCategory>
    <researchViewX>2.00</researchViewX>
    <researchViewY>0.70</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->fleshbeast summoning</li>

        <li>subject_story->read ancient tomes about the origins of life</li>
        <li>subject_story->experienced firsthand spontaneous generation in the boomrats of Sahe</li>
        <li>subject_story->watched an ancient movie about fleshmelding and was inspired</li>
        <li>subject_story->studied consciousness transfer and separation in organic merging</li>
        <li>subject_story->observed a dirtmole community that had encountered horrifying beasts underground</li>

        <li>subject_gerund->summoning fleshbeasts</li>

        <li>subject_gerund->performing a ritual to summon fleshbeasts</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <!-- Advanced psychic rituals -->

  <ResearchProjectDef>
    <defName>AdvancedPsychicRituals</defName>
    <label>advanced psychic rituals</label>
    <description>Perform advanced psychic rituals.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>BasicPsychicRituals</li>
    </prerequisites>
    <knowledgeCost>15</knowledgeCost>
    <knowledgeCategory>Advanced</knowledgeCategory>
    <researchViewX>4.50</researchViewX>
    <researchViewY>1.05</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->advanced psychic rituals</li>

        <li>subject_story->was brought into a cult circle</li>
        <li>subject_story->encountered an exiled cult master, who happily boasted about his secrets</li>
        <li>subject_story->deepened [ANYPAWN_possessive] understanding of ritual sacrifice</li>
        <li>subject_story->was appointed professor of psychic history at the University of Ix</li>

        <li>subject_gerund->performing advanced psychic rituals</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>SummonPitGate</defName>
    <label>provoke pit gate</label>
    <description>Perform a psychic ritual which causes underground fleshbeasts to open up a massive hole in the ground. The hole leads down to a fleshbeast-infested cavern which can be explored for resources.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>AdvancedPsychicRituals</li>
    </prerequisites>
    <knowledgeCost>80</knowledgeCost>
    <knowledgeCategory>Advanced</knowledgeCategory>
    <researchViewX>6.50</researchViewX>
    <researchViewY>1.30</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->pit gate invocation</li>

        <li>subject_story->researched fleshmeld infestations</li>
        <li>subject_story->gathered tales of gestalt organisms</li>
        <li>subject_story->managed the tome library of a deadworld cult-school</li>
        <li>subject_story->officiated in a ritual of subsidence</li>

        <li>subject_gerund->performing a ritual to create a pit gate</li>

        <li>subject_gerund->summoning a pit gate</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>DeathRefusal</defName>
    <label>death refusal</label>
    <description>Perform a psychic ritual which grants death refusal to an individual, allowing them to self-resurrect once after dying.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>AdvancedPsychicRituals</li>
    </prerequisites>
    <knowledgeCost>30</knowledgeCost>
    <knowledgeCategory>Advanced</knowledgeCategory>
    <researchViewX>5.50</researchViewX>
    <researchViewY>1.90</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->death refusal invocation</li>

        <li>subject_story->investigated tales of impossible survival</li>
        <li>subject_story->observed the last moments of the many casualties of Last Battle VII</li>
        <li>subject_story->participated in the attempted resurrection of the ur-prophet of Axhor</li>
        <li>subject_story->gathered evidence of death refusal in the libraries of a coreworld</li>

        <li>subject_gerund->fending off death</li>

        <li>subject_gerund->performing a ritual to rebuke death</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>Philophagy</defName>
    <label>philophagy</label>
    <description>Perform a psychic ritual that drains experience from a victim and gives it to the invoker. The process will dull the target's mind, weakening their skills and causing brain damage.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>Chronophagy</li>
      <li>AdvancedPsychicRituals</li>
    </prerequisites>
    <knowledgeCost>30</knowledgeCost>
    <knowledgeCategory>Advanced</knowledgeCategory>
    <researchViewX>5.50</researchViewX>
    <researchViewY>0.10</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->philophagy</li>
        <li>subject->skill transfer</li>

        <li>subject_story->followed a trail of victims left by a philophage</li>
        <li>subject_story->researched neural replication processes in a glitterworld's labs</li>
        <li>subject_story->investigated the damaged minds of cult victims</li>
        <li>subject_story->found [ANYPAWN_possessive] skills weakened after a psychic assault</li>

        <li>subject_gerund->transferring a subject's learning</li>

        <li>subject_gerund->performing a ritual to steal a victim's skills</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>PleasurePulse</defName>
    <label>pleasure pulse</label>
    <description>Perform a psychic ritual that makes everyone in the region happier but reduces their desire to work.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>AdvancedPsychicRituals</li>
    </prerequisites>
    <knowledgeCost>30</knowledgeCost>
    <knowledgeCategory>Advanced</knowledgeCategory>
    <researchViewX>5.50</researchViewX>
    <researchViewY>0.70</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->pleasure invocation</li>

        <li>subject_story->read an ancient Earth story that boasted of a miracle food called soma</li>
        <li>subject_story->researched the myth of the Lotus Eaters</li>
        <li>subject_story->worked with social philosophers to create a utilitarian ritual</li>
        <li>subject_story->joined a rare archotech cult dedicated to leisure and pleasure</li>

        <li>subject_gerund->inducing pleasure psychically</li>

        <li>subject_gerund->performing a psychic ritual to induce pleasure</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>NeurosisPulse</defName>
    <label>neurosis pulse</label>
    <description>Perform a psychic ritual that makes everyone in the region work faster but become more irritable.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>AdvancedPsychicRituals</li>
    </prerequisites>
    <knowledgeCost>40</knowledgeCost>
    <knowledgeCategory>Advanced</knowledgeCategory>
    <researchViewX>6.50</researchViewX>
    <researchViewY>0.70</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->neurosis pulse</li>
        <li>subject->work imperative</li>

        <li>subject_story->joined a workers' cult on an urbworld</li>
        <li>subject_story->felt the psychic pressure of a ritual during work</li>
        <li>subject_story->studied the neural-pathways of the neurotic</li>
        <li>subject_story->investigated the crossover between management tropes and cult rituals</li>

        <li>subject_gerund->inducing neuroses psychically</li>

        <li>subject_gerund->performing a ritual to raise worker productivity</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>
  
  <ResearchProjectDef>
    <defName>BloodRain</defName>
    <label>blood rain</label>
    <description>Perform a psychic ritual which causes blood-like psychofluid to fall from the sky. Anyone caught outside will soon be driven into a berserk rage.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>AdvancedPsychicRituals</li>
    </prerequisites>
    <knowledgeCost>50</knowledgeCost>
    <knowledgeCategory>Advanced</knowledgeCategory>
    <researchViewX>5.50</researchViewX>
    <researchViewY>1.30</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->blood rain</li>

        <li>subject_story->investigated ancient deist myths about organic precipitation</li>
        <li>subject_story->studied the neural pathways of anger sufferers</li>
        <li>subject_story->sampled psychofluid flows on the transcendent world of Ix</li>
        <li>subject_story->was exposed to a psychofluid shower and filled with unmanageable rage</li>

        <li>subject_gerund->causing it to rain blood</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>Brainwipe</defName>
    <label>brainwipe</label>
    <description>Perform a psychic ritual which erases much of a person's episodic memories. Traumatic events will be forgotten. Resistant prisoners will become easier to recruit. Allows recruiting unwaveringly loyal prisoners.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>Philophagy</li>
    </prerequisites>
    <knowledgeCost>40</knowledgeCost>
    <knowledgeCategory>Advanced</knowledgeCategory>
    <researchViewX>6.50</researchViewX>
    <researchViewY>0.10</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->mind erasure</li>

        <li>subject_story->studied stories of episodice amnesia</li>
        <li>subject_story->worked in a clinic with brain lesion survivors</li>
        <li>subject_story->researched the neural pathways of amnesiacs</li>
        <li>subject_story->managed a brainwashing facility after the Forgotten Wars</li>

        <li>subject_gerund->wiping a subject's mind</li>

        <li>subject_gerund->performing a ritual to erase a subject's mind</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <!-- Serums -->

  <ResearchProjectDef>
    <defName>SerumSynthesis</defName>
    <label>serum synthesis</label>
    <description>Build a serum lab to synthesize high-tech serums. Serums harness archotechnological power to bestow a temporary effect on the user.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>BioferriteShaping</li>
    </prerequisites>
    <knowledgeCost>15</knowledgeCost>
    <knowledgeCategory>Advanced</knowledgeCategory>
    <researchViewX>4.50</researchViewX>
    <researchViewY>3.40</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->serum synthesis</li>

        <li>subject_story->was injected with archites to understand their effects</li>
        <li>subject_story->discovered a pool of oily black fluid in a deadworld temple</li>
        <li>subject_story->survived a dangerous fall thanks to a serum</li>
        <li>subject_story->managed a serum-testing programme for a coreworld brigade</li>

        <li>subject_gerund->synthesizing serums</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>MetalbloodSerum</defName>
    <label>metalblood serum</label>
    <description>Synthesize metalblood serums that make the user more resistant to damage.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>SerumSynthesis</li>
    </prerequisites>
    <knowledgeCost>35</knowledgeCost>
    <knowledgeCategory>Advanced</knowledgeCategory>
    <researchViewX>6.50</researchViewX>
    <researchViewY>4.00</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->metalblood serums</li>

        <li>subject_story->researched reactive liquids for use in milworld armor</li>
        <li>subject_story->was an early pioneer of blood-replacement technologies</li>
        <li>subject_story->discovered a metalblood dispenser on a desolate deadworld</li>
        <li>subject_story->deployed flamethrowers against a metalblood-using militia</li>

        <li>subject_gerund->synthesizing metalblood serums</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>MindNumbSerum</defName>
    <label>mind-numb serum</label>
    <description>Synthesize a serum that stabilizes a person's mood, preventing mental breaks.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>SerumSynthesis</li>
    </prerequisites>
    <knowledgeCost>35</knowledgeCost>
    <knowledgeCategory>Advanced</knowledgeCategory>
    <researchViewX>6.50</researchViewX>
    <researchViewY>3.40</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->mind-numb serum</li>

        <li>subject_story->sabotaged an emotional performance by secretly numbing the lead singer</li>
        <li>subject_story->filtered low-dose mind-numb serum into an asylum water supply</li>
        <li>subject_story->distributed mind-numb serum to junior soldiers before and after battle</li>
        <li>subject_story->investigated the use of archotech to numb pain</li>

        <li>subject_gerund->synthesizing mind-numb serums</li>      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>JuggernautSerum</defName>
    <label>juggernaut serum</label>
    <description>Synthesize a serum which temporarily increases the user's strength and speed, and allows them to recover from injuries faster. However, it reduces their mood.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>SerumSynthesis</li>
    </prerequisites>
    <knowledgeCost>35</knowledgeCost>
    <knowledgeCategory>Advanced</knowledgeCategory>
    <researchViewX>6.50</researchViewX>
    <researchViewY>2.80</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->juggernaut serum</li>

        <li>subject_story->was banned from the coreworld leagues after [ANYPAWN_pronoun] was caught doping a team with Juggernaut Serum</li>
        <li>subject_story->gave soldiers Juggernaut Serum when their ammo ran out</li>
        <li>subject_story->researched serum use among indentured building sectors</li>
        <li>subject_story->fell into a barrel of juggernaut serum</li>

        <li>subject_gerund->synthesizing juggernaut serums</li>      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <!-- Misc -->

  <ResearchProjectDef>
    <defName>BlissLobotomy</defName>
    <label>bliss lobotomy</label>
    <description>Perform a surgery that lobotomizes a person, making them happier while rendering them incapable of intellectual and skilled labor.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>BioferriteExtraction</li>
    </prerequisites>
    <knowledgeCost>25</knowledgeCost>
    <knowledgeCategory>Basic</knowledgeCategory>
    <researchViewX>1.00</researchViewX>
    <researchViewY>2.80</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->lobotomy</li>

        <li>subject_story->was eventually lobotomized for thought-crimes against the state</li>
        <li>subject_story->rehabilitated the few survivors of the Eldof experiment</li>
        <li>subject_story->took an apprenticeship as a brain surgeon and barber</li>

        <li>subject_gerund->performing lobotomies</li>
        <li>subject_gerund->excising brain tissue</li>

      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>GhoulInfusion</defName>
    <label>ghoul infusion</label>
    <description>Perform a surgery that infuses a living person with an archotech shard, transforming them into a terrifying ghoul. Ghouls are strong melee combatants but are incapable of doing work. Ghouls will go mad if they are not fed enough raw meat.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>BioferriteExtraction</li>
    </prerequisites>
    <knowledgeCost>30</knowledgeCost>
    <knowledgeCategory>Basic</knowledgeCategory>
    <researchViewX>3.00</researchViewX>
    <researchViewY>2.80</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->ghoul creation</li>

        <li>subject_story->supplied condemned prisonworld inhabitants for urbworld conflicts</li>
        <li>subject_story->militarized archite injections as part of an experimental program</li>
        <li>subject_story->studied feral deadlife in the ruins of a deadworld</li>

        <li>subject_gerund->infusing an archotech shard into a living person</li>
        <li>subject_gerund->creating a ghoul</li>

      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>GhoulEnhancements</defName>
    <label>ghoul enhancements</label>
    <description>Craft bioferrite prosthetics to make ghouls stronger.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>GhoulInfusion</li>
    </prerequisites>
    <knowledgeCost>40</knowledgeCost>
    <knowledgeCategory>Advanced</knowledgeCategory>
    <researchViewX>4.50</researchViewX>
    <researchViewY>2.80</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->ghoul enhancements</li>

        <li>subject_story->experimented with the dead and the twice-dead</li>
        <li>subject_story->implanted increasingly strange items into ghouls</li>
        <li>subject_story->researched more complex ghoul prosthesis</li>

        <li>subject_gerund->enhancing a ghoul with bioferrite prosthetics</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>GhoulResurrection</defName>
    <label>ghoul resurrection</label>
    <description>Synthesize a serum that can bring dead ghouls back to life.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>SerumSynthesis</li>
      <li>GhoulEnhancements</li>
    </prerequisites>
    <knowledgeCost>20</knowledgeCost>
    <knowledgeCategory>Advanced</knowledgeCategory>
    <researchViewX>5.50</researchViewX>
    <researchViewY>2.80</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->ghoul resurrection</li>

        <li>subject_story->developed a new method of resurrecting ghouls</li>
        <li>subject_story->injected a variety of archotech compounds into corpses</li>
        <li>subject_story->researched the effects of higher archotech concentrations on ghouls</li>
        <li>subject_story->witnessed the repeated resurrection of a ghoul</li>

        <li>subject_gerund->resurrecting a ghoul</li>

        <li>subject_gerund->synthesizing a serum to resurrect ghouls</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>DeadlifeDust</defName>
    <label>deadlife dust</label>
    <description>Craft devices that produce clouds of microscopic archites. The archites can reanimate corpses to produce deadly shamblers that will only attack your enemies.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>BioferriteShaping</li>
    </prerequisites>
    <knowledgeCost>60</knowledgeCost>
    <knowledgeCategory>Basic</knowledgeCategory>
    <researchViewX>2.00</researchViewX>
    <researchViewY>2.80</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->deadlife dust</li>

        <li>subject_story->synthesized an aerosol-dispersal system for nanobots</li>
        <li>subject_story->used atomic centrifuges to separate out different grades of archite</li>
        <li>subject_story->watched a deadly shambler assault on an Urbworld vidclip</li>
        <li>subject_story->developed an archite control device with autotuned IFF systems</li>

        <li>subject_gerund->crafting deadlife dust</li>

        <li>subject_gerund->distributing archites to create shamblers</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

  <ResearchProjectDef>
    <defName>RevenantInvisibility</defName>
    <label>revenant invisibility</label>
    <description>Modify a revenant spine so that it can be implanted in a human, allowing them to become temporarily invisible.</description>
    <tab>Anomaly</tab>
    <prerequisites>
      <li>DisruptorFlares</li>
    </prerequisites>
    <knowledgeCost>40</knowledgeCost>
    <knowledgeCategory>Advanced</knowledgeCategory>
    <researchViewX>5.50</researchViewX>
    <researchViewY>5.10</researchViewY>
    <generalRules>
      <rulesStrings>
        <li>subject->invisibility</li>

        <li>subject_story->implanted a monster's spine into a human, without considering the consequences</li>
        <li>subject_story->wrote a novel about the vanishing morality of an invisible person</li>
        <li>subject_story->discovered a strange metallic spine in a deadstar system</li>
        <li>subject_story->dreamed of an army of invisible warriors</li>

        <li>subject_gerund->adapting a revenant spine</li>

        <li>subject_gerund->implanting a revenant spine into a human</li>
      </rulesStrings>
    </generalRules>
  </ResearchProjectDef>

</Defs>