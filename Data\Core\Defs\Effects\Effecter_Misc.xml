﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <EffecterDef>
    <defName>Vomit</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <scale>0.5~0.7</scale>
        <ticksBetweenMotes>20</ticksBetweenMotes>
        <positionLerpFactor>0.35</positionLerpFactor>
        <positionRadius>0.2</positionRadius>
        <moteDef>Mote_VomitBit</moteDef>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>ExtinguisherExplosion</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.2</positionRadius>
        <moteDef>Mote_ExtinguisherPuff</moteDef>
        <burstCount>10~15</burstCount>
        <speed>4.8~8.4</speed>
        <scale>4~5</scale>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>ExtinguisherPuffSmall</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.2</positionRadius>
        <moteDef>Mote_ExtinguisherPuff</moteDef>
        <burstCount>5~10</burstCount>
        <speed>0.8~1.5</speed>
        <scale>0.5~0.8</scale>
      </li>
    </children>
  </EffecterDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_ExtinguisherPuff</defName>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.25</fadeInTime>
      <solidTime>0.55</solidTime>
      <fadeOutTime>1.65</fadeOutTime>
      <growthRate>0.07</growthRate>
      <collide>true</collide>
      <speedPerTime>-0.12</speedPerTime>
    </mote>
    <graphicData>
      <texPath>Things/Mote/Smoke</texPath>
    </graphicData>
  </ThingDef>

  <EffecterDef>
    <defName>PlayPoker</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_InteractSymbol</subEffecterClass>
        <moteDef>Mote_Cards</moteDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SoundIntermittent</subEffecterClass>
        <soundDef>PokerChips</soundDef>
        <intermittentSoundInterval><min>750</min><max>4300</max></intermittentSoundInterval>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>GiantExplosion</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_GiantExplosion</moteDef>
        <scale>1~1</scale>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_GiantExplosionInner</moteDef>
        <scale>5~5</scale>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>DisabledByEMP</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_Sustainer</subEffecterClass>
        <soundDef>EmpDisabled</soundDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>GrowingFlash</fleckDef>
        <burstCount>1~1</burstCount>
        <scale>5~5</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>BlastEMP</fleckDef>
        <scale>0.6~1</scale>
        <spawnLocType>RandomCellOnTarget</spawnLocType>
        <burstCount>1~1</burstCount>
        <chancePerTick>0.052</chancePerTick>
        <positionRadius>0.25</positionRadius>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <fleckDef>Smoke</fleckDef>
        <ticksBetweenMotes>30</ticksBetweenMotes>
        <scale>0.6~1.1</scale>
        <spawnLocType>OnSource</spawnLocType>
        <absoluteAngle>true</absoluteAngle>
        <angle>30~70</angle>
        <speed>0.2~0.35</speed>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>DisabledByEMPLarge</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_Sustainer</subEffecterClass>
        <soundDef>EmpDisabled</soundDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>GrowingFlash</fleckDef>
        <burstCount>1~1</burstCount>
        <scale>5~5</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>ArcLargeEMP_A</fleckDef>
        <scale>0.5~1.0</scale>
        <spawnLocType>RandomCellOnTarget</spawnLocType>
        <burstCount>1~1</burstCount>
        <chancePerTick>0.06</chancePerTick>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>ArcLargeEMP_B</fleckDef>
        <scale>0.6~1.8</scale>
        <spawnLocType>RandomCellOnTarget</spawnLocType>
        <burstCount>1~1</burstCount>
        <chancePerTick>0.06</chancePerTick>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <fleckDef>Smoke</fleckDef>
        <ticksBetweenMotes>30</ticksBetweenMotes>
        <scale>0.6~1.1</scale>
        <spawnLocType>OnSource</spawnLocType>
        <absoluteAngle>true</absoluteAngle>
        <angle>30~70</angle>
        <speed>0.2~0.35</speed>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>WatchingTelevision</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_Sustainer</subEffecterClass>
        <soundDef>Television_Ambience</soundDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>PlainFlash</fleckDef>
        <spawnLocType>RandomCellOnTarget</spawnLocType>
        <chancePerTick>0.05</chancePerTick>
        <color>(1,1,1,0.1)</color>
        <scale>1.5~3</scale>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>RaisedRock_Collapse</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.2</positionRadius>
        <fleckDef>DustPuff</fleckDef>
        <burstCount>4~7</burstCount>
        <speed>0.8~1.4</speed>
        <scale>2~2.5</scale>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>BulletShieldGenerator_Reactivate</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>BulletShieldGenerator_Reactivate</soundDef>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>GrowingFlash</fleckDef>
        <burstCount>1~1</burstCount>
        <scale>0~0</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>AcidSpray_Directional</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.15</positionRadius>
        <fleckDef>AcidSpray</fleckDef>
        <burstCount>10~15</burstCount>
        <speed>0.6~3.0</speed>
        <scale>0.5~1.2</scale>
        <angle>-15~15</angle>
        <positionLerpFactor>0.8</positionLerpFactor>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>FoamSpray_Directional</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.15</positionRadius>
        <fleckDef>FoamSpray</fleckDef>
        <burstCount>2~4</burstCount>
        <speed>0.6~3.3</speed>
        <scale>0.8~1.25</scale>
        <angle>-24~24</angle>
        <positionLerpFactor>0.85</positionLerpFactor>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Vaporize_Heatwave</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>Fleck_HeatWaveDistortion</fleckDef>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>Fleck_HeatDiffusion</fleckDef>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>Fleck_RadialSparks</fleckDef>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>Fleck_VaporizeGlow</fleckDef>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>Fleck_VaporizeRays_A</fleckDef>
        <burstCount>1</burstCount>
        <rotationRate>30</rotationRate>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>Fleck_VaporizeRays_B</fleckDef>
        <burstCount>1</burstCount>
        <rotationRate>-20</rotationRate>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>Fleck_VaporizeCenterFlash</fleckDef>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>
  
  <EffecterDef>
    <defName>Shield_Break</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>EnergyShield_Broken</soundDef>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>FlashShieldBreak</moteDef>
        <scale>2</scale>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <attachToSpawnThing>true</attachToSpawnThing>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>CracksShieldBreak</moteDef>
        <scale>2</scale>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <attachToSpawnThing>true</attachToSpawnThing>
      </li>
    </children>
  </EffecterDef>
  
  <EffecterDef Name="Power_Cell_Burning_Base" Abstract="True">
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <positionRadius>0.15</positionRadius>
        <moteDef>Mote_PowerCellBurning</moteDef>
        <ticksBetweenMotes>50</ticksBetweenMotes>
        <scale>0.9~1.1</scale>
        <angle>0</angle>
        <rotation>-15~15</rotation>
        <positionOffset>(0, 0, 0.4)</positionOffset>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>SmokeGrowing</fleckDef>
        <chancePerTick>0.05</chancePerTick>
        <scale>1.1~1.5</scale>
        <rotationRate>15~30</rotationRate>
        <speed>0.6~1.8</speed>
        <angle>32~40</angle>
        <fleckUsesAngleForVelocity>true</fleckUsesAngleForVelocity>
        <positionOffset>(0, 0, 0.4)</positionOffset>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef ParentName="Power_Cell_Burning_Base">
  <defName>Power_Cell_Burning</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <positionRadius>0.2</positionRadius>
        <fleckDef>FlashPowerCell</fleckDef>
        <ticksBetweenMotes>15</ticksBetweenMotes>
        <scale>4~5</scale>
        <angle>0</angle>
        <rotation>0~360</rotation>
        <positionOffset>(0, 0, 0.35)</positionOffset>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>BurningPowerCell_Start</soundDef>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_Sustainer</subEffecterClass>
        <soundDef>BurningPowerCell_Loop</soundDef>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef ParentName="Power_Cell_Burning_Base">
    <defName>Power_Cell_Sparks</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <positionRadius>0.2</positionRadius>
        <fleckDef>FlashPowerCellIntense</fleckDef>
        <ticksBetweenMotes>13</ticksBetweenMotes>
        <scale>4~6</scale>
        <angle>0</angle>
        <rotation>0~360</rotation>
        <positionOffset>(0, 0, 0.35)</positionOffset>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <moteDef>Mote_SparkThrownFastBright</moteDef>
        <chancePerTick>0.7</chancePerTick>
        <scale>0.2~0.25</scale>
        <airTime>0.08~0.16</airTime>
        <rotationRate>-240~240</rotationRate>
        <speed>9~15</speed>
        <angle>-30~30</angle>
        <positionOffset>(0, 0, 0.4)</positionOffset>
        <positionRadius>0.01</positionRadius>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>BurningPowerCell_End</soundDef>
        <spawnLocType>OnTarget</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Skip_EntryNoDelay</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>PsycastSkipFlashEntry</fleckDef>
        <burstCount>1~1</burstCount>
        <scale>1~1</scale>
        <rotation>0~0</rotation>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>PlainFlash</fleckDef>
        <burstCount>1~1</burstCount>
        <scale>7.0~7.0</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <fleckDef>PsycastSkipInnerEntry</fleckDef>
        <initialDelayTicks>11</initialDelayTicks>
        <burstCount>1~1</burstCount>
        <scale>1~1</scale>
        <rotation>0~0</rotation>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <fleckDef>PsycastSkipOuterRingEntry</fleckDef>
        <initialDelayTicks>11</initialDelayTicks>
        <burstCount>1~1</burstCount>
        <scale>1~1</scale>
        <rotation>0~0</rotation>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>1.3</positionRadius>
        <fleckDef>ElectricalSpark</fleckDef>
        <burstCount>7~9</burstCount>
        <scale>2.5</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <positionRadius>1.3</positionRadius>
        <initialDelayTicks>5</initialDelayTicks>
        <fleckDef>ElectricalSpark</fleckDef>
        <burstCount>5~7</burstCount>
        <scale>2.5</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <positionRadius>1.3</positionRadius>
        <initialDelayTicks>10</initialDelayTicks>
        <fleckDef>ElectricalSpark</fleckDef>
        <burstCount>3~7</burstCount>
        <scale>1.5</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>ToxGasReleasing</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_Sustainer</subEffecterClass>
        <soundDef>GasReleasing</soundDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.3</positionRadius>
        <fleckDef>Fleck_ToxGasSmall</fleckDef>
        <chancePerTick>0.1</chancePerTick>
        <scale>0.5~1</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>
  
  <EffecterDef>
    <defName>ForcedVisible</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_ForcedVisible</moteDef>
        <scale>.2</scale>
        <spawnLocType>OnTarget</spawnLocType>
        <absoluteAngle>true</absoluteAngle>
        <angle>0</angle>
        <speed>0</speed>
        <rotation>0</rotation>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>Skip_Entry</defName>
    <maintainTicks>180</maintainTicks>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <fleckDef>PlainFlash</fleckDef>
        <burstCount>1~1</burstCount>
        <scale>7.0~7.0</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <fleckDef>PsycastSkipInnerEntry</fleckDef>
        <initialDelayTicks>11</initialDelayTicks>
        <burstCount>1~1</burstCount>
        <scale>1~1</scale>
        <rotation>0~0</rotation>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <fleckDef>PsycastSkipOuterRingEntry</fleckDef>
        <initialDelayTicks>11</initialDelayTicks>
        <burstCount>1~1</burstCount>
        <scale>1~1</scale>
        <rotation>0~0</rotation>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>1.3</positionRadius>
        <fleckDef>ElectricalSpark</fleckDef>
        <burstCount>7~9</burstCount>
        <scale>2.5</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <positionRadius>1.3</positionRadius>
        <initialDelayTicks>5</initialDelayTicks>
        <fleckDef>ElectricalSpark</fleckDef>
        <burstCount>5~7</burstCount>
        <scale>2.5</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <positionRadius>1.3</positionRadius>
        <initialDelayTicks>10</initialDelayTicks>
        <fleckDef>ElectricalSpark</fleckDef>
        <burstCount>3~7</burstCount>
        <scale>1.5</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Skip_Exit</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <fleckDef>PsycastSkipFlashExit</fleckDef>
        <initialDelayTicks>12</initialDelayTicks>
        <burstCount>1~1</burstCount>
        <scale>1~1</scale>
        <rotation>0~0</rotation>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <fleckDef>PlainFlash</fleckDef>
        <initialDelayTicks>14</initialDelayTicks>
        <burstCount>1~1</burstCount>
        <scale>7.0~7.0</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <positionRadius>1.3</positionRadius>
        <initialDelayTicks>16</initialDelayTicks>
        <fleckDef>ElectricalSpark</fleckDef>
        <burstCount>7~9</burstCount>
        <scale>2.5</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <positionRadius>1.3</positionRadius>
        <initialDelayTicks>24</initialDelayTicks>
        <fleckDef>ElectricalSpark</fleckDef>
        <burstCount>5~7</burstCount>
        <scale>2.5</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <positionRadius>1.3</positionRadius>
        <initialDelayTicks>32</initialDelayTicks>
        <fleckDef>ElectricalSpark</fleckDef>
        <burstCount>3~7</burstCount>
        <scale>1.5</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Skip_ExitNoDelay</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>PsycastSkipInnerExit</fleckDef>
        <burstCount>1~1</burstCount>
        <scale>1~1</scale>
        <rotation>0~0</rotation>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>PsycastSkipOuterRingExit</fleckDef>
        <burstCount>1~1</burstCount>
        <scale>1~1</scale>
        <rotation>0~0</rotation>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <fleckDef>PsycastSkipFlashExit</fleckDef>
        <initialDelayTicks>12</initialDelayTicks>
        <burstCount>1~1</burstCount>
        <scale>1~1</scale>
        <rotation>0~0</rotation>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <fleckDef>PlainFlash</fleckDef>
        <initialDelayTicks>14</initialDelayTicks>
        <burstCount>1~1</burstCount>
        <scale>7.0~7.0</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <positionRadius>1.3</positionRadius>
        <initialDelayTicks>16</initialDelayTicks>
        <fleckDef>ElectricalSpark</fleckDef>
        <burstCount>7~9</burstCount>
        <scale>2.5</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <positionRadius>1.3</positionRadius>
        <initialDelayTicks>24</initialDelayTicks>
        <fleckDef>ElectricalSpark</fleckDef>
        <burstCount>5~7</burstCount>
        <scale>2.5</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggeredDelayed</subEffecterClass>
        <positionRadius>1.3</positionRadius>
        <initialDelayTicks>32</initialDelayTicks>
        <fleckDef>ElectricalSpark</fleckDef>
        <burstCount>3~7</burstCount>
        <scale>1.5</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>EmergencePointSustained8X8</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_GroupedChance</subEffecterClass> <!-- For synchronizing effects from the same random event -->
        <chancePerTick>.01</chancePerTick>
        <chancePeriodTicks>20</chancePeriodTicks>
        <children>
          <li>
            <subEffecterClass>SubEffecter_CameraShakeSingle</subEffecterClass>
            <cameraShake>.1~.3</cameraShake>
            <distanceAttenuationScale>1</distanceAttenuationScale>
            <distanceAttenuationMax>30</distanceAttenuationMax>
            <soundDef>Emergence_Quake</soundDef>
          </li>
          <li>
            <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
            <fleckDef>DustPuffThick</fleckDef>
            <initialDelayTicks>0</initialDelayTicks>
            <burstCount>2~4</burstCount>
            <spawnLocType>OnSource</spawnLocType>
            <positionRadius>4</positionRadius>
            <scale>1.5~3</scale>
            <rotation>0</rotation>
            <rotationRate>-60~60</rotationRate>
            <absoluteAngle>False</absoluteAngle>
            <positionOffset>(0, 0, 0)</positionOffset>
            <angle>0~360</angle>
            <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
            <speed>.6~.75</speed>
            <makeMoteOnSubtrigger>true</makeMoteOnSubtrigger>
          </li>

          <li>
            <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
            <avoidLastPositionRadius>1</avoidLastPositionRadius>
            <fleckDef>GroundCrack</fleckDef>
            <initialDelayTicks>0</initialDelayTicks>
            <burstCount>1</burstCount>
            <color>(1, 1, 1, .75)</color>
            <spawnLocType>OnSource</spawnLocType>
            <positionRadius>4</positionRadius>
            <positionRadiusMin>3</positionRadiusMin>
            <scale>.75~2</scale>
            <rotation>0~360</rotation>
            <rotationRate>0</rotationRate>
            <absoluteAngle>True</absoluteAngle>
            <positionOffset>(-.5, 0, -.5)</positionOffset>
            <angle>0</angle>
            <fleckUsesAngleForVelocity>False</fleckUsesAngleForVelocity>
            <speed>0</speed>
            <makeMoteOnSubtrigger>true</makeMoteOnSubtrigger>
          </li>
          
          <li>
            <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
            <avoidLastPositionRadius>1</avoidLastPositionRadius>
            <fleckDef>ThrownDebris</fleckDef>
            <initialDelayTicks>0</initialDelayTicks>
            <burstCount>5~10</burstCount>
            <spawnLocType>OnSource</spawnLocType>
            <rotationRate>100~200</rotationRate>
            <positionRadius>4</positionRadius>
            <positionRadiusMin>0</positionRadiusMin>
            <scale>.5~1</scale>
            <rotation>0~360</rotation>
            <absoluteAngle>False</absoluteAngle>
            <positionOffset>(-.5, 0, -.5)</positionOffset>
            <angle>0~360</angle>
            <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
            <speed>1~3</speed>
            <makeMoteOnSubtrigger>true</makeMoteOnSubtrigger>
          </li>
        </children>
      </li>
      
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>GroundCrackHuge</moteDef>
        <destroyMoteOnCleanup>true</destroyMoteOnCleanup>
        <initialDelayTicks>0</initialDelayTicks>
        <burstCount>1</burstCount>
        <color>(1, 1, 1, .65)</color>
        <spawnLocType>OnSource</spawnLocType>
        <scale>6</scale>
        <rotation>0~360</rotation>
        <rotationRate>0</rotationRate>
        <absoluteAngle>True</absoluteAngle>
        <positionOffset>(0, 0, 0)</positionOffset>
        <angle>0</angle>
        <fleckUsesAngleForVelocity>False</fleckUsesAngleForVelocity>
        <speed>0</speed>
        <makeMoteOnSubtrigger>true</makeMoteOnSubtrigger>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>EmergencePointComplete8X8</defName>
    <maintainTicks>300</maintainTicks>
    <children>
      <li>
        <subEffecterClass>SubEffecter_CameraShakeSingle</subEffecterClass>
        <cameraShake>.25</cameraShake>
        <distanceAttenuationScale>1</distanceAttenuationScale>
        <distanceAttenuationMax>100</distanceAttenuationMax>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>ShockwaveFast</fleckDef>
        <burstCount>1</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <positionRadius>0</positionRadius>
        <scale>.01</scale>
        <rotation>0</rotation>
        <rotationRate>0</rotationRate>
        <absoluteAngle>True</absoluteAngle>
        <positionOffset>(0, 0, 0)</positionOffset>
        <angle>0</angle>
        <fleckUsesAngleForVelocity>false</fleckUsesAngleForVelocity>
        <speed>0</speed>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>DustPuffThick</fleckDef>
        <burstCount>30~40</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <positionRadius>8</positionRadius>
        <scale>1.5~3</scale>
        <rotation>0</rotation>
        <rotationRate>-60~60</rotationRate>
        <absoluteAngle>False</absoluteAngle>
        <positionOffset>(0, 0, 0)</positionOffset>
        <angle>0~360</angle>
        <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
        <speed>.6~.75</speed>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>ThrownDebris</fleckDef>
        <initialDelayTicks>0</initialDelayTicks>
        <chancePerTick>1</chancePerTick>
        <chancePeriodTicks>3</chancePeriodTicks>
        <burstCount>2~6</burstCount>
        <lifespanMaxTicks>40</lifespanMaxTicks>
        <spawnLocType>OnSource</spawnLocType>
        <rotationRate>100~200</rotationRate>
        <positionRadius>4</positionRadius>
        <positionRadiusMin>4</positionRadiusMin>
        <scale>1.5~2</scale>
        <rotation>0~360</rotation>
        <absoluteAngle>False</absoluteAngle>
        <positionOffset>(-.5, 0, -.5)</positionOffset>
        <angle>0~360</angle>
        <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
        <speed>.5~5</speed>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>EmergencePointSustained3X3</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_GroupedChance</subEffecterClass>
        <chancePerTick>.01</chancePerTick>
        <chancePeriodTicks>20</chancePeriodTicks>
        <children>
          <li>
            <subEffecterClass>SubEffecter_CameraShakeSingle</subEffecterClass>
            <cameraShake>.1~.2</cameraShake>
            <soundDef>Emergence_Quake</soundDef>
            <distanceAttenuationScale>1</distanceAttenuationScale>
            <distanceAttenuationMax>20</distanceAttenuationMax>
          </li>
          <li>
            <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
            <fleckDef>DustPuffThick</fleckDef>
            <burstCount>1~3</burstCount>
            <spawnLocType>OnSource</spawnLocType>
            <positionRadius>2</positionRadius>
            <scale>1.5~3</scale>
            <rotation>0</rotation>
            <rotationRate>-10~10</rotationRate>
            <absoluteAngle>False</absoluteAngle>
            <positionOffset>(0, 0, 0)</positionOffset>
            <angle>0~360</angle>
            <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
            <speed>.2~.3</speed>
            <makeMoteOnSubtrigger>true</makeMoteOnSubtrigger>
          </li>
          <li>
            <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
            <avoidLastPositionRadius>1</avoidLastPositionRadius>
            <fleckDef>GroundCrack</fleckDef>
            <initialDelayTicks>0</initialDelayTicks>
            <burstCount>1</burstCount>
            <color>(1, 1, 1, .75)</color>
            <spawnLocType>OnSource</spawnLocType>
            <positionRadius>1</positionRadius>
            <positionRadiusMin>1</positionRadiusMin>
            <scale>.5~1.5</scale>
            <rotation>0~360</rotation>
            <rotationRate>0</rotationRate>
            <absoluteAngle>True</absoluteAngle>
            <positionOffset>(-.25, 0, -.25)</positionOffset>
            <angle>0</angle>
            <fleckUsesAngleForVelocity>False</fleckUsesAngleForVelocity>
            <speed>0</speed>
            <makeMoteOnSubtrigger>true</makeMoteOnSubtrigger>
          </li>
          <li>
            <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
            <avoidLastPositionRadius>1</avoidLastPositionRadius>
            <fleckDef>ThrownDebris</fleckDef>
            <initialDelayTicks>0</initialDelayTicks>
            <burstCount>2~6</burstCount>
            <spawnLocType>OnSource</spawnLocType>
            <rotationRate>100~200</rotationRate>
            <positionRadius>2</positionRadius>
            <positionRadiusMin>0</positionRadiusMin>
            <scale>.5~1</scale>
            <rotation>0~360</rotation>
            <absoluteAngle>False</absoluteAngle>
            <positionOffset>(-.5, 0, -.5)</positionOffset>
            <angle>0~360</angle>
            <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
            <speed>.1~1</speed>
            <makeMoteOnSubtrigger>true</makeMoteOnSubtrigger>
          </li>
        </children>
      </li>
      
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>GroundCrackHuge</moteDef>
        <destroyMoteOnCleanup>true</destroyMoteOnCleanup>
        <initialDelayTicks>0</initialDelayTicks>
        <burstCount>1</burstCount>
        <color>(1, 1, 1, .75)</color>
        <spawnLocType>OnSource</spawnLocType>
        <scale>2.25</scale>
        <rotation>0~360</rotation>
        <rotationRate>0</rotationRate>
        <absoluteAngle>True</absoluteAngle>
        <positionOffset>(0, 0, 0)</positionOffset>
        <angle>0</angle>
        <fleckUsesAngleForVelocity>False</fleckUsesAngleForVelocity>
        <speed>0</speed>
        <makeMoteOnSubtrigger>true</makeMoteOnSubtrigger>
      </li>
     
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>EmergencePointComplete3X3</defName>
    <maintainTicks>300</maintainTicks>
    <children>
      <li>
        <subEffecterClass>SubEffecter_CameraShakeSingle</subEffecterClass>
        <cameraShake>.2</cameraShake>
        <distanceAttenuationScale>1</distanceAttenuationScale>
        <distanceAttenuationMax>100</distanceAttenuationMax>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>DustPuffThick</fleckDef>
        <burstCount>20</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <positionRadius>3</positionRadius>
        <scale>1.5~3</scale>
        <rotation>0</rotation>
        <rotationRate>-60~60</rotationRate>
        <absoluteAngle>False</absoluteAngle>
        <positionOffset>(0, 0, 0)</positionOffset>
        <angle>0~360</angle>
        <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
        <speed>.6~.75</speed>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>ThrownDebris</fleckDef>
        <initialDelayTicks>0</initialDelayTicks>
        <chancePerTick>1</chancePerTick>
        <chancePeriodTicks>3</chancePeriodTicks>
        <burstCount>2~5</burstCount>
        <lifespanMaxTicks>40</lifespanMaxTicks>
        <spawnLocType>OnSource</spawnLocType>
        <rotationRate>100~200</rotationRate>
        <positionRadius>2</positionRadius>
        <positionRadiusMin>2</positionRadiusMin>
        <scale>1~1.5</scale>
        <rotation>0~360</rotation>
        <absoluteAngle>False</absoluteAngle>
        <positionOffset>(-.5, 0, -.5)</positionOffset>
        <angle>0~360</angle>
        <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
        <speed>.5~4</speed>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>EmergencePointSustained2X2</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_GroupedChance</subEffecterClass> <!-- For synchronizing effects from the same random event -->
        <chancePerTick>.01</chancePerTick>
        <chancePeriodTicks>20</chancePeriodTicks>
        <children>
          <li>
            <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
            <fleckDef>DustPuffThick</fleckDef>
            <initialDelayTicks>0</initialDelayTicks>
            <burstCount>1~2</burstCount>
            <spawnLocType>OnSource</spawnLocType>
            <positionRadius>1.5</positionRadius>
            <scale>1.5~3</scale>
            <rotation>0</rotation>
            <rotationRate>-60~60</rotationRate>
            <absoluteAngle>False</absoluteAngle>
            <positionOffset>(0, 0, 0)</positionOffset>
            <angle>0~360</angle>
            <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
            <speed>.1~.3</speed>
            <makeMoteOnSubtrigger>true</makeMoteOnSubtrigger>
          </li>

          <li>
            <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
            <avoidLastPositionRadius>1</avoidLastPositionRadius>
            <fleckDef>GroundCrack</fleckDef>
            <initialDelayTicks>0</initialDelayTicks>
            <burstCount>1</burstCount>
            <color>(1, 1, 1, .75)</color>
            <spawnLocType>OnSource</spawnLocType>
            <positionRadius>.75</positionRadius>
            <positionRadiusMin>.75</positionRadiusMin>
            <scale>.5~1.2</scale>
            <rotation>0~360</rotation>
            <rotationRate>0</rotationRate>
            <absoluteAngle>True</absoluteAngle>
            <positionOffset>(-.25, 0, -.25)</positionOffset>
            <angle>0</angle>
            <fleckUsesAngleForVelocity>False</fleckUsesAngleForVelocity>
            <speed>0</speed>
            <makeMoteOnSubtrigger>true</makeMoteOnSubtrigger>
          </li>
          <li>
            <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
            <avoidLastPositionRadius>1</avoidLastPositionRadius>
            <fleckDef>ThrownDebris</fleckDef>
            <initialDelayTicks>0</initialDelayTicks>
            <burstCount>5~10</burstCount>
            <spawnLocType>OnSource</spawnLocType>
            <rotationRate>100~200</rotationRate>
            <positionRadius>1</positionRadius>
            <positionRadiusMin>0</positionRadiusMin>
            <scale>.5~1</scale>
            <rotation>0~360</rotation>
            <absoluteAngle>False</absoluteAngle>
            <positionOffset>(-.5, 0, -.5)</positionOffset>
            <angle>0~360</angle>
            <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
            <speed>1~3</speed>
            <makeMoteOnSubtrigger>true</makeMoteOnSubtrigger>
          </li>
        </children>
      </li>
      
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>GroundCrackHuge</moteDef>
        <initialDelayTicks>0</initialDelayTicks>
        <destroyMoteOnCleanup>true</destroyMoteOnCleanup>
        <burstCount>1</burstCount>
        <color>(1, 1, 1, .75)</color>
        <spawnLocType>OnSource</spawnLocType>
        <scale>2.25</scale>
        <rotation>0~360</rotation>
        <rotationRate>0</rotationRate>
        <absoluteAngle>True</absoluteAngle>
        <positionOffset>(0, 0, 0)</positionOffset>
        <angle>0</angle>
        <fleckUsesAngleForVelocity>False</fleckUsesAngleForVelocity>
        <speed>0</speed>
        <makeMoteOnSubtrigger>true</makeMoteOnSubtrigger>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>EmergencePointComplete2X2</defName>
    <maintainTicks>300</maintainTicks>
    <children>
      <li>
        <subEffecterClass>SubEffecter_CameraShakeSingle</subEffecterClass>
        <cameraShake>.1</cameraShake>
        <distanceAttenuationScale>1</distanceAttenuationScale>
        <distanceAttenuationMax>100</distanceAttenuationMax>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>DustPuffThick</fleckDef>
        <burstCount>20</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <positionRadius>3</positionRadius>
        <scale>1.5~3</scale>
        <rotation>0</rotation>
        <rotationRate>-60~60</rotationRate>
        <absoluteAngle>False</absoluteAngle>
        <positionOffset>(0, 0, 0)</positionOffset>
        <angle>0~360</angle>
        <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
        <speed>.6~.75</speed>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>ThrownDebris</fleckDef>
        <initialDelayTicks>0</initialDelayTicks>
        <chancePerTick>1</chancePerTick>
        <chancePeriodTicks>3</chancePeriodTicks>
        <burstCount>2~5</burstCount>
        <lifespanMaxTicks>40</lifespanMaxTicks>
        <spawnLocType>OnSource</spawnLocType>
        <rotationRate>100~200</rotationRate>
        <positionRadius>2</positionRadius>
        <positionRadiusMin>2</positionRadiusMin>
        <scale>1~1.5</scale>
        <rotation>0~360</rotation>
        <absoluteAngle>False</absoluteAngle>
        <positionOffset>(-.5, 0, -.5)</positionOffset>
        <angle>0~360</angle>
        <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
        <speed>.5~3</speed>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>PawnEmergeFromWater</defName>
    <maintainTicks>60</maintainTicks>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <chancePeriodTicks>7</chancePeriodTicks>
        <lifespanMaxTicks>14</lifespanMaxTicks>
        <chancePerTick>.3</chancePerTick>
        <fleckDef>GroundWaterSplash</fleckDef>
        <burstCount>2~3</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <positionRadius>0</positionRadius>
        <positionOffset>(0, 0, 0)</positionOffset>
        <scale>1~2</scale>
        <absoluteAngle>True</absoluteAngle>
        <rotationRate>0</rotationRate>
        <rotation>0</rotation>
        <speed>0</speed>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>WaterChurnNoise</fleckDef>
        <burstCount>10~12</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <positionRadius>.5</positionRadius>
        <positionOffset>(0, 0, 0)</positionOffset>
        <scale>4~10</scale>
        <angle>0~360</angle>
        <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
        <absoluteAngle>false</absoluteAngle>
        <rotationRate>30~50</rotationRate>
        <rotation>0</rotation>
        <speed>-3~3</speed>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>PawnEmergeFromWaterLarge</defName>
    <maintainTicks>60</maintainTicks>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <chancePeriodTicks>7</chancePeriodTicks>
        <lifespanMaxTicks>14</lifespanMaxTicks>
        <chancePerTick>1</chancePerTick>
        <fleckDef>GroundWaterSplash</fleckDef>
        <burstCount>2~3</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <positionRadius>0</positionRadius>
        <positionOffset>(0, 0, 0)</positionOffset>
        <scale>5~7</scale>
        <absoluteAngle>True</absoluteAngle>
        <rotationRate>0</rotationRate>
        <rotation>0</rotation>
        <speed>0</speed>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>WaterChurnNoise</fleckDef>
        <burstCount>10~12</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <positionRadius>.75</positionRadius>
        <positionOffset>(0, 0, 0)</positionOffset>
        <scale>4~10</scale>
        <angle>0~360</angle>
        <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
        <absoluteAngle>false</absoluteAngle>
        <rotationRate>30~50</rotationRate>
        <rotation>0</rotation>
        <speed>-3~3</speed>
      </li>
    </children>
  </EffecterDef>


  <EffecterDef>
    <defName>WaterMist</defName>
    <maintainTicks>40</maintainTicks>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <fleckDef>WaterChurnNoise</fleckDef>
        <burstCount>1~2</burstCount>
        <spawnLocType>OnSource</spawnLocType>
        <positionRadius>.75</positionRadius>
        <positionOffset>(0, 0, 0)</positionOffset>
        <scale>4~10</scale>
        <angle>0~360</angle>
        <fleckUsesAngleForVelocity>True</fleckUsesAngleForVelocity>
        <absoluteAngle>false</absoluteAngle>
        <rotationRate>30~50</rotationRate>
        <rotation>0</rotation>
        <speed>-3~3</speed>
      </li>
    </children>
  </EffecterDef>


</Defs>
