﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <SoundDef>
    <defName>Pawn_Fleshbeast_EmergeFromPitGate</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Building/PitGate/FleshbeastEmerge</clipFolderPath>
          </li>
        </grains>
        <volumeRange>20~28</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <distRange>0~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Fleshbeast_Bulbfreak_Call</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Bulbfreak/Call</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <pitchRange>0.95~1.05</pitchRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Fleshbeast_Bulbfreak_Wounded</defName>
    <context>MapOnly</context>
    <maxVoices>1</maxVoices>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Bulbfreak/Pain</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <pitchRange>0.95~1.05</pitchRange>
        <repeatMode>NeverTwice</repeatMode>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Fleshbeast_Bulbfreak_Death</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Bulbfreak/Death</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <pitchRange>0.95~1.05</pitchRange>
        <sustainLoop>False</sustainLoop> 
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Fleshbeast_Bulbfreak_Moving</defName>
    <context>MapOnly</context>
    <sustain>True</sustain>
    <maxSimultaneous>5</maxSimultaneous>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Bulbfreak/Moving</clipFolderPath>
          </li>
        </grains>
        <pitchRange>0.85~1.15</pitchRange>
        <volumeRange>5~10</volumeRange>
        <gameSpeedRange>1</gameSpeedRange>
        <muteWhenPaused>true</muteWhenPaused>
        <sustainIntervalRange>0.5~2</sustainIntervalRange>
        <repeatMode>NeverTwice</repeatMode>
        <sustainLoop>false</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Fleshbeast_Toughspike_Call</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Toughspike/Call</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <pitchRange>0.95~1.05</pitchRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Fleshbeast_Toughspike_Wounded</defName>
    <context>MapOnly</context>
    <maxVoices>1</maxVoices>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Toughspike/Pain</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <pitchRange>0.95~1.05</pitchRange>
        <repeatMode>NeverTwice</repeatMode>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Fleshbeast_Toughspike_Death</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Toughspike/Death</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <pitchRange>0.95~1.05</pitchRange>
        <sustainLoop>False</sustainLoop> 
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Fleshbeast_Trispike_Call</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Trispike/Call</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <pitchRange>0.95~1.05</pitchRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Fleshbeast_Trispike_Wounded</defName>
    <context>MapOnly</context>
    <maxVoices>1</maxVoices>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Trispike/Pain</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <pitchRange>0.95~1.05</pitchRange>
        <repeatMode>NeverTwice</repeatMode>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Fleshbeast_Trispike_Death</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Trispike/Death</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <pitchRange>0.95~1.05</pitchRange>
        <sustainLoop>False</sustainLoop> 
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Fleshbeast_Fingerspike_Call</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Fingerspike/Call</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <pitchRange>0.95~1.05</pitchRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Fleshbeast_Fingerspike_Wounded</defName>
    <context>MapOnly</context>
    <maxVoices>1</maxVoices>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Fingerspike/Pain</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <pitchRange>0.95~1.05</pitchRange>
        <repeatMode>NeverTwice</repeatMode>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Fleshbeast_Fingerspike_Death</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Fingerspike/Death</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <pitchRange>0.95~1.05</pitchRange>
        <sustainLoop>False</sustainLoop> 
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Fleshbeast_Dreadmeld_Call</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Dreadmeld/Call</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <pitchRange>0.95~1.05</pitchRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Fleshbeast_Dreadmeld_Wounded</defName>
    <context>MapOnly</context>
    <maxVoices>1</maxVoices>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Dreadmeld/Pain</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <pitchRange>0.95~1.05</pitchRange>
        <repeatMode>NeverTwice</repeatMode>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Fleshbeast_Dreadmeld_Death</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Dreadmeld/Death</clipFolderPath>
          </li>
          <li Class="AudioGrain_Clip">
            <clipPath>Pawn/Dreadmeld/Dreadmeld_Explode_A_01</clipPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <pitchRange>0.95~1.05</pitchRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Fleshbeast_Dreadmeld_Moving</defName>
    <context>MapOnly</context>
    <sustain>True</sustain>
    <maxSimultaneous>5</maxSimultaneous>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Dreadmeld/Moving</clipFolderPath>
          </li>
        </grains>
        <pitchRange>0.85~1.15</pitchRange>
        <volumeRange>5~10</volumeRange>
        <gameSpeedRange>1</gameSpeedRange>
        <muteWhenPaused>true</muteWhenPaused>
        <sustainIntervalRange>0.5~2</sustainIntervalRange>
        <repeatMode>NeverTwice</repeatMode>
        <sustainLoop>false</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Fleshbeast_Attack_Blunt</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Fleshbeast/Attack/Blunt</clipFolderPath>
          </li>
        </grains>
        <volumeRange>10~18</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <distRange>0~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Fleshbeast_Attack_Spike</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Fleshbeast/Attack/Spike</clipFolderPath>
          </li>
        </grains>
        <volumeRange>10~18</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <distRange>0~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Dreadmeld_Attack_Spike</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Dreadmeld/Attack</clipFolderPath>
          </li>
        </grains>
        <volumeRange>10~18</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <distRange>0~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>
  
</Defs>