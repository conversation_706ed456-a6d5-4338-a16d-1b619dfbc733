﻿<?xml version="1.0" encoding="utf-8"?>

<Defs>

  <ThingSetMakerDef>
    <defName>MapGen_FleshSackLoot</defName>
    <root Class="ThingSetMaker_RandomOption">
      <options>
        <!-- Corpses -->
        <li>
          <weight>1</weight>
          <thingSetMaker Class="ThingSetMaker_Pawn">
            <pawnKind>Pirate</pawnKind>
            <alive>false</alive>
            <corpseAgeRangeDays>10~300</corpseAgeRangeDays>
          </thingSetMaker>
        </li>
        <li>
          <weight>1</weight>
          <thingSetMaker Class="ThingSetMaker_Pawn">
            <pawnKind>Villager</pawnKind>
            <alive>false</alive>
            <corpseAgeRangeDays>10~300</corpseAgeRangeDays>
          </thingSetMaker>
        </li>
        <li>
          <weight>1</weight>
          <thingSetMaker Class="ThingSetMaker_Pawn">
            <pawnKind>PirateBoss</pawnKind>
            <alive>false</alive>
            <corpseAgeRangeDays>10~300</corpseAgeRangeDays>
          </thingSetMaker>
        </li>

        <!-- Tome -->
        <li>
          <weight>1</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Tome</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>

        <!-- Harbinger seed -->
        <li>
          <weight>1</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>HarbingerSeed</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        
        <!-- Shard -->
        <li>
          <weight>0.5</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Shard</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        
        <!-- Resources -->
        <li>
          <weight>0.5</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Gold</li>
                </thingDefs>
              </filter>
              <countRange>15~30</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.5</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Plasteel</li>
                </thingDefs>
              </filter>
              <countRange>15~30</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.5</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Hyperweave</li>
                </thingDefs>
              </filter>
              <countRange>15~30</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        
        <!-- Prosthetics -->
        <li>
          <weight>0.25</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>BionicEye</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.25</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>BionicArm</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.25</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>BionicLeg</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.25</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>BionicSpine</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.25</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>BionicHeart</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.25</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>BionicStomach</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.25</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>ArchotechArm</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.25</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>ArchotechEye</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.25</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>ArchotechLeg</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        
        <!-- Armor -->
        <li>
          <weight>0.25</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Apparel_ArmorHelmetRecon</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.25</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Apparel_ShieldBelt</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.25</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Apparel_AdvancedHelmet</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.25</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Apparel_FlakJacket</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.25</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Apparel_PlateArmor</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>

        <!-- Weapons -->
        <li>
          <weight>0.25</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>NerveSpiker</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.25</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Apparel_PackTurret</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        
        <!-- Rare items -->
        <li>
          <weight>0.15</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Shell_AntigrainWarhead</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.15</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>ShardAnimalPulser</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.15</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>PsychicSoothePulser</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.15</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>BiomutationPulser</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.15</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Gun_DoomsdayRocket</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.15</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Gun_TripleRocket</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.15</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Apparel_ShardPsychicInsanityLance</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.15</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Apparel_ShardPsychicShockLance</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.15</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Apparel_BiomutationLance</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.15</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>VoidSculpture</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.15</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>PsychicEmanator</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
        <li>
          <weight>0.15</weight>
          <thingSetMaker Class="ThingSetMaker_StackCount">
            <fixedParams>
              <filter>
                <thingDefs>
                  <li>Apparel_DisruptorFlarePack</li>
                </thingDefs>
              </filter>
              <countRange>1</countRange>
            </fixedParams>
          </thingSetMaker>
        </li>
      </options>
    </root>
  </ThingSetMakerDef>

</Defs>