<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <!--============================== Industrial world ============================-->

  <BackstoryDef>
    <defName>IndustrialOrphan13</defName>
    <title>industrial orphan</title>
    <titleShort>orphan</titleShort>
    <description>[PAWN_nameDef] never knew [PAWN_possessive] parents. [PAWN_possessive] earliest memories were of drudgery in the mines and workhouses of [PAWN_possessive] industrial world.\n\nBecause of this, [PAWN_pronoun] never received a proper education.</description>
    <slot>Childhood</slot>
    <skillGains>
      <Crafting>3</Crafting>
      <Mining>3</Mining>
      <Intellectual>-2</Intellectual>
    </skillGains>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
  </BackstoryDef>

  <!--============================== Urbworld ============================-->

  <BackstoryDef>
    <defName>UrbworldUrchin61</defName>
    <title>urbworld urchin</title>
    <titleShort>urchin</titleShort>
    <description>The urbworlds - ancient and deep industrial cityscapes bursting with humanity and poison. [PAWN_nameDef] grew up in the dark, unwanted reaches of such a place. [PAWN_pronoun] had to fight for every scrap of food.</description>
    <slot>Childhood</slot>
    <skillGains>
      <Melee>4</Melee>
      <Shooting>2</Shooting>
    </skillGains>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
      <li>Cult</li>
    </spawnCategories>
  </BackstoryDef>

  <BackstoryDef>
    <defName>TestSubject15</defName>
    <title>test subject</title>
    <titleShort>testee</titleShort>
    <description>On the most corrupt urbworlds, scientists without a moral compass commit unspeakable atrocities in the name of research. [PAWN_nameDef] was kept alone in a sealed facility from birth and subjected to a variety of behavioural experiments in an attempt to turn [PAWN_objective] into a perfect super-soldier.</description>
    <slot>Childhood</slot>
    <workDisables>
      <li>Social</li>
      <li>Caring</li>
      <li>Firefighting</li>
    </workDisables>
    <skillGains>
      <Shooting>4</Shooting>
    </skillGains>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
      <li>Madman</li>
      <li>Cult</li>
    </spawnCategories>
  </BackstoryDef>

  <!--============================== War-torn world =======================-->

  <BackstoryDef>
    <defName>WreckageExplorer93</defName>
    <title>wreckage explorer</title>
    <titleShort>explorer</titleShort>
    <description>[PAWN_nameDef] was tasked with watching [PAWN_possessive] family's herds, but often shirked [PAWN_possessive] duties to go exploring the crashed warships scattered around the planet.</description>
    <slot>Childhood</slot>
    <skillGains>
      <Animals>3</Animals>
      <Intellectual>3</Intellectual>
    </skillGains>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
      <li>Scientist</li>
    </spawnCategories>
  </BackstoryDef>

  <BackstoryDef>
    <defName>ApocalypseSurvivor23</defName>
    <title>apocalypse survivor</title>
    <titleShort>survivor</titleShort>
    <description>[PAWN_nameDef] was born during a time of unrest on [PAWN_possessive] homeworld, as climate change threatened mass starvation and flooding. As [PAWN_pronoun] grew up the situation worsened - billions died and peaceful states descended into anarchy. [PAWN_nameDef] and [PAWN_possessive] parents did whatever they had to to survive.</description>
    <slot>Childhood</slot>
    <skillGains>
      <Shooting>4</Shooting>
      <Artistic>-3</Artistic>
      <Intellectual>-3</Intellectual>
    </skillGains>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
      <li>Cult</li>
    </spawnCategories>
  </BackstoryDef>

  <!--============================== Caveworld ============================-->

  <BackstoryDef>
    <defName>CaveworldTender26</defName> 
    <title>caveworld tender</title>
    <titleShort>cave kid</titleShort>
    <description>[PAWN_nameDef] grew up in cave complex deep beneath the surface of an inhospitable world. [PAWN_pronoun] worked with the other children tending the tribe's fungus crops.</description>
    <slot>Childhood</slot>
    <skillGains>
      <Plants>4</Plants>
      <Mining>2</Mining>
      <Shooting>-3</Shooting>
    </skillGains>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
	    <li>Farmer</li>
    </spawnCategories>
    <forcedTraits>
      <Undergrounder>0</Undergrounder>
    </forcedTraits>
  </BackstoryDef>

  <BackstoryDef>
    <defName>CaveworldTunneler48</defName>
    <title>caveworld tunneler</title>
    <titleShort>tunneler</titleShort>
    <description>[PAWN_nameDef] worked as a digger in the massive underground cave complex.\n\n[PAWN_pronoun] knows rock so well that [PAWN_pronoun] can almost navigate caves by smell.</description>
    <slot>Childhood</slot>
    <workDisables>
      <li>Intellectual</li>
      <li>Crafting</li>
    </workDisables>
    <skillGains>
      <Construction>1</Construction>
      <Mining>6</Mining>
      <Shooting>-2</Shooting>
    </skillGains>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
	    <li>Miner</li>
    </spawnCategories>
    <bodyTypeMale>Hulk</bodyTypeMale>
    <bodyTypeFemale>Hulk</bodyTypeFemale>
    <forcedTraits>
      <Undergrounder>0</Undergrounder>
    </forcedTraits>
  </BackstoryDef>

  <!--============================== Rimworld ============================-->

  <BackstoryDef>
    <defName>Scout44</defName>
    <title>scout</title>
    <titleShort>scout</titleShort>
    <description>Born to the administrators of a rimworld colony, [PAWN_nameDef] was enrolled in a youth program that taught military scouting skills.\n\n[PAWN_pronoun] learned to survive in the wilderness, to obey, and not to ask questions.</description>
    <slot>Childhood</slot>
    <workDisables>
      <li>Artistic</li>
      <li>Intellectual</li>
    </workDisables>
    <skillGains>
      <Shooting>3</Shooting>
      <Crafting>2</Crafting>
      <Construction>1</Construction>
    </skillGains>
    <spawnCategories>
      <li>Offworld</li>
      <li>Outlander</li>
      <li>Pirate</li>
      <li>ImperialCommon</li>
    </spawnCategories>
  </BackstoryDef>

  <!--============================== Toxic world ======================-->

  <BackstoryDef>
    <defName>ShelterChild50</defName>
    <title>shelter child</title>
    <titleShort>shelterkid</titleShort>
    <description>[PAWN_nameDef] grew up in a shelter deep beneath a toxic world. [PAWN_pronoun] received a comprehensive education, but had no opportunity to do physical labour.</description>
    <slot>Childhood</slot>
    <skillGains>
      <Intellectual>3</Intellectual>
      <Medicine>3</Medicine>
      <Social>2</Social>
      <Construction>-2</Construction>
      <Mining>-2</Mining>
    </skillGains>
    <spawnCategories>
      <li>Offworld</li>
      <li>Pirate</li>
    </spawnCategories>
    <possessions>
      <TextBook>1</TextBook>
    </possessions>
  </BackstoryDef>

  <!--============================== Space ============================-->

  <!-- none -->

</Defs>