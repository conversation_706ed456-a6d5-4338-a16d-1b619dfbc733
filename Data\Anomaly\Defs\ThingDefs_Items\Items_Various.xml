﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>
  
  <ThingDef ParentName="ResourceBase">
    <defName>Shard</defName>
    <label>shard</label>
    <description>A scavenged fragment of dark archotechnology. Originally created by a hyperintelligent archotech mind, this shard is packed with technology beyond human comprehension. It is capable of manipulating psychic flows, inducing archotech influences and more esoteric outcomes. While it is useless by itself, it can be built into devices that harness its power to produce exotic effects.</description>
    <graphicData>
      <texPath>Things/Item/Resource/Shard</texPath>
      <graphicClass>Graphic_StackCount</graphicClass>
    </graphicData>
    <soundInteract>Metal_Drop</soundInteract>
    <soundDrop>Standard_Drop</soundDrop>
    <stackLimit>5</stackLimit>
    <healthAffectsPrice>false</healthAffectsPrice>
    <genericMarketSellable>false</genericMarketSellable>
    <useHitPoints>false</useHitPoints>
    <statBases>
      <MaxHitPoints>250</MaxHitPoints>
      <MarketValue>400</MarketValue>
      <Mass>0.6</Mass>
      <Flammability>0</Flammability>
      <DeteriorationRate>0</DeteriorationRate>
    </statBases>
    <intricate>true</intricate>
    <thingCategories>
      <li>Manufactured</li>
    </thingCategories>
  </ThingDef>

  <ThingDef ParentName="ResourceBase">
    <defName>RevenantSpine</defName>
    <label>revenant spine</label>
    <description>An articulated spine-like object from a dead revenant. It is a chaotic mesh of sharpened metal spokes, twisted into a warped cage. The material appears to be rough-cast black iron smeared in a dark, slippery fluid. Anyone who peers too closely inside its jagged form is left with a skull-splitting headache.\n\nYou can destroy the spine to permanently kill the revenant.\n\nAlternatively, you can secure it on a holding platform and wait for the revenant to re-emerge, so you can study the captured entity.</description>
    <thingClass>ThingWithComps</thingClass>
    <graphicData>
      <texPath>Things/Item/Special/RevenantSpine</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <tickerType>Rare</tickerType>
    <stackLimit>1</stackLimit>
    <healthAffectsPrice>false</healthAffectsPrice>
    <genericMarketSellable>false</genericMarketSellable>
    <statBases>
      <MaxHitPoints>30</MaxHitPoints>
      <MarketValue>1000</MarketValue>
      <Mass>5</Mass>
      <Flammability>0.2</Flammability>
      <MinimumContainmentStrength>80</MinimumContainmentStrength> <!-- Must match Revenant's minimum containment strength value -->
    </statBases>
    <thingCategories>
      <li>ItemsMisc</li>
    </thingCategories>
    <hiddenWhileUndiscovered>true</hiddenWhileUndiscovered>
    <comps>
      <li Class="CompProperties_HoldingPlatformTarget">
        <heldPawnKind>Revenant</heldPawnKind>
        <capturedLetterLabel>Revenant reformed</capturedLetterLabel>
        <capturedLetterText>After {PAWN_nameDef} chained the revenant spine to a holding platform, the revenant re-emerged.\n\nWhen killed, a revenant's body dissolves, leaving behind only its archotechnological spine. This cage of slick iron waits for a chance to grow a new body.</capturedLetterText>
      </li>
      <li Class="CompProperties_AttachPoints">
        <points>
          <li>
            <offset>(-.25, 0, .25)</offset>
            <type>PlatformRestraint0</type>
          </li>
          <li>
            <offset>(.25, 0, .25)</offset>
            <type>PlatformRestraint1</type>
          </li>
          <li>
            <offset>(.25, 0, -.25)</offset>
            <type>PlatformRestraint2</type>
          </li>
          <li>
            <offset>(-.25, 0, -.25)</offset>
            <type>PlatformRestraint3</type>
          </li>
        </points>
      </li>
      <li Class="CompProperties_SpawnsRevenant"/>
      <li Class="CompProperties_Usable">
        <useJob>SmashThing</useJob>
        <useLabel>Smash {0_label}</useLabel>
        <useMessage>{PAWN_nameDef} smashed the revenant spine. It dissolved to reveal a shard of dark archotechnology.</useMessage>
        <useDuration>300</useDuration>
      </li>
      <li Class="CompProperties_UseEffectPlaySound">
        <soundOnUsed>RevenantSpineSmash</soundOnUsed>
      </li>
      <li Class="CompProperties_UseEffectDestroySelf">
        <leavings>
          <Shard>1</Shard>
        </leavings>
      </li>
      <li Class="CompProperties_BiosignatureOwner"/>
      <li Class="CompProperties_Interactable">
        <compClass>CompRevenantSpine</compClass>
        <ticksToActivate>0</ticksToActivate>
        <activateTexPath>UI/Commands/SmashRevenantSpine</activateTexPath>
        <activateLabelString>Smash...</activateLabelString>
        <activateDescString>Smash the revenant spine to prevent it reforming into a new revenant.</activateDescString>
        <activatingStringPending>smashing revenant spine</activatingStringPending>
        <guiLabelString>Choose who should do this</guiLabelString>
        <showMustBeActivatedByColonist>false</showMustBeActivatedByColonist>
        <targetingParameters>
          <canTargetBuildings>false</canTargetBuildings>
          <canTargetAnimals>false</canTargetAnimals>
          <canTargetMechs>false</canTargetMechs>
          <onlyTargetControlledPawns>true</onlyTargetControlledPawns>
        </targetingParameters>
      </li>
    </comps>
  </ThingDef>

  <ThingDef>
    <defName>Meat_Twisted</defName>
    <label>twisted meat</label>
    <description>Pieces of twisted muscle and gristle, bloated with tumorous growths. These bizarre shreds of flesh must have come from a horrific creature.</description>
    <thingClass>ThingWithComps</thingClass>
    <selectable>true</selectable>
    <category>Item</category>
    <drawerType>MapMeshOnly</drawerType>
    <tickerType>Rare</tickerType>
    <rotatable>false</rotatable>
    <altitudeLayer>Item</altitudeLayer>
    <resourceReadoutPriority>Middle</resourceReadoutPriority>
    <drawGUIOverlay>true</drawGUIOverlay>
    <healthAffectsPrice>false</healthAffectsPrice>
    <stackLimit>75</stackLimit>
    <alwaysHaulable>true</alwaysHaulable>
    <socialPropernessMatters>true</socialPropernessMatters>
    <tradeability>Sellable</tradeability>
    <pathCost>14</pathCost>
    <statBases>
      <MaxHitPoints>60</MaxHitPoints>
      <Beauty>-6</Beauty>
      <DeteriorationRate>6</DeteriorationRate>
      <Mass>0.03</Mass>
      <Flammability>0.5</Flammability>
      <Nutrition>0.05</Nutrition>
      <FoodPoisonChanceFixedHuman>0.02</FoodPoisonChanceFixedHuman>
      <MarketValue>0.5</MarketValue>
    </statBases>
    <graphicData>
      <texPath>Things/Item/Resource/TwistedMeat</texPath>
      <graphicClass>Graphic_StackCount</graphicClass>
    </graphicData>
    <thingCategories>
      <li>MeatRaw</li>
    </thingCategories>
    <ingredient>
      <mergeCompatibilityTags>
        <li>TwistedMeat</li>
      </mergeCompatibilityTags>
    </ingredient>
    <ingestible>
      <foodType>Meat</foodType>
      <preferability>RawBad</preferability>
      <ingestEffect>EatMeat</ingestEffect>
      <ingestSound>RawMeat_Eat</ingestSound>
      <specialThoughtDirect>AteTwistedMeat</specialThoughtDirect>
      <specialThoughtAsIngredient>AteTwistedMeat</specialThoughtAsIngredient>
    </ingestible>
    <comps>
      <li Class="CompProperties_Forbiddable" />
      <li Class="CompProperties_Rottable">
        <daysToRotStart>2</daysToRotStart>
        <rotDestroys>true</rotDestroys>
      </li>
      <li>
        <compClass>CompHarbingerTreeConsumable</compClass>
      </li>
    </comps>
  </ThingDef>
  
  <!-- Golden cube -->
  
  <ThingDef>
    <thingClass>ThingWithComps</thingClass>
    <defName>GoldenCube</defName>
    <label>golden cube</label>
    <description>A cube that fits snugly in the hand. Golden in color, it is always invitingly warm to the touch, like a trusted pet or a hug from a good friend. Those that look closely are rewarded for their attention by the delightful way light plays across its welcoming surface.\n\nThe cube seems impervious to most damage.</description>
    <category>Item</category>
    <genericMarketSellable>false</genericMarketSellable>
    <drawerType>MapMeshOnly</drawerType>
    <tickerType>Normal</tickerType>
    <rotatable>false</rotatable>
    <altitudeLayer>Item</altitudeLayer>
    <selectable>true</selectable>
    <useHitPoints>false</useHitPoints>
    <healthAffectsPrice>false</healthAffectsPrice>
    <alwaysHaulable>true</alwaysHaulable>
    <drawGUIOverlay>true</drawGUIOverlay>
    <pathCost>14</pathCost>
    <graphicData>
      <graphicClass>Graphic_Single</graphicClass>
      <texPath>Things/Item/Special/GoldenCube</texPath>
    </graphicData>
    <statBases>
      <Mass>1</Mass>
      <Beauty>400</Beauty>
      <Flammability>0</Flammability>
      <MarketValue>1200</MarketValue>
    </statBases>
    <thingCategories>
      <li>ItemsMisc</li>
    </thingCategories>
    <inspectorTabs>
      <li>ITab_StudyNotes</li>
    </inspectorTabs>
    <hiddenWhileUndiscovered>true</hiddenWhileUndiscovered>
    <allowedArchonexusCount>1</allowedArchonexusCount>
    <comps>
      <li Class="CompProperties_Forbiddable" />
      <li Class="CompProperties_Studiable">
        <frequencyTicks>120000</frequencyTicks>
        <knowledgeCategory>Advanced</knowledgeCategory>
        <anomalyKnowledge>2</anomalyKnowledge>
        <minMonolithLevelForStudy>1</minMonolithLevelForStudy>
        <showToggleGizmo>true</showToggleGizmo>
      </li>
      <li Class="CompProperties_StudyUnlocks">
        <studyNotes>
          <li>
            <threshold>4</threshold>
            <label>Golden cube study progress</label>
            <text>{PAWN_nameDef}'s investigation of the golden cube has revealed more. It is able to psychically influence anyone who interacts with it. Those under the cube's influence will experience extreme withdrawal if they're separated from it.\n\n{PAWN_nameDef} thinks that there may be a way to deactivate the cube, but {PAWN_pronoun} will need to study it further.</text>
          </li>
          <li>
            <thresholdRange>24~40</thresholdRange>
            <label>Golden cube study complete</label>
            <text>{PAWN_nameDef}'s investigation of the golden cube has revealed that it can be deactivated using an archotech shard. Doing so will destroy the cube, breaking its psychic hold over your colonists.\n\nHowever, the psychic whiplash from the cube’s destruction will send any cube-interested people into a berserk rage. Those in caravans will abandon your colony entirely.\n\nSelect the golden cube to deactivate it.</text>
          </li>
        </studyNotes>
      </li>
      <li Class="CompProperties_GoldenCube">
        
        <!-- Interactable -->

        <activeTicks>1</activeTicks>
        <ticksToActivate>540</ticksToActivate>
        <activateTexPath>UI/Commands/DeactivateGoldenCube</activateTexPath>
        
        <activateLabelString>Deactivate golden cube</activateLabelString>
        <activateDescString>Destroy the golden cube, breaking its psychic hold. This will cause any cube-interested people to enter a temporary berserk rage. If they’re in a caravan, they will be permanently lost.\n\nRequires 1 shard.</activateDescString>
        <guiLabelString>Choose who should deactivate this.</guiLabelString>
        <jobString>deactivate</jobString>
        <activatingStringPending>deactivating {0}</activatingStringPending>
        <activatingString>deactivating {0}: {1}s</activatingString>

        <fleckOnUsed>PsycastAreaEffect</fleckOnUsed>
        <fleckOnUsedScale>10</fleckOnUsedScale>
        <soundActivate>PsychicAnimalPulserCast</soundActivate>

        <targetingParameters>
          <canTargetBuildings>false</canTargetBuildings>
          <canTargetAnimals>false</canTargetAnimals>
          <canTargetMechs>false</canTargetMechs>
          <onlyTargetControlledPawns>true</onlyTargetControlledPawns>
        </targetingParameters>
      </li>
    </comps>
  </ThingDef>
  
  <ThingDef>
    <defName>GrayFleshSample</defName>
    <label>gray flesh sample</label>
    <description>This strip of human flesh is stained with a dark and gritty ferrous material. Tiny threads of metallic filament run throughout, interspersed with tiny silvery beads. Its form suggests that it sloughed off someone's body during some unnatural liquification process.</description>
    <thingClass>ThingWithComps</thingClass>
    <category>Item</category>
    <genericMarketSellable>false</genericMarketSellable>
    <drawerType>MapMeshOnly</drawerType>
    <tickerType>Normal</tickerType>
    <rotatable>false</rotatable>
    <altitudeLayer>Item</altitudeLayer>
    <selectable>true</selectable>
    <alwaysHaulable>true</alwaysHaulable>
    <drawGUIOverlay>true</drawGUIOverlay>
    <scatterableOnMapGen>false</scatterableOnMapGen>
    <graphicData>
      <texPath>Things/Item/Resource/GrayFleshStrip/GrayFleshStrip</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <soundInteract>Standard_Pickup</soundInteract>
    <soundDrop>Standard_Drop</soundDrop>
    <stackLimit>1</stackLimit>
    <intricate>true</intricate>
    <statBases>
      <Mass>0.03</Mass>
      <Beauty>-10</Beauty>
      <MaxHitPoints>50</MaxHitPoints>
      <Flammability>1.6</Flammability>
      <DeteriorationRate>6</DeteriorationRate>
    </statBases>
    <thingCategories>
      <li>ItemsMisc</li>
    </thingCategories>
    <hiddenWhileUndiscovered>true</hiddenWhileUndiscovered>
    <comps>
      <li Class="CompProperties_Forbiddable">
        <forbidOnMake>true</forbidOnMake>
      </li>
      <li Class="CompProperties_CompAnalyzableBiosignature">
        <analysisRequiredRange>2~3</analysisRequiredRange>
        <analysisDurationHours>0.75</analysisDurationHours>
        <destroyedOnAnalyzed>true</destroyedOnAnalyzed>
        <allowRepeatAnalysis>true</allowRepeatAnalysis>
        <ignoreForbidden>true</ignoreForbidden>
        <canStudyInPlace>true</canStudyInPlace>
        
        <progressedLetterLabel>Analysis progress</progressedLetterLabel>
        <progressedLetters>
          <li>{PAWN_nameDef} has finished analyzing the gray flesh sample. The sample carried the biosignature {BIOSIGNATURE}.\n\n{PAWN_nameDef} made progress in learning to identify this biosignature in living subjects, but needs more samples to complete the work. Until then, your surgical inspections will not detect {BIOSIGNATURE}.\n\nCollect and analyze more gray flesh samples.</li>
        </progressedLetters>
        <progressedLetterDef>NeutralEvent</progressedLetterDef>
        
        <completedLetterLabel>Analysis complete</completedLetterLabel>
        <completedLetter>While analyzing the gray flesh sample, {PAWN_nameDef} finally isolated the biosignature {BIOSIGNATURE}!\n\nThe signature emerges from the fusing of human tissue and metallic bioferrite filaments which spider through the flesh and take control of the nervous system.\n\nYou can now use the surgical inspection operation to detect {BIOSIGNATURE} in living subjects.</completedLetter>
        <completedLetterDef>ThreatSmall</completedLetterDef>

        <repeatCompletedLetterLabel>Analysis complete</repeatCompletedLetterLabel>
        <repeatCompletedLetter>{PAWN_nameDef} has completed {PAWN_possessive} analysis of the gray flesh sample. It carries the previously discovered biosignature {BIOSIGNATURE}.\n\nYour colonists now understand this biosignature well enough to detect it with the surgical inspection operation.</repeatCompletedLetter>
        <repeatCompletedLetterDef>NeutralEvent</repeatCompletedLetterDef>
        
        <!-- Interactable -->
        
        <activateTexPath>UI/Commands/Analyze</activateTexPath>
        
        <activateLabelString>Analyze...</activateLabelString>
        <activateDescString>Analyze the gray flesh sample. Once enough flesh samples are collected and analyzed, the threat's biosignature will be detectable using a surgical inspection operation, allowing you to identify its host.</activateDescString>
        <jobString>Analyze gray flesh sample</jobString>
        <guiLabelString>Choose who should analyze this</guiLabelString>
        <inspectString>Can be analyzed by a colonist.</inspectString>
        
        <targetingParameters>
          <canTargetBuildings>false</canTargetBuildings>
          <canTargetAnimals>false</canTargetAnimals>
          <canTargetMechs>false</canTargetMechs>
          <onlyTargetControlledPawns>true</onlyTargetControlledPawns>
        </targetingParameters>
      </li>
    </comps>
  </ThingDef>

  <ThingDef ParentName="ResourceBase">
    <defName>FleshmassNeuralLump</defName>
    <label>fleshmass neural lump</label>
    <description>A clump of fleshmass nervous tissue and fibres. You can analyze it to find a way to kill the fleshmass heart itself.</description>
    <thingClass>ThingWithComps</thingClass>
    <graphicData>
      <texPath>Things/Item/Special/FleshmassNeuralLump</texPath>
      <graphicClass>Graphic_Random</graphicClass>
    </graphicData>
    <stackLimit>1</stackLimit>
    <statBases>
      <Mass>0.03</Mass>
      <Beauty>-10</Beauty>
      <MaxHitPoints>50</MaxHitPoints>
      <Flammability>0</Flammability>
      <DeteriorationRate>6</DeteriorationRate>
    </statBases>
    <intricate>true</intricate>
    <thingCategories>
      <li>ItemsMisc</li>
    </thingCategories>
    <hiddenWhileUndiscovered>true</hiddenWhileUndiscovered>
    <scatterableOnMapGen>false</scatterableOnMapGen>
    <comps Inherit="False">
      <li Class="CompProperties_Forbiddable">
        <forbidOnMake>true</forbidOnMake>
      </li>
      <li Class="CompProperties_CompAnalyzableBiosignature">
        <analysisRequiredRange>3</analysisRequiredRange>
        <analysisDurationHours>0.375</analysisDurationHours>
        <destroyedOnAnalyzed>true</destroyedOnAnalyzed>
        <allowRepeatAnalysis>true</allowRepeatAnalysis>
        <showProgress>true</showProgress>
        <ignoreForbidden>true</ignoreForbidden>
        <canStudyInPlace>true</canStudyInPlace>

        <progressedLetterLabel>Analysis progress</progressedLetterLabel>
        <progressedLetters>
          <li>{PAWN_nameDef} has finished analyzing the fleshmass neural lump. The sample carries the biosignature {BIOSIGNATURE}.\n\n{PAWN_nameDef} is convinced that {PAWN_pronoun} can find a way to kill the fleshmass heart, but {PAWN_pronoun} needs to study more neural samples first.</li>
        </progressedLetters>
        <progressedLetterDef>NeutralEvent</progressedLetterDef>

        <completedLetterLabel>Analysis complete</completedLetterLabel>
        <completedLetter>While analyzing the fleshmass neural tissue, {PAWN_nameDef} made a breakthrough!\n\nThis biosignature responds strongly when stimulated by a specific electric pulse. If used on the fleshmass heart itself, an electric signal could cause a tachycardiac overload and kill the heart.\n\nYou can now destroy the {BIOSIGNATURE} fleshmass heart by interacting with it directly.</completedLetter>
        <completedLetterDef>NeutralEvent</completedLetterDef>

        <repeatCompletedLetterLabel>Analysis complete</repeatCompletedLetterLabel>
        <repeatCompletedLetter>{PAWN_nameDef} has finished analyzing the fleshmass neural tissue. This tissue belongs to the previously studied biosignature {BIOSIGNATURE}, so you didn't gain any new knowledge.\n\nYou can destroy the {BIOSIGNATURE} fleshmass heart by interacting with it directly and inducing a tachycardiac overload.</repeatCompletedLetter>
        <repeatCompletedLetterDef>NeutralEvent</repeatCompletedLetterDef>

        <!-- Interactable -->

        <activateTexPath>UI/Commands/Analyze</activateTexPath>

        <activateLabelString>Analyze...</activateLabelString>
        <activateDescString>Analyze the fleshmass neural sample. Once enough neural samples are collected and analyzed, you should be able to figure out a way to kill the heart.</activateDescString>
        <jobString>Analyze neural lump</jobString>
        <guiLabelString>Choose who should analyze this</guiLabelString>
        <inspectString>Can be analyzed by a colonist.</inspectString>

        <targetingParameters>
          <canTargetBuildings>false</canTargetBuildings>
          <canTargetAnimals>false</canTargetAnimals>
          <canTargetMechs>false</canTargetMechs>
          <onlyTargetControlledPawns>true</onlyTargetControlledPawns>
        </targetingParameters>
      </li>
    </comps>
  </ThingDef>

  <ThingDef>
    <defName>RevenantFleshChunk</defName>
    <label>revenant flesh chunk</label>
    <description>A strip of leathery, desiccated flesh that sloughed off a revenant when it was harmed. The tissue oozes a dark, oily liquid.</description>
    <thingClass>ThingWithComps</thingClass>
    <category>Item</category>
    <genericMarketSellable>false</genericMarketSellable>
    <drawerType>MapMeshOnly</drawerType>
    <tickerType>Normal</tickerType>
    <rotatable>false</rotatable>
    <altitudeLayer>Item</altitudeLayer>
    <selectable>true</selectable>
    <alwaysHaulable>true</alwaysHaulable>
    <drawGUIOverlay>true</drawGUIOverlay>
    <scatterableOnMapGen>false</scatterableOnMapGen>
    <graphicData>
      <texPath>Things/Item/Resource/RevenantFleshChunk</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>(0.75, 0.75)</drawSize>
    </graphicData>
    <soundInteract>Standard_Pickup</soundInteract>
    <soundDrop>Standard_Drop</soundDrop>
    <stackLimit>1</stackLimit>
    <intricate>true</intricate>
    <statBases>
      <Mass>0.03</Mass>
      <Beauty>-10</Beauty>
      <MaxHitPoints>50</MaxHitPoints>
      <Flammability>1.6</Flammability>
      <DeteriorationRate>6</DeteriorationRate>
    </statBases>
    <thingCategories>
      <li>ItemsMisc</li>
    </thingCategories>
    <hiddenWhileUndiscovered>true</hiddenWhileUndiscovered>
    <comps>
      <li Class="CompProperties_Forbiddable">
        <forbidOnMake>true</forbidOnMake>
      </li>
      <li Class="CompProperties_CompAnalyzableBiosignature">
        <analysisRequiredRange>3</analysisRequiredRange>
        <analysisDurationHours>0.75</analysisDurationHours>
        <destroyedOnAnalyzed>true</destroyedOnAnalyzed>
        <allowRepeatAnalysis>true</allowRepeatAnalysis>
        <ignoreForbidden>true</ignoreForbidden>
        <canStudyInPlace>true</canStudyInPlace>

        <progressedLetterLabel>Analysis progress</progressedLetterLabel>
        <progressedLetters>
          <li>{PAWN_nameDef} has finished analyzing the revenant flesh chunk with biosignature {BIOSIGNATURE}.\n\n{PAWN_pronoun} learned more about this particular revenant's biology and how to identify fluids it leaves behind.\n\nThis revenant will now leave a much longer trail of visible smudges when it flees.\n\nPerhaps you'll be able to find it and kill it while it sleeps. You can track it by smears it leaves behind, and reveal it with disruptor flares, explosives, EMP, firefoam, or fire.</li>
          <li>{PAWN_nameDef} has finished analyzing the revenant flesh chunk with biosignature {BIOSIGNATURE}.\n\n{PAWN_pronoun} has learned more about this specific revenant's biology and the vocalizations it makes. Your colonists can now hear this revenant when it is nearby. If you go hunting the revenant and get close, you'll be notified which colonists can hear it.\n\nPerhaps you'll be able to find it and kill it while it sleeps. You can track it by smears it leaves behind, and reveal it with disruptor flares, explosives, EMP, firefoam, or fire.</li>
        </progressedLetters>
        <progressedLetterDef>NeutralEvent</progressedLetterDef>

        <completedLetterLabel>Analysis progress</completedLetterLabel>
        <completedLetter>{PAWN_nameDef} has finished analyzing the revenant flesh chunk with biosignature {BIOSIGNATURE}.\n\n{PAWN_pronoun} has learned more about this specific revenant's biology and its method of hiding from sight. Your colonists can now see this revenant when it is nearby.\n\nPerhaps you'll be able to find it and kill it while it sleeps.</completedLetter>
        <completedLetterDef>NeutralEvent</completedLetterDef>

        <repeatCompletedLetterLabel>Analysis complete</repeatCompletedLetterLabel>
        <repeatCompletedLetter>{PAWN_nameDef} has finished analyzing the revenant flesh chunk with the previously studied biosignature {BIOSIGNATURE}.\n\nYour colonists can now see this revenant when it is nearby. Perhaps you'll be able to find it and kill it while it's sleeping.</repeatCompletedLetter>
        <repeatCompletedLetterDef>NeutralEvent</repeatCompletedLetterDef>

        <!-- Interactable -->

        <activateTexPath>UI/Commands/Analyze</activateTexPath>

        <activateLabelString>Analyze...</activateLabelString>
        <activateDescString>Analyze the revenant flesh chunk. Analyzing this tissue sample will give you tools to track and reveal the revenant.</activateDescString>
        <jobString>Analyze revenant flesh chunk</jobString>
        <guiLabelString>Choose who should analyze this</guiLabelString>
        <inspectString>Can be analyzed by a colonist.</inspectString>

        <targetingParameters>
          <canTargetBuildings>false</canTargetBuildings>
          <canTargetAnimals>false</canTargetAnimals>
          <canTargetMechs>false</canTargetMechs>
          <onlyTargetControlledPawns>true</onlyTargetControlledPawns>
        </targetingParameters>
      </li>
    </comps>
  </ThingDef>

  <ThingDef ParentName="ResourceBase">
    <defName>MonolithFragment</defName>
    <label>monolith fragment</label>
    <description>A broken-off fragment of the void monolith. It can be studied to learn more about void phenomena.</description>
    <genericMarketSellable>true</genericMarketSellable>
    <useHitPoints>false</useHitPoints>
    <healthAffectsPrice>false</healthAffectsPrice>
    <graphicData>
      <graphicClass>Graphic_Random</graphicClass>
      <texPath>Things/Item/MonolithFragment</texPath>
    </graphicData>
    <randomizeRotationOnSpawn>true</randomizeRotationOnSpawn>
    <scatterableOnMapGen>false</scatterableOnMapGen>
    <stackLimit>1</stackLimit>
    <statBases>
      <Mass>1</Mass>
      <Beauty>-5</Beauty>
      <Flammability>0</Flammability>
      <MarketValue>2000</MarketValue>
    </statBases>
    <thingCategories>
      <li>ItemsMisc</li>
    </thingCategories>
    <inspectorTabs>
      <li>ITab_StudyNotes</li>
    </inspectorTabs>
    <hiddenWhileUndiscovered>true</hiddenWhileUndiscovered>
    <comps>
      <li Class="CompProperties_Studiable">
        <frequencyTicks>120000</frequencyTicks>
        <knowledgeCategory>Advanced</knowledgeCategory>
        <anomalyKnowledge>1</anomalyKnowledge>
        <minMonolithLevelForStudy>1</minMonolithLevelForStudy>
        <showToggleGizmo>true</showToggleGizmo>
      </li>
    </comps>
  </ThingDef>
</Defs>