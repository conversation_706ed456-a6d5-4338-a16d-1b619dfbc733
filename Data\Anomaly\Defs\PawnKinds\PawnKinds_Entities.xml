<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <PawnKindDef Name="NociosphereKind">
    <defName>Nociosphere</defName>
    <label>nociosphere</label>
    <race>Nociosphere</race>
    <canMeleeAttack>false</canMeleeAttack>
    <hostileToAll>true</hostileToAll>
    <allowOldAgeInjuries>false</allowOldAgeInjuries>
    <defaultFactionType>Entities</defaultFactionType>
    <immuneToGameConditionEffects>true</immuneToGameConditionEffects>
    <preventIdeo>true</preventIdeo>
    <canStrip>false</canStrip>
    <immuneToTraps>true</immuneToTraps>
    <collidesWithPawns>false</collidesWithPawns>
    <ignoresPainShock>true</ignoresPainShock>
    <minGenerationAge>200</minGenerationAge>
    <useFixedRotation>true</useFixedRotation>
    <fixedRotation>South</fixedRotation>
    <overrideDebugActionCategory>Entity</overrideDebugActionCategory>
    <abilities>
      <li>EntitySkip</li>
      <li>Heatspikes</li>
      <li>FleshmelterBolt</li>
      <li>AgonyPulse</li>
    </abilities>
    <combatPower>800</combatPower>
    <lifeStages>
      <li>
        <bodyGraphicData>
          <texPath>Things/Pawn/Nociosphere/Nociosphere_Center</texPath>
          <graphicClass>Graphic_Single</graphicClass>
          <drawSize>2</drawSize>
        </bodyGraphicData>
      </li>
    </lifeStages>
  </PawnKindDef>

  <PawnKindDef>
    <defName>Metalhorror</defName>
    <label>metalhorror</label>
    <race>Metalhorror</race>
    <allowOldAgeInjuries>false</allowOldAgeInjuries>
    <defaultFactionType>Entities</defaultFactionType>
    <immuneToGameConditionEffects>true</immuneToGameConditionEffects>
    <preventIdeo>true</preventIdeo>
    <combatPower>300</combatPower>
    <aiAvoidCover>true</aiAvoidCover>
    <canStrip>false</canStrip>
    <overrideDebugActionCategory>Entity</overrideDebugActionCategory>
    <lifeStages>
      <li>
        <bodyGraphicData>
          <texPath>Things/Pawn/Metalhorror/Metalhorror_Larva</texPath>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>1.2</drawSize>
        </bodyGraphicData>
      </li>
      <li>
        <bodyGraphicData>
          <texPath>Things/Pawn/Metalhorror/Metalhorror_Juvenile</texPath>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>1.6</drawSize>
        </bodyGraphicData>
      </li>
      <li>
        <bodyGraphicData>
          <texPath>Things/Pawn/Metalhorror/Metalhorror_Mature</texPath>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>2</drawSize>
        </bodyGraphicData>
      </li>
    </lifeStages>
  </PawnKindDef>

  <PawnKindDef Name="RevenantKind">
    <defName>Revenant</defName>
    <label>revenant</label>
    <race>Revenant</race>
    <defaultFactionType>Entities</defaultFactionType>
    <overrideDeathOnDownedChance>1</overrideDeathOnDownedChance>
    <forceDeathOnDowned>true</forceDeathOnDowned>
    <immuneToGameConditionEffects>true</immuneToGameConditionEffects>
    <immuneToTraps>true</immuneToTraps>
    <collidesWithPawns>false</collidesWithPawns>
    <overrideDebugActionCategory>Entity</overrideDebugActionCategory>
    <lifeStages>
      <li>
        <bodyGraphicData>
          <texPath>Things/Pawn/Revenant/Revenant</texPath>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>2</drawSize>
        </bodyGraphicData>
        <silhouetteGraphicData>
          <texPath>Things/Pawn/Revenant/Revenant_MenuIcon</texPath>
          <graphicClass>Graphic_Single</graphicClass>
          <drawSize>2</drawSize>
        </silhouetteGraphicData>
      </li>
    </lifeStages>
    <startingHediffs>
      <li>
        <def>HoraxianInvisibility</def>
      </li>
    </startingHediffs>
    <combatPower>300</combatPower>
    <disabledWorkTags>
      <li>Violent</li>
    </disabledWorkTags>
  </PawnKindDef>
  
  <PawnKindDef Name="SightstealerKind">
    <defName>Sightstealer</defName>
    <label>sightstealer</label>
    <race>Sightstealer</race>
    <defaultFactionType>Entities</defaultFactionType>
    <immuneToGameConditionEffects>true</immuneToGameConditionEffects>
    <immuneToTraps>true</immuneToTraps>
    <overrideDebugActionCategory>Entity</overrideDebugActionCategory>
    <alternateGraphicChance>0.75</alternateGraphicChance>
    <alternateGraphics>
      <li>
        <texPath>Things/Pawn/Sightstealer/Sightstealer_B</texPath>
        <attachPoints>
          <li>
            <offset>(-.4, 0, .3)</offset>
            <type>PlatformRestraint0</type>
          </li>
          <li>
            <offset>(.2, 0, .1)</offset>
            <type>PlatformRestraint1</type>
          </li>
          <li>
            <offset>(.1, 0, -.6)</offset>
            <type>PlatformRestraint2</type>
          </li>
          <li>
            <offset>(-.15, 0, -.6)</offset>
            <type>PlatformRestraint3</type>
          </li>
        </attachPoints>
        <dessicatedGraphicData>
          <texPath>Things/Pawn/Sightstealer/Dessicated_Sightstealer</texPath>
          <drawSize>1.7</drawSize>
          <attachPoints>
            <li>
              <offset>(-.2, 0, .3)</offset>
              <type>PlatformRestraint0</type>
            </li>
            <li>
              <offset>(.2, 0, .3)</offset>
              <type>PlatformRestraint1</type>
            </li>
            <li>
              <offset>(.12, 0, -.55)</offset>
              <type>PlatformRestraint2</type>
            </li>
            <li>
              <offset>(-.15, 0, -.6)</offset>
              <type>PlatformRestraint3</type>
            </li>
          </attachPoints>
        </dessicatedGraphicData>
      </li>

      <li>
        <texPath>Things/Pawn/Sightstealer/Sightstealer_C</texPath>
        <attachPoints>
          <li>
            <offset>(-.4, 0, .3)</offset>
            <type>PlatformRestraint0</type>
          </li>
          <li>
            <offset>(.15, 0, .3)</offset>
            <type>PlatformRestraint1</type>
          </li>
          <li>
            <offset>(.05, 0, -.5)</offset>
            <type>PlatformRestraint2</type>
          </li>
          <li>
            <offset>(-.15, 0, -.5)</offset>
            <type>PlatformRestraint3</type>
          </li>
        </attachPoints>
        <dessicatedGraphicData>
          <texPath>Things/Pawn/Sightstealer/Dessicated_Sightstealer</texPath>
          <drawSize>1.7</drawSize>
          <attachPoints>
            <li>
              <offset>(-.2, 0, .3)</offset>
              <type>PlatformRestraint0</type>
            </li>
            <li>
              <offset>(.2, 0, .3)</offset>
              <type>PlatformRestraint1</type>
            </li>
            <li>
              <offset>(.12, 0, -.55)</offset>
              <type>PlatformRestraint2</type>
            </li>
            <li>
              <offset>(-.15, 0, -.6)</offset>
              <type>PlatformRestraint3</type>
            </li>
          </attachPoints>
        </dessicatedGraphicData>
      </li>
      
      <li>
        <texPath>Things/Pawn/Sightstealer/Sightstealer_D</texPath>
        <attachPoints>
          <li>
            <offset>(-.1, 0, .4)</offset>
            <type>PlatformRestraint0</type>
          </li>
          <li>
            <offset>(.45, 0, .35)</offset>
            <type>PlatformRestraint1</type>
          </li>
          <li>
            <offset>(.25, 0, -.2)</offset>
            <type>PlatformRestraint2</type>
          </li>
          <li>
            <offset>(-.15, 0, -.2)</offset>
            <type>PlatformRestraint3</type>
          </li>
        </attachPoints>
        <dessicatedGraphicData>
          <texPath>Things/Pawn/Sightstealer/Dessicated_Sightstealer</texPath>
          <drawSize>1.7</drawSize>
          <attachPoints>
            <li>
              <offset>(-.2, 0, .3)</offset>
              <type>PlatformRestraint0</type>
            </li>
            <li>
              <offset>(.2, 0, .3)</offset>
              <type>PlatformRestraint1</type>
            </li>
            <li>
              <offset>(.12, 0, -.55)</offset>
              <type>PlatformRestraint2</type>
            </li>
            <li>
              <offset>(-.15, 0, -.6)</offset>
              <type>PlatformRestraint3</type>
            </li>
          </attachPoints>
        </dessicatedGraphicData>
      </li>
    </alternateGraphics>
    <lifeStages>
      <li>
        <bodyGraphicData>
          <texPath>Things/Pawn/Sightstealer/Sightstealer_A</texPath>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>1.7</drawSize>
        </bodyGraphicData>
        <dessicatedBodyGraphicData>
          <texPath>Things/Pawn/Sightstealer/Dessicated_Sightstealer</texPath>
          <drawSize>1.7</drawSize>
          <attachPoints>
            <li>
              <offset>(-.2, 0, .3)</offset>
              <type>PlatformRestraint0</type>
            </li>
            <li>
              <offset>(.2, 0, .3)</offset>
              <type>PlatformRestraint1</type>
            </li>
            <li>
              <offset>(.12, 0, -.55)</offset>
              <type>PlatformRestraint2</type>
            </li>
            <li>
              <offset>(-.15, 0, -.6)</offset>
              <type>PlatformRestraint3</type>
            </li>
          </attachPoints>
        </dessicatedBodyGraphicData>
      </li>
    </lifeStages>
    <startingHediffs>
      <li>
        <def>HoraxianInvisibility</def>
      </li>
    </startingHediffs>
    <combatPower>70</combatPower>
    <meleeAttackInfectionPathways>
      <li>EntityAttacked</li>
    </meleeAttackInfectionPathways>
  </PawnKindDef>

  <PawnKindDef>
    <defName>Noctol</defName>
    <label>noctol</label>
    <race>Noctol</race>
    <defaultFactionType>Entities</defaultFactionType>
    <combatPower>80</combatPower>
    <immuneToGameConditionEffects>true</immuneToGameConditionEffects>
    <overrideDebugActionCategory>Entity</overrideDebugActionCategory>
    <lifeStages>
      <li>
        <bodyGraphicData>
          <texPath>Things/Pawn/Noctol/Noctol</texPath>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>2</drawSize>
        </bodyGraphicData>
        <dessicatedBodyGraphicData>
          <texPath>Things/Pawn/Noctol/Dessicated_Noctol</texPath>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>2</drawSize>
          <attachPoints>
            <li>
              <offset>(-.35, 0, .12)</offset>
              <type>PlatformRestraint0</type>
            </li>
            <li>
              <offset>(.32, 0, .12)</offset>
              <type>PlatformRestraint1</type>
            </li>
            <li>
              <offset>(.29, 0, -.85)</offset>
              <type>PlatformRestraint2</type>
            </li>
            <li>
              <offset>(-.22, 0, -.85)</offset>
              <type>PlatformRestraint3</type>
            </li>
          </attachPoints>
        </dessicatedBodyGraphicData>
      </li>
    </lifeStages>
    <startingHediffs>
      <li>
        <def>LightExposure</def>
      </li>
    </startingHediffs>
    <meleeAttackInfectionPathways>
      <li>EntityAttacked</li>
    </meleeAttackInfectionPathways>
  </PawnKindDef>

  <PawnKindDef>
    <defName>FleshmassNucleus</defName>
    <label>fleshmass nucleus</label>
    <race>FleshmassNucleus</race>
    <canMeleeAttack>false</canMeleeAttack>
    <hostileToAll>true</hostileToAll>
    <allowOldAgeInjuries>false</allowOldAgeInjuries>
    <defaultFactionType>Entities</defaultFactionType>
    <immuneToGameConditionEffects>true</immuneToGameConditionEffects>
    <preventIdeo>true</preventIdeo>
    <canStrip>false</canStrip>
    <immuneToTraps>true</immuneToTraps>
    <collidesWithPawns>false</collidesWithPawns>
    <ignoresPainShock>true</ignoresPainShock>
    <minGenerationAge>200</minGenerationAge>
    <useFixedRotation>true</useFixedRotation>
    <fixedRotation>South</fixedRotation>
    <overrideDebugActionCategory>Entity</overrideDebugActionCategory>
    <startingHediffs>
      <li>
        <def>Regeneration</def>
        <severity>0.1</severity>
      </li>
    </startingHediffs>
    <combatPower>100</combatPower>
    <lifeStages>
      <li>
        <bodyGraphicData>
          <texPath>Things/Pawn/FleshmassNucleus</texPath>
          <graphicClass>Graphic_ActivityStaged</graphicClass>
          <drawSize>2</drawSize>
        </bodyGraphicData>
      </li>
    </lifeStages>
  </PawnKindDef>
  
  <PawnKindDef Name="Gorehulk">
    <defName>Gorehulk</defName>
    <label>gorehulk</label>
    <race>Gorehulk</race>
    <defaultFactionType>Entities</defaultFactionType>
    <immuneToGameConditionEffects>true</immuneToGameConditionEffects>
    <overrideDebugActionCategory>Entity</overrideDebugActionCategory>
    <alternateGraphicChance>0.833</alternateGraphicChance>
    <alternateGraphics>
      <li>
        <texPath>Things/Pawn/Gorehulk/Gorehulk_B</texPath>
        <attachPoints>
          <li>
            <offset>(-.4, 0, .4)</offset>
            <type>PlatformRestraint0</type>
          </li>
          <li>
            <offset>(.5, 0, .2)</offset>
            <type>PlatformRestraint1</type>
          </li>
          <li>
            <offset>(.3, 0, -.6)</offset>
            <type>PlatformRestraint2</type>
          </li>
          <li>
            <offset>(-.2, 0, -.65)</offset>
            <type>PlatformRestraint3</type>
          </li>
        </attachPoints>
        <dessicatedTexPath>Things/Pawn/Gorehulk/Dessicated_Gorehulk</dessicatedTexPath>
        <dessicatedGraphicData>
          <attachPoints>
            <li>
              <offset>(-.35, 0, .3)</offset>
              <type>PlatformRestraint0</type>
            </li>
            <li>
              <offset>(.4, 0, .15)</offset>
              <type>PlatformRestraint1</type>
            </li>
            <li>
              <offset>(.2, 0, -.6)</offset>
              <type>PlatformRestraint2</type>
            </li>
            <li>
              <offset>(-.2, 0, -.6)</offset>
              <type>PlatformRestraint3</type>
            </li>
          </attachPoints>
        </dessicatedGraphicData>
      </li>

      <li>
        <texPath>Things/Pawn/Gorehulk/Gorehulk_C</texPath>
        <attachPoints>
          <li>
            <offset>(-.5, 0, .5)</offset>
            <type>PlatformRestraint0</type>
          </li>
          <li>
            <offset>(.45, 0, .65)</offset>
            <type>PlatformRestraint1</type>
          </li>
          <li>
            <offset>(.3, 0, -.6)</offset>
            <type>PlatformRestraint2</type>
          </li>
          <li>
            <offset>(-.2, 0, -.65)</offset>
            <type>PlatformRestraint3</type>
          </li>
        </attachPoints>
        <dessicatedTexPath>Things/Pawn/Gorehulk/Dessicated_Gorehulk</dessicatedTexPath>
        <dessicatedGraphicData>
          <attachPoints>
            <li>
              <offset>(-.35, 0, .3)</offset>
              <type>PlatformRestraint0</type>
            </li>
            <li>
              <offset>(.4, 0, .15)</offset>
              <type>PlatformRestraint1</type>
            </li>
            <li>
              <offset>(.2, 0, -.6)</offset>
              <type>PlatformRestraint2</type>
            </li>
            <li>
              <offset>(-.2, 0, -.6)</offset>
              <type>PlatformRestraint3</type>
            </li>
          </attachPoints>
        </dessicatedGraphicData>
      </li>
      
      <li>
        <texPath>Things/Pawn/Gorehulk/Gorehulk_D</texPath>
        <attachPoints>
          <li>
            <offset>(-.35, 0, .5)</offset>
            <type>PlatformRestraint0</type>
          </li>
          <li>
            <offset>(.3, 0, .5)</offset>
            <type>PlatformRestraint1</type>
          </li>
          <li>
            <offset>(.5, 0, -.6)</offset>
            <type>PlatformRestraint2</type>
          </li>
          <li>
            <offset>(-.5, 0, -.6)</offset>
            <type>PlatformRestraint3</type>
          </li>
        </attachPoints>
        <dessicatedTexPath>Things/Pawn/Gorehulk/Dessicated_Gorehulk</dessicatedTexPath>
        <dessicatedGraphicData>
          <attachPoints>
              <li>
                <offset>(-.35, 0, .3)</offset>
                <type>PlatformRestraint0</type>
              </li>
              <li>
                <offset>(.4, 0, .15)</offset>
                <type>PlatformRestraint1</type>
              </li>
              <li>
                <offset>(.2, 0, -.6)</offset>
                <type>PlatformRestraint2</type>
              </li>
              <li>
                <offset>(-.2, 0, -.6)</offset>
                <type>PlatformRestraint3</type>
              </li>
          </attachPoints>
        </dessicatedGraphicData>
      </li>

      <li>
        <texPath>Things/Pawn/Gorehulk/Gorehulk_E</texPath>
        <attachPoints>
          <li>
            <offset>(-.35, 0, .2)</offset>
            <type>PlatformRestraint0</type>
          </li>
          <li>
            <offset>(.5, 0, .35)</offset>
            <type>PlatformRestraint1</type>
          </li>
          <li>
            <offset>(.3, 0, -.8)</offset>
            <type>PlatformRestraint2</type>
          </li>
          <li>
            <offset>(-.2, 0, -.8)</offset>
            <type>PlatformRestraint3</type>
          </li>
        </attachPoints>
        <dessicatedTexPath>Things/Pawn/Gorehulk/Dessicated_Gorehulk</dessicatedTexPath>
        <dessicatedGraphicData>
          <attachPoints>
            <li>
              <offset>(-.35, 0, .3)</offset>
              <type>PlatformRestraint0</type>
            </li>
            <li>
              <offset>(.4, 0, .15)</offset>
              <type>PlatformRestraint1</type>
            </li>
            <li>
              <offset>(.2, 0, -.6)</offset>
              <type>PlatformRestraint2</type>
            </li>
            <li>
              <offset>(-.2, 0, -.6)</offset>
              <type>PlatformRestraint3</type>
            </li>
          </attachPoints>
        </dessicatedGraphicData>
      </li>
      <li>
        <texPath>Things/Pawn/Gorehulk/Gorehulk_F</texPath>
        <attachPoints>
          <li>
            <offset>(-.4, 0, .3)</offset>
            <type>PlatformRestraint0</type>
          </li>
          <li>
            <offset>(.3, 0, .5)</offset>
            <type>PlatformRestraint1</type>
          </li>
          <li>
            <offset>(.4, 0, -.7)</offset>
            <type>PlatformRestraint2</type>
          </li>
          <li>
            <offset>(-.4, 0, -.7)</offset>
            <type>PlatformRestraint3</type>
          </li>
        </attachPoints>
        <dessicatedTexPath>Things/Pawn/Gorehulk/Dessicated_Gorehulk</dessicatedTexPath>
        <dessicatedGraphicData>
          <attachPoints>
            <li>
              <offset>(-.35, 0, .3)</offset>
              <type>PlatformRestraint0</type>
            </li>
            <li>
              <offset>(.4, 0, .15)</offset>
              <type>PlatformRestraint1</type>
            </li>
            <li>
              <offset>(.2, 0, -.6)</offset>
              <type>PlatformRestraint2</type>
            </li>
            <li>
              <offset>(-.2, 0, -.6)</offset>
              <type>PlatformRestraint3</type>
            </li>
          </attachPoints>
        </dessicatedGraphicData>
      </li>
    </alternateGraphics>
    <lifeStages>
      <li>
        <bodyGraphicData>
          <texPath>Things/Pawn/Gorehulk/Gorehulk_A</texPath>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>2</drawSize>
        </bodyGraphicData>
        <dessicatedBodyGraphicData>
          <texPath>Things/Pawn/Gorehulk/Dessicated_Gorehulk</texPath>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>2</drawSize>
          <attachPoints>
            <li>
              <offset>(-.35, 0, .3)</offset>
              <type>PlatformRestraint0</type>
            </li>
            <li>
              <offset>(.4, 0, .15)</offset>
              <type>PlatformRestraint1</type>
            </li>
            <li>
              <offset>(.2, 0, -.6)</offset>
              <type>PlatformRestraint2</type>
            </li>
            <li>
              <offset>(-.2, 0, -.6)</offset>
              <type>PlatformRestraint3</type>
            </li>
          </attachPoints>
        </dessicatedBodyGraphicData>
      </li>
    </lifeStages>
    <combatPower>75</combatPower>
    <abilities>
      <li>SpineLaunch_Gorehulk</li>
    </abilities>
  </PawnKindDef>

  <PawnKindDef>
    <defName>Devourer</defName>
    <label>devourer</label>
    <race>Devourer</race>
    <defaultFactionType>Entities</defaultFactionType>
    <immuneToGameConditionEffects>true</immuneToGameConditionEffects>
    <overrideDebugActionCategory>Entity</overrideDebugActionCategory>
    <lifeStages>
      <li>
        <bodyGraphicData>
          <texPath>Things/Pawn/Devourer/Devourer</texPath>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>3.2</drawSize>
        </bodyGraphicData>
        <dessicatedBodyGraphicData>
          <texPath>Things/Pawn/Devourer/Dessicated_Devourer</texPath>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>3.2</drawSize>
          <attachPoints>
            <li>
              <offset>(-.4, 0, .85)</offset>
              <type>PlatformRestraint0</type>
            </li>
            <li>
              <offset>(.45, 0, .85)</offset>
              <type>PlatformRestraint1</type>
            </li>
            <li>
              <offset>(.5, 0, -.85)</offset>
              <type>PlatformRestraint2</type>
            </li>
            <li>
              <offset>(-.5, 0, -.85)</offset>
              <type>PlatformRestraint3</type>
            </li>
          </attachPoints>
        </dessicatedBodyGraphicData>
      </li>
    </lifeStages>
    <combatPower>250</combatPower>
    <abilities>
      <li>ConsumeLeap_Devourer</li>
    </abilities>
  </PawnKindDef>

  <PawnKindDef>
    <defName>Chimera</defName>
    <label>chimera</label>
    <race>Chimera</race>
    <overrideDeathOnDownedChance>0.25</overrideDeathOnDownedChance>
    <overrideDebugActionCategory>Entity</overrideDebugActionCategory>
    <combatPower>135</combatPower>
    <ecoSystemWeight>0.5</ecoSystemWeight>
    <defaultFactionType>Entities</defaultFactionType>
    <alternateGraphicChance>0.75</alternateGraphicChance>
    <alternateGraphics>
      <li>
        <texPath>Things/Pawn/Chimera/Chimera_D</texPath>
        <dessicatedTexPath>Things/Pawn/Chimera/Dessicated/DessicatedChimera_D</dessicatedTexPath>
        <dessicatedGraphicData>
            <attachPoints>
              <li>
                <offset>(-.2, 0, .7)</offset>
                <type>PlatformRestraint0</type>
              </li>
              <li>
                <offset>(.2, 0, .7)</offset>
                <type>PlatformRestraint1</type>
              </li>
              <li>
                <offset>(.3, 0, -.8)</offset>
                <type>PlatformRestraint2</type>
              </li>
              <li>
                <offset>(-.3, 0, -.8)</offset>
                <type>PlatformRestraint3</type>
              </li>
            </attachPoints>
        </dessicatedGraphicData> 
        <attachPoints>
          <li>
            <offset>(-.3, 0, .75)</offset>
            <type>PlatformRestraint0</type>
          </li>
          <li>
            <offset>(.2, 0, .75)</offset>
            <type>PlatformRestraint1</type>
          </li>
          <li>
            <offset>(.2, 0, -.6)</offset>
            <type>PlatformRestraint2</type>
          </li>
          <li>
            <offset>(-.2, 0, -.6)</offset>
            <type>PlatformRestraint3</type>
          </li>
        </attachPoints>
      </li>
      <li>
        <texPath>Things/Pawn/Chimera/Chimera_C</texPath>
        <dessicatedTexPath>Things/Pawn/Chimera/Dessicated/DessicatedChimera_C</dessicatedTexPath>
        <dessicatedGraphicData>
          <attachPoints>
            <li>
              <offset>(-.25, 0, .7)</offset>
              <type>PlatformRestraint0</type>
            </li>
            <li>
              <offset>(.2, 0, .7)</offset>
              <type>PlatformRestraint1</type>
            </li>
            <li>
              <offset>(.3, 0, -.7)</offset>
              <type>PlatformRestraint2</type>
            </li>
            <li>
              <offset>(-.3, 0, -.7)</offset>
              <type>PlatformRestraint3</type>
            </li>
          </attachPoints>
        </dessicatedGraphicData>
        <attachPoints>
         <li>
           <offset>(-.4, 0, .6)</offset>
           <type>PlatformRestraint0</type>
         </li>
         <li>
           <offset>(.3, 0, .6)</offset>
           <type>PlatformRestraint1</type>
         </li>
         <li>
           <offset>(.2, 0, -.7)</offset>
           <type>PlatformRestraint2</type>
         </li>
         <li>
           <offset>(-.25, 0, -.7)</offset>
           <type>PlatformRestraint3</type>
         </li>
       </attachPoints>
      </li>
      <li>
        <texPath>Things/Pawn/Chimera/Chimera_B</texPath>
        <dessicatedTexPath>Things/Pawn/Chimera/Dessicated/DessicatedChimera_B</dessicatedTexPath>
        <dessicatedGraphicData>
          <attachPoints>
            <li>
              <offset>(-.2, 0, .6)</offset>
              <type>PlatformRestraint0</type>
            </li>
            <li>
              <offset>(.2, 0, .6)</offset>
              <type>PlatformRestraint1</type>
            </li>
            <li>
              <offset>(.5, 0, -.8)</offset>
              <type>PlatformRestraint2</type>
            </li>
            <li>
              <offset>(-.5, 0, -.8)</offset>
              <type>PlatformRestraint3</type>
            </li>
          </attachPoints>
        </dessicatedGraphicData>
       <attachPoints>
         <li>
           <offset>(-.4, 0, .7)</offset>
           <type>PlatformRestraint0</type>
         </li>
         <li>
           <offset>(.35, 0, .7)</offset>
           <type>PlatformRestraint1</type>
         </li>
         <li>
           <offset>(.6, 0, -.85)</offset>
           <type>PlatformRestraint2</type>
         </li>
         <li>
           <offset>(-.6, 0, -.85)</offset>
           <type>PlatformRestraint3</type>
         </li>
       </attachPoints>
      </li>
    </alternateGraphics>
    <lifeStages>
      <li>
        <bodyGraphicData>
          <texPath>Things/Pawn/Chimera/Chimera_A</texPath>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>2.7</drawSize>
        </bodyGraphicData>
        <dessicatedBodyGraphicData>
          <texPath>Things/Pawn/Chimera/Dessicated/DessicatedChimera_A</texPath>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>2.7</drawSize>
          <attachPoints>
            <li>
              <offset>(-.5, 0, .5)</offset>
              <type>PlatformRestraint0</type>
            </li>
            <li>
              <offset>(.5, 0, .5)</offset>
              <type>PlatformRestraint1</type>
            </li>
            <li>
              <offset>(.4, 0, -.5)</offset>
              <type>PlatformRestraint2</type>
            </li>
            <li>
              <offset>(-.4, 0, -.5)</offset>
              <type>PlatformRestraint3</type>
            </li>
          </attachPoints>
        </dessicatedBodyGraphicData>
      </li>
    </lifeStages>
  </PawnKindDef>
  
</Defs>