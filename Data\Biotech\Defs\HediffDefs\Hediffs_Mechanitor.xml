<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingDef Abstract="True" Name="MechanitorImplantBase">
    <thingClass>ThingWithComps</thingClass>
    <category>Item</category>
    <drawerType>MapMeshOnly</drawerType>
    <techLevel>Archotech</techLevel>
    <useHitPoints>true</useHitPoints>
    <pathCost>14</pathCost>
    <selectable>true</selectable>
    <altitudeLayer>Item</altitudeLayer>
    <tickerType>Never</tickerType>
    <alwaysHaulable>true</alwaysHaulable>
    <resourceReadoutPriority>Middle</resourceReadoutPriority>
    <thingCategories>
      <li>BodyPartsMechtech</li>
    </thingCategories>
    <comps>
      <li Class="CompProperties_UseEffectDestroySelf" />
    </comps>
  </ThingDef>

  <!-- Mechlink -->
  <ThingDef ParentName="MechanitorImplantBase">
    <defName>Mechlink</defName>
    <label>mechlink</label>
    <description>A bionic implant that allows direct control of mechanoids. Mechlinks are used by soldiers to control war mechs, and by workers to control labor mechs. A person with a mechlink is known as a mechanitor.\n\nMechlinks are self-installable. Pressed into the back of the throat, the unit injects itself through the flesh to clamp inside the base of the skull where it meets the spinal cord. There, it threads a web of ultrafine wires throughout nearby neuronal tissue to make the direct mental link between the user and the mechanoid control band.\n\nMechlinks are not simply devices for sending radio signals to mechanoids, because mechanoids are not merely robots. In addition to traditional computers, mechanoids have a dim psychic presence, so fluently controlling them requires a psychic connection. By linking mechanitor and mechanoid both psychically and electromagnetically, the mechlink permits deeper control than screen-and-button interfaces.\n\nSince the mechlink interlaces deep within the brain tissue, it can only be removed after death.\n\nNote: A mechanitor must be capable of smithing work to gestate mechanoids.</description>
    <thingClass>Mechlink</thingClass>
    <descriptionHyperlinks><HediffDef>MechlinkImplant</HediffDef></descriptionHyperlinks>
    <statBases>
      <MaxHitPoints>100</MaxHitPoints>
      <Beauty>-4</Beauty>
      <Mass>1</Mass>
      <MarketValue>500</MarketValue>
      <DeteriorationRate>0</DeteriorationRate>
    </statBases>
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/Mechlink</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>0.65</drawSize>
    </graphicData>
    <comps>
      <li Class="CompProperties_Forbiddable"/>
      <li Class="CompProperties_Usable">
        <compClass>CompUsableImplant</compClass>
        <useJob>InstallMechlink</useJob>
        <useLabel>Install {0_label} to become mechanitor</useLabel>
      </li>
      <li Class="CompProperties_UseEffectInstallImplant">
        <compClass>CompUseEffect_InstallImplantMechlink</compClass>
        <hediffDef>MechlinkImplant</hediffDef>
        <bodyPart>Brain</bodyPart>
        <requiresPsychicallySensitive>true</requiresPsychicallySensitive>
      </li>
      <li Class="CompProperties_UseEffectPlaySound">
        <soundOnUsed>MechlinkInstalled</soundOnUsed>
      </li>
      <li Class="CompProperties_UseEffectGiveQuest">
        <quest>MechanitorStartingMech</quest>
        <skipIfMechlinkAlreadySentMechs>true</skipIfMechlinkAlreadySentMechs>
      </li>
    </comps>
  </ThingDef>
  
    <HediffDef ParentName="ImplantHediffBase">
      <defName>MechlinkImplant</defName>
      <hediffClass>Hediff_Mechlink</hediffClass>
      <label>mechlink</label>
      <description>A bionic implant that allows direct control of mechanoids. Mechlinks are used by soldiers to control war mechs, and by workers to control labor mechs. A person with a mechlink is known as a mechanitor.\n\nMechlinks are self-installable. Pressed into the back of the throat, the unit injects itself through the flesh to clamp inside the base of the skull where it meets the spinal cord. There, it threads a web of ultrafine wires throughout nearby neuronal tissue to make the direct mental link between the user and the mechanoid control band.\n\nMechlinks are not simply devices for sending radio signals to mechanoids, because mechanoids are not merely robots. In addition to traditional computers, mechanoids have a dim psychic presence, so fluently controlling them requires a psychic connection. By linking mechanitor and mechanoid both psychically and electromagnetically, the mechlink permits deeper control than screen-and-button interfaces.\n\nSince the mechlink interlaces deep within the brain tissue, it can only be removed after death.\n\nNote: A mechanitor must be capable of smithing work to gestate mechanoids.</description>
      <descriptionShort>A bionic implant that allows direct control of mechanoids. Can only be removed after death.</descriptionShort>
      <isBad>false</isBad>
      <duplicationAllowed>false</duplicationAllowed>
      <keepOnBodyPartRestoration>true</keepOnBodyPartRestoration>
      <removeOnRedressIfNotOfKind>
        <li>Mechanitor</li>
        <li>Mechanitor_Basic</li>
      </removeOnRedressIfNotOfKind>
      <stages>
        <li>
          <statOffsets>
            <MechBandwidth>6</MechBandwidth>
            <MechControlGroups>2</MechControlGroups>
          </statOffsets>
        </li>
      </stages>
      <comps>
        <li Class="HediffCompProperties_LetterOnDeath">
          <letterDef>NeutralEvent</letterDef>
          <letterLabel>Reusable {HEDIFF_label} available</letterLabel>
          <letterText>{PAWN} has died with a {HEDIFF_label} installed. This can be removed and installed by someone else.</letterText>
          <onlyIfNoMechanitorDied>true</onlyIfNoMechanitorDied>
        </li>
      </comps>
    </HediffDef>

  <ThingDef Abstract="True" Name="MechanitorImplantCraftableBase" ParentName="MechanitorImplantBase">
    <statBases>
      <MaxHitPoints>100</MaxHitPoints>
      <Beauty>-4</Beauty>
      <Mass>1</Mass>
      <MarketValue>1200</MarketValue>
      <DeteriorationRate>0</DeteriorationRate>
      <WorkToMake>3200</WorkToMake>
    </statBases>
    <graphicData>
      <texPath>Things/Item/Health/HealthItem</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>0.80</drawSize>
      <color>(154,124,104)</color>
    </graphicData>
    <recipeMaker>
      <unfinishedThingDef>UnfinishedHealthItemProsthetic</unfinishedThingDef>
      <recipeUsers>
        <li>TableMachining</li>
      </recipeUsers>
      <useIngredientsForColor>false</useIngredientsForColor>
      <workSpeedStat>GeneralLaborSpeed</workSpeedStat>
      <workSkill>Crafting</workSkill>
      <effectWorking>Smith</effectWorking>
      <soundWorking>Recipe_Machining</soundWorking>
      <skillRequirements>
        <Crafting>6</Crafting>
      </skillRequirements>
      <displayPriority>650</displayPriority>
    </recipeMaker>
    <comps>
      <li Class="CompProperties_Forbiddable"/>
    </comps>
    <tradeability>Sellable</tradeability>
  </ThingDef>

  <!-- Band node -->
  <HediffDef>
    <defName>BandNode</defName>
    <hediffClass>Hediff_BandNode</hediffClass>
    <label>band node (tuned)</label>
    <description>This mechanitor's bandwidth is being increased thanks to the influence of a band node structure.</description>
    <isBad>false</isBad>
  </HediffDef>

  <!-- Control sublink -->
  <ThingDef Abstract="True" Name="ControlSublinkBase" ParentName="MechanitorImplantCraftableBase">
    <descriptionHyperlinks><HediffDef>ControlSublinkImplant</HediffDef></descriptionHyperlinks>
    <statBases>
      <MaxInstallCount>3</MaxInstallCount>
    </statBases>
  </ThingDef>

  <ThingDef ParentName="ControlSublinkBase">
    <defName>ControlSublink</defName>
    <label>control sublink (standard)</label>
    <description>A neural implant which gives a mechanitor an additional control group. Additionally, the sublink slightly increases the work speed of any mechanoids controlled by the mechanitor.\n\nThis standard-tier sublink can only increase the control group count up to 3. To go higher, a higher-tier sublink implant must be used.\n\nIt can be self-installed without surgery.\n\nThis implant can be installed up to 3 times to increase its effect.</description>
    <costList>
      <SignalChip>1</SignalChip>
      <ComponentIndustrial>4</ComponentIndustrial>
    </costList>
    <recipeMaker>
      <researchPrerequisite>StandardMechtech</researchPrerequisite>
    </recipeMaker>
    <comps>
      <li Class="CompProperties_Usable">
        <compClass>CompUsableImplant</compClass>
        <useJob>UseItem</useJob>
        <useLabel>Implant sublink</useLabel>
        <userMustHaveHediff>MechlinkImplant</userMustHaveHediff>
      </li>
      <li Class="CompProperties_UseEffectInstallImplant">
        <hediffDef>ControlSublinkImplant</hediffDef>
        <bodyPart>Brain</bodyPart>
        <canUpgrade>true</canUpgrade>
        <maxSeverity>3</maxSeverity>
      </li>
    </comps>
  </ThingDef>
  
  <ThingDef ParentName="ControlSublinkBase">
    <defName>ControlSublinkHigh</defName>
    <label>control sublink (high)</label>
    <description>A neural implant which gives a mechanitor an additional control group. Additionally, the sublink slightly increases the work speed of any mechanoids controlled by the mechanitor.\n\nThis high-tier implant can increase the control group count up to 6.\n\nIt can be self-installed without surgery.\n\nThis implant can be installed up to 3 times to increase its effect.</description>
    <costList>
      <PowerfocusChip>1</PowerfocusChip>
      <ComponentIndustrial>3</ComponentIndustrial>
      <ComponentSpacer>3</ComponentSpacer>
    </costList>
    <recipeMaker>
      <researchPrerequisite>HighMechtech</researchPrerequisite>
    </recipeMaker>
    <comps>
      <li Class="CompProperties_Usable">
        <compClass>CompUsableImplant</compClass>
        <useJob>UseItem</useJob>
        <useLabel>Implant high sublink</useLabel>
        <userMustHaveHediff>MechlinkImplant</userMustHaveHediff>
      </li>
      <li Class="CompProperties_UseEffectInstallImplant">
        <hediffDef>ControlSublinkImplant</hediffDef>
        <bodyPart>Brain</bodyPart>
        <canUpgrade>true</canUpgrade>
        <requiresExistingHediff>true</requiresExistingHediff>
        <minSeverity>3</minSeverity>
      </li>
    </comps>
  </ThingDef>

  <HediffDef>
    <defName>ControlSublinkImplant</defName>
    <hediffClass>Hediff_Level</hediffClass>
    <descriptionHyperlinks><ThingDef>ControlSublink</ThingDef></descriptionHyperlinks>
    <label>control sublink</label>
    <description>An implant which increases a mechanitor's control group count and the work speed of a mechanitor's mechs.\n\nThis implant can be installed up to 6 times to increase its effect.</description>
    <defaultLabelColor>(0.6, 0.6, 1.0)</defaultLabelColor>
    <isBad>false</isBad>
    <priceImpact>true</priceImpact>
    <initialSeverity>1</initialSeverity> <!-- Severity is bound to level of implant -->
    <minSeverity>0</minSeverity>
    <maxSeverity>6</maxSeverity>
    <keepOnBodyPartRestoration>True</keepOnBodyPartRestoration>
    <levelIsQuantity>true</levelIsQuantity>
    <duplicationAllowed>false</duplicationAllowed>
    <stages>
      <li>
        <minSeverity>1</minSeverity>
        <statOffsets>
          <MechControlGroups>1</MechControlGroups>
          <WorkSpeedGlobalOffsetMech>0.06</WorkSpeedGlobalOffsetMech>
        </statOffsets>
      </li>
      <li>
        <minSeverity>2</minSeverity>
        <statOffsets>
          <MechControlGroups>2</MechControlGroups>
          <WorkSpeedGlobalOffsetMech>0.12</WorkSpeedGlobalOffsetMech>
        </statOffsets>
      </li>
      <li>
        <minSeverity>3</minSeverity>
        <statOffsets>
          <MechControlGroups>3</MechControlGroups>
          <WorkSpeedGlobalOffsetMech>0.18</WorkSpeedGlobalOffsetMech>
        </statOffsets>
      </li>
      <li>
        <minSeverity>4</minSeverity>
        <statOffsets>
          <MechControlGroups>4</MechControlGroups>
          <WorkSpeedGlobalOffsetMech>0.24</WorkSpeedGlobalOffsetMech>
        </statOffsets>
      </li>
      <li>
        <minSeverity>5</minSeverity>
        <statOffsets>
          <MechControlGroups>5</MechControlGroups>
          <WorkSpeedGlobalOffsetMech>0.30</WorkSpeedGlobalOffsetMech>
        </statOffsets>
      </li>
      <li>
        <minSeverity>6</minSeverity>
        <statOffsets>
          <MechControlGroups>6</MechControlGroups>
          <WorkSpeedGlobalOffsetMech>0.36</WorkSpeedGlobalOffsetMech>
        </statOffsets>
      </li>
    </stages>
  </HediffDef>

  <!-- Remote repairer -->
  <ThingDef ParentName="MechanitorImplantCraftableBase">
    <defName>RemoteRepairer</defName>
    <label>remote repairer</label>
    <description>A mechlink upgrade which allows the mechanitor to repair mechs from a distance. The user links with the mechanoid and uses their own brain to psychically guide its self-repair mechanites.\n\nThis implant can be self-installed without surgery.\n\nThis implant can be installed up to 3 times to increase its range.</description>
    <descriptionHyperlinks><HediffDef>RemoteRepairerImplant</HediffDef></descriptionHyperlinks>
    <costList>
      <PowerfocusChip>1</PowerfocusChip>
      <ComponentIndustrial>6</ComponentIndustrial>
    </costList>
    <statBases>
      <MaxInstallCount>3</MaxInstallCount>
    </statBases>
    <recipeMaker>
      <researchPrerequisite>HighMechtech</researchPrerequisite>
    </recipeMaker>
    <comps>
      <li Class="CompProperties_Usable">
        <compClass>CompUsableImplant</compClass>
        <useJob>UseItem</useJob>
        <useLabel>Implant {0_label}</useLabel>
        <userMustHaveHediff>MechlinkImplant</userMustHaveHediff>
      </li>
      <li Class="CompProperties_UseEffectInstallImplant">
        <hediffDef>RemoteRepairerImplant</hediffDef>
        <bodyPart>Brain</bodyPart>
        <canUpgrade>true</canUpgrade>
      </li>
    </comps>
  </ThingDef>

  <HediffDef>
    <defName>RemoteRepairerImplant</defName>
    <hediffClass>Hediff_Level</hediffClass>
    <descriptionHyperlinks><ThingDef>RemoteRepairer</ThingDef></descriptionHyperlinks>
    <label>remote repairer</label>
    <description>A mechlink upgrade which allows the mechanitor to repair mechs from a distance. The user links with the mechanoid and uses their own brain to psychically guide its self-repair mechanites.\n\nThis implant can be installed up to 3 times to increase its range.</description>
    <descriptionShort>An implant which allows the user to repair mechs from a distance. This can be installed up to 3 times.</descriptionShort>
    <defaultLabelColor>(0.6, 0.6, 1.0)</defaultLabelColor>
    <isBad>false</isBad>
    <priceImpact>true</priceImpact>
    <initialSeverity>1</initialSeverity> <!-- Severity is bound to level of implant -->
    <minSeverity>0</minSeverity>
    <maxSeverity>3</maxSeverity>
    <keepOnBodyPartRestoration>True</keepOnBodyPartRestoration>
    <levelIsQuantity>true</levelIsQuantity>
    <duplicationAllowed>false</duplicationAllowed>
    <comps>
      <li Class="HediffCompProperties_GiveAbility">
        <abilityDefs>
          <li>RemoteRepair</li>
        </abilityDefs>
      </li>
    </comps>
    <stages>
      <li>
        <minSeverity>1</minSeverity>
        <statOffsets>
          <MechRemoteRepairDistance>7.9</MechRemoteRepairDistance>
        </statOffsets>
      </li>
      <li>
        <minSeverity>2</minSeverity>
        <statOffsets>
          <MechRemoteRepairDistance>13.9</MechRemoteRepairDistance>
        </statOffsets>
      </li>
      <li>
        <minSeverity>3</minSeverity>
        <statOffsets>
          <MechRemoteRepairDistance>19.9</MechRemoteRepairDistance>
        </statOffsets>
      </li>
    </stages>
  </HediffDef>

  <AbilityDef>
    <defName>RemoteRepair</defName>
    <label>remote repair</label>
    <description>Remotely repair a mech. The mechanitor links with the mech and uses their brain to provide detailed guidance for its self-repair mechanites beyond its own psychic capacity. This allows the mech to make progress in rapidly healing damage.</description>
    <jobDef>RepairMechRemote</jobDef>
    <targetRequired>true</targetRequired>
    <canUseAoeToGetTargets>False</canUseAoeToGetTargets>
    <iconPath>UI/Gizmos/RemoteRepair</iconPath>
    <stunTargetWhileCasting>True</stunTargetWhileCasting>
    <showPsycastEffects>False</showPsycastEffects>
    <displayGizmoWhileUndrafted>true</displayGizmoWhileUndrafted>
    <disableGizmoWhileUndrafted>false</disableGizmoWhileUndrafted>
    <casterMustBeCapableOfViolence>false</casterMustBeCapableOfViolence>
    <hotKey>Misc12</hotKey>
    <uiOrder>3</uiOrder>
    <comps>
    </comps>
    <verbProperties>
      <verbClass>Verb_CastAbility</verbClass>
      <warmupTime>0</warmupTime>
      <range>7.9</range>
      <rangeStat>MechRemoteRepairDistance</rangeStat>
      <drawAimPie>true</drawAimPie>
      <requireLineOfSight>true</requireLineOfSight>
      <targetParams>
        <canTargetSelf>false</canTargetSelf>
        <canTargetPawns>true</canTargetPawns>
        <canTargetBuildings>false</canTargetBuildings>
        <canTargetAnimals>false</canTargetAnimals>
        <canTargetHumans>false</canTargetHumans>
        <canTargetMechs>true</canTargetMechs>
        <onlyRepairableMechs>true</onlyRepairableMechs>
      </targetParams>
    </verbProperties>
  </AbilityDef>

  <!-- Mech gestation processorr -->
  <ThingDef ParentName="MechanitorImplantCraftableBase">
    <defName>MechFormfeeder</defName>
    <label>mech gestation processor</label>
    <description>An implant which increases the speed at which a mechanitor can produce mechs in a mech gestator. The processor optimizes the behavior of the mechanites in the mech forming tank.\n\nThis implant can be self-installed in the same way as the mechlink.\n\nThis implant can be installed up to 6 times to increase its effect.</description>
    <descriptionHyperlinks><HediffDef>MechFormfeederImplant</HediffDef></descriptionHyperlinks>
    <costList>
      <SignalChip>1</SignalChip>
      <ComponentIndustrial>6</ComponentIndustrial>
    </costList>
    <statBases>
      <MaxInstallCount>6</MaxInstallCount>
    </statBases>
    <recipeMaker>
      <researchPrerequisite>StandardMechtech</researchPrerequisite>
    </recipeMaker>
    <comps>
      <li Class="CompProperties_Usable">
        <compClass>CompUsableImplant</compClass>
        <useJob>UseItem</useJob>
        <useLabel>Implant {0_label}</useLabel>
        <userMustHaveHediff>MechlinkImplant</userMustHaveHediff>
      </li>
      <li Class="CompProperties_UseEffectInstallImplant">
        <hediffDef>MechFormfeederImplant</hediffDef>
        <bodyPart>Brain</bodyPart>
        <canUpgrade>true</canUpgrade>
      </li>
    </comps>
  </ThingDef>

  <HediffDef>
    <defName>MechFormfeederImplant</defName>
    <hediffClass>Hediff_Level</hediffClass>
    <descriptionHyperlinks><ThingDef>MechFormfeeder</ThingDef></descriptionHyperlinks>
    <label>mech gestation processor</label>
    <description>An implant which increases the speed at which a mechanitor can produce mechs in a mech gestator. The processor optimizes the behavior of the mechanites in the mech forming tank.\n\nThis implant can be self-installed in the same way as the mechlink.\n\nThis implant can be installed up to 6 times to increase its effect.</description>
    <descriptionShort>An implant which lets a mechanitor gestate new mechs faster. This can be installed up to 6 times to increase the effect.</descriptionShort>
    <defaultLabelColor>(0.6, 0.6, 1.0)</defaultLabelColor>
    <isBad>false</isBad>
    <priceImpact>true</priceImpact>
    <initialSeverity>1</initialSeverity><!-- Severity is bound to level of implant -->
    <minSeverity>0</minSeverity>
    <maxSeverity>6</maxSeverity>
    <keepOnBodyPartRestoration>True</keepOnBodyPartRestoration>
    <levelIsQuantity>true</levelIsQuantity>
    <duplicationAllowed>false</duplicationAllowed>
    <stages>
      <li>
        <minSeverity>1</minSeverity>
        <statOffsets>
          <MechFormingSpeed>0.33</MechFormingSpeed>
        </statOffsets>
      </li>
      <li>
        <minSeverity>2</minSeverity>
        <statOffsets>
          <MechFormingSpeed>0.66</MechFormingSpeed>
        </statOffsets>
      </li>
      <li>
        <minSeverity>3</minSeverity>
        <statOffsets>
          <MechFormingSpeed>1</MechFormingSpeed>
        </statOffsets>
      </li>
      <li>
        <minSeverity>4</minSeverity>
        <statOffsets>
          <MechFormingSpeed>1.33</MechFormingSpeed>
        </statOffsets>
      </li>
      <li>
        <minSeverity>5</minSeverity>
        <statOffsets>
          <MechFormingSpeed>1.66</MechFormingSpeed>
        </statOffsets>
      </li>
      <li>
        <minSeverity>6</minSeverity>
        <statOffsets>
          <MechFormingSpeed>2</MechFormingSpeed>
        </statOffsets>
      </li>
    </stages>
  </HediffDef>

    <!-- Remote shielder -->
    <ThingDef ParentName="MechanitorImplantCraftableBase">
      <defName>RemoteShielder</defName>
      <label>remote shielder</label>
      <description>An implant which allows the mechanitor to place a personal defense shield on a mech. The mechanitor must continuously focus on the mech in order to maintain the shield.\n\nPlacing a shield at a distance requires a substantial amount of focused energy. It can be used to save a mech at a critical moment.\n\nThis implant can be self-installed in the same way as the mechlink.\n\nThis implant can be installed up to 3 times to increase its range and shield energy.</description>
      <descriptionHyperlinks><HediffDef>RemoteShielderImplant</HediffDef></descriptionHyperlinks>
      <costList>
        <PowerfocusChip>1</PowerfocusChip>
        <ComponentIndustrial>6</ComponentIndustrial>
      </costList>
      <recipeMaker>
        <researchPrerequisite>HighMechtech</researchPrerequisite>
      </recipeMaker>
      <statBases>
        <MaxInstallCount>3</MaxInstallCount>
      </statBases>
      <comps>
        <li Class="CompProperties_Usable">
          <compClass>CompUsableImplant</compClass>
          <useJob>UseItem</useJob>
          <useLabel>Implant {0_label}</useLabel>
          <userMustHaveHediff>MechlinkImplant</userMustHaveHediff>
        </li>
        <li Class="CompProperties_UseEffectInstallImplant">
          <hediffDef>RemoteShielderImplant</hediffDef>
          <bodyPart>Brain</bodyPart>
          <canUpgrade>true</canUpgrade>
        </li>
      </comps>
    </ThingDef>
  
    <HediffDef>
      <defName>RemoteShielderImplant</defName>
      <hediffClass>Hediff_Level</hediffClass>
      <descriptionHyperlinks><ThingDef>RemoteShielder</ThingDef></descriptionHyperlinks>
      <label>remote shielder</label>
      <description>An implant which allows the mechanitor to place a personal defense shield on a mech. The mechanitor must continuously focus on the mech in order to maintain the shield. It can be used to save a mech at a critical moment.\n\nThis implant can be installed up to 3 times to increase its range and shield energy.</description>
      <defaultLabelColor>(0.6, 0.6, 1.0)</defaultLabelColor>
      <isBad>false</isBad>
      <priceImpact>true</priceImpact>
      <initialSeverity>1</initialSeverity>
      <minSeverity>0</minSeverity>
      <maxSeverity>3</maxSeverity>
      <keepOnBodyPartRestoration>True</keepOnBodyPartRestoration>
      <levelIsQuantity>true</levelIsQuantity>
      <duplicationAllowed>false</duplicationAllowed>
      <comps>
        <li Class="HediffCompProperties_GiveAbility">
          <abilityDefs>
            <li>RemoteShield</li>
          </abilityDefs>
        </li>
      </comps>
      <stages>
        <li>
          <minSeverity>1</minSeverity>
          <statOffsets>
            <MechRemoteShieldDistance>7.9</MechRemoteShieldDistance>
            <MechRemoteShieldEnergy>75</MechRemoteShieldEnergy>
          </statOffsets>
        </li>
        <li>
          <minSeverity>2</minSeverity>
          <statOffsets>
            <MechRemoteShieldDistance>13.9</MechRemoteShieldDistance>
            <MechRemoteShieldEnergy>150</MechRemoteShieldEnergy>
          </statOffsets>
        </li>
        <li>
          <minSeverity>3</minSeverity>
          <statOffsets>
            <MechRemoteShieldDistance>19.9</MechRemoteShieldDistance>
            <MechRemoteShieldEnergy>225</MechRemoteShieldEnergy>
          </statOffsets>
        </li>
      </stages>
    </HediffDef>
  
    <AbilityDef>
      <defName>RemoteShield</defName>
      <label>remote shield</label>
      <description>Remotely project a personal defense shield onto a mech. The shield stops incoming damage, but also prevents shooting out, so this can be useful even when cast on enemies. The mechanitor must continuously focus on the mech in order to maintain the shield.</description>
      <jobDef>ShieldMech</jobDef>
      <targetRequired>true</targetRequired>
      <canUseAoeToGetTargets>False</canUseAoeToGetTargets>
      <iconPath>UI/Gizmos/ShieldMech</iconPath>
      <stunTargetWhileCasting>True</stunTargetWhileCasting>
      <showPsycastEffects>False</showPsycastEffects>
      <hotKey>Misc12</hotKey>
      <uiOrder>3</uiOrder>
      <displayGizmoWhileUndrafted>true</displayGizmoWhileUndrafted>
      <disableGizmoWhileUndrafted>false</disableGizmoWhileUndrafted>
      <cooldownTicksRange>300</cooldownTicksRange>
      <waitForJobEnd>true</waitForJobEnd>
      <casterMustBeCapableOfViolence>false</casterMustBeCapableOfViolence>
      <verbProperties>
        <verbClass>Verb_CastAbility</verbClass>
        <range>7.9</range>
        <rangeStat>MechRemoteShieldDistance</rangeStat>
        <drawAimPie>true</drawAimPie>
        <requireLineOfSight>true</requireLineOfSight>
        <warmupTime>0</warmupTime>
        <targetParams>
          <canTargetSelf>false</canTargetSelf>
          <canTargetPawns>true</canTargetPawns>
          <canTargetBuildings>false</canTargetBuildings>
          <canTargetAnimals>false</canTargetAnimals>
          <canTargetHumans>false</canTargetHumans>
          <canTargetMechs>true</canTargetMechs>
        </targetParams>
      </verbProperties>
    </AbilityDef>

  <!-- Repair probe -->
  
  <ThingDef ParentName="MechanitorImplantCraftableBase">
    <defName>RepairProbe</defName>
    <label>repair probe</label>
    <description>An implant which increases the speed at which a mechanitor can repair a mechanoid. This does not affect the overall energy needed to repair a mechanoid.\n\nThe implant allows the mechanitor to plug directly into a mechanoid while they are repairing it. This allows them to more quickly understand the mech's injuries, and better-direct the mech's self-repair mechanites to assist with the repair.\n\nThis implant can be self-installed in the same way as the mechlink.\n\nThis implant can be installed up to 6 times to increase its effect.</description>
    <descriptionHyperlinks><HediffDef>RepairProbeImplant</HediffDef></descriptionHyperlinks>
    <costList>
      <NanostructuringChip>1</NanostructuringChip>
      <ComponentSpacer>2</ComponentSpacer>
    </costList>
    <recipeMaker>
      <researchPrerequisite>UltraMechtech</researchPrerequisite>
    </recipeMaker>
    <statBases>
      <MaxInstallCount>6</MaxInstallCount>
    </statBases>
    <comps>
      <li Class="CompProperties_Usable">
        <compClass>CompUsableImplant</compClass>
        <useJob>UseItem</useJob>
        <useLabel>Implant {0_label}</useLabel>
        <userMustHaveHediff>MechlinkImplant</userMustHaveHediff>
      </li>
      <li Class="CompProperties_UseEffectInstallImplant">
        <hediffDef>RepairProbeImplant</hediffDef>
        <bodyPart>Brain</bodyPart>
        <canUpgrade>true</canUpgrade>
      </li>
    </comps>
  </ThingDef>

  <HediffDef>
    <defName>RepairProbeImplant</defName>
    <hediffClass>Hediff_Level</hediffClass>
    <descriptionHyperlinks><ThingDef>RepairProbe</ThingDef></descriptionHyperlinks>
    <label>repair probe</label>
    <description>An implant which increases a mechanitor's mech repair speed. This does not affect the overall energy needed to repair a mechanoid.\n\nThe implant allows the mechanitor to plug directly into a mechanoid while repairing it and better-direct the mech's self-repair mechanites.\n\nThis implant can be installed up to 6 times to increase its effect.</description>
    <descriptionShort>An implant which lets a mechanitor repair mechs faster. This can be installed up to 6 times.</descriptionShort>
    <defaultLabelColor>(0.6, 0.6, 1.0)</defaultLabelColor>
    <isBad>false</isBad>
    <priceImpact>true</priceImpact>
    <initialSeverity>1</initialSeverity><!-- Severity is bound to level of implant -->
    <minSeverity>0</minSeverity>
    <maxSeverity>6</maxSeverity>
    <keepOnBodyPartRestoration>True</keepOnBodyPartRestoration>
    <levelIsQuantity>true</levelIsQuantity>
    <stages>
      <li>
        <minSeverity>1</minSeverity>
        <statOffsets>
          <MechRepairSpeed>0.33</MechRepairSpeed>
        </statOffsets>
      </li>
      <li>
        <minSeverity>2</minSeverity>
        <statOffsets>
          <MechRepairSpeed>0.66</MechRepairSpeed>
        </statOffsets>
      </li>
      <li>
        <minSeverity>3</minSeverity>
        <statOffsets>
          <MechRepairSpeed>1</MechRepairSpeed>
        </statOffsets>
      </li>
      <li>
        <minSeverity>4</minSeverity>
        <statOffsets>
          <MechRepairSpeed>1.33</MechRepairSpeed>
        </statOffsets>
      </li>
      <li>
        <minSeverity>5</minSeverity>
        <statOffsets>
          <MechRepairSpeed>1.66</MechRepairSpeed>
        </statOffsets>
      </li>
      <li>
        <minSeverity>6</minSeverity>
        <statOffsets>
          <MechRepairSpeed>2</MechRepairSpeed>
        </statOffsets>
      </li>
    </stages>
  </HediffDef>

</Defs>