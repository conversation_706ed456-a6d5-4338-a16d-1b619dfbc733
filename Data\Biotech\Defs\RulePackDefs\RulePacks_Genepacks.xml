﻿<?xml version="1.0" encoding="utf-8" ?>

<Defs>

  <RulePackDef>
    <defName>NamerGenepack</defName>
    <rulePack>
      <rulesStrings>
        <li>r_name->genepack ([geneWord][otherGenesInfo])</li>

        <li>otherGenesInfo(geneCount==1)-></li>
        <li>otherGenesInfo(geneCount>=2)-> +[geneCountMinusOne]</li>
      </rulesStrings>
    </rulePack>
  </RulePackDef>

  <RulePackDef>
    <defName>NamerXenotype</defName>
    <rulePack>
      <rulesStrings>
        <li>r_name(p=1)->[anyPrefix][anySuffix]</li>
        <li>r_name(p=0.1)->[wholeName]</li>

        <li>anyPrefix(p=2)->[genePrefix]</li>
        <li>anyPrefix->[commonPrefix]</li>

        <li>anySuffix->[geneSuffix]</li>
        <li>anySuffix(p=3)->[commonSuffix]</li>

        <!-- Common parts -->
        <li>commonPrefix->xeno</li>
        <li>commonPrefix->alt</li>
        <li>commonPrefix->ab</li>
        <li>commonPrefix->out</li>
        <li>commonPrefix->sub</li>
        <li>commonPrefix->exo</li>
        <li>commonPrefix->tube</li>
        <li>commonPrefix->lab</li>
        <li>commonPrefix->pod</li>
        <li>commonPrefix->an</li>
        <li>commonPrefix->bon</li>
        <li>commonPrefix->cel</li>
        <li>commonPrefix->del</li>
        <li>commonPrefix->ech</li>
        <li>commonPrefix->fil</li>
        <li>commonPrefix->gor</li>
        <li>commonPrefix->hor</li>
        <li>commonPrefix->ich</li>
        <li>commonPrefix->jack</li>
        <li>commonPrefix->kol</li>
        <li>commonPrefix->lox</li>
        <li>commonPrefix->mack</li>
        <li>commonPrefix->nor</li>
        <li>commonPrefix->os</li>
        <li>commonPrefix->per</li>
        <li>commonPrefix->quin</li>
        <li>commonPrefix->rax</li>
        <li>commonPrefix->sil</li>
        <li>commonPrefix->tel</li>
        <li>commonPrefix->ultra</li>
        <li>commonPrefix->vox</li>
        <li>commonPrefix->whis</li>
        <li>commonPrefix->xen</li>
        <li>commonPrefix->yon</li>
        <li>commonPrefix->zel</li>

        <li>commonSuffix->kind</li>
        <li>commonSuffix->man</li>
        <li>commonSuffix->an</li>
        <li>commonSuffix->ar</li>
        <li>commonSuffix->dol</li>
        <li>commonSuffix->dal</li>
        <li>commonSuffix(p=3)->er</li>
        <li>commonSuffix(p=2)->en</li>
        <li>commonSuffix->ex</li>
        <li>commonSuffix->id</li>
        <li>commonSuffix->in</li>
        <li>commonSuffix->il</li>
        <li>commonSuffix->ist</li>
        <li>commonSuffix->on</li>
        <li>commonSuffix->ol</li>
        <li>commonSuffix->ox</li>
        <li>commonSuffix->ox</li>
        <li>commonSuffix->ub</li>
        <li>commonSuffix->ul</li>
        <li>commonSuffix->ur</li>
        <li>commonSuffix->ux</li>

      </rulesStrings>
    </rulePack>
  </RulePackDef>

</Defs>
