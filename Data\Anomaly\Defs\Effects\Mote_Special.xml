﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <ThingDef ParentName="MoteBase">
    <defName>Mote_ShamblerAlert</defName>
    <graphicData>
      <texPath>Things/Mote/ShamblerAlert</texPath>
    </graphicData>
    <thingClass>MoteThrownAttached</thingClass>
    <altitudeLayer>MetaOverlays</altitudeLayer>
    <mote>
      <fadeInTime>0.25</fadeInTime>
      <solidTime>1</solidTime>
      <fadeOutTime>1.4</fadeOutTime>
      <attachedDrawOffset>(0.45, 0, 0.45)</attachedDrawOffset>
    </mote>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_RevenantHeard</defName>
    <graphicData>
      <texPath>Things/Mote/RevenantHeard</texPath>
    </graphicData>
    <thingClass>MoteThrownAttached</thingClass>
    <altitudeLayer>MetaOverlays</altitudeLayer>
    <mote>
      <solidTime>1</solidTime>
    </mote>
  </ThingDef>
</Defs>