﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <SoundDef>
    <defName>Pawn_Shambler_Call</defName>
    <context>MapOnly</context>
    <maxVoices>15</maxVoices>
    <priorityMode>PrioritizeExisting</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Shambler/Call</clipFolderPath>
          </li>
        </grains>
        <volumeRange>10~18</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <distRange>0~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>
  
  <SoundDef>
    <defName>Pawn_Shambler_Attack</defName>
    <context>MapOnly</context>
    <maxVoices>15</maxVoices>
    <priorityMode>PrioritizeExisting</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Shambler/Attack</clipFolderPath>
          </li>
        </grains>
        <volumeRange>10~18</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <distRange>0~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>
  
  <SoundDef>
    <defName>Pawn_Shambler_Alert</defName>
    <context>MapOnly</context>
    <maxVoices>15</maxVoices>
    <priorityMode>PrioritizeExisting</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Shambler/Alert</clipFolderPath>
          </li>
        </grains>
        <volumeRange>20~25</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <distRange>0~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>
  
  <SoundDef>
    <defName>Pawn_Shambler_Wounded</defName>
    <context>MapOnly</context>
    <maxVoices>15</maxVoices>
    <priorityMode>PrioritizeExisting</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Shambler/Pain</clipFolderPath>
          </li>
        </grains>
        <volumeRange>10~18</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <distRange>0~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>
  
  <SoundDef>
    <defName>Pawn_Shambler_Killed</defName>
    <context>MapOnly</context>
    <maxVoices>15</maxVoices>
    <priorityMode>PrioritizeExisting</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Shambler/Death</clipFolderPath>
          </li>
        </grains>
        <volumeRange>20~25</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <distRange>0~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>
  
  <SoundDef>
    <defName>Pawn_Shambler_Rise</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Pawn/Shambler/Shambler_Rise_Loop_A_01</clipPath>
          </li>
        </grains>
        <muteWhenPaused>true</muteWhenPaused>
        <randomStartPoint>true</randomStartPoint>
      </li>
    </subSounds>
    <sustainFadeoutTime>0.25</sustainFadeoutTime>
    <sustainFadeoutStartSound>Pawn_Shambler_RiseEnd</sustainFadeoutStartSound>
  </SoundDef>
  
  <SoundDef>
    <defName>Pawn_Shambler_RiseEnd</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Shambler/RiseEnd</clipFolderPath>
          </li>
        </grains>
        <volumeRange>20~28</volumeRange>
        <distRange>0~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>
  
</Defs>