<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <StatDef ParentName="IntellectualSkillBase">
    <defName>EntityStudyRate</defName>
    <label>entity study rate</label>
    <description>The rate at which this person generates knowledge from studying unnatural entities.</description>
    <showOnDefaultValue>false</showOnDefaultValue>
    <minValue>0.001</minValue>
    <showOnAnimals>false</showOnAnimals>
    <showOnMechanoids>false</showOnMechanoids>
    <scenarioRandomizable>true</scenarioRandomizable>
    <displayPriorityInCategory>211</displayPriorityInCategory>
  </StatDef>
  
  <StatDef>
    <defName>StudyEfficiency</defName>
    <label>study efficiency</label>
    <description>A multiplier on how much knowledge a person gets when studying unnatural entities.</description>
    <category>PawnWork</category>
    <defaultBaseValue>1</defaultBaseValue>
    <showOnDefaultValue>false</showOnDefaultValue>
    <toStringStyle>PercentZero</toStringStyle>
    <minValue>0.001</minValue>
    <showOnAnimals>false</showOnAnimals>
    <showOnMechanoids>false</showOnMechanoids>
    <showDevelopmentalStageFilter>Child, Adult</showDevelopmentalStageFilter>
    <displayPriorityInCategory>210</displayPriorityInCategory>
  </StatDef>
  
  <StatDef>
    <defName>ActivitySuppressionRate</defName>
    <label>activity suppression speed</label>
    <description>The speed at which this person can suppress unnatural entities. A higher speed means they take less time to suppress each entity.</description>
    <category>PawnWork</category>
    <defaultBaseValue>0.065</defaultBaseValue>
    <showOnDefaultValue>false</showOnDefaultValue>
    <toStringStyle>PercentOne</toStringStyle>
    <formatString>{0} per hour</formatString>
    <minValue>0</minValue>
    <showOnAnimals>false</showOnAnimals>
    <showOnMechanoids>false</showOnMechanoids>
    <showDevelopmentalStageFilter>Adult</showDevelopmentalStageFilter>
    <skillNeedOffsets>
      <li Class="SkillNeed_BaseBonus">
        <skill>Intellectual</skill>
        <baseValue>-0.015</baseValue>
        <bonusPerLevel>0.0075</bonusPerLevel>
        <required>false</required>
      </li>
      <li Class="SkillNeed_BaseBonus">
        <skill>Social</skill>
        <baseValue>-0.015</baseValue>
        <bonusPerLevel>0.0075</bonusPerLevel>
        <required>false</required>
      </li>
    </skillNeedOffsets>
    <statFactors>
      <li>PsychicSensitivity</li>
    </statFactors>
    <displayPriorityInCategory>209</displayPriorityInCategory>
  </StatDef>

</Defs>
