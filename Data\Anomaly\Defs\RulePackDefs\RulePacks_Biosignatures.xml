﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>
  
  <RulePackDef>
    <defName>NamerBiosignature</defName>
    <include>
      <li>ArtNameUtility</li>
    </include>
    <rulePack>
      <rulesStrings>
        <li>r_root->[bioWord]-[id]</li>
        
        <li>id->[Digit][Letter]</li>
        <li>id->[Letter][Digit]</li>

        <li>bioWord->[halfA][halfB]</li>

        <li>halfA->Luna</li>
        <li>halfA->Xeno</li>
        <li>halfA->Quor</li>
        <li>halfA->Veni</li>
        <li>halfA->Zypho</li>
        <li>halfA->Plasmo</li>
        <li>halfA->Rift</li>
        <li>halfA->Nexo</li>
        <li>halfA->Bio</li>
        <li>halfA->Ichor</li>
        <li>halfA->Vira</li>
        <li>halfA->Chroma</li>
        <li>halfA->Toxi</li>
        <li>halfA->Gloom</li>
        <li>halfA->Astra</li>
        <li>halfA->Lumi</li>
        <li>halfA->Fera</li>
        <li>halfA->Panda</li>
        <li>halfA->Aether</li>
        <li>halfA->Phazo</li>
        <li>halfA->Aqua</li>
        <li>halfA->Corru</li>
        <li>halfA->Verda</li>
        <li>halfA->Pyro</li>
        <li>halfA->Cryo</li>
        <li>halfA->Mort</li>
        <li>halfA->Auro</li>
        <li>halfA->Void</li>
        <li>halfA->Umbra</li>
        <li>halfA->Cele</li>
        <li>halfA->Erebo</li>
        <li>halfA->Cinder</li>
        <li>halfA->Viri</li>
        <li>halfA->Zephy</li>
        <li>halfA->Soul</li>
        <li>halfA->Infes</li>

        <li>halfB->ris</li>
        <li>halfB->pox</li>
        <li>halfB->dium</li>
        <li>halfB->mold</li>
        <li>halfB->rona</li>
        <li>halfB->gem</li>
        <li>halfB->flux</li>
        <li>halfB->cyst</li>
        <li>halfB->mire</li>
        <li>halfB->lith</li>
        <li>halfB->dine</li>
        <li>halfB->gen</li>
        <li>halfB->rot</li>
        <li>halfB->cide</li>
        <li>halfB->nex</li>
        <li>halfB->lis</li>
        <li>halfB->bane</li>
        <li>halfB->rust</li>
        <li>halfB->flux</li>
        <li>halfB->mite</li>
        <li>halfB->ray</li>
        <li>halfB->mal</li>
        <li>halfB->nite</li>
        <li>halfB->forge</li>
        <li>halfB->cel</li>
        <li>halfB->grip</li>
        <li>halfB->wisp</li>
        <li>halfB->rash</li>
        <li>halfB->coil</li>
        <li>halfB->nine</li>
        <li>halfB->tine</li>
        <li>halfB->s</li>
        <li>halfB->sia</li>
        <li>halfB->phage</li>
        <li>halfB->dium</li>
        <li>halfB->razor</li>
        <li>halfB->bloom</li>
        <li>halfB->weave</li>
        <li>halfB->bane</li>
        <li>halfB->tus</li>

      </rulesStrings>
    </rulePack>
  </RulePackDef>
  
</Defs>