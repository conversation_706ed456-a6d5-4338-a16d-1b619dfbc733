﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <QuestScriptDef>
    <defName>SightstealerArrival</defName>
    <root Class="QuestNode_Root_SightstealerArrival" />
    <rootSelectionWeight>0</rootSelectionWeight>
    <isRootSpecial>true</isRootSpecial>
    <defaultHidden>true</defaultHidden>
    <autoAccept>true</autoAccept>
    <questNameRules>
      <rulesStrings>
        <li>questDescription->sightstealerarrival</li> <!-- hidden -->
      </rulesStrings>
    </questNameRules>
    <questDescriptionRules>
      <rulesStrings>
        <li>questDescription->sightstealerarrival</li> <!-- hidden -->
      </rulesStrings>
    </questDescriptionRules>
  </QuestScriptDef>

</Defs>