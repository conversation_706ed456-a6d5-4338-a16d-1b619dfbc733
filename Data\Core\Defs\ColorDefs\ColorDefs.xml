<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <!-- These colors are used for carpets and paints. -->

  <ColorDef Name="StructureColorBase" Abstract="True">
    <colorType>Structure</colorType>
  </ColorDef>



  <!-- Reds -->

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Red</defName>
    <label>red</label>
    <color>(118, 49, 57)</color>
    <displayOrder>0</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_RedSubtle</defName>
    <label>subtle red</label>
    <color>(132, 84, 72)</color>
    <displayOrder>10</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Auburn</defName>
    <label>auburn</label>
    <color>(138, 51, 36)</color>
    <displayOrder>20</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Scarlet</defName>
    <label>scarlet</label>
    <color>(163, 41, 42)</color>
    <displayOrder>30</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Burgundy</defName>
    <label>burgundy</label>
    <color>(91, 41, 45)</color>
    <displayOrder>40</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Plum</defName>
    <label>plum</label>
    <color>(90, 38, 60)</color>
    <displayOrder>50</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_DarkMauve</defName>
    <label>dark mauve</label>
    <color>(131, 78, 101)</color>
    <displayOrder>60</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Magenta</defName>
    <label>magenta</label>
    <color>(119, 50, 80)</color>
    <displayOrder>70</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_MagentaSubtle</defName>
    <label>subtle magenta</label>
    <color>(101, 67, 82)</color>
    <displayOrder>80</displayOrder>
  </ColorDef>

  <!-- Purples -->

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Purple</defName>
    <label>purple</label>
    <color>(99, 50, 119)</color>
    <displayOrder>90</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_PurpleSubtle</defName>
    <label>subtle purple</label>
    <color>(90, 72, 97)</color>
    <displayOrder>100</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_PurpleMuted</defName>
    <label>muted purple</label>
    <color>(116, 78, 131)</color>
    <displayOrder>110</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_PurpleDeep</defName>
    <label>deep purple</label>
    <color>(75, 38, 90)</color>
    <displayOrder>120</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Grape</defName>
    <label>grape</label>
    <color>(108, 52, 97)</color>
    <displayOrder>130</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Indigo</defName>
    <label>indigo</label>
    <color>(125, 73, 125)</color>
    <displayOrder>140</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_IndigoLight</defName>
    <label>light indigo</label>
    <color>(144, 86, 139)</color>
    <displayOrder>150</displayOrder>
  </ColorDef>

  <!-- Blues -->

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Blue</defName>
    <label>blue</label>
    <color>(24, 65, 121)</color>
    <displayOrder>160</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_BlueSubtle</defName>
    <label>subtle blue</label>
    <color>(67, 81, 101)</color>
    <displayOrder>170</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_BlueIce</defName>
    <label>ice blue</label>
    <color>(140, 148, 174)</color>
    <displayOrder>180</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Marine</defName>
    <label>marine</label>
    <color>(38, 59, 90)</color>
    <displayOrder>190</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_BlueSky</defName>
    <label>sky blue</label>
    <color>(95, 133, 191)</color>
    <displayOrder>200</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Dusk</defName>
    <label>dusk</label>
    <color>(78, 100, 131)</color>
    <displayOrder>210</displayOrder>
  </ColorDef>

  <!-- Blue-greens -->

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Moss</defName>
    <label>moss</label>
    <color>(38, 89, 90)</color>
    <displayOrder>220</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_MossBright</defName>
    <label>bright moss</label>
    <color>(78, 130, 131)</color>
    <displayOrder>230</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Teal</defName>
    <label>teal</label>
    <color>(50, 118, 119)</color>
    <displayOrder>240</displayOrder>
  </ColorDef>

  <!-- Greens -->

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Green</defName>
    <label>green</label>
    <color>(89, 105, 62)</color>
    <displayOrder>250</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Sage</defName>
    <label>sage</label>
    <color>(74, 110, 80)</color>
    <displayOrder>260</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_GreenFaded</defName>
    <label>faded green</label>
    <color>(86, 93, 76)</color>
    <displayOrder>270</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_GreenForest</defName>
    <label>forest green</label>
    <color>(78, 131, 105)</color>
    <displayOrder>280</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_GreenPine</defName>
    <label>pine green</label>
    <color>(38, 90, 64)</color>
    <displayOrder>290</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_GreenSwamp</defName>
    <label>swamp green</label>
    <color>(112, 131, 78)</color>
    <displayOrder>300</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_GreenMuddy</defName>
    <label>muddy green</label>
    <color>(96, 97, 72)</color>
    <displayOrder>310</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_GreenMarsh</defName>
    <label>marsh green</label>
    <color>(130, 131, 78)</color>
    <displayOrder>320</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Viridian</defName>
    <label>viridian</label>
    <color>(50, 119, 84)</color>
    <displayOrder>330</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_ViridianSubtle</defName>
    <label>subtle viridian</label>
    <color>(74, 94, 84)</color>
    <displayOrder>340</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Olive</defName>
    <label>olive</label>
    <color>(118, 119, 50)</color>
    <displayOrder>350</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_OliveDark</defName>
    <label>dark olive</label>
    <color>(69, 88, 36)</color>
    <displayOrder>360</displayOrder>
  </ColorDef>

  <!-- Yellow-browns -->

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Mustard</defName>
    <label>mustard</label>
    <color>(163, 131, 49)</color>
    <displayOrder>370</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_LimePale</defName>
    <label>pale lime</label>
    <color>(166, 166, 90)</color>
    <displayOrder>380</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_BrownFaded</defName>
    <label>faded brown</label>
    <color>(86, 76, 57)</color>
    <displayOrder>390</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_BrownSubtle</defName>
    <label>subtle brown</label>
    <color>(101, 88, 67)</color>
    <displayOrder>400</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_BrownLight</defName>
    <label>light brown</label>
    <color>(131, 110, 78)</color>
    <displayOrder>410</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_BrownDark</defName>
    <label>dark brown</label>
    <color>(90, 69, 38)</color>
    <displayOrder>420</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_BrownDirt</defName>
    <label>dirt brown</label>
    <color>(119, 91, 50)</color>
    <displayOrder>430</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_UmberBurnt</defName>
    <label>burnt umber</label>
    <color>(90, 58, 32)</color>
    <displayOrder>440</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_BrownWood</defName>
    <label>wood brown</label>
    <color>(108, 78, 55)</color>
    <displayOrder>450</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Orange</defName>
    <label>orange</label>
    <color>(167, 96, 39)</color>
    <displayOrder>460</displayOrder>
  </ColorDef>

  <!-- Pastels -->

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_RedPastel</defName>
    <label>pastel red</label>
    <color>(201, 112, 112)</color>
    <displayOrder>470</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Pink</defName>
    <label>pastel pink</label>
    <color>(241, 192, 218)</color>
    <displayOrder>480</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_BluePastel</defName>
    <label>pastel blue</label>
    <color>(126, 164, 204)</color>
    <displayOrder>490</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_GreenPastel</defName>
    <label>pastel green</label>
    <color>(161, 213, 180)</color>
    <displayOrder>500</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_YellowPastel</defName>
    <label>pastel yellow</label>
    <color>(204, 204, 100)</color>
    <displayOrder>510</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_OrangePastel</defName>
    <label>pastel orange</label>
    <color>(191, 137, 33)</color>
    <displayOrder>520</displayOrder>
  </ColorDef>

  <!-- Monochrome and near-monochrome -->

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_White</defName>
    <label>white</label>
    <color>(184, 184, 184)</color>
    <displayOrder>530</displayOrder>
    <displayInStylingStationUI>false</displayInStylingStationUI>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Cream</defName>
    <label>cream</label>
    <color>(195, 192, 176)</color>
    <displayOrder>540</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_GrayLight</defName>
    <label>light gray</label>
    <color>(166, 166, 166)</color>
    <displayOrder>550</displayOrder>
    <displayInStylingStationUI>false</displayInStylingStationUI>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Limestone</defName>
    <label>limestone</label>
    <color>(158, 153, 135)</color>
    <displayOrder>560</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Marble</defName>
    <label>marble</label>
    <color>(132, 135, 132)</color>
    <displayOrder>570</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Sandstone</defName>
    <label>sandstone</label>
    <color>(126, 104, 94)</color>
    <displayOrder>580</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Granite</defName>
    <label>granite</label>
    <color>(105, 95, 97)</color>
    <displayOrder>590</displayOrder>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_GreyDark</defName>
    <label>dark grey</label>
    <color>(81, 81, 81)</color>
    <displayOrder>600</displayOrder>
    <displayInStylingStationUI>false</displayInStylingStationUI>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Slate</defName>
    <label>slate</label>
    <color>(70, 70, 70)</color>
    <displayOrder>610</displayOrder>
    <displayInStylingStationUI>false</displayInStylingStationUI>
  </ColorDef>

  <ColorDef ParentName="StructureColorBase">
    <defName>Structure_Black</defName>
    <label>black</label>
    <color>(60, 60, 60)</color>
    <displayOrder>620</displayOrder>
    <displayInStylingStationUI>false</displayInStylingStationUI>
  </ColorDef>


  <!-- Hair colors -->

  <ColorDef Name="HairColorDefBase" Abstract="True">
    <colorType>Hair</colorType>
  </ColorDef>

  <ColorDef ParentName="HairColorDefBase">
    <defName>PitchBlack</defName>
    <color>(0.1, 0.1, 0.1)</color>
  </ColorDef>

  <ColorDef ParentName="HairColorDefBase">
    <defName>DarkBlack</defName>
    <color>(0.2, 0.2, 0.2)</color>
  </ColorDef>

  <ColorDef ParentName="HairColorDefBase">
    <defName>MidBlack</defName>
    <color>(0.31, 0.28, 0.26)</color>
  </ColorDef>

  <ColorDef ParentName="HairColorDefBase">
    <defName>DarkReddish</defName>
    <color>(0.25, 0.2, 0.15)</color>
  </ColorDef>

  <ColorDef ParentName="HairColorDefBase">
    <defName>DarkSaturatedReddish</defName>
    <color>(0.3, 0.2, 0.1)</color>
  </ColorDef>

  <ColorDef ParentName="HairColorDefBase">
    <defName>DarkBrown</defName>
    <color>(90, 58, 32)</color>
  </ColorDef>

  <ColorDef>
    <defName>ReddishBrown</defName>
    <color>(132, 83, 47)</color>
  </ColorDef>

  <ColorDef ParentName="HairColorDefBase">
    <defName>SandyBlonde</defName>
    <color>(193, 146, 85)</color>
  </ColorDef>

  <ColorDef ParentName="HairColorDefBase">
    <defName>Blonde</defName>
    <color>(237, 202, 156)</color>
  </ColorDef>

</Defs>