<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingDef ParentName="FurnitureBase">
    <defName>ToyBox</defName>
    <label>toy box</label>
    <description>A toy box containing many small toys. Toy boxes can be used to play with babies, satisfying their need for play.</description>
    <designationCategory>Furniture</designationCategory>
    <passability>PassThroughOnly</passability>
    <fillPercent>0.30</fillPercent>
    <rotatable>false</rotatable>
    <statBases>
      <MaxHitPoints>50</MaxHitPoints>
      <WorkToBuild>1200</WorkToBuild>
      <Mass>15</Mass>
      <Flammability>1.0</Flammability>
      <BabyPlayGainFactor>1.25</BabyPlayGainFactor>
      <JoyGainFactor>1.25</JoyGainFactor>
    </statBases>
    <stuffCategories>
      <li>Woody</li>
      <li>Stony</li>
      <li>Metallic</li>
    </stuffCategories>
    <costStuffCount>80</costStuffCount>
    <canOverlapZones>true</canOverlapZones>
    <pathCost>30</pathCost>
    <size>(1,1)</size>
    <researchPrerequisites>
      <li>ComplexFurniture</li>
    </researchPrerequisites>
    <graphicData>
      <texPath>Things/Building/Childcare/ToyBox</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <shaderType>CutoutComplex</shaderType>
      <shadowData>
        <volume>(0.8,0.15,0.8)</volume>
      </shadowData>
    </graphicData>
    <building>
      <paintable>true</paintable>
    </building>
  </ThingDef>

  <ThingDef ParentName="BuildingBase">
    <defName>SchoolDesk</defName>
    <label>school desk</label>
    <description>A desk where adults can teach lessons to children.</description>
    <graphicData>
      <texPath>Things/Building/Childcare/LessonDesk/LessonDesk</texPath>
      <graphicClass>Graphic_Multi</graphicClass>
      <drawSize>(4,3)</drawSize>
      <damageData>
        <cornerTL>Damage/Corner</cornerTL>
        <cornerTR>Damage/Corner</cornerTR>
        <cornerBL>Damage/Corner</cornerBL>
        <cornerBR>Damage/Corner</cornerBR>
      </damageData>
    </graphicData>
    <castEdgeShadows>true</castEdgeShadows>
    <staticSunShadowHeight>0.15</staticSunShadowHeight>
    <altitudeLayer>Building</altitudeLayer>
    <uiIconScale>0.8</uiIconScale>
    <designationCategory>Furniture</designationCategory>
    <uiOrder>2305</uiOrder>
    <minifiedDef>MinifiedThing</minifiedDef>
    <interactionCellIcon>DiningChair</interactionCellIcon>
    <multipleInteractionCellOffsets>
      <li>(1,0,-1)</li>
      <li>(0,0,-1)</li>
    </multipleInteractionCellOffsets>
    <descriptionHyperlinks>
      <ThingDef>Blackboard</ThingDef>
    </descriptionHyperlinks>
    <thingCategories>
      <li>BuildingsFurniture</li>
    </thingCategories>
    <passability>PassThroughOnly</passability>
    <canOverlapZones>false</canOverlapZones>
    <pathCost>50</pathCost>
    <fillPercent>0.5</fillPercent>
    <statBases>
      <MaxHitPoints>75</MaxHitPoints>
      <WorkToBuild>750</WorkToBuild>
      <Mass>5</Mass>
      <Flammability>1.0</Flammability>
      <Beauty>0.5</Beauty>
    </statBases>
    <size>(2,1)</size>
    <researchPrerequisites>
      <li>ComplexFurniture</li>
    </researchPrerequisites>
    <costStuffCount>30</costStuffCount>
    <stuffCategories>
      <li>Woody</li>
      <li>Stony</li>
      <li>Metallic</li>
    </stuffCategories>
    <placeWorkers>
      <li>PlaceWorker_PreventInteractionSpotOverlap</li>
      <li>PlaceWorker_ShowFacilitiesConnections</li>
    </placeWorkers>
    <building>
      <paintable>true</paintable>
    </building>
    <comps>
      <li Class="CompProperties_AffectedByFacilities">
        <linkableFacilities>
          <li>Blackboard</li>
        </linkableFacilities>
      </li>
      <li Class="CompProperties_StatEntry">
        <compClass>CompStatEntrySchoolDesk</compClass>
        <statCategoryDef>Building</statCategoryDef>
        <statLabel>Learning rate bonus</statLabel>
        <reportText>A multiplier on how quickly children learn during lessons which are assisted by this.</reportText>
        <displayPriorityInCategory>300</displayPriorityInCategory>
      </li>
      <li Class="CompProperties_InspectString">
        <compClass>CompInspectStringSchooldesk</compClass>
        <inspectString>Learning rate bonus</inspectString>
      </li>
    </comps>
  </ThingDef>

  <ThingDef ParentName="FurnitureBase">
    <defName>BabyDecoration</defName>
    <label>baby decoration</label>
    <description>A decoration for babies. Baby decorations increase the rate at which babies fill their play need when an adult plays with them. Place these around your child play areas.</description>
    <designationCategory>Furniture</designationCategory>
    <passability>PassThroughOnly</passability>
    <fillPercent>0.30</fillPercent>
    <rotatable>false</rotatable>
    <graphicData>
      <texPath>Things/Building/Childcare/BabyDecoration</texPath>
      <graphicClass>Graphic_Random</graphicClass>
      <shadowData>
        <volume>(0.55, 0.35, 0.55)</volume>
      </shadowData>
    </graphicData>
    <statBases>
      <MaxHitPoints>30</MaxHitPoints>
      <WorkToBuild>800</WorkToBuild>
      <Mass>10</Mass>
      <Flammability>1.0</Flammability>
      <BabyPlayGainFactor>1.1</BabyPlayGainFactor>
    </statBases>
    <stuffCategories>
      <li>Woody</li>
      <li>Stony</li>
      <li>Metallic</li>
    </stuffCategories>
    <costStuffCount>60</costStuffCount>
    <canOverlapZones>true</canOverlapZones>
    <pathCost>30</pathCost>
    <size>(1,1)</size>
    <researchPrerequisites>
      <li>ComplexFurniture</li>
    </researchPrerequisites>
    <building>
      <paintable>true</paintable>
    </building>
  </ThingDef>

  <ThingDef ParentName="SleepingSpotBase">
    <defName>BabySleepingSpot</defName>
    <label>baby sleeping spot</label>
    <description>Designates a spot on the ground where people should leave a baby. Not comfortable.</description>
    <graphicData>
      <texPath>Things/Building/Childcare/BabySleepSpot</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>(2,2)</drawSize>
    </graphicData>
    <size>(1,1)</size>
    <building>
      <bed_maxBodySize>0.25</bed_maxBodySize> <!-- Larger than HumanlikeBaby.BodySizeFactor but less than HumanlikeChild.BodySizeFactor. -->
      <bed_DisplayOwnerType>false</bed_DisplayOwnerType>
    </building>
  </ThingDef>

  <ThingDef ParentName="SansComfortBedBase">
    <defName>Crib</defName>
    <label>crib</label>
    <description>A cozy bed with safe latticed sides, perfect for a sleepy baby. A high quality crib will make a baby happy. Multiple cribs can be placed in a bedroom without turning it into a barracks.</description>
    <comps>
      <li Class="CompProperties_AffectedByFacilities">
        <linkableFacilities>
          <li MayRequire="Ludeon.RimWorld.Ideology">SleepAccelerator</li>
        </linkableFacilities>
      </li>
    </comps>
    <graphicData>
      <texPath>Things/Building/Childcare/Crib/Crib</texPath>
      <graphicClass>Graphic_Multi</graphicClass>
      <shaderType>CutoutComplex</shaderType>
      <drawSize>2.2</drawSize>
      <shadowData>
        <volume>(0.8,0.4,0.9)</volume>
      </shadowData>
    </graphicData>
    <staticSunShadowHeight>0</staticSunShadowHeight>
    <statBases>
      <BirthRitualQualityOffset MayRequire="Ludeon.RimWorld.Biotech">0</BirthRitualQualityOffset>
      <MaxHitPoints>70</MaxHitPoints>
      <Beauty>1</Beauty>
      <WorkToBuild>400</WorkToBuild>
      <Mass>15</Mass>
      <BedRestEffectiveness>1</BedRestEffectiveness>
    </statBases>
    <size>(1,1)</size>
    <costStuffCount>25</costStuffCount>
    <uiOrder>2022</uiOrder>
    <building>
      <bed_maxBodySize>0.25</bed_maxBodySize> <!-- Larger than HumanlikeBaby.BodySizeFactor but less than HumanlikeChild.BodySizeFactor. -->
      <bed_showSleeperBody>True</bed_showSleeperBody>
      <bed_emptyCountsForBarracks>false</bed_emptyCountsForBarracks>
      <bed_crib>True</bed_crib>
      <bed_pawnDrawOffset>-0.15</bed_pawnDrawOffset>
      <bed_DisplayOwnerType>false</bed_DisplayOwnerType>
      <paintable>true</paintable>
    </building>
    <researchPrerequisites Inherit="False">
    </researchPrerequisites>
  </ThingDef>

  <ThingDef ParentName="BuildingBase">
    <defName>Blackboard</defName>
    <label>blackboard</label>
    <description>A flat stone-like surface for writing on with chalk. Placed near a lesson desk, blackboards increase childrens' learning rate during lessons. Up to three blackboards can be used at a time.</description>
    <graphicData>
      <texPath>Things/Building/Childcare/Blackboard/Blackboard</texPath>
      <graphicClass>Graphic_Multi</graphicClass>
      <shaderType>CutoutComplex</shaderType>
      <drawSize>1.9</drawSize>
      <damageData>
        <rectN>(0.1, 0.3, 1.8, 0.5)</rectN>
        <rectS>(0.1, 0.3, 1.8, 0.5)</rectS>
        <rectE>(0.45, 0, 0.2, 1)</rectE>
        <rectW>(0.55, 0, 0.2, 1)</rectW>
      </damageData>
      <shadowData>
        <volume>(2, 0.5, 0.2)</volume>
      </shadowData>
    </graphicData>
    <descriptionHyperlinks>
      <ThingDef>SchoolDesk</ThingDef>
    </descriptionHyperlinks>
    <altitudeLayer>Building</altitudeLayer>
    <designationCategory>Furniture</designationCategory>
    <minifiedDef>MinifiedThing</minifiedDef>
    <defaultPlacingRot>South</defaultPlacingRot>
    <thingCategories>
      <li>BuildingsFurniture</li>
    </thingCategories>
    <passability>PassThroughOnly</passability>
    <canOverlapZones>false</canOverlapZones>
    <pathCost>50</pathCost>
    <fillPercent>0.5</fillPercent>
    <statBases>
      <MaxHitPoints>60</MaxHitPoints>
      <WorkToBuild>1000</WorkToBuild>
      <Mass>7</Mass>
      <Flammability>1.0</Flammability>
      <Beauty>2</Beauty>
    </statBases>
    <size>(2,1)</size>
    <researchPrerequisites>
      <li>ComplexFurniture</li>
    </researchPrerequisites>
    <costStuffCount>15</costStuffCount>
    <stuffCategories>
      <li>Woody</li>
      <li>Stony</li>
    </stuffCategories>
    <placeWorkers>
      <li>PlaceWorker_ShowFacilitiesConnections</li>
    </placeWorkers>
    <building>
      <paintable>true</paintable>
    </building>
    <comps>
      <li Class="CompProperties_InspectString">
        <compClass>CompInspectStringBlackboard</compClass>
        <inspectString>Learning rate</inspectString>
      </li>
      <li Class="CompProperties_Facility">
        <maxSimultaneous>3</maxSimultaneous>
        <maxDistance>6</maxDistance>
      </li>
    </comps>
  </ThingDef>

</Defs>