﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <SoundDef>
    <defName>Pawn_Gorehulk_Call</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Gorehulk/Call</clipFolderPath>
          </li>
        </grains>
        <volumeRange>20~28</volumeRange>
        <pitchRange>0.9770588~1.137647</pitchRange>
        <distRange>0~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>
  
  <SoundDef>
    <defName>Pawn_Gorehulk_Wounded</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Gorehulk/Pain</clipFolderPath>
          </li>
        </grains>
        <volumeRange>25~35</volumeRange>
        <pitchRange>0.9311764~1.068824</pitchRange>
        <distRange>5~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Gorehulk_Death</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Gorehulk/Death</clipFolderPath>
          </li>
        </grains>
        <volumeRange>25~35</volumeRange>
        <pitchRange>0.9311764~1.068824</pitchRange>
        <distRange>5~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>
  
  <SoundDef>
    <defName>Pawn_Gorehulk_Attack</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <onCamera>True</onCamera>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Gorehulk/Attack</clipFolderPath>
          </li>
        </grains>
        <volumeRange>25~35</volumeRange>
        <pitchRange>0.9311764~1.068824</pitchRange>
        <distRange>5~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>
  
</Defs>