﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <ScenarioDef>
    <defName>TheAnomaly</defName>
    <label>The Anomaly</label>
    <description>Your research expedition has finally arrived at the source of the anomalous signal: an ancient archotech monolith on a distant planet. As you survey the structure from orbit, the scanner's audio feed forms garbled syllables which repeat your own name over and over. Your crewmate screams as her flesh begins flowing and reforming. A blinding beam of energy reaches up from the planet and tears your research vessel apart. You have disturbed something ancient and inhuman.\n\nNote: This is a difficult scenario and is not recommended for new players.</description>
    <scenario>
      <summary>Three researchers and a ghoul investigate an ancient monolith.</summary>
      <playerFaction>
        <def>PlayerFaction</def>
        <factionDef>ResearchExpedition</factionDef>
      </playerFaction>
      <standardAnomalyPlaystyleOnly>true</standardAnomalyPlaystyleOnly>

      <parts>
        <!-- Game start dialog -->
        <li Class="ScenPart_GameStartDialog">
          <def>GameStartDialog</def>
          <text>The ancient monolith unleashed an energy pulse that tore your research vessel apart and mutated your crew. Only a few of you made it to the escape pods.\n\nDespite the setback, your mission is far from over. If you can learn more about the monolith, perhaps you can find a way to shut it down - or harness its inhuman power.</text>
          <closeSound>GameStartSting</closeSound>
        </li>

        <!-- Config pages -->
        <li Class="ScenPart_ConfigPage_ConfigureStartingPawns_Mutants">
          <def>ConfigurePawnsMutants</def>
          <pawnChoiceCount>8</pawnChoiceCount>
          <customSummary>Start with one ghoul and three colonists.</customSummary>
          <mutantCounts>
            <li>
              <mutant />
              <count>3</count>
            </li>
            <li>
              <mutant>Ghoul</mutant>
              <count>1</count>
              <allowedDevelopmentalStages>Adult</allowedDevelopmentalStages>
              <requiredAtStart>true</requiredAtStart>
            </li>
          </mutantCounts>
        </li>

        <!-- Player starting stuff spawn method-->
        <li Class="ScenPart_PlayerPawnsArriveMethod">
          <def>PlayerPawnsArriveMethod</def>
          <method>DropPods</method>
          <visible>false</visible>
        </li>

        <!-- Starting research -->
        <li Class="ScenPart_StartingResearch">
          <def>StartingResearch</def>
          <project>SecurityDoor</project>
        </li>
        <li Class="ScenPart_StartingResearch">
          <def>StartingResearch</def>
          <project>Batteries</project>
        </li>

        <!-- Player pawns modifiers -->
        <li Class="ScenPart_ForcedHediff">
          <def>ForcedHediff</def>
          <visible>false</visible>
          <context>PlayerStarter</context>
          <chance>0.04</chance>
          <hediff>Tentacle</hediff>
          <severityRange>1~1</severityRange>
        </li>
        <li Class="ScenPart_ForcedHediff">
          <def>ForcedHediff</def>
          <visible>false</visible>
          <context>PlayerStarter</context>
          <chance>0.04</chance>
          <hediff>FleshWhip</hediff>
          <severityRange>1~1</severityRange>
        </li>
        <li Class="ScenPart_ForcedHediff">
          <def>ForcedHediff</def>
          <visible>false</visible>
          <context>PlayerStarter</context>
          <chance>0.04</chance>
          <hediff>FleshmassLung</hediff>
          <severityRange>1~1</severityRange>
        </li>
        <li Class="ScenPart_ForcedHediff">
          <def>ForcedHediff</def>
          <visible>false</visible>
          <context>PlayerStarter</context>
          <chance>0.04</chance>
          <hediff>FleshmassStomach</hediff>
          <severityRange>1~1</severityRange>
        </li>

        <!-- Player starting things -->
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>MealSurvivalPack</thingDef>
          <count>50</count>
        </li>
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>Meat_Twisted</thingDef>
          <count>100</count>
        </li>
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>MedicineIndustrial</thingDef>
          <count>45</count>
        </li>
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>ComponentIndustrial</thingDef>
          <count>45</count>
        </li>
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>HoldingPlatform</thingDef>
          <count>1</count>
        </li>
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>ProximityDetector</thingDef>
          <count>1</count>
        </li>
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>TextBook</thingDef>
          <count>2</count>
          <quality>Normal</quality>
        </li>
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>Tome</thingDef>
          <count>1</count>
          <quality>Good</quality>
        </li>
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>Gun_PumpShotgun</thingDef>
        </li>
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>MeleeWeapon_Knife</thingDef>
          <stuff>Steel</stuff>
        </li>
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>Shard</thingDef>
          <count>1</count>
        </li>
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>Apparel_PackTurret</thingDef>
          <count>2</count>
        </li>

        <!-- Scatter some things near player start -->
        <li Class="ScenPart_ScatterThingsNearPlayerStart">
          <def>StartingThing_Defined</def>
          <thingDef>Bioferrite</thingDef>
          <count>100</count>
        </li>
        <li Class="ScenPart_ScatterThingsNearPlayerStart">
          <def>StartingThing_Defined</def>
          <thingDef>WoodLog</thingDef>
          <count>250</count>
        </li>
        <li Class="ScenPart_ScatterThingsNearPlayerStart">
          <def>StartingThing_Defined</def>
          <thingDef>Steel</thingDef>
          <count>800</count>
        </li>
        <li Class="ScenPart_ScatterThingsNearPlayerStart">
          <def>StartingThing_Defined</def>
          <thingDef>Plasteel</thingDef>
          <count>70</count>
        </li>

        <!-- Scatter some things anywhere -->
        <li Class="ScenPart_ScatterThingsAnywhere">
          <def>ScatterThingsAnywhere</def>
          <thingDef>Steel</thingDef>
          <count>700</count>
        </li>
        <li Class="ScenPart_ScatterThingsAnywhere">
          <def>ScatterThingsAnywhere</def>
          <thingDef>ShipChunk</thingDef>
          <allowRoofed>false</allowRoofed>
          <count>5</count>
        </li>

        <li Class="ScenPart_MonolithGeneration">
          <def>ScenPart_MonolithGeneration</def>
          <method>NearColonists</method>
        </li>

        <li Class="ScenPart_AutoActivateMonolith">
          <def>AutoActivateMonolith</def>
          <delayTicks>480000</delayTicks>
        </li>

        <li Class="ScenPart_CreateIncident">
          <def>CreateIncident</def>
          <incident>RefugeePodCrash_Ghoul</incident>
          <minDays>12</minDays>
          <maxDays>15</maxDays>
        </li>
      </parts>
    </scenario>
  </ScenarioDef>
</Defs>