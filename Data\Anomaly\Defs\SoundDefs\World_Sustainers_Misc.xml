﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <SoundDef>
    <defName>FleshbeastDigging</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Misc/FleshbeastDigging/Fleshbeast_Digging_A</clipPath>
          </li>
        </grains>
        <volumeRange>18~18</volumeRange>
        <distRange>15~60</distRange>
      </li>
    </subSounds>
    <sustainStopSound>FleshbeastDigging_End</sustainStopSound>
  </SoundDef>

  <SoundDef>
    <defName>FleshbeastDigging_End</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Misc/FleshbeastDigging/Fleshbeast_Digging_End_A</clipPath>
          </li>
        </grains>
        <volumeRange>18~18</volumeRange>
        <distRange>15~60</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>PitBurrowOpening</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Misc/Emergence/Emergence_Surface</clipPath>
          </li>
        </grains>
        <volumeRange>20</volumeRange>
        <distRange>15~30</distRange>
      </li>
    </subSounds>
    <sustainStopSound>PitBurrowOpening_End</sustainStopSound>
  </SoundDef>

  <SoundDef>
    <defName>PitBurrowOpening_End</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Misc/Emergence/End_Small</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <distRange>15~30</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>PitGateOpening</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Misc/Emergence/Emergence_Surface</clipPath>
          </li>
        </grains>
        <volumeRange>20</volumeRange>
        <distRange>15~30</distRange>
      </li>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Misc/Emergence/Emergence_Deep</clipPath>
          </li>
        </grains>
        <volumeRange>25</volumeRange>
        <distRange>15~60</distRange>
      </li>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/PitGate/Emergence_Point_PitGate_Loop_A</clipPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <distRange>15~60</distRange>
      </li>
    </subSounds>
    <sustainStopSound>PitGateOpening_End</sustainStopSound>
  </SoundDef>

  <SoundDef>
    <defName>PitGateOpening_End</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Misc/Emergence/End_Large</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30~30</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>PitGateCollapsing</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/PitGate/PitGate_Collapsing_Loop_A</clipPath>
          </li>
        </grains>
        <volumeRange>18~18</volumeRange>
        <distRange>20~100</distRange>
        <randomStartPoint>true</randomStartPoint>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>PitGateCollapsing_End</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Building/PitGate/FinishCollapsing</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30~30</volumeRange>
        <sustainLoop>False</sustainLoop>
      </li>
      <li>
        <onCamera>True</onCamera>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Ambience/Undercave/Collapsing/Shriek</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30~30</volumeRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>
  
  <SoundDef>
    <defName>UndercaveRumble</defName>
    <context>MapOnly</context>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <onCamera>true</onCamera>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Ambience/Undercave/Collapsing/Rumble</clipFolderPath>
          </li>
        </grains>
        <volumeRange>24~34</volumeRange>
      </li>
    </subSounds>
  </SoundDef>
  
  <SoundDef>
    <defName>UndercaveCollapsingStage1</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <onCamera>true</onCamera>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Ambience/Undercave/Collapsing/Pitgate_Collapsing_Stage1_Loop_A</clipPath>
          </li>
        </grains>
        <volumeRange>24~24</volumeRange>
        <randomStartPoint>true</randomStartPoint>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>UndercaveCollapsingStage2</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <onCamera>true</onCamera>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Ambience/Undercave/Collapsing/Pitgate_Collapsing_Stage2_Loop_A</clipPath>
          </li>
        </grains>
        <volumeRange>24~24</volumeRange>
        <randomStartPoint>true</randomStartPoint>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>UndercaveCollapsing_End</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <onCamera>True</onCamera>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Building/PitGate/FinishCollapsing</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30~30</volumeRange>
        <sustainLoop>False</sustainLoop>
      </li>
      <li>
        <onCamera>True</onCamera>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Ambience/Undercave/Collapsing/Shriek</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30~30</volumeRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>PitGateGas</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/PitGate/PitGate_Emission_Gas_Loop_A</clipPath>
          </li>
        </grains>
        <volumeRange>18~18</volumeRange>
        <distRange>20~100</distRange>
        <pitchRange>0.9~1.1</pitchRange>
        <randomStartPoint>true</randomStartPoint>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Ambient_Undercave</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <onCamera>True</onCamera>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Ambience/Undercave/Undercave_Ambience_Loop_A</clipPath>
          </li>
        </grains>
        <volumeRange>4</volumeRange>
        <paramMappings>
          <li>
            <inParam Class="SoundParamSource_AmbientVolume" />
            <outParam Class="SoundParamTarget_Volume" />
          </li>
        </paramMappings>
        <randomStartPoint>true</randomStartPoint>
      </li>
      <li>
        <onCamera>True</onCamera>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Ambience/Undercave/Drip</clipFolderPath>
          </li>
        </grains>
        <volumeRange>4~8</volumeRange>
        <pitchRange>0.7~1.3</pitchRange>
        <sustainLoop>false</sustainLoop>
        <sustainIntervalRange>5~25</sustainIntervalRange>
        <startDelayRange>5~10</startDelayRange>
        <paramMappings>
          <li>
            <inParam Class="SoundParamSource_AmbientVolume" />
            <outParam Class="SoundParamTarget_Volume" />
          </li>
        </paramMappings>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>PsychicRitual_Ongoing_Human</defName>
    <context>MapOnly</context>
    <sustain>true</sustain>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Pawn/PsychicRitual/Horax_PsychicRitual_Ongoing_Human_B2</clipPath>
          </li>
        </grains>
        <volumeRange>35</volumeRange>
        <distRange>15~60</distRange>
      </li>
    </subSounds>
    <sustainFadeoutTime>5</sustainFadeoutTime>
  </SoundDef>

  <SoundDef>
    <defName>PsychicRitual_Ongoing</defName>
    <context>MapOnly</context>
    <sustain>true</sustain>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Pawn/PsychicRitual/Horax_PsychicRitual_Ongoing_Loop_A</clipPath>
          </li>
        </grains>
        <volumeRange>15</volumeRange>
        <distRange>15~60</distRange>
        <sustainAttack>6.0</sustainAttack>
      </li>
    </subSounds>
    <sustainFadeoutTime>5</sustainFadeoutTime>
  </SoundDef>

  <SoundDef>
    <defName>ChainToPlatform</defName>
    <context>MapOnly</context>
    <sustain>true</sustain>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Misc/HoldingPlatform/Capture</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <distRange>10~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>ReleaseFromPlatform</defName>
    <context>MapOnly</context>
    <sustain>true</sustain>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Misc/HoldingPlatform/Release</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <distRange>10~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>PillarSpawning</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <maxSimultaneous>2</maxSimultaneous>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Misc/Tunnel/TunnelLoop</clipPath>
          </li>
        </grains>
        <volumeRange>15</volumeRange>
        <sustainAttack>2.5</sustainAttack>
        <sustainRelease>2.5</sustainRelease>
        <distRange>10~50</distRange>
      </li>
    </subSounds>
    <sustainStopSound>Tunnel_End</sustainStopSound>
  </SoundDef>

  <SoundDef>
    <defName>Ambient_UnnaturalDarkness_Phase1</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <onCamera>True</onCamera>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Ambience/UnnaturalDarkness/Amb_UnnaturalDarkness_Phase1_A</clipPath>
          </li>
        </grains>
        <volumeRange>4</volumeRange>
        <paramMappings>
          <li>
            <inParam Class="SoundParamSource_AmbientVolume" />
            <outParam Class="SoundParamTarget_Volume" />
          </li>
        </paramMappings>
        <randomStartPoint>true</randomStartPoint>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Ambient_UnnaturalDarkness_Phase2</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <onCamera>True</onCamera>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Ambience/UnnaturalDarkness/Amb_UnnaturalDarkness_Phase2_A</clipPath>
          </li>
        </grains>
        <volumeRange>4</volumeRange>
        <paramMappings>
          <li>
            <inParam Class="SoundParamSource_AmbientVolume" />
            <outParam Class="SoundParamTarget_Volume" />
          </li>
        </paramMappings>
        <randomStartPoint>true</randomStartPoint>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>FleshmassAmbience</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Ambience/Fleshmass</clipFolderPath>
          </li>
        </grains>
        <volumeRange>14~24</volumeRange>
        <distRange>5~30</distRange>
        <pitchRange>0.7~1.3</pitchRange>
        <sustainLoop>false</sustainLoop>
        <sustainIntervalRange>3~5</sustainIntervalRange>
        <sustainIntervalFactorByAggregateSize>
          <points>
            <li>
              <loc>
                <x>1</x>
                <y>3</y>
              </loc>
            </li>
            <li>
              <loc>
                <x>10</x>
                <y>2</y>
              </loc>
            </li>
            <li>
              <loc>
                <x>100</x>
                <y>1</y>
              </loc>
            </li>
          </points>
        </sustainIntervalFactorByAggregateSize>
        <startDelayRange>1</startDelayRange>
        <paramMappings>
          <li>
            <inParam Class="SoundParamSource_AmbientVolume" />
            <outParam Class="SoundParamTarget_Volume" />
          </li>
        </paramMappings>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Awoken_Hypnotize</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Pawn/Abilities/CorpseAttack/Corpse_Attack_Loop_A</clipPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <sustainAttack>2.5</sustainAttack>
        <sustainRelease>2.5</sustainRelease>
        <distRange>20~100</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Ambient_DeathPall</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <name>main</name>
        <onCamera>True</onCamera>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Ambience/DeathPall/Deathpall_Ambience_Loop_B</clipPath>
          </li>
        </grains>
        <volumeRange>25</volumeRange>
        <paramMappings>
          <li>
            <inParam Class="SoundParamSource_MusicPlayingFadeOut" />
            <outParam Class="SoundParamTarget_Volume" />
            <paramUpdateMode>Constant</paramUpdateMode>
            <curve>
              <points>
                <li>
                  <loc>
                    <x>0</x>
                    <y>0</y>
                  </loc>
                </li>
                <li>
                  <loc>
                    <x>1</x>
                    <y>1</y>
                  </loc>
                </li>
              </points>
            </curve>
          </li>
          <li>
            <inParam Class="SoundParamSource_CameraAltitude" />
            <outParam Class="SoundParamTarget_Volume" />
            <paramUpdateMode>Constant</paramUpdateMode>
            <curve>
              <points>
                <li>
                  <loc>
                    <x>1</x>
                    <y>1</y>
                  </loc>
                </li>
                <li>
                  <loc>
                    <x>40.93931</x>
                    <y>0.9981107</y>
                  </loc>
                </li>
                <li>
                  <loc>
                    <x>60.05113</x>
                    <y>-0.001349449</y>
                  </loc>
                </li>
              </points>
            </curve>
          </li>
          <li>
            <inParam Class="SoundParamSource_External">
              <inParamName>LerpFactor</inParamName>
              <defaultValue>0</defaultValue>
            </inParam>
            <outParam Class="SoundParamTarget_Volume" />
            <paramUpdateMode>Constant</paramUpdateMode>
            <curve>
              <points>
                <li>
                  <loc>
                    <x>0</x>
                    <y>0</y>
                  </loc>
                </li>
                <li>
                  <loc>
                    <x>1</x>
                    <y>1</y>
                  </loc>
                </li>
              </points>
            </curve>
          </li>
          <li>
            <inParam Class="SoundParamSource_AmbientVolume" />
            <outParam Class="SoundParamTarget_Volume" />
          </li>
        </paramMappings>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Zap_Loud</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Building/VoidMonolith/Activation/Zap</clipFolderPath>
          </li>
        </grains>
        <volumeRange>10</volumeRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Zap_Quiet</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Building/VoidMonolith/Activation/Zap</clipFolderPath>
          </li>
        </grains>
        <volumeRange>5</volumeRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>BiomutationLanceWarmup</defName>
    <context>MapOnly</context>
    <priorityMode>PrioritizeNearest</priorityMode>
    <sustain>True</sustain>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Item/BiomutationLance</clipFolderPath>
          </li>
        </grains>
        <volumeRange>35~35</volumeRange>
        <muteWhenPaused>True</muteWhenPaused>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>BiomutationPulserWarmup</defName>
    <context>MapOnly</context>
    <priorityMode>PrioritizeNearest</priorityMode>
    <sustain>True</sustain>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipPath>Item/BiomutationPulser</clipPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <muteWhenPaused>True</muteWhenPaused>
      </li>
    </subSounds>
  </SoundDef>
</Defs>