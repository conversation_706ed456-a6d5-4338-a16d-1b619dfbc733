﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <SoundDef>
    <defName>Pawn_Nociosphere_Call</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Nociosphere/Call</clipFolderPath>
          </li>
        </grains>
        <volumeRange>20~28</volumeRange>
        <distRange>5~70</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>
  
  <SoundDef>
    <defName>Pawn_Nociosphere_Wounded</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Nociosphere/Pain</clipFolderPath>
          </li>
        </grains>
        <volumeRange>25~35</volumeRange>
        <distRange>5~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>
  
  <SoundDef>
    <defName>Pawn_Nociosphere_Attack</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <onCamera>True</onCamera>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Nociosphere/Attack</clipFolderPath>
          </li>
        </grains>
        <volumeRange>25~35</volumeRange>
        <distRange>5~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Nociosphere_Arc</defName>
    <context>MapOnly</context>
    <maxSimultaneous>2</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Nociosphere/Departing/Arc</clipFolderPath>
          </li>
        </grains>
        <volumeRange>25~35</volumeRange>
        <distRange>5~50</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Nociosphere_Departing</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Pawn/Nociosphere/Departing/Nociosphere_Departing_Power_A_01</clipPath>
          </li>
        </grains>
        <volumeRange>50</volumeRange>
        <distRange>0~100</distRange>
        <muteWhenPaused>true</muteWhenPaused>
      </li>
    </subSounds>
  </SoundDef>
  
</Defs>