<?xml version="1.0" encoding="utf-8" ?>
<LanguageData>

  <!-- Mechanitors -->
  <AnyMechanitor>Any mechanitor</AnyMechanitor>
  <BossgroupWarningDialog>Summoning the {LEADERKIND_label} threat will summon the following hostile mechanoids:\n\n{PAWNS}\n\nThe mechanoids will arrive in the next few hours or days. Are you ready to fight?</BossgroupWarningDialog>
  <MechControllerDowned>{PAWN_labelShort} is downed.</MechControllerDowned>
  <MechControllerImprisoned>{PAWN_labelShort} is a prisoner.</MechControllerImprisoned>
  <MechControllerInsideContainer>{PAWN_labelShort} is inside {CONTAINER}</MechControllerInsideContainer>
  <MechControllerInsufficientBandwidth>{PAWN_labelShort} has insufficient bandwidth</MechControllerInsufficientBandwidth>
  <MechControllerMentalState>{PAWN_labelShort} is in a '{MENTALSTATE_label}' mental state.</MechControllerMentalState>
  <ConfirmDisassemblingMech>Do you really want to permanently disassemble {0}?\n\nPermanently disassembling {0} will recover</ConfirmDisassemblingMech>
  <ConfirmInstallMechlink_Smithing>A mechanitor must be capable of smithing to create mechanoids.\n\nAre you sure you want to continue?</ConfirmInstallMechlink_Smithing>
  <ConfirmInstallMechlink_Intellectual>A mechanitor must be capable of intellectual tasks to unlock mechanitor research.\n\nAre you sure you want to continue?</ConfirmInstallMechlink_Intellectual>
  <InsufficientBandwidth>insufficient bandwidth</InsufficientBandwidth>
  <MechanitorLeftMapWithoutMechs>The following mechs have been left behind by {PAWN_labelShort}:\n\n{MECHS}\n\nThe mechs will stay here until they are picked up.\n\nWould you like to set their work mode to escort so they follow {PAWN_labelShort} off the map?</MechanitorLeftMapWithoutMechs>

  <!-- Apparel -->
  <ApparelRequirementAnyMechlordApparel>Any mechlord apparel</ApparelRequirementAnyMechlordApparel>
  <ApparelRequirementOrAnyPsycasterOrPrestigeApparelOrMechlord>or any psycaster, prestige, or mechlord apparel</ApparelRequirementOrAnyPsycasterOrPrestigeApparelOrMechlord>

  <!-- Mech gestator -->
  <GestationCyclesCompleted>Gestation cycles completed</GestationCyclesCompleted>
  <FormerIngredients>Gathered ingredients</FormerIngredients>

  <!-- Subcore scanner -->
  <ConfirmRipscanPawn>Ripscanning {PAWN_nameDef} will kill {PAWN_objective}. Are you sure you want to continue?</ConfirmRipscanPawn>
  <ConfirmCancelRipscan>Cancelling a ripscan will still kill {PAWN_nameDef}. Are you sure you want to continue?</ConfirmCancelRipscan>

  <!-- Xenogerm -->
  <AssembleGenes>Assemble genes</AssembleGenes>
  <CreateXenotype>Create xenotype</CreateXenotype>
  <SelectIcon>Select icon</SelectIcon>
  <SelectIconDesc>The icon of this xenotype.</SelectIconDesc>
  <InspectGenesDesc>Inspect the genes inside the {0_labelShort}.</InspectGenesDesc>
  <InspectGenesHediffDesc>Inspect the unborn baby's genes.</InspectGenesHediffDesc>
  <InspectGenesEmbryoDesc>Inspect the embryo's genes.</InspectGenesEmbryoDesc>
  <NoGenePacks>No genepacks</NoGenePacks>
  <StartCombining>Start combining</StartCombining>
  <SelectedGenepacks>Selected genepacks</SelectedGenepacks>
  <GenepackLibrary>Genepack library</GenepackLibrary>
  <ClickToAddOrRemove>Click to add or remove</ClickToAddOrRemove>
  <ClickToAdd>Click to add</ClickToAdd>
  <ClickToRemove>Click to remove</ClickToRemove>
  <SelectXenogerm>Select xenogerm</SelectXenogerm>
  <WarningPawnWillDieFromReimplanting>Warning: {PAWN_nameDef}'s genes are currently regrowing. If {PAWN_pronoun} implants {PAWN_possessive} xenogerm before {PAWN_possessive} genes are fully regrown, it will kill {PAWN_objective}.\n\nAre you sure you wish to continue?</WarningPawnWillDieFromReimplanting>
  <NotEnoughArchites>Not enough archite capsules.</NotEnoughArchites>
  <XenogenesDesc>Xenogenes are genes derived from an implanted xenogerm. If the xenogerm is removed or replaced, the xenogenes go with it. Since xenogenes are not part of the organism's own germline DNA, they are not passed to children.</XenogenesDesc>
  <EndogenesDesc>Germline genes are naturally-inherited genes which are in every cell in an organism's body and don't require any external support. They are passed from parent to child.</EndogenesDesc>
  <MemberXenotypeChances>Member xenotypes</MemberXenotypeChances>
  <XenotypeName>xenotype name</XenotypeName>
  <LockNameButtonDesc>Lock or unlock the xenotype's name. When locked, the xenotype name will not auto-regenerate when genepacks are added or removed.</LockNameButtonDesc>
  <LockNameOn>Xenotype name lock is on. Xenotype name will not regenerate when adding or removing genepacks.</LockNameOn>
  <LockNameOff>Xenotype name lock is off. Xenotype name will regenerate when adding or removing genepacks.</LockNameOff>
  <CreatingXenogerm>Creating xenogerm</CreatingXenogerm>
  <GenepackUnusableGenebankUnpowered>This genepack is contained in an unpowered genebank. If added, production on the xenogerm will be suspended until power is restored.</GenepackUnusableGenebankUnpowered>
  <GeneProcessorUnpowered>This xenogerm requires more genetic complexity than the gene assembler and nearby powered gene processors can support.</GeneProcessorUnpowered>
  <RecombineDuration>Estimated recombination time</RecombineDuration>
  <RecombineDurationDesc>How long it would take for an average worker to combine the selected genes into a xenogerm. Recombination time increases with the total complexity of the genes in the xenogerm.</RecombineDurationDesc>
  <MustResearchProject>You must research {0_label} before you can assemble a xenogerm that requires archite capsules.</MustResearchProject>
  <AssemblingRequiresResearch>Assembling xenogerms with archite genes requires {0_label} research.</AssemblingRequiresResearch>
  <ConfirmStartNewXenogerm>You are currently combining a {XENOGERMNAME} xenogerm. If you start combining a new xenogerm, it will cancel your current xenogerm.\n\nAre you sure you want to continue?</ConfirmStartNewXenogerm>
  <SelectAGeneToRandomizeName>Select at least one gene to randomize a name for this xenotype.</SelectAGeneToRandomizeName>
  <SelectAGeneToChooseAName>Select at least one gene to choose a name for this xenotype.</SelectAGeneToChooseAName>

  <!-- View genes -->
  <InspectGenes>Inspect genes</InspectGenes>
  <InspectBabyGenes>Inspect baby genes</InspectBabyGenes>
  <NoXenogermImplanted>no xenogerm implanted</NoXenogermImplanted>
  <Endogenes>germline genes</Endogenes>
  <Xenogenes>xenogenes</Xenogenes>
  <Overridden>overridden</Overridden>
  <OverriddenByGene>Overridden by gene</OverriddenByGene>
  <OverriddenByIdenticalGene>Overridden by identical gene</OverriddenByIdenticalGene>
  <MoreInfoInInfoScreen>More information is available on this xenotype's information screen.</MoreInfoInInfoScreen>
  <TakesEffectAfterAge>Takes effect after age</TakesEffectAfterAge>

  <!-- Xenotype editor -->
  <XenotypeEditor>Xenotype editor</XenotypeEditor>
  <AnyNonArchite>Any (non-archite)</AnyNonArchite>
  <SelectedGenes>Selected genes</SelectedGenes>
  <Custom>custom</Custom>
  <Hybrid>hybrid</Hybrid>
  <GenesAreInheritable>Genes are heritable</GenesAreInheritable>
  <GenesAreInheritableXenotypeDef>Whether or not this xenotype can be passed on to children.</GenesAreInheritableXenotypeDef>
  <GenesAreInheritableDesc>All genes in this xenotype can be passed on to children.</GenesAreInheritableDesc>
  <GeneWillBeRandomChosen>For each individual, one of the following genes will be randomly selected to be active</GeneWillBeRandomChosen>
  <GeneIncompatibleDesc>This gene is incompatible with other genes in this xenotype. Only one of these genes will be active</GeneIncompatibleDesc>
  <GeneLeftmostActive>Of the following genes, the left-most one will always apply and the others are suppressed</GeneLeftmostActive>
  <GeneOneActive>Of the following genes, only one will apply and the others will be suppressed</GeneOneActive>
  <WarnChangingXenotypeWillRandomizePawn>Changing xenotype will randomize this colonist.\n\nContinue anyway?</WarnChangingXenotypeWillRandomizePawn>
  <DeleteThisXenotype>Delete this xenotype.</DeleteThisXenotype>
  <IgnoreRestrictions>Ignore restrictions</IgnoreRestrictions>
  <IgnoreRestrictionsDesc>Created xenotypes have no biostat limits. Genes requiring archite capsules can be added.</IgnoreRestrictionsDesc>
  <XenotypeBreaksLimits>This xenotype breaks normal gameplay limits</XenotypeBreaksLimits>
  <XenotypeBreaksLimits_Archites>It contains archite genes and is inheritable, but archite genes are usually never inheritable.</XenotypeBreaksLimits_Archites>
  <XenotypeBreaksLimits_Exceeds>{STAT} is {VALUE}, which exceeds the normal limit of {MAX}.</XenotypeBreaksLimits_Exceeds>
  <XenotypeBreaksLimits_Below>{STAT} is {VALUE}, which is below the normal limit of {MIN}.</XenotypeBreaksLimits_Below>
  <SaveAnyway>Save anyway?</SaveAnyway>
  <IgnoreRestrictionsConfirmation>Ignoring restrictions allows you to create impossible gene combinations by exceeding biostat limits. It also gives you access to powerful archite genes, which can make the game very easy.\n\nAre you sure you want to ignore restrictions?</IgnoreRestrictionsConfirmation>
  <DevelopmentalAgeSelectionDesc>Colonists will always be within this developmental stage's age range when generated.</DevelopmentalAgeSelectionDesc>
  <XenotypeSelectionDesc>Colonists will always carry the genes defined by this xenotype.</XenotypeSelectionDesc>
  <SaveAndApply>Save and apply</SaveAndApply>
  <LoadCustom>Load custom</LoadCustom>
  <LoadPremade>Load premade</LoadPremade>
  <GenesConflict>Genes conflict</GenesConflict>
  <GenesConflictDesc>{FIRST_label}, {SECOND_label}</GenesConflictDesc>

  <!-- Xenogerm editor -->
  <LoadXenogermTemplate>Load template</LoadXenogermTemplate>
  <SaveXenogermTemplate>Save template</SaveXenogermTemplate>
  <CannotSaveXenogermTemplate>Cannot save a template with no {0}.</CannotSaveXenogermTemplate>
  <XenogermTemplateSaved>{NAME} template saved</XenogermTemplateSaved>
  <MissingGenepacksForXenogerm>Missing {COUNT} genepacks for {NAME} template.</MissingGenepacksForXenogerm>
  <MissingGenepackForXenogerm>Missing genepack for {NAME} template</MissingGenepackForXenogerm>
  <DeleteThisXenogerm>Delete this xenogerm.</DeleteThisXenogerm>

  <!-- Babies -->
  <ChooseChildOrAdult>Select at least one child or adult.</ChooseChildOrAdult>

  <!-- Deathrest -->
  <WarningWakingInterruptsDeathrest>Waking {PAWN_nameDef} before {MINDURATION} has passed will interrupt {PAWN_possessive} deathrest, leaving {PAWN_objective} sick for days while {PAWN_pronoun} recovers. Waking early means {PAWN_nameDef} will not benefit from any deathrest buildings. {PAWN_pronoun} has been deathresting for {CURDURATION}.\n\nAre you sure you wish to wake {PAWN_nameDef}?</WarningWakingInterruptsDeathrest>

  <!-- Text motes -->
  <NumWoundsTended>{0} wounds tended</NumWoundsTended>

  <!-- Mechs -->
  <MechWeightClass>Weight class</MechWeightClass>
  <MechWeightClassExplanation>The weight class of this mech.</MechWeightClassExplanation>
  <MechOverseer>This mech's overseer is {0}.</MechOverseer>
  <MechWorkActivities>Work activities</MechWorkActivities>
  <MechWorkActivitiesExplanation>The work activities that can be performed by this mechanoid.\n\nThe following list details which work activities can be done within each work type</MechWorkActivitiesExplanation>
  <MechRechargeSettingsTitle>Recharge settings</MechRechargeSettingsTitle>
  <MechRechargeSettingsExplanation>Change when mechanoids will start and stop charging.\n\nMechanoids with energy below this range will automatically seek out a recharger. They will stop charging once they reach the top of this range. These settings only apply in work mode.</MechRechargeSettingsExplanation>
  <MechWorkSkill>Work skill</MechWorkSkill>
  <MechWorkSkillDesc>The skill level that this mechanoid acts with.</MechWorkSkillDesc>
  <AnyMech>Any mech</AnyMech>
  <AnyNonMech>Any human</AnyNonMech>
  <CaravanLacksMechMechanitorWarning>Your caravan contains a mechanoid but not their overseer. You will have limited control over the mechanoid when you reach your destination.</CaravanLacksMechMechanitorWarning>
  <MechsSection>Mechanoids</MechsSection>

  <!-- Pawn naming -->
  <RenameMech>Rename mech</RenameMech>
  <MechGainsName>The mech's name is now {0}.</MechGainsName>

  <FertilizeOvumInbredChance>{FATHER_nameDef} is {MOTHER_nameDef}'s {RELATION}. There's a {CHANCE} chance that the resulting baby will have genetic abnormalities due to inbreeding.\n\nAre you sure you want to continue?</FertilizeOvumInbredChance>

  <ConfirmationPawnPregnancyTerminated>{PAWN_nameDef} is pregnant. Implanting an IUD while {PAWN_pronoun} is in {PAWN_possessive} first trimester of pregnancy will terminate the pregnancy.\n\nAre you sure you want to continue?</ConfirmationPawnPregnancyTerminated>

  <!-- Pollution -->
  <ConfirmSettleNearPollution>This tile is near polluted tiles, so there will be an acidic smog here roughly every {0} days. Acidic smog slows plant growth, deteriorates exposed items, and makes people unhappy.</ConfirmSettleNearPollution>

  <!-- Pregnancy -->
  <PawnIsAway>{PAWN_nameDef} is away.</PawnIsAway>
  <PawnsHaveSameGender>{PAWN1_nameDef} and {PAWN2_nameDef} are the same gender.</PawnsHaveSameGender>
  <PawnsAreSterile>{PAWN1_nameDef} and {PAWN2_nameDef} are sterile.</PawnsAreSterile>
  <PawnIsSterile>{PAWN_nameDef} is sterile.</PawnIsSterile>
  <PawnsAreInfertile>{PAWN1_nameDef} and {PAWN2_nameDef} are infertile.</PawnsAreInfertile>
  <PawnIsInfertile>{PAWN_nameDef} is infertile.</PawnIsInfertile>
  <PawnsAreTooYoung>{PAWN1_nameDef} and {PAWN2_nameDef} are too young to have children.</PawnsAreTooYoung>
  <PawnIsTooYoung>{PAWN_nameDef} is too young to have children.</PawnIsTooYoung>
  <PawnIsDead>{PAWN_nameDef} is dead.</PawnIsDead>

  <PsychicBondDistanceWillBeActive_Cryptosleep>Placing {PAWN_nameDef} into cryptosleep will strain {PAWN_possessive} psychic bond with {BOND_nameDef}, causing stress to both partners.\n\nAre you sure you want to continue?</PsychicBondDistanceWillBeActive_Cryptosleep>
  <PsychicBondDistanceWillBeActive_Caravan>Someone in this caravan is psychically bonded by their partner is not coming with them. These people will be stressed if separated from their bond-partners</PsychicBondDistanceWillBeActive_Caravan>
  <Partner>partner {0_nameFull}</Partner>


  <ChooseMechAccentColor>Choose mech accent color</ChooseMechAccentColor>

  <!-- Faction specific comms overrides -->
  <PigFactionGreetingHostile>{0} squeals a string of curses into the microphone and disconnects.</PigFactionGreetingHostile>
  <PigFactionGreetingHostileAppreciative>Oinking angrily, {0} demands that you make a gesture of unity, like bringing gifts to one of {0_possessive} settlements or releasing {0_possessive} faction's prisoners unharmed. Until then, {0_pronoun} is unwilling to talk.\n\nSnorting an insult, {0_pronoun} disconnects.</PigFactionGreetingHostileAppreciative>
  <PigFactionGreetingWary>{0} oinks a greeting and asks what business you need done.</PigFactionGreetingWary>
  <PigFactionGreetingWarm>Oinking enthusiastically, {0} greets {NEGOTIATOR_labelShort} and asks what {LEADER_pronoun} can do to help you.</PigFactionGreetingWarm>
  <PigMilitaryAidSent>{0} agrees to send pigskin fighters to help you.</PigMilitaryAidSent>

  <YttakinFactionGreetingHostile>{0} roars into the microphone and disconnects.</YttakinFactionGreetingHostile>
  <YttakinFactionGreetingHostileAppreciative>Growling under {0_possessive} breath, {0} demands that you make a gesture of friendship, like bringing gifts to one of {0_possessive} settlements or releasing {0_possessive} faction's prisoners unharmed. Until then, {0_pronoun} is unwilling to talk.\n\nSnarling an insult, {0_pronoun} disconnects.</YttakinFactionGreetingHostileAppreciative>
  <YttakinFactionGreetingWary>{0} grumbles a greeting to {NEGOTIATOR_labelShort} and asks why you are calling.</YttakinFactionGreetingWary>
  <YttakinFactionGreetingWarm>{0} bellows a friendly greeting and asks what {LEADER_pronoun} can do to help you.</YttakinFactionGreetingWarm>

  <NeanderthalFactionGreetingHostile>{0} grunts angrily and disconnects.</NeanderthalFactionGreetingHostile>
  <NeanderthalFactionGreetingHostileAppreciative>{0} grunts aggressively. Until you make a gesture of goodwill, {0_pronoun} will not do business with you. You can improve relations through a number of activities, like bringing gifts to one of {0_possessive} settlements or releasing {0_possessive} faction's prisoners unharmed.\n\n{0_pronoun} disconnects abruptly.</NeanderthalFactionGreetingHostileAppreciative>
  <NeanderthalFactionGreetingWary>{0} grunts a greeting.</NeanderthalFactionGreetingWary>
  <NeanderthalFactionGreetingWarm>{0} grunts a warm greeting to {NEGOTIATOR_labelShort}.</NeanderthalFactionGreetingWarm>

  <MaxTier>Max tier</MaxTier>
  <MaxTierDesc>{PAWN_nameDef} has accumulated the maximum number of growth points.</MaxTierDesc>
  <ProgressToNextGrowthTier>Progress to next growth tier</ProgressToNextGrowthTier>
  <ThisGrowthTier>This growth tier</ThisGrowthTier>
  <NextGrowthTier>Next growth tier</NextGrowthTier>
  <NumPassionsFromOptions>Choose {0} passions from {1} options.</NumPassionsFromOptions>
  <NumTraitsFromOptions>Choose {0} trait from {1} options.</NumTraitsFromOptions>
  <NextGrowthMomentAt>Next growth moment at age</NextGrowthMomentAt>

  <RoomRequirementNoBiotechBuildings>no biotech buildings allowed</RoomRequirementNoBiotechBuildings>

  <WarningPawnWillHaveSeriousBloodlossFromBloodfeeding>Bloodfeeding on {PAWN_nameDef} will result in serious blood loss, leaving {PAWN_objective} unconscious.\n\nAre you sure?</WarningPawnWillHaveSeriousBloodlossFromBloodfeeding>
  <WarningPawnWillDieFromBloodfeeding>Bloodfeeding on {PAWN_nameDef} will kill {PAWN_objective}.\n\nAre you sure?</WarningPawnWillDieFromBloodfeeding>

  <AbilityMustHaveTendableWound>{0_labelShort} must have a tendable wound.</AbilityMustHaveTendableWound>

  <!-- Abandon home (including mechs) -->
  <ConfirmAbandonHomeWithColonyPawnsIncMechs>Some of your colonists, animals or mechanoids are in this area. Are you sure you want to abandon it?\n\nThe following people, animals and mechs will be left behind:\n\n{0}</ConfirmAbandonHomeWithColonyPawnsIncMechs>

</LanguageData>