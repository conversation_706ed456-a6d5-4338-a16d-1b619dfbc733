<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <PrisonerInteractionModeDef>
    <defName>Bloodfeed</defName>
    <label>bloodfeed</label>
    <listOrder>25</listOrder>
    <description>When a bloodfeeder's hemogen levels are low, they may feed on this prisoner. Other prisoners who are bloodfeeders can also feed on this prisoner, if they are in the same cell.</description>
    <hideIfNoBloodfeeders>true</hideIfNoBloodfeeders>
    <hideOnHemogenicPawns>true</hideOnHemogenicPawns>
    <mustBeAwake>false</mustBeAwake>
    <isNonExclusiveInteraction>true</isNonExclusiveInteraction>
  </PrisonerInteractionModeDef>
  
  <PrisonerInteractionModeDef>
    <defName>HemogenFarm</defName>
    <label>hemogen farm</label>
    <listOrder>26</listOrder>
    <description>An operation to extract hemogen packs will be automatically queued on this prisoner whenever it can be done without killing them. The operation will be carried out by a doctor.</description>
    <hideOnHemogenicPawns>true</hideOnHemogenicPawns>
    <mustBeAwake>false</mustBeAwake>
    <isNonExclusiveInteraction>true</isNonExclusiveInteraction>
  </PrisonerInteractionModeDef>

</Defs>
