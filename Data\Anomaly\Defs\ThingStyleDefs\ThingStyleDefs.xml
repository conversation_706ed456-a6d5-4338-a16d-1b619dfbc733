<?xml version="1.0" encoding="utf-8"?>
<Defs>
  
  <!-- Horaxian weapons -->
  <ThingStyleDef>
    <defName>Horaxian_Axe</defName>
    <graphicData>
      <texPath>Things/Item/Equipment/WeaponMelee/HoraxianAxe</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
  </ThingStyleDef>
  
  <ThingStyleDef>
    <defName>Horaxian_Knife</defName>
    <graphicData>
      <texPath>Things/Item/Equipment/WeaponMelee/HoraxianKnife</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
  </ThingStyleDef>
  
  <ThingStyleDef>
    <defName>Horaxian_Gladius</defName>
    <graphicData>
      <texPath>Things/Item/Equipment/WeaponMelee/HoraxianGladius</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
  </ThingStyleDef>
  
  <ThingStyleDef>
    <defName>Horax<PERSON>_<PERSON></defName>
    <graphicData>
      <texPath>Things/Item/Equipment/WeaponMelee/HoraxianMace</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
  </ThingStyleDef>
  
  <ThingStyleDef>
    <defName>Horaxian_Club</defName>
    <graphicData>
      <texPath>Things/Item/Equipment/WeaponMelee/HoraxianClub</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
  </ThingStyleDef>

  <!-- Cultist masks -->
  <ThingStyleDef>
    <defName>CultistMask_TwistedMask</defName>
    <wornGraphicPath>Things/Pawn/Humanlike/Apparel/TwistedMask/TwistedMask</wornGraphicPath>
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/TwistedMask/TwistedMask</texPath>
      <drawSize>0.9</drawSize>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
  </ThingStyleDef>
  
  <ThingStyleDef>
    <defName>CultistMask_Spikemask</defName>
    <wornGraphicPath>Things/Pawn/Humanlike/Apparel/Spikemask/Spikemask</wornGraphicPath>
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/Spikemask/Spikemask</texPath>
      <drawSize>0.9</drawSize>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
  </ThingStyleDef>

  <ThingStyleDef>
    <defName>CultistMask_SpikedApex</defName>
    <wornGraphicPath>Things/Pawn/Humanlike/Apparel/SpikedApex/SpikedApex</wornGraphicPath>
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/SpikedApex/SpikedApex</texPath>
      <drawSize>0.9</drawSize>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
  </ThingStyleDef>

  <ThingStyleDef>
    <defName>CultistMask_Dreadmask</defName>
    <wornGraphicPath>Things/Pawn/Humanlike/Apparel/Dreadmask/Dreadmask</wornGraphicPath>
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/Dreadmask/Dreadmask</texPath>
      <drawSize>0.9</drawSize>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
  </ThingStyleDef>

  <!-- Ceremonial cultist masks -->
  <ThingStyleDef>
    <defName>CeremonialCultistMask_CeremonialDreadcap</defName>
    <wornGraphicPath>Things/Pawn/Humanlike/Apparel/CeremonialDreadcap/CeremonialDreadcap</wornGraphicPath>
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/CeremonialDreadcap/CeremonialDreadcap</texPath>
      <drawSize>0.8</drawSize>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
  </ThingStyleDef>

  <ThingStyleDef>
    <defName>CeremonialCultistMask_WhispererMask</defName>
    <wornGraphicPath>Things/Pawn/Humanlike/Apparel/WhispererMask/WhispererMask</wornGraphicPath>
    <useWornGraphicMask>true</useWornGraphicMask>
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/WhispererMask/WhispererMask</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <shaderType>CutoutComplex</shaderType>
    </graphicData>
  </ThingStyleDef>
  
</Defs>