﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <!-- Harbinger trees -->
  <GenStepDef>
    <defName>HarbingerTrees</defName>
    <order>1200</order>
    <genStep Class="GenStep_HarbingerTrees">
      <treeDef>Plant_TreeHarbinger</treeDef>
      <minProximityToSameTree>16</minProximityToSameTree>
      <pollutionNone>0</pollutionNone>
      <pollutionLight>0</pollutionLight>
      <pollutionModerate>1</pollutionModerate>
      <pollutionExtreme>3</pollutionExtreme>
    </genStep>
  </GenStepDef>

  <!-- Monolith -->
  <GenStepDef>
    <defName>VoidMonolith</defName>
    <order>1600</order><!-- after fogged -->
    <genStep Class="GenStep_Monolith">
      <count>1</count>
      <validators>
        <li Class="ScattererValidator_Buildable">
          <radius>4</radius>
          <affordance>Heavy</affordance>
        </li>
        <li Class="ScattererValidator_NoNonNaturalEdifices">
          <radius>4</radius>
        </li>
        <li Class="ScattererValidator_AvoidSpecialThings" />
      </validators>
      <fallbackValidators>
        <li Class="ScattererValidator_Buildable">
          <radius>3</radius>
          <affordance>Heavy</affordance>
        </li>
      </fallbackValidators>
      <allowRoofed>false</allowRoofed>
      <minEdgeDistPct>0.166667</minEdgeDistPct>
      <minDistToPlayerStartPct>0.166667</minDistToPlayerStartPct>
      <allowFoggedPositions>false</allowFoggedPositions>
      <onlyOnStartingMap>true</onlyOnStartingMap>
    </genStep>
  </GenStepDef>

</Defs>