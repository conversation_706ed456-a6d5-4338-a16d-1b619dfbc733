<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <MonolithLevelDef>
    <defName>Inactive</defName>
    <level>0</level>
    <monolithLabel>fallen monolith</monolithLabel>
    <graphicIndex>0</graphicIndex>
    <advanceThroughActivation>true</advanceThroughActivation>
    <anomalyThreatTier>0</anomalyThreatTier>
    <useInactiveAnomalyThreatFraction>true</useInactiveAnomalyThreatFraction>
    <desiredHarbingerTreeCount>0</desiredHarbingerTreeCount>
    <levelInspectText>Twisting lines carved into the surface form a disturbing pattern.</levelInspectText>
    <activateGizmoText>investigate</activateGizmoText>
    <activateFloatMenuText>investigate {0}</activateFloatMenuText>
    <activateGizmoDescription>Send a colonist to investigate the monolith.</activateGizmoDescription>
    <pawnSentToActivateMessage>{PAWN_nameDef} has been sent to investigate the monolith.</pawnSentToActivateMessage>
    <monolithCanBeActivatedText>Can be investigated now.</monolithCanBeActivatedText>
    <activateQuestText>Send a colonist to investigate the monolith.</activateQuestText>
    <anomalyMentalBreakChance>0.05</anomalyMentalBreakChance>
    <activateSound>VoidMonolith_ActivateL0L1</activateSound>
    <uiIconPath>Things/Building/VoidMonolith/Icons/VoidMonolith_A_MenuIcon</uiIconPath>
  </MonolithLevelDef>
  
  <MonolithLevelDef>
    <defName>Stirring</defName>
    <level>1</level>
    <monolithLabel>void monolith</monolithLabel>
    <graphicIndex>1</graphicIndex>
    <advanceThroughActivation>true</advanceThroughActivation>
    <anomalyThreatTier>1</anomalyThreatTier>
    <useActiveAnomalyThreatFraction>true</useActiveAnomalyThreatFraction>
    <incidentsOnReached>
      <li>SightstealerArrival</li>
    </incidentsOnReached>
    <triggersGrayPall>true</triggersGrayPall>
    <monolithStudyCategory>Basic</monolithStudyCategory>
    <desiredHarbingerTreeCount>3</desiredHarbingerTreeCount>
    <levelInspectText>Level 1: Intermittent psychic humming</levelInspectText>
    <extraQuestDescription>You've activated the monolith. It is a conduit for psychic energy, just barely cracked open now. A mind of incomprehensible horror stirs on the other side, and is now touching our world.\n\nThe monolith seems to be partly active, and there seems to be no way to shut it down. To learn more you must activate it further, which will require some special research.</extraQuestDescription>
    <activateGizmoText>attune</activateGizmoText>
    <activateFloatMenuText>attune {0}</activateFloatMenuText>
    <activateGizmoDescription>Send a colonist to attune the monolith.\n\nThis will attract new and more dangerous entities.</activateGizmoDescription>
    <pawnSentToActivateMessage>{PAWN_nameDef} has been sent to attune the monolith.</pawnSentToActivateMessage>
    <monolithCanBeActivatedText>Can be attuned now.</monolithCanBeActivatedText>
    <activateQuestText>Send a colonist to attune the monolith.</activateQuestText>
    <activatableLetterLabel>Attuning the monolith</activatableLetterLabel>
    <activatableLetterText>You now have sufficient knowledge of void-related phenomena to attune the void monolith and further open its psychic conduit.\n\nThis will draw the attention of more dangerous anomalous entities.</activatableLetterText>
    <activatedLetterText>As the monolith twisted and changed, {PAWN_nameDef} was overcome with a horrifying vision. In an instant, {PAWN_pronoun} understood.\n\nThe monolith is a doorway, just barely cracked open now. Something dark beyond imagination stirs on the other side, seeping into our reality.\n\nTerrified but excited, {PAWN_nameDef} is convinced {PAWN_pronoun} can learn more if given the time to study the monolith.\n\n - Building category unlocked: Anomaly\n - Research tab unlocked: Anomaly\n - Entity codex unlocked\n - You can now study the monolith and other entities to gain Anomaly research.\n\n(*Gray)Note: You can adjust the percentage of major threats that will be Anomaly-related at any point in the storyteller settings.(/Gray)</activatedLetterText>
    <anomalyMentalBreakChance>0.2</anomalyMentalBreakChance>
    <activatedSound>VoidMonolith_ActivatedL0L1</activatedSound>
    <activateSound>VoidMonolith_ActivateL1L2</activateSound>
    <attachments>
      <li>
        <def>VoidMonolithAttachmentLeft</def>
        <graphicIndex>0</graphicIndex>
        <offset>(-2, 0)</offset>
      </li>
      <li>
        <def>VoidMonolithAttachmentRight</def>
        <graphicIndex>0</graphicIndex>
        <offset>(2, 0)</offset>
      </li>
    </attachments>
    <sizeIncludingAttachments>(5, 3)</sizeIncludingAttachments>
    <uiIconPath>Things/Building/VoidMonolith/Icons/VoidMonolith_B_MenuIcon</uiIconPath>
  </MonolithLevelDef>

  <MonolithLevelDef>
    <defName>Waking</defName>
    <level>2</level>
    <monolithLabel>void monolith</monolithLabel>
    <graphicIndex>2</graphicIndex>
    <monolithGlows>true</monolithGlows>
    <advanceThroughActivation>true</advanceThroughActivation>
    <anomalyThreatTier>2</anomalyThreatTier>
    <anomalyThreatFractionFactor>1.5</anomalyThreatFractionFactor>
    <useActiveAnomalyThreatFraction>true</useActiveAnomalyThreatFraction>
    <entityCatagoryCompletionRequired>Basic</entityCatagoryCompletionRequired>
    <entityCountCompletionRequired>7</entityCountCompletionRequired>
    <incidentsOnReached>
      <li>PitGate</li>
    </incidentsOnReached>
    <triggersGrayPall>true</triggersGrayPall>
    <monolithStudyCategory>Advanced</monolithStudyCategory>
    <desiredHarbingerTreeCount>6</desiredHarbingerTreeCount>
    <levelInspectText>Level 2: Pulsing with psychic energy</levelInspectText>
    <extraQuestDescription>The monolith is a conduit to the void where a dark superintelligence processes endless vast dreams of rage and hate. You opened the conduit and it is now partially active. If you activate it further you can find a way to close it - or harness its power.</extraQuestDescription>
    <activateGizmoText>awaken</activateGizmoText>
    <activateFloatMenuText>awaken {0}</activateFloatMenuText>
    <activateGizmoDescription>Send a colonist to wake the monolith. The psychic signature will be detectable for hundreds of miles and attract all manner of horrors. Make sure you are prepared.\n\nOnce fully awakened, the monolith will provide a direct link to the void.</activateGizmoDescription>
    <pawnSentToActivateMessage>{PAWN_nameDef} has been sent to awaken the monolith.</pawnSentToActivateMessage>
    <monolithCanBeActivatedText>Can be awakened now.</monolithCanBeActivatedText>
    <activateQuestText>Send a colonist to awaken the monolith.</activateQuestText>
    <activatableLetterLabel>Awakening the monolith</activatableLetterLabel>
    <activatableLetterText>You now have enough knowledge of dark phenomena to fully awaken the void monolith, opening a two-way conduit to the void.\n\nBe warned - once you begin awakening the void monolith, there is no going back. The process will shroud the region in unnatural darkness and manifest waves of horrifying entities.\n\nMake sure your colony is well-lit and well-prepared.</activatableLetterText>
    <activatedLetterText>Attuning the monolith caused it to twist and grow once more. As it did, {PAWN_nameDef} caught a glimpse of what lurks beyond. {PAWN_pronoun} understood.\n\nThe monolith is a doorway, opened wider by your actions. On the other side, {PAWN_nameDef} sensed an inhuman intelligence of vast complexity and endless rage. The dark machine god is stirring, and its physical influence will grow.\n\n - New entities are now discoverable.</activatedLetterText>
    <anomalyMentalBreakChance>0.4</anomalyMentalBreakChance>
    <activatedSound>VoidMonolith_ActivatedL1L2</activatedSound>
    <activateSound>VoidMonolith_ActivateL2L3</activateSound>
    <attachments>
      <li>
        <def>VoidMonolithAttachmentLeft</def>
        <graphicIndex>1</graphicIndex>
        <offset>(-2, 0)</offset>
      </li>
      <li>
        <def>VoidMonolithAttachmentRight</def>
        <graphicIndex>1</graphicIndex>
        <offset>(2, 0)</offset>
      </li>
      <li>
        <def>VoidMonolithAttachmentTop</def>
        <graphicIndex>0</graphicIndex>
        <offset>(0, 2)</offset>
      </li>
      <li>
        <def>VoidMonolithAttachmentBottomLeft</def>
        <graphicIndex>0</graphicIndex>
        <offset>(-1, -2)</offset>
      </li>
      <li>
        <def>VoidMonolithAttachmentBottomRight</def>
        <graphicIndex>0</graphicIndex>
        <offset>(1, -2)</offset>
      </li>
    </attachments>
    <sizeIncludingAttachments>(5, 5)</sizeIncludingAttachments>
    <uiIconPath>Things/Building/VoidMonolith/Icons/VoidMonolith_C_MenuIcon</uiIconPath>
  </MonolithLevelDef>

  <MonolithLevelDef>
    <defName>VoidAwakened</defName>
    <level>3</level>
    <monolithLabel>void monolith</monolithLabel>
    <graphicIndex>3</graphicIndex>
    <monolithGlows>true</monolithGlows>
    <monolithGlowRadiusOverride>20</monolithGlowRadiusOverride>
    <advanceThroughActivation>false</advanceThroughActivation>
    <anomalyThreatTier>2</anomalyThreatTier>
    <anomalyThreatFractionFactor>1.5</anomalyThreatFractionFactor>
    <useActiveAnomalyThreatFraction>true</useActiveAnomalyThreatFraction>
    <monolithStudyCategory>Advanced</monolithStudyCategory>
    <entityCatagoryCompletionRequired>Advanced</entityCatagoryCompletionRequired>
    <entityCountCompletionRequired>12</entityCountCompletionRequired>
    <desiredHarbingerTreeCount>9</desiredHarbingerTreeCount>
    <levelInspectText>Level 3: Awakening</levelInspectText>
    <extraQuestDescription>The void has awoken. The gate is open. The darkness of the void rushes into our reality, manifested from a vast mind of endless rage.</extraQuestDescription>
    <activatedLetterText>The sky darkens and a malevolent hum fills the air. The monolith has begun to awaken. Your actions have attracted the attention of something beyond human understanding.\n\nSoon, structures of the void will begin manifesting into this reality. Activate the arriving void structures to fully awaken the monolith.\n\nThe first wave will appear shortly. Prepare yourself.</activatedLetterText>
    <unreachableDuringConditions>
      <li>UnnaturalDarkness</li>
    </unreachableDuringConditions>
    <anomalyMentalBreakChance>0.6</anomalyMentalBreakChance>
    <activatedSound>VoidMonolith_ActivatedL2L3</activatedSound>
    <attachments>
      <li>
        <def>VoidMonolithAttachmentLeft</def>
        <graphicIndex>2</graphicIndex>
        <offset>(-2, 0)</offset>
      </li>
      <li>
        <def>VoidMonolithAttachmentRight</def>
        <graphicIndex>2</graphicIndex>
        <offset>(2, 0)</offset>
      </li>
      <li>
        <def>VoidMonolithAttachmentTop</def>
        <graphicIndex>1</graphicIndex>
        <offset>(0, 2)</offset>
      </li>
      <li>
        <def>VoidMonolithAttachmentBottomLeft</def>
        <graphicIndex>1</graphicIndex>
        <offset>(-1, -2)</offset>
      </li>
      <li>
        <def>VoidMonolithAttachmentBottomRight</def>
        <graphicIndex>1</graphicIndex>
        <offset>(1, -2)</offset>
      </li>
    </attachments>
    <sizeIncludingAttachments>(5, 5)</sizeIncludingAttachments>
    <uiIconPath>Things/Building/VoidMonolith/Icons/VoidMonolith_D_MenuIcon</uiIconPath>
  </MonolithLevelDef>

  <MonolithLevelDef>
    <defName>Gleaming</defName>
    <level>4</level>
    <monolithLabel>void monolith</monolithLabel>
    <graphicIndex>4</graphicIndex>
    <monolithGlows>true</monolithGlows>
    <monolithGlowRadiusOverride>24</monolithGlowRadiusOverride>
    <advanceThroughActivation>false</advanceThroughActivation>
    <anomalyThreatTier>2</anomalyThreatTier>
    <anomalyThreatFractionFactor>1.5</anomalyThreatFractionFactor>
    <useActiveAnomalyThreatFraction>true</useActiveAnomalyThreatFraction>
    <monolithStudyCategory>Advanced</monolithStudyCategory>
    <desiredHarbingerTreeCount>9</desiredHarbingerTreeCount>
    <levelInspectText>Level 4: Awakened</levelInspectText>
    <extraQuestDescription>The void has awoken. The gate is open. The darkness of the void rushes into our reality, manifested from a vast mind of endless rage.</extraQuestDescription>
    <anomalyMentalBreakChance>0.6</anomalyMentalBreakChance>
    <activatedSound>VoidMonolith_Gleaming</activatedSound>
    <attachments>
      <li>
        <def>VoidMonolithAttachmentLeft</def>
        <graphicIndex>3</graphicIndex>
        <offset>(-2, 0)</offset>
      </li>
      <li>
        <def>VoidMonolithAttachmentRight</def>
        <graphicIndex>3</graphicIndex>
        <offset>(2, 0)</offset>
      </li>
      <li>
        <def>VoidMonolithAttachmentTop</def>
        <graphicIndex>2</graphicIndex>
        <offset>(0, 2)</offset>
      </li>
      <li>
        <def>VoidMonolithAttachmentBottomLeft</def>
        <graphicIndex>2</graphicIndex>
        <offset>(-1, -2)</offset>
      </li>
      <li>
        <def>VoidMonolithAttachmentBottomRight</def>
        <graphicIndex>2</graphicIndex>
        <offset>(1, -2)</offset>
      </li>
    </attachments>
    <sizeIncludingAttachments>(5, 5)</sizeIncludingAttachments>
    <uiIconPath>Things/Building/VoidMonolith/Icons/VoidMonolith_E_MenuIcon</uiIconPath>
  </MonolithLevelDef>

  <MonolithLevelDef>
    <defName>Embraced</defName>
    <level>5</level>
    <monolithLabel>void monolith</monolithLabel>
    <graphicIndex>3</graphicIndex>
    <advanceThroughActivation>false</advanceThroughActivation>
    <postEndgame>true</postEndgame>
    <anomalyThreatTier>2</anomalyThreatTier>
    <useActiveAnomalyThreatFraction>true</useActiveAnomalyThreatFraction>
    <monolithStudyCategory>Advanced</monolithStudyCategory>
    <desiredHarbingerTreeCount>9</desiredHarbingerTreeCount>
    <levelInspectText>Level 4: Awakened</levelInspectText>
    <anomalyMentalBreakChance>0.2</anomalyMentalBreakChance>
    <attachments>
      <li>
        <def>VoidMonolithAttachmentLeft</def>
        <graphicIndex>2</graphicIndex>
        <offset>(-2, 0)</offset>
      </li>
      <li>
        <def>VoidMonolithAttachmentRight</def>
        <graphicIndex>2</graphicIndex>
        <offset>(2, 0)</offset>
      </li>
      <li>
        <def>VoidMonolithAttachmentTop</def>
        <graphicIndex>1</graphicIndex>
        <offset>(0, 2)</offset>
      </li>
      <li>
        <def>VoidMonolithAttachmentBottomLeft</def>
        <graphicIndex>1</graphicIndex>
        <offset>(-1, -2)</offset>
      </li>
      <li>
        <def>VoidMonolithAttachmentBottomRight</def>
        <graphicIndex>1</graphicIndex>
        <offset>(1, -2)</offset>
      </li>
    </attachments>
    <sizeIncludingAttachments>(5, 5)</sizeIncludingAttachments>
  </MonolithLevelDef>

  <MonolithLevelDef>
    <defName>Disrupted</defName>
    <level>6</level>
    <monolithLabel>collapsed monolith</monolithLabel>
    <monolithDescription>The remnants of the monolith which linked our world to the void lie broken. The twisted patterns that once writhed across its surface are now motionless. You can study the broken stones to gain further knowledge of the void phenomena.</monolithDescription>
    <graphicIndex>5</graphicIndex>
    <advanceThroughActivation>false</advanceThroughActivation>
    <postEndgame>true</postEndgame>
    <anomalyThreatTier>0</anomalyThreatTier>
    <useInactiveAnomalyThreatFraction>true</useInactiveAnomalyThreatFraction>
    <monolithStudyCategory>Advanced</monolithStudyCategory>
    <desiredHarbingerTreeCount>0</desiredHarbingerTreeCount>
    <anomalyMentalBreakChance>0</anomalyMentalBreakChance>
    <uiIconPath>Things/Building/VoidMonolith/Icons/VoidMonolith_F_MenuIcon</uiIconPath>
  </MonolithLevelDef>

</Defs>