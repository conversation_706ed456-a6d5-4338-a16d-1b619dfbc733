<?xml version="1.0" encoding="utf-8"?>
<Defs>
  
  <SoundDef>
    <defName>MechChargerCharging</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <sustain>True</sustain>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/Mechanoid/MechCharger/Mech_Charger_Charging_Loop_01a</clipPath>
          </li>
        </grains>
        <volumeRange>120</volumeRange>
        <distRange>5~20</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>GeneAssembler_Working</defName>
    <context>MapOnly</context>
    <sustain>True</sustain>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/GeneAssembler/GeneAssembler_Working_Loop</clipPath>
          </li>
        </grains>
        <volumeRange>100</volumeRange>
        <distRange>5~30</distRange>
        <sustainLoop>false</sustainLoop>
      </li>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Buildings/GeneAssembler/Working</clipFolderPath>
          </li>
        </grains>
        <sustainLoop>false</sustainLoop>
        <volumeRange>50</volumeRange>
        <distRange>5~30</distRange>
        <sustainIntervalRange>1~3</sustainIntervalRange>
        <repeatMode>NeverTwice</repeatMode>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>GrowthVat_Working</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <sustain>True</sustain>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/GrowthVat/GrowthVat_Working_Loop</clipPath>
          </li>
        </grains>
        <volumeRange>50</volumeRange>
        <distRange>5~20</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>GeneExtractor_Working</defName>
    <context>MapOnly</context>
    <sustain>True</sustain>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/GeneExtractor/GeneExtractor_Working_Amb_Loop</clipPath>
          </li>
        </grains>
        <volumeRange>110</volumeRange>
        <distRange>5~30</distRange>
        <sustainLoop>false</sustainLoop>
      </li>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Buildings/GeneExtractor/Grains</clipFolderPath>
          </li>
        </grains>
        <sustainLoop>false</sustainLoop>
        <volumeRange>30</volumeRange>
        <distRange>5~30</distRange>
        <sustainIntervalRange>1~4</sustainIntervalRange>
        <repeatMode>NeverTwice</repeatMode>
      </li>
    </subSounds>
  </SoundDef>
  
  <SoundDef>
    <defName>BandNodeTuning_Ambience</defName>
    <context>MapOnly</context>
    <sustain>True</sustain>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/Mechanoid/BandNode/Tuning/BandNode_Tune_Loop_01a</clipPath>
          </li>
        </grains>
        <volumeRange>40</volumeRange>
        <distRange>5~20</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>BandNodeTuned_Ambience</defName>
    <context>MapOnly</context>
    <sustain>True</sustain>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/Mechanoid/BandNode/Tuned/BandNode_Sustainer_Loop_01a</clipPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <distRange>5~20</distRange>
      </li>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Buildings/Mechanoid/BandNode/Tuned/Random</clipFolderPath>
          </li>
        </grains>
        <volumeRange>40</volumeRange>
        <distRange>5~20</distRange>
        <sustainLoop>False</sustainLoop>
        <sustainIntervalRange>5~10</sustainIntervalRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>MechBooster_Working</defName>
    <context>MapOnly</context>
    <sustain>True</sustain>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/Mechanoid/MechBooster/MechBooster_Charging_Loop_01a</clipPath>
          </li>
        </grains>
        <volumeRange>40</volumeRange>
        <distRange>5~20</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Toxifier_Working</defName>
    <context>MapOnly</context>
    <sustain>True</sustain>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/Mechanoid/Toxifier/Toxifier_Generating_Loop_04a</clipPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <distRange>5~20</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>MechGestator_Ambience</defName>
    <context>MapOnly</context>
    <sustain>True</sustain>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/Mechanoid/MechGestator/Mech_Formation_Cycle_Loop_04a</clipPath>
          </li>
        </grains>
        <volumeRange>42</volumeRange>
        <distRange>5~20</distRange>
        <sustainAttack>3</sustainAttack>
        <sustainRelease>2</sustainRelease>
        <startDelayRange>4</startDelayRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Hemopump_Ambience</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <sustain>True</sustain>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/Hemopump/Hemopump_Working_Loop</clipPath>
          </li>
        </grains>
        <sustainAttack>2</sustainAttack>
        <sustainRelease>0.125</sustainRelease>
        <volumeRange>50</volumeRange>
        <distRange>5~20</distRange>
        <sustainSkipFirstAttack>false</sustainSkipFirstAttack>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>GlucosoidPump_Ambience</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <sustain>True</sustain>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/GlucosoidPump/GlucosoidPump_Working_Loop</clipPath>
          </li>
        </grains>
        <sustainIntervalRange>3</sustainIntervalRange>
        <sustainAttack>1</sustainAttack>
        <sustainRelease>1</sustainRelease>
        <volumeRange>30</volumeRange>
        <distRange>5~20</distRange>
        <sustainSkipFirstAttack>false</sustainSkipFirstAttack>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>HemogenAmplifier_Ambience</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <sustain>True</sustain>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/HemogenAmplifier/HemogenAmplifier_Working_Loop</clipPath>
          </li>
        </grains>
        <sustainAttack>0.5</sustainAttack>
        <sustainRelease>0.5</sustainRelease>
        <volumeRange>50</volumeRange>
        <distRange>5~20</distRange>
        <sustainSkipFirstAttack>false</sustainSkipFirstAttack>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>DeathrestAccelerator_Ambience</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <sustain>True</sustain>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/DeathrestAccelerator_Working_Loop</clipPath>
          </li>
        </grains>
        <volumeRange>50</volumeRange>
        <distRange>5~20</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>PsychofluidPump_Ambience</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <sustain>True</sustain>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/PsychofluidPump/PsychofluidPump_Working_Loop</clipPath>
          </li>
        </grains>
        <sustainAttack>0.5</sustainAttack>
        <sustainRelease>0.125</sustainRelease>
        <volumeRange>50</volumeRange>
        <distRange>5~20</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>SubcoreSoftscanner_Working</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <sustain>True</sustain>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/SubcoreSoftscanner/Softscanner_Scan_Loop_01a</clipPath>
          </li>
        </grains>
        <volumeRange>20</volumeRange>
        <distRange>10~30</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>SubcoreRipscanner_Working</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <sustain>True</sustain>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/SubcoreRipscanner/Ripscanner_Scan_Loop_01a</clipPath>
          </li>
        </grains>
        <startDelayRange>1.15</startDelayRange>
        <sustainAttack>1</sustainAttack>
        <sustainRelease>0.5</sustainRelease>
        <volumeRange>20</volumeRange>
        <distRange>10~30</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>WastepackAtomizer_Working</defName>
    <context>MapOnly</context>
    <sustain>True</sustain>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Buildings/WastepackAtomizer/Working</clipFolderPath>
          </li>
        </grains>
        <volumeRange>25</volumeRange>
        <distRange>5~20</distRange>
        <repeatMode>NeverTwice</repeatMode>      
        <sustainLoop>False</sustainLoop>
        <sustainIntervalRange>-1</sustainIntervalRange>      
        <sustainAttack>0.5</sustainAttack>
        <sustainRelease>0.5</sustainRelease>
      </li>
    </subSounds>
  </SoundDef>

</Defs>