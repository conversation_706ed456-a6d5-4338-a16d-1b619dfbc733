﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <!-- Long term, tense music -->
  <MusicSequenceDef>
    <defName>HorrorRelax</defName>
    <loopDelayRange>1~2</loopDelayRange>
    <minTimeToPlay>30</minTimeToPlay>
    <loop>true</loop>
    <songs>
      <li>Abandoned_By_The_Light</li>
      <li>Blood_Rain_Falling</li>
      <li>Shamblers_Blues</li>
      <li>A_Twisted_Path</li>
    </songs>
  </MusicSequenceDef>
  
  <!-- Short term, tense music -->
  <MusicSequenceDef>
    <defName>HorrorTension</defName>
    <loopDelayRange>1~2</loopDelayRange>
    <fadeoutLastSongDuration>5</fadeoutLastSongDuration>
    <minTimeToPlay>30</minTimeToPlay>
    <loop>true</loop>
    <songs>
      <li>Have_They_Come_For_Us</li>
      <li>Is_It_Truly_Alive</li>
    </songs>
  </MusicSequenceDef>
  
  <MusicSequenceDef>
    <defName>HorrorMonolithAdvanced</defName>
    <fadeoutLastSongDuration>5</fadeoutLastSongDuration>
    <canBeInterrupted>false</canBeInterrupted>
    <useTransitionForLifetime>false</useTransitionForLifetime> <!-- so will play to completition -->
    <songs>
      <li>Have_They_Come_For_Us</li>
      <li>Is_It_Truly_Alive</li>
    </songs>
  </MusicSequenceDef>

  <!-- Combat music -->
  <MusicSequenceDef>
    <defName>HorrorCombat</defName>
    <endOnNoDanger>true</endOnNoDanger>
    <loopDelayRange>5~10</loopDelayRange>
    <canBeInterrupted>false</canBeInterrupted>
    <minTimeToPlay>30</minTimeToPlay>
    <fadeoutLastSongDuration>5</fadeoutLastSongDuration>
    <pausedVolumeFactor>0.5</pausedVolumeFactor>
    <loop>true</loop>
    <songs>
      <li>They_See_You</li>
      <li>Death_Pall_Rising</li>
      <li>Shadow_Work</li>
      <li>Spent_Introject</li>
    </songs>
  </MusicSequenceDef>

  <!-- Metal Hell -->
  <MusicSequenceDef>
    <defName>MetalHell</defName>
    <canBeInterrupted>false</canBeInterrupted>
    <loop>true</loop>
    <fadeoutLastSongDuration>2</fadeoutLastSongDuration>
    <song>MetalHell_Ambience</song>
    <overrideVolume>0.6</overrideVolume>
  </MusicSequenceDef>

  <!-- Corpse Attack -->
  <MusicSequenceDef>
    <defName>CorpseAttack</defName>
    <canBeInterrupted>false</canBeInterrupted>
    <loop>true</loop>
    <fadeoutLastSongDuration>4</fadeoutLastSongDuration>
    <fadeInDuration>4</fadeInDuration> <!-- this starts suddenly, so ease it in -->
    <overrideFadeout>4</overrideFadeout>
    <song>Corpse_Attack</song>
<!--    <overrideVolume>0.5</overrideVolume>-->
    <loopFadeout>true</loopFadeout>
    <pausedVolumeFactor>0.5</pausedVolumeFactor>
  </MusicSequenceDef>

</Defs>
