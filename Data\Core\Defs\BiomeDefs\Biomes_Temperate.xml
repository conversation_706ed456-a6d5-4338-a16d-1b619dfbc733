﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <BiomeDef>
    <defName>TemperateForest</defName>
    <label>temperate forest</label>
    <description>Forests of deciduous trees interspersed with fertile clearings. Many species of animals move around among the trees and on the plains.</description>
    <workerClass>BiomeWorker_TemperateForest</workerClass>
    <animalDensity>3.7</animalDensity>
    <plantDensity>0.65</plantDensity>
    <settlementSelectionWeight>1</settlementSelectionWeight>
    <movementDifficulty>1</movementDifficulty>
    <texture>World/Biomes/TemperateForest</texture>
    <forageability>1</forageability>
    <foragedFood>RawBerries</foragedFood>
    <wildPlantRegrowDays>20</wildPlantRegrowDays>
    <soundsAmbient>
      <li>Ambient_NightInsects_Standard</li>
    </soundsAmbient>
    <diseaseMtbDays>50</diseaseMtbDays>
    <diseases>
      <li>
        <diseaseInc>Disease_Flu</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_Plague</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_Malaria</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_GutWorms</diseaseInc>
        <commonality>50</commonality>
      </li>
      <li>
        <diseaseInc>Disease_FibrousMechanites</diseaseInc>
        <commonality>30</commonality>
      </li>
      <li>
        <diseaseInc>Disease_SensoryMechanites</diseaseInc>
        <commonality>30</commonality>
      </li>
      <li>
        <diseaseInc>Disease_MuscleParasites</diseaseInc>
        <commonality>50</commonality>
      </li>

      <li>
        <diseaseInc>Disease_AnimalFlu</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_AnimalPlague</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_OrganDecay</diseaseInc>
        <commonality>10</commonality>
      </li>
    </diseases>
    <terrainsByFertility>
      <li>
        <terrain>Soil</terrain>
        <min>-999</min>
        <max>0.87</max>
      </li>
      <li>
        <terrain>SoilRich</terrain>
        <min>0.87</min>
        <max>999</max>
      </li>
    </terrainsByFertility>
    <terrainPatchMakers>
      <li>
        <perlinFrequency>0.015</perlinFrequency>
        <thresholds>
          <li>
            <terrain>SoilRich</terrain>
            <min>0.73</min>
            <max>0.80</max>
          </li>
          <li>
            <terrain>Mud</terrain>
            <min>0.80</min>
            <max>0.93</max>
          </li>
          <li>
            <terrain>WaterShallow</terrain>
            <min>0.93</min>
            <max>1.06</max>
          </li>
          <li>
            <terrain>WaterDeep</terrain>
            <min>1.06</min>
            <max>999</max>
          </li>
        </thresholds>
      </li>
    </terrainPatchMakers>
    <baseWeatherCommonalities>
      <Clear>18</Clear>
      <Fog>1</Fog>
      <Rain>2</Rain>
      <DryThunderstorm>1</DryThunderstorm>
      <RainyThunderstorm>1</RainyThunderstorm>
      <FoggyRain>1</FoggyRain>
      <SnowGentle>4</SnowGentle>
      <SnowHard>4</SnowHard>
      <GrayPall MayRequire="Ludeon.RimWorld.Anomaly">1</GrayPall>
    </baseWeatherCommonalities>
    <wildPlants>
      <Plant_Grass>5.0</Plant_Grass>
      <Plant_GrayGrass MayRequire="Ludeon.RimWorld.Biotech">2</Plant_GrayGrass>
      <Plant_TallGrass>2.0</Plant_TallGrass>
      <Plant_Brambles>1.0</Plant_Brambles>
      <Plant_Ripthorn MayRequire="Ludeon.RimWorld.Biotech">0.8</Plant_Ripthorn>
      <Plant_Dandelion>0.5</Plant_Dandelion>
      <Plant_Bush>0.6</Plant_Bush>
      <Plant_TreeOak>0.5</Plant_TreeOak>
      <Plant_TreePoplar>0.5</Plant_TreePoplar>
      <Plant_Berry>0.05</Plant_Berry>
      <Plant_HealrootWild>0.05</Plant_HealrootWild>
      <Plant_Witchwood MayRequire="Ludeon.RimWorld.Biotech">0.5</Plant_Witchwood>
    </wildPlants>
    <wildAnimals>
      <Donkey>0.20</Donkey>
      <GuineaPig>0.45</GuineaPig>
      <Yak>0.06</Yak>
      <Bison>0.07</Bison>
      <Horse>0.20</Horse>
      <Hare>1.0</Hare>
      <Squirrel>1.0</Squirrel>
      <Rat>1.0</Rat>
      <Deer>0.5</Deer>
      <WildBoar>0.5</WildBoar>
      <Turkey>0.5</Turkey>
      <Raccoon>0.5</Raccoon>
      <Ibex>0.5</Ibex>
      <Muffalo>0.5</Muffalo>
      <Alpaca>0.5</Alpaca>
      <Boomalope>0.4</Boomalope>
      <Boomrat>0.4</Boomrat>
      <Tortoise>0.3</Tortoise>
      <Gazelle>0.3</Gazelle>
      <Megasloth>0.2</Megasloth>
      <Rhinoceros>0.1</Rhinoceros>
      <Bear_Grizzly>0.07</Bear_Grizzly>
      <Wolf_Timber>0.07</Wolf_Timber>
      <Fox_Red>0.07</Fox_Red>
      <Cougar>0.07</Cougar>
      <Lynx>0.07</Lynx>
      <Warg>0.07</Warg>
    </wildAnimals>
    <pollutionWildAnimals MayRequire="Ludeon.RimWorld.Biotech">
      <Toxalope MayRequire="Ludeon.RimWorld.Biotech">0.4</Toxalope>
      <WasteRat MayRequire="Ludeon.RimWorld.Biotech">0.1</WasteRat>
      <Rat>1</Rat>
      <Raccoon>0.5</Raccoon>
      <Boomrat>0.4</Boomrat>
      <Boomalope>0.1</Boomalope>
      <Warg>0.05</Warg>
    </pollutionWildAnimals>
    <allowedPackAnimals>
      <li>Muffalo</li>
      <li>Alpaca</li>
    </allowedPackAnimals>
  </BiomeDef>

  <BiomeDef>
    <defName>TemperateSwamp</defName>
    <label>temperate swamp</label>
    <description>Wetlands choked with vegetation and disease. Dense overgrowth makes it hard to move around, and clearing areas for building takes a long time. Much of the terrain is too marshy to support heavy structures.</description>
    <workerClass>BiomeWorker_TemperateSwamp</workerClass>
    <animalDensity>4.3</animalDensity>
    <plantDensity>0.80</plantDensity>
    <settlementSelectionWeight>0.6</settlementSelectionWeight>
    <campSelectionWeight>0.9</campSelectionWeight>
    <movementDifficulty>4</movementDifficulty>
    <texture>World/Biomes/TemperateSwamp</texture>
    <forageability>0.75</forageability>
    <foragedFood>RawBerries</foragedFood>
    <wildPlantRegrowDays>15</wildPlantRegrowDays>
    <soundsAmbient>
      <li>Ambient_NightInsects_Standard</li>
    </soundsAmbient>
    <diseaseMtbDays>40</diseaseMtbDays>
    <diseases>
      <li>
        <diseaseInc>Disease_Flu</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_Plague</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_Malaria</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_GutWorms</diseaseInc>
        <commonality>50</commonality>
      </li>
      <li>
        <diseaseInc>Disease_FibrousMechanites</diseaseInc>
        <commonality>30</commonality>
      </li>
      <li>
        <diseaseInc>Disease_SensoryMechanites</diseaseInc>
        <commonality>30</commonality>
      </li>

      <li>
        <diseaseInc>Disease_MuscleParasites</diseaseInc>
        <commonality>50</commonality>
      </li>

      <li>
        <diseaseInc>Disease_AnimalFlu</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_AnimalPlague</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_OrganDecay</diseaseInc>
        <commonality>10</commonality>
      </li>
    </diseases>
    <terrainsByFertility>
      <li>
        <terrain>Soil</terrain>
        <min>-999</min>
        <max>0.87</max>
      </li>
      <li>
        <terrain>SoilRich</terrain>
        <min>0.87</min>
        <max>999</max>
      </li>
    </terrainsByFertility>
    <terrainPatchMakers>
      <li>
        <perlinFrequency>0.04</perlinFrequency>
        <thresholds>
          <li>
            <terrain>MarshyTerrain</terrain>
            <min>0.32</min>
            <max>999</max>
          </li>
        </thresholds>
      </li>
      <li>
        <perlinFrequency>0.027</perlinFrequency>
        <thresholds>
          <li>
            <terrain>SoilRich</terrain>
            <min>-0.12</min>
            <max>0.32</max>
          </li>
          <li>
            <terrain>Mud</terrain>
            <min>0.32</min>
            <max>0.6</max>
          </li>
          <li>
            <terrain>WaterShallow</terrain>
            <min>0.6</min>
            <max>999</max>
          </li>
        </thresholds>
      </li>
    </terrainPatchMakers>
    <baseWeatherCommonalities>
      <Clear>18</Clear>
      <Fog>1</Fog>
      <Rain>2</Rain>
      <DryThunderstorm>1</DryThunderstorm>
      <RainyThunderstorm>1</RainyThunderstorm>
      <FoggyRain>1</FoggyRain>
      <SnowGentle>4</SnowGentle>
      <SnowHard>4</SnowHard>
      <GrayPall MayRequire="Ludeon.RimWorld.Anomaly">1</GrayPall>
    </baseWeatherCommonalities>
    <wildPlants>
      <Plant_TallGrass>3.2</Plant_TallGrass>
      <Plant_Chokevine>0.8</Plant_Chokevine>
      <Plant_Bush>0.2</Plant_Bush>
      <Plant_TreeWillow>1.0</Plant_TreeWillow>
      <Plant_TreeCypress>1.0</Plant_TreeCypress>
      <Plant_TreeMaple>1.0</Plant_TreeMaple>
      <Plant_Berry>0.05</Plant_Berry>
      <Plant_HealrootWild>0.05</Plant_HealrootWild>
      <Plant_Witchwood MayRequire="Ludeon.RimWorld.Biotech">1.0</Plant_Witchwood>
    </wildPlants>
    <wildAnimals>
      <GuineaPig>0.15</GuineaPig>
      <Yak>0.04</Yak>
      <Horse>0.20</Horse>
      <Hare>1.0</Hare>
      <Squirrel>1.0</Squirrel>
      <Rat>1.0</Rat>
      <Tortoise>0.6</Tortoise>
      <Deer>0.5</Deer>
      <WildBoar>0.5</WildBoar>
      <Turkey>0.5</Turkey>
      <Raccoon>0.5</Raccoon>
      <Ibex>0.5</Ibex>
      <Muffalo>0.5</Muffalo>
      <Alpaca>0.5</Alpaca>
      <Boomalope>0.4</Boomalope>
      <Boomrat>0.4</Boomrat>
      <Megasloth>0.2</Megasloth>
      <Rhinoceros>0.1</Rhinoceros>
      <Bear_Grizzly>0.07</Bear_Grizzly>
      <Wolf_Timber>0.07</Wolf_Timber>
      <Fox_Red>0.07</Fox_Red>
      <Cougar>0.07</Cougar>
      <Lynx>0.07</Lynx>
      <Warg>0.07</Warg>
    </wildAnimals>
    <pollutionWildAnimals MayRequire="Ludeon.RimWorld.Biotech">
      <Toxalope MayRequire="Ludeon.RimWorld.Biotech">0.4</Toxalope>
      <WasteRat MayRequire="Ludeon.RimWorld.Biotech">0.5</WasteRat>
      <Rat>1</Rat>
      <Raccoon>0.5</Raccoon>
      <Boomrat>0.4</Boomrat>
      <Boomalope>0.1</Boomalope>
      <Warg>0.05</Warg>
    </pollutionWildAnimals>
    <allowedPackAnimals>
      <li>Muffalo</li>
      <li>Alpaca</li>
    </allowedPackAnimals>
  </BiomeDef>

</Defs>
