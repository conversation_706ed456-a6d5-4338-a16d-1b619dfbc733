<?xml version="1.0" encoding="utf-8" ?>
<Defs>
  
  <QuestScriptDef Name="MysteriousCargoBase" Abstract="True">
    <expireDaysRange>2</expireDaysRange>
    <rootSelectionWeight>0</rootSelectionWeight> <!-- Fired from incident -->
    <isRootSpecial>true</isRootSpecial>
    <minRefireDays>240</minRefireDays> <!-- 4 years -->
    <questNameRules>
      <rulesStrings>
        <li>questName->Mysterious cargo</li>
      </rulesStrings>
    </questNameRules>
    <questDescriptionRules>
      <rulesStrings>
        <!-- Asker is null -->
        <li>questDescription(askerIsNull==true)->An anonymous AI has reached out with an odd request. It would like you to accept some unspecified cargo. It avoids all your questions about the cargo.\n\nIf you accept, it will deliver the mystery cargo by transport pod.</li>

        <!-- Leader asker -->
        <li>questDescription(asker_factionLeader==true)->[asker_faction_leaderTitle] [asker_nameFull] of [asker_faction_name] has an odd request. [asker_pronoun] would like you to accept some unspecified cargo. When pressed for more details, [asker_pronoun] avoids the question. There is desperation in [asker_possessive] voice.\n\nIf you accept, [asker_nameFull] will deliver the mystery cargo by transport pod.</li>

        <!-- Royal asker -->
        <li>questDescription(asker_royalInCurrentFaction==true)->[asker_nameFull], a [asker_royalTitleInCurrentFaction] of [asker_faction_name] has an odd request. [asker_pronoun] demands that you accept some unspecified cargo. When pressed for more details, [asker_pronoun] avoids the question. There is desperation in [asker_possessive] voice.\n\nIf you accept, [asker_nameFull] will deliver the mystery cargo by transport pod.</li>
      </rulesStrings>
    </questDescriptionRules>
  </QuestScriptDef>
  
  <QuestScriptDef ParentName="MysteriousCargoBase">
    <defName>MysteriousCargoUnnaturalCorpse</defName>
    <root Class="QuestNode_Root_MysteriousCargoUnnaturalCorpse" />
    <questContentRules>
      <rulesStrings>
        <!-- Letter -->
        <li>deliveredLetterLabel->Mysterious cargo</li>
        
        <li>deliveredLetterText(pawnOnMap==true)->The mysterious cargo has been delivered. It appears to be a human corpse.\n\nOn further inspection, the corpse looks identical to [pawn_nameDef]. Its skin feels waxy and warm to the touch. [pawn_nameDef] is deeply unsettled by its presence.\n\nStudy the corpse to learn more about it.</li>
        <li>deliveredLetterText(pawnOnMap==false)->The mysterious cargo has been delivered. It appears to be a human corpse.\n\nOn further inspection, the corpse looks identical to [pawn_nameDef]. Its skin feels waxy and warm to the touch. [pawn_nameDef] will be deeply unsettled by its presence.\n\nStudy the corpse to learn more about it.</li>
      </rulesStrings>
    </questContentRules>
  </QuestScriptDef>
  
  <QuestScriptDef ParentName="MysteriousCargoBase">
    <defName>MysteriousCargoCube</defName>
    <root Class="QuestNode_Root_MysteriousCargoUnnaturalCube" />
    <questContentRules>
      <rulesStrings>
        <!-- Letter -->
        <li>deliveredLetterLabel->Mysterious cargo</li>
        
        <li>deliveredLetterText(pawnOnMap==true)->The mysterious cargo has been delivered. It is a simple cube that shines like gold but is impossible to scratch. It is light and warm to the touch. The way light plays across its surface is captivating.\n\n[pawn_nameDef] feels inexplicably drawn to the cube.</li>
        <li>deliveredLetterText(pawnOnMap==false)->The mysterious cargo has been delivered. It is a simple cube that shines like gold but is impossible to scratch. It is light and warm to the touch. The way light plays across its surface is captivating.</li>
      </rulesStrings>
    </questContentRules>
  </QuestScriptDef>
  
  <QuestScriptDef ParentName="MysteriousCargoBase">
    <defName>MysteriousCargoRevenantSpine</defName>
    <root Class="QuestNode_Root_MysteriousCargoRevenantSpine" />
    <questContentRules>
      <rulesStrings>
        <!-- Letter -->
        <li>deliveredLetterLabel->Mysterious cargo</li>
        
        <li>deliveredLetterText->The mysterious cargo has been delivered. It resembles a thick, jagged human spine made out of a rough-cast black metal. It looks like it's covered in a dark fluid.\n\nThe spine vibrates in response to touch and twists angrily. You can study it on a holding platform to learn more.</li>
      </rulesStrings>
    </questContentRules>
  </QuestScriptDef>
  
</Defs>