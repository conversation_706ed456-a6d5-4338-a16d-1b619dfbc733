<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <PrisonerInteractionModeDef>
    <defName>Study</defName>
    <label>study</label>
    <listOrder>30</listOrder> 
    <description>Researchers interact with them to gain knowledge.</description>
    <hideIfNotStudiableAsPrisoner>true</hideIfNotStudiableAsPrisoner>
  </PrisonerInteractionModeDef>
  
  <PrisonerInteractionModeDef>
    <defName>Interrogate</defName>
    <label>interrogate</label>
    <listOrder>30</listOrder>
    <description>Wardens will interrogate the prisoner and watch their reactions to try to determine if they are who they say they are. The chance of detecting an imposter increases with the warden's social interaction ability. There is always a chance the imposter goes unnoticed.</description>
    <hideIfGrayFleshNotAppeared>true</hideIfGrayFleshNotAppeared>
  </PrisonerInteractionModeDef>
  
</Defs>
