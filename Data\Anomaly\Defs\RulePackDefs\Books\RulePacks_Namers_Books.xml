﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <!-- ty_todo - all this needs to be reviewed -->
  
  <RulePackDef>
    <defName>Namer_Tome</defName>
    <include>
      <li>ArtNameUtility</li>
    </include>
    <rulePack>
      <rulesStrings>
        <!-- Roots -->
        <li>title->[maybeThe] [bookEvil] of [nounEvilUncountable]</li>
        <li>title->[maybeThe] [bookEvil] of [nounEvilCountable]s</li>
        <li>title->[maybeThe] [bookEvil] of the [nounEvilCountable]</li>
        <li>title->[maybeThe] [nounEvilCountable]s [inThe] [placeEvil]</li>
        <li>title->[maybeThe] [bookEvil] of [ANYPAWN_nameIndef]</li>
        <li>title->[maybeThe] [nounEvilAny] [inThe] [placeEvil]</li>
        <li>title->[maybeThe] [nounEvilAny] of [ANYPAWN_nameIndef]</li>
        <li>title->[maybeThe] [adjectiveEvil] [nounEvilAny]</li>
        <li>title->[ANYPAWN_nameIndef]'s [bookEvil]</li>
        <li>title->[maybeThe] [bookCompound]</li>
        <li>title->[ANYPAWN_nameIndef]'s [bookCompound]</li>
        <li>title(p=0.5)->[firstHalfEvil][connector][nounEvilCountable]</li>
        <li>title(p=0.5)->[firstHalfEvil][connector][nounEvilUncountable]</li>
        <li>title(p=0.5)->[nounEvilCountable] [suffixEvil]</li>
        <li>title(p=0.5)->[nounEvilUncountable] [suffixEvil]</li>

        <!-- Content -->

        <li>bookCompound(p=2)->[firstHalfEvil][suffixEvil]</li>
        <li>bookCompound->[firstHalfEvil][connector][suffixEvil]</li>

        <li>connector->'</li>
        <li>connector->-</li>

        <li>maybeThe-></li>
        <li>maybeThe->The</li>

        <li>bookEvil(p=2)->Book</li>
        <li>bookEvil(p=2)->[firstHalfEvil][suffixEvil]</li>
        <li>bookEvil->Tome</li>
        <li>bookEvil->Codex</li>
        <li>bookEvil->Writings</li>
        <li>bookEvil->Record</li>
        <li>bookEvil->Secrets</li>
        <li>bookEvil->Chronicles</li>
        <li>bookEvil->Cipher</li>
        <li>bookEvil->Whispers</li>
        <li>bookEvil->Testimony</li>
        <li>bookEvil->Lamentations</li>
        <li>bookEvil->Grimoire</li>
        <li>bookEvil->Almanac</li>
        <li>bookEvil->Manuscript</li>
        <li>bookEvil->Annals</li>
        <li>bookEvil->Incantations</li>
        <li>bookEvil->Revelations</li>
        <li>bookEvil->Manifesto</li>
        <li>bookEvil->Testament</li>
        <li>bookEvil->Fragments</li>
        <li>bookEvil->Shards</li>

        <li>inThe->in the</li>
        <li>inThe->within the</li>
        <li>inThe->beneath the</li>
        <li>inThe->under the</li>
        <li>inThe->by the</li>
        <li>inThe->on the</li>
        <li>inThe->across the</li>

        <li>adjectiveEvil(p=3)->Void</li>
        <li>adjectiveEvil->Golden</li>
        <li>adjectiveEvil->Unnatural</li>
        <li>adjectiveEvil->Dark</li>
        <li>adjectiveEvil->Black</li>
        <li>adjectiveEvil->Deep</li>
        <li>adjectiveEvil->Ancient</li>
        <li>adjectiveEvil->Corrupt</li>
        <li>adjectiveEvil->Blood</li>
        <li>adjectiveEvil->Chaos</li>
        <li>adjectiveEvil->Abyssal</li>
        <li>adjectiveEvil->Uncanny</li>
        <li>adjectiveEvil->Phantom</li>
        <li>adjectiveEvil->Cryptic</li>
        <li>adjectiveEvil->Whispering</li>
        <li>adjectiveEvil->Forgotten</li>
        <li>adjectiveEvil->Veiled</li>
        <li>adjectiveEvil->Forbidden</li>
        <li>adjectiveEvil->Subsurface</li>
        <li>adjectiveEvil->Snake</li>

        <li>nounEvilAny->[nounEvilCountable]</li>
        <li>nounEvilAny->[nounEvilUncountable]</li>

        <li>nounEvilUncountable->Fleshmass</li>
        <li>nounEvilUncountable->Bioferrite</li>
        <li>nounEvilUncountable->Deadlife</li>
        <li>nounEvilUncountable->Archotech</li>
        <li>nounEvilUncountable->Necrotech</li>
        <li>nounEvilUncountable->Horax</li>
        <li>nounEvilUncountable->Blood</li>
        <li>nounEvilUncountable->Undeath</li>
        <li>nounEvilUncountable->Power</li>
        <li>nounEvilUncountable->Whispers</li>
        <li>nounEvilUncountable->Knowledge</li>
        <li>nounEvilUncountable->Horror</li>
        <li>nounEvilUncountable->Fungi</li>
        <li>nounEvilUncountable->Putrescence</li>
        <li>nounEvilUncountable->Rituals</li>
        <li>nounEvilUncountable->Murder</li>
        <li>nounEvilUncountable->Darkness</li>
        <li>nounEvilUncountable->Madness</li>
        <li>nounEvilUncountable->Chaos</li>
        <li>nounEvilUncountable->Despair</li>
        <li>nounEvilUncountable->Suffering</li>
        <li>nounEvilUncountable->Nightmares</li>
        <li>nounEvilUncountable->Corruption</li>
        <li>nounEvilUncountable->Doom</li>
        <li>nounEvilUncountable->Terror</li>
        <li>nounEvilUncountable->Malevolence</li>
        <li>nounEvilUncountable->Shadows</li>
        <li>nounEvilUncountable->Void</li>
        <li>nounEvilUncountable->Gold</li>

        <li>nounEvilCountable->Ghoul</li>
        <li>nounEvilCountable->Gorehulk</li>
        <li>nounEvilCountable->Noctol</li>
        <li>nounEvilCountable->Shambler</li>
        <li>nounEvilCountable->Fleshbeast</li>
        <li>nounEvilCountable->Nociosphere</li>
        <li>nounEvilCountable->Corpse</li>
        <li>nounEvilCountable->Cube</li>
        <li>nounEvilCountable->Revenant</li>
        <li>nounEvilCountable->Metalhorror</li>
        <li>nounEvilCountable->Abductor</li>
        <li>nounEvilCountable->Duplicator</li>
        <li>nounEvilCountable->Mutator</li>
        <li>nounEvilCountable->Harbinger</li>
        <li>nounEvilCountable->Sightstealer</li>
        <li>nounEvilCountable->Chimera</li>
        <li>nounEvilCountable->Devourer</li>
        <li>nounEvilCountable->Shard</li>
        <li>nounEvilCountable->Sculpture</li>
        <li>nounEvilCountable->Serum</li>
        <li>nounEvilCountable->Cult</li>
        <li>nounEvilCountable->Cultist</li>
        <li>nounEvilCountable->Goat</li>
        <li>nounEvilCountable->Shadow</li>
        <li>nounEvilCountable->Lurker</li>
        <li>nounEvilCountable->Rites</li>
        <li>nounEvilCountable->Hex</li>
        <li>nounEvilCountable->Snake</li>
        <li>nounEvilCountable->Feaster</li>

        <li>placeEvil->Ocean</li>
        <li>placeEvil->Void</li>
        <li>placeEvil->Abyss</li>
        <li>placeEvil->Deep</li>
        <li>placeEvil->Shadows</li>
        <li>placeEvil->Tombs</li>
        <li>placeEvil->Darkness</li>
        <li>placeEvil->Mountain</li>
        <li>placeEvil->Chamber</li>
        <li>placeEvil->Catacombs</li>
        <li>placeEvil->Sanctum</li>
        <li>placeEvil->Grove</li>
        <li>placeEvil->Earth</li>
        <li>placeEvil->Stars</li>
        <li>placeEvil->Ocean</li>
        <li>placeEvil->Depths</li>
        <li>placeEvil->Underverse</li>
        <li>placeEvil->Caverns</li>

        <li>firstHalfEvil->vibro</li>
        <li>firstHalfEvil->kelno</li>
        <li>firstHalfEvil->nekro</li>
        <li>firstHalfEvil->chrono</li>
        <li>firstHalfEvil->drago</li>
        <li>firstHalfEvil->krimi</li>
        <li>firstHalfEvil->vadi</li>
        <li>firstHalfEvil->horror</li>
        <li>firstHalfEvil->nocio</li>
        <li>firstHalfEvil->xeno</li>
        <li>firstHalfEvil->psycho</li>
        <li>firstHalfEvil->voro</li>
        <li>firstHalfEvil->tyrano</li>
        <li>firstHalfEvil->inverno</li>
        <li>firstHalfEvil->phobo</li>
        <li>firstHalfEvil->leva</li>

        <li>suffixEvil->nomicon</li>
        <li>suffixEvil->doron</li>
        <li>suffixEvil->nax</li>
        <li>suffixEvil->novicon</li>
        <li>suffixEvil->vokon</li>
        <li>suffixEvil->mancy</li>
        <li>suffixEvil->gorgon</li>
        <li>suffixEvil->morth</li>
        <li>suffixEvil->cypher</li>
        <li>suffixEvil->vex</li>
        <li>suffixEvil->vortex</li>
        <li>suffixEvil->nox</li>
        <li>suffixEvil->cybex</li>
        <li>suffixEvil->voron</li>
        <li>suffixEvil->karnix</li>
        <li>suffixEvil->nemex</li>
        <li>suffixEvil->vexor</li>
        <li>suffixEvil->tronix</li>
        <li>suffixEvil->cryptex</li>
        <li>suffixEvil->chaonix</li>
        <li>suffixEvil->nix</li>
        <li>suffixEvil->vix</li>

      </rulesStrings>
    </rulePack>
  </RulePackDef>

</Defs>