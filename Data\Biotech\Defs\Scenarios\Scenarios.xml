<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ScenarioDef>
    <defName>Mechanitor</defName>
    <label>The Mechanitor</label>
    <description>You knew you could only achieve greatness with help. People were too unreliable, so you chose to take on mechanoids as your servants, workers, and warriors. As you gained strength, others became fearful. It became clear you needed to get away from the influence of humanity. Now you've migrated to this sparsely-populated rimworld with some of your metallic helpers. Finally, you have the space to grasp your true potential!\n\nNote: Since your starting character will lack some skills, this is a difficult scenario.</description>
    <scenario>
      <summary>One mechanitor and a few servant mechanoids.</summary>
      <playerFaction>
        <def>PlayerFaction</def>
        <factionDef>PlayerColony</factionDef>
      </playerFaction>
      <parts>

        <!-- Game start dialog -->
        <li Class="ScenPart_GameStartDialog">
          <def>GameStartDialog</def>
          <text>You knew you could only achieve greatness with help. People were too unreliable, so you chose to take on mechanoids as your servants, workers, and warriors.\n\nAs you gained strength, others became fearful. It became clear you needed to get away from the influence of humanity.\n\nNow you've migrated to this sparsely-populated rimworld with some of your metallic helpers. Finally, you have the space to grasp your true potential!</text>
          <closeSound>GameStartSting</closeSound>
        </li>

        <!-- Config pages -->
        <li Class="ScenPart_ConfigPage_ConfigureStartingPawns_KindDefs">
          <def>ConfigurePawnsKindDefs</def>
          <pawnChoiceCount>8</pawnChoiceCount>
          <leftBehindPawnKind>Mechanitor</leftBehindPawnKind>
          <kindCounts>
            <li>
              <kindDef>Mechanitor</kindDef>
              <count>1</count>
              <requiredAtStart>true</requiredAtStart>
            </li>
          </kindCounts>
        </li>

        <!-- Player starting stuff spawn method-->
        <li Class="ScenPart_PlayerPawnsArriveMethod">
          <def>PlayerPawnsArriveMethod</def>
          <visible>false</visible>
          <method>DropPods</method>
        </li>

        <!-- Starting research -->
        <li Class="ScenPart_StartingResearch">
          <def>StartingResearch</def>
          <project>MicroelectronicsBasics</project>
        </li>
        <li Class="ScenPart_StartingResearch">
          <def>StartingResearch</def>
          <project>BasicMechtech</project>
        </li>
        <li Class="ScenPart_StartingResearch">
          <def>StartingResearch</def>
          <project>Batteries</project>
        </li>

        <!-- Starting mechs -->
        <li Class="ScenPart_StartingMech">
          <def>StartingMech</def>
          <mechKind>Mech_Lifter</mechKind>
          <overseenByPlayerPawnChance>1</overseenByPlayerPawnChance>
        </li>
        <li Class="ScenPart_StartingMech">
          <def>StartingMech</def>
          <mechKind>Mech_Constructoid</mechKind>
          <overseenByPlayerPawnChance>1</overseenByPlayerPawnChance>
        </li>

        <!-- Player starting things -->
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>Silver</thingDef>
          <count>1000</count>
        </li>
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>MealSurvivalPack</thingDef>
          <count>40</count>
        </li>
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>MedicineIndustrial</thingDef>
          <count>10</count>
        </li>
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>ComponentIndustrial</thingDef>
          <count>55</count>
        </li>
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>Steel</thingDef>
          <count>900</count>
        </li>
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>Plasteel</thingDef>
          <count>100</count>
        </li>
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>SubcoreBasic</thingDef>
          <count>5</count>
        </li>

        <li Class="ScenPart_DisableMapGen">
          <def>DisableExostriderRemains</def>
        </li>

      </parts>
    </scenario>
  </ScenarioDef>

  <ScenarioDef>
    <defName>Sanguophage</defName>
    <label>The Sanguophage</label>
    <description>When a dark stranger offered you a cure for your cancer, you were ready to agree to anything. He made you into a sanguophage - an immortal blood-drinker hated by a thousand religions. You hid your condition for years, but eventually a hunter tracked you down. Stowing away on the first starship leaving the system, you barely escaped alive.\n\nNote: Sanguophages have unique strengths and weaknesses, which makes this a difficult scenario.</description>
    <scenario>
      <summary>One crashlanded sanguophage needing blood to survive, and one human colonist.</summary>
      <playerFaction>
        <def>PlayerFaction</def>
        <factionDef>PlayerColony</factionDef>
      </playerFaction>
      <parts>

        <!-- Config pages -->
        <li Class="ScenPart_ConfigPage_ConfigureStartingPawns_Xenotypes">
          <def>ConfigurePawnsXenotypes</def>
          <pawnChoiceCount>8</pawnChoiceCount>
          <customSummary>Start with one adult or child sanguophage and one other colonist of any age.</customSummary>
          <overrideKinds>
            <li>
              <xenotype>Sanguophage</xenotype>
              <pawnKind>Sanguophage_Player</pawnKind>
            </li>
          </overrideKinds>
          <xenotypeCounts>
            <li>
              <xenotype>Sanguophage</xenotype>
              <count>1</count>
              <description>non-baby sanguophage</description>
              <requiredAtStart>true</requiredAtStart>
              <allowedDevelopmentalStages>Adult, Child</allowedDevelopmentalStages>
            </li>
            <li>
              <xenotype>Baseliner</xenotype>
              <count>1</count>
            </li>
          </xenotypeCounts>
        </li>

        <!-- Player starting stuff spawn method-->
        <li Class="ScenPart_PlayerPawnsArriveMethod">
          <def>PlayerPawnsArriveMethod</def>
          <visible>false</visible>
          <method>DropPods</method>
        </li>

        <!-- Game start dialog -->
        <li Class="ScenPart_GameStartDialog">
          <def>GameStartDialog</def>
          <text>When a dark stranger offered you a cure for your cancer, you were ready to agree to anything. He made you into a sanguophage - an immortal blood-drinker hated by a thousand religions. You hid your condition for years, but a hunter eventually tracked you down. Stowing away on the first starship leaving the system, you barely escaped alive.\n\nThen came the crash. Only a few made it to the escape pods. You land on an unknown rimworld and find that you are not alone. What will you do for the blood you need to survive?</text>
          <closeSound>GameStartSting</closeSound>
        </li>

        <!-- Player starting things -->
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>Silver</thingDef>
          <count>2000</count>
        </li>
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>MealSurvivalPack</thingDef>
          <count>30</count>
        </li>
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>HemogenPack</thingDef>
          <count>30</count>
        </li>
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>MedicineIndustrial</thingDef>
          <count>20</count>
        </li>
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>ComponentIndustrial</thingDef>
          <count>10</count>
        </li>
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>WoodLog</thingDef>
          <count>200</count>
        </li>
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>Steel</thingDef>
          <count>200</count>
        </li>
        <li Class="ScenPart_StartingThing_Defined">
          <def>StartingThing_Defined</def>
          <thingDef>MeleeWeapon_LongSword</thingDef>
          <quality>Masterwork</quality>
          <stuff>Steel</stuff>
        </li>

        <!-- Starting research -->
        <li Class="ScenPart_StartingResearch">
          <def>StartingResearch</def>
          <project>Deathrest</project>
        </li>

      </parts>
    </scenario>
  </ScenarioDef>

</Defs>