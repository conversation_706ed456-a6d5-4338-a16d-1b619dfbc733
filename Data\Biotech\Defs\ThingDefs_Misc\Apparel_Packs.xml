<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingDef Abstract="True" Name="MechanitorPackBase" ParentName="ApparelNoQualityBase">
    <colorGenerator Class="ColorGenerator_Options">
      <options>
        <li>
          <weight>10</weight>
          <only>(0.9,0.9,0.9)</only>
        </li>
        <li>
          <weight>10</weight>
          <only>(0.33,0.33,0.33)</only>
        </li>
        <li>
          <weight>10</weight>
          <only>(0.65,0.65,0.65)</only>
        </li>
        <li>
          <weight>6</weight>
          <min>(0.3,0.3,0.3)</min>
          <max>(0.5,0.5,0.5)</max>
        </li>
      </options>
    </colorGenerator>
    <recipeMaker>
      <displayPriority>720</displayPriority>
    </recipeMaker>
    <tradeability>Sellable</tradeability>
  </ThingDef>

  <ThingDef ParentName="MechanitorPackBase">
    <defName>Apparel_PackControl</defName>
    <label>control pack</label>
    <description>A backpack containing mech control assist computers. It allows a mechanitor to control one additional control group.</description>
    <techLevel>Spacer</techLevel>
    <recipeMaker>
      <unfinishedThingDef>UnfinishedPack</unfinishedThingDef>
      <researchPrerequisite>BasicMechtech</researchPrerequisite>
      <recipeUsers>
        <li>TableMachining</li>
      </recipeUsers>
      <workSpeedStat>GeneralLaborSpeed</workSpeedStat>
      <workSkill>Crafting</workSkill>
      <effectWorking>Smith</effectWorking>
      <soundWorking>Recipe_Machining</soundWorking>
      <skillRequirements>
        <Crafting>4</Crafting>
      </skillRequirements>
    </recipeMaker>
    <costList>
      <Steel>100</Steel>
      <ComponentIndustrial>6</ComponentIndustrial>
    </costList>
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/ControlPack/ControlPack</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <tickerType>Normal</tickerType>
    <statBases>
      <WorkToMake>3200</WorkToMake>
      <Mass>3</Mass>
      <Flammability>0.4</Flammability>
      <EquipDelay>2</EquipDelay>
    </statBases>
    <thingCategories>
      <li>ApparelUtility</li>
    </thingCategories>
    <equippedStatOffsets>
      <MechControlGroups>1</MechControlGroups>
    </equippedStatOffsets>
    <apparel>
      <countsAsClothingForNudity>false</countsAsClothingForNudity>
      <scoreOffset>4</scoreOffset>
      <mechanitorApparel>true</mechanitorApparel>
      <careIfWornByCorpse>false</careIfWornByCorpse>
      <careIfDamaged>false</careIfDamaged>
      <wearPerDay>0</wearPerDay>
      <bodyPartGroups>
        <li>Waist</li>
      </bodyPartGroups>
      <layers>
        <li>Belt</li>
      </layers>
      <wornGraphicPath>Things/Pawn/Humanlike/Apparel/ControlPack/ControlPack</wornGraphicPath>
      <wornGraphicData>
        <renderUtilityAsPack>true</renderUtilityAsPack>
        <north>
          <offset>(0,-0.15)</offset>
        </north>
        <south>
          <offset>(0,-0.15)</offset>
        </south>
        <east>
          <offset>(-0.35,-0.1)</offset>
          <thin><offset>(0.05,0)</offset></thin>
          <hulk><offset>(-0.15,0)</offset></hulk>
          <fat> <offset>(-0.20,0)</offset></fat>
        </east>
        <west>
          <offset>(0.35,-0.1)</offset>
          <thin><offset>(-0.05,0)</offset></thin>
          <hulk><offset>(0.15,0)</offset></hulk>
          <fat> <offset>(0.20,0)</offset></fat>
        </west>
        <male>  <scale>(0.75,0.75)</scale></male>
        <female><scale>(0.75,0.75)</scale></female>
        <thin>  <scale>(0.7,0.7)</scale></thin>
        <hulk>  <scale>(0.9,0.9)</scale></hulk>
        <fat>   <scale>(0.9,0.9)</scale></fat>
      </wornGraphicData>
    </apparel>
    <tradeTags>
      <li>ExoticMisc</li>
      <li>Clothing</li>
    </tradeTags>
  </ThingDef>

  <ThingDef ParentName="MechanitorPackBase">
    <defName>Apparel_PackBandwidth</defName>
    <label>bandwidth pack</label>
    <description>A backpack containing bandwidth-expansion computer and antenna. It can increase a mechanitor's maximum total bandwidth.</description>
    <techLevel>Spacer</techLevel>
    <recipeMaker>
      <unfinishedThingDef>UnfinishedPack</unfinishedThingDef>
      <researchPrerequisite>StandardMechtech</researchPrerequisite>
      <recipeUsers>
        <li>TableMachining</li>
      </recipeUsers>
      <workSpeedStat>GeneralLaborSpeed</workSpeedStat>
      <workSkill>Crafting</workSkill>
      <effectWorking>Smith</effectWorking>
      <soundWorking>Recipe_Machining</soundWorking>
      <skillRequirements>
        <Crafting>4</Crafting>
      </skillRequirements>
    </recipeMaker>
    <costList>
      <SignalChip>2</SignalChip>
      <Steel>50</Steel>
      <ComponentIndustrial>8</ComponentIndustrial>
    </costList>
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/BandwidthPack/BandwidthPack</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>(0.7,0.7)</drawSize>
    </graphicData>
    <tickerType>Normal</tickerType>
    <statBases>
      <WorkToMake>3200</WorkToMake>
      <Mass>3</Mass>
      <Flammability>0.4</Flammability>
      <EquipDelay>2</EquipDelay>
    </statBases>
    <thingCategories>
      <li>ApparelUtility</li>
    </thingCategories>
    <equippedStatOffsets>
      <MechBandwidth>9</MechBandwidth>
    </equippedStatOffsets>
    <apparel>
      <countsAsClothingForNudity>false</countsAsClothingForNudity>
      <scoreOffset>4</scoreOffset>
      <mechanitorApparel>true</mechanitorApparel>
      <careIfWornByCorpse>false</careIfWornByCorpse>
      <careIfDamaged>false</careIfDamaged>
      <wearPerDay>0</wearPerDay>
      <bodyPartGroups>
        <li>Waist</li>
      </bodyPartGroups>
      <layers>
        <li>Belt</li>
      </layers>
      <wornGraphicPath>Things/Pawn/Humanlike/Apparel/BandwidthPack/BandwidthPack</wornGraphicPath>
      <wornGraphicData>
        <renderUtilityAsPack>true</renderUtilityAsPack>
        <north>
          <offset>(0,-0.15)</offset>
        </north>
        <south>
          <offset>(0,-0.15)</offset>
        </south>
        <east>
          <offset>(-0.35,-0.1)</offset>
          <thin><offset>(0.05,0)</offset></thin>
          <hulk><offset>(-0.15,0)</offset></hulk>
          <fat> <offset>(-0.20,0)</offset></fat>
        </east>
        <west>
          <offset>(0.35,-0.1)</offset>
          <thin><offset>(-0.05,0)</offset></thin>
          <hulk><offset>(0.15,0)</offset></hulk>
          <fat> <offset>(0.20,0)</offset></fat>
        </west>
        <male>  <scale>(0.75,0.75)</scale></male>
        <female><scale>(0.75,0.75)</scale></female>
        <thin>  <scale>(0.7,0.7)</scale></thin>
        <hulk>  <scale>(0.9,0.9)</scale></hulk>
        <fat>   <scale>(0.9,0.9)</scale></fat>
      </wornGraphicData>
    </apparel>
    <tradeTags>
      <li>ExoticMisc</li>
    </tradeTags>
  </ThingDef>

  <ThingDef ParentName="ApparelNoQualityBase">
    <defName>Apparel_PackTox</defName>
    <label>tox pack</label>
    <description>A reusable backpack containing canisters of reagents and a mechanism that uses them to generate tox gas. When the wearer activates the pack, it will begin spreading tox gas and continue for several seconds until it runs out of reagents.\n\nOnce used, it must be reloaded with chemfuel before it can be used again.\n\nTox gas burns the lungs and eyes, causing a temporary shortness of breath and reduction in sight. Continued exposure to tox gas results in toxic buildup which can be lethal.</description>
    <graphicData>
      <texPath>Things/Pawn/Humanlike/Apparel/ToxPack/ToxPack</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>(0.7,0.7)</drawSize>
    </graphicData>
    <tickerType>Normal</tickerType>
    <techLevel>Industrial</techLevel>
    <statBases>
      <Mass>3</Mass>
      <Flammability>0.6</Flammability>
      <EquipDelay>2</EquipDelay>
      <WorkToMake>3600</WorkToMake>
    </statBases>
    <thingCategories>
      <li>ApparelUtility</li>
    </thingCategories>
    <costList>
      <Steel>10</Steel>
      <ComponentIndustrial>1</ComponentIndustrial>
      <Chemfuel>35</Chemfuel>
    </costList>
    <recipeMaker>
      <unfinishedThingDef>UnfinishedPack</unfinishedThingDef>
      <researchPrerequisite>ToxGas</researchPrerequisite>
      <recipeUsers>
        <li>TableMachining</li>
      </recipeUsers>
      <useIngredientsForColor>false</useIngredientsForColor>
      <workSpeedStat>GeneralLaborSpeed</workSpeedStat>
      <workSkill>Crafting</workSkill>
      <effectWorking>Smith</effectWorking>
      <soundWorking>Recipe_Smith</soundWorking>
      <skillRequirements>
        <Crafting>3</Crafting>
      </skillRequirements>
      <displayPriority>350</displayPriority>
    </recipeMaker>
    <apparel>
      <countsAsClothingForNudity>false</countsAsClothingForNudity>
      <scoreOffset>4</scoreOffset>
      <careIfWornByCorpse>false</careIfWornByCorpse>
      <careIfDamaged>false</careIfDamaged>
      <wearPerDay>0</wearPerDay>
      <tags>
        <li>BeltDefenseTox</li>
      </tags>
      <bodyPartGroups>
        <li>Waist</li>
      </bodyPartGroups>
      <layers>
        <li>Belt</li>
      </layers>
      <wornGraphicPath>Things/Pawn/Humanlike/Apparel/ToxPack/ToxPack</wornGraphicPath>
      <wornGraphicData>
        <renderUtilityAsPack>true</renderUtilityAsPack>
        <north>
          <offset>(0,-0.15)</offset>
        </north>
        <south>
          <offset>(0,-0.15)</offset>
        </south>
        <east>
          <offset>(-0.35,-0.1)</offset>
          <thin><offset>(0.05,0)</offset></thin>
          <hulk><offset>(-0.15,0)</offset></hulk>
          <fat> <offset>(-0.20,0)</offset></fat>
        </east>
        <west>
          <offset>(0.35,-0.1))</offset>
          <thin><offset>(-0.05,0)</offset></thin>
          <hulk><offset>(0.15,0)</offset></hulk>
          <fat> <offset>(0.20,0)</offset></fat>
        </west>

        <male>  <scale>(0.6,0.6)</scale></male>
        <female><scale>(0.6,0.6)</scale></female>
        <thin>  <scale>(0.6,0.6)</scale></thin>
        <hulk>  <scale>(0.75,0.75)</scale></hulk>
        <fat>   <scale>(0.75,0.75)</scale></fat>
      </wornGraphicData>
    </apparel>
    <comps>
      <li Class="CompProperties_ApparelReloadable">
        <maxCharges>1</maxCharges>
        <soundReload>Standard_Reload</soundReload>
        <chargeNoun>tox pack</chargeNoun>
        <displayGizmoWhileUndrafted>false</displayGizmoWhileUndrafted>
        <ammoDef>Chemfuel</ammoDef>
        <ammoCountToRefill>35</ammoCountToRefill>
        <baseReloadTicks>60</baseReloadTicks>
        <hotKey>Misc4</hotKey>
      </li>
      <li Class="CompProperties_ReleaseGas">
        <gasType>ToxGas</gasType>
        <cellsToFill>45</cellsToFill>
        <durationSeconds>12.75</durationSeconds>
        <effecterReleasing>ToxGasReleasing</effecterReleasing>
      </li>
      <li Class="CompProperties_AIUSablePack">
        <compClass>CompToxPack</compClass>
        <checkInterval>60</checkInterval>
      </li>
    </comps>
    <verbs>
      <li>
        <verbClass>Verb_DeployToxPack</verbClass>
        <label>deploy tox pack</label>
        <violent>false</violent>
        <hasStandardCommand>true</hasStandardCommand>
        <targetable>false</targetable>
        <soundCast>GasPack_Deploy</soundCast>
        <nonInterruptingSelfCast>true</nonInterruptingSelfCast>
      </li>
    </verbs>
    <tradeTags>
      <li>Clothing</li>
    </tradeTags>
  </ThingDef>

</Defs>
