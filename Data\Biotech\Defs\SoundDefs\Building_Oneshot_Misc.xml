<?xml version="1.0" encoding="utf-8"?>
<Defs>
  
  <SoundDef>
    <defName>SubcoreSoftscanner_Start</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/SubcoreSoftscanner/Softscanner_Scan_Start_01a</clipPath>
          </li>
        </grains>
        <volumeRange>37</volumeRange>
        <distRange>10~30</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>SubcoreSoftscanner_Complete</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/SubcoreSoftscanner/Softscanner_Scan_Stop_01a</clipPath>
          </li>
        </grains>
        <volumeRange>37</volumeRange>
        <distRange>10~30</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>SubcoreRipscanner_Start</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/SubcoreRipscanner/Ripscanner_Scan_Start_01a</clipPath>
          </li>
        </grains>
        <volumeRange>37</volumeRange>
        <distRange>10~30</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>SubcoreRipscanner_Complete</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/SubcoreRipscanner/Ripscanner_Scan_Stop_01a</clipPath>
          </li>
        </grains>
        <volumeRange>37</volumeRange>
        <distRange>10~30</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>GrowthVat_Open</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/GrowthVat/GrowthVat_Open</clipPath>
          </li>
        </grains>
        <volumeRange>37</volumeRange>
        <distRange>10~30</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>GrowthVat_Close</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/GrowthVat/GrowthVat_Close</clipPath>
          </li>
        </grains>
        <volumeRange>37</volumeRange>
        <distRange>10~30</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>GeneAssembler_Complete</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/GeneAssembler/GeneAssembler_Complete</clipPath>
          </li>
        </grains>
        <volumeRange>50</volumeRange>
        <distRange>10~40</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>BandNodeTuning_Complete</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/Mechanoid/BandNode/Tuned/BandNode_Tune_Complete_01a</clipPath>
          </li>
        </grains>
        <volumeRange>40</volumeRange>
        <distRange>5~20</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Toxifier_Pollute</defName>
    <context>MapOnly</context>
    <maxSimultaneous>5</maxSimultaneous>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Buildings/Mechanoid/Toxifier/Pollute</clipFolderPath>
          </li>
        </grains>
        <volumeRange>40</volumeRange>
        <distRange>20~40</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>MechGestatorCycle_Started</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/Mechanoid/MechGestator/Mech_Formation_Cycle_Started_01a</clipPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <distRange>10~30</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>MechGestatorBill_Completed</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Buildings/Mechanoid/MechGestator/Mech_Formation_Cycle_Complete_01a</clipPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <distRange>10~30</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>MechGestator_MaterialInserted</defName>
    <context>MapOnly</context>
    <maxSimultaneous>5</maxSimultaneous>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Interact/Work/MechGestatorMaterialInsertion</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <distRange>10~30</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>WastepackAtomizer_Atomized</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Buildings/WastepackAtomizer/Finished</clipFolderPath>
          </li>
        </grains>
        <volumeRange>37</volumeRange>
        <distRange>10~30</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>WastepackAtomizer_MaterialInserted</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Buildings/WastepackAtomizer/Inserted</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <distRange>10~30</distRange>
      </li>
    </subSounds>
  </SoundDef>

</Defs>