<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <ThingDef ParentName="ApparelNoQualityBase">
    <defName>Apparel_ShardPsychicShockLance</defName>
    <label>shard shock lance</label>
    <description>A limited-use psychic effector. It shocks the target's mind with chaotic images, blocking coherent perception and dropping them into a state of psychic shock. There is a risk of brain damage.\n\nThis shock lance was crafted from a broken shard of dark archotechnology. It has a shorter range than its non-shard based counterpart and it requires line of sight.</description>
    <graphicData>
      <texPath>Things/Item/Artifact/ShardPsychicShockLance/ShardPsychicShockLance</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <techLevel>Archotech</techLevel>
    <relicChance>1</relicChance>
    <genericMarketSellable>false</genericMarketSellable>
    <tradeTags>
      <li>UtilitySpecial</li>
    </tradeTags>
    <thingSetMakerTags>
      <li>RewardStandardLowFreq</li>
    </thingSetMakerTags>
    <statBases>
      <WorkToMake>18000</WorkToMake>
      <Mass>0.7</Mass>
      <EquipDelay>1</EquipDelay>
      <MaxHitPoints>60</MaxHitPoints>
    </statBases>
    <recipeMaker>
      <researchPrerequisite>InsanityWeaponry</researchPrerequisite>
      <recipeUsers>
        <li>BioferriteShaper</li>
      </recipeUsers>
      <skillRequirements>
        <Crafting>4</Crafting>
      </skillRequirements>
      <displayPriority>80</displayPriority>
      <unfinishedThingDef>UnfinishedWeapon</unfinishedThingDef>
      <workSpeedStat>GeneralLaborSpeed</workSpeedStat>
      <workSkill>Crafting</workSkill>
      <effectWorking>Smith</effectWorking>
      <soundWorking>Recipe_Machining</soundWorking>
    </recipeMaker>
    <costList>
      <Shard>1</Shard>
      <Bioferrite>60</Bioferrite>
    </costList>
    <tickerType>Normal</tickerType>
    <thingCategories>
      <li>ApparelUtility</li>
    </thingCategories>
    <apparel>
      <countsAsClothingForNudity>false</countsAsClothingForNudity>
      <scoreOffset>4</scoreOffset>
      <careIfWornByCorpse>false</careIfWornByCorpse>
      <careIfDamaged>false</careIfDamaged>
      <ignoredByNonViolent>true</ignoredByNonViolent>
      <wearPerDay>0</wearPerDay>
      <bodyPartGroups>
        <li>Waist</li>
      </bodyPartGroups>
      <layers>
        <li>Belt</li>
      </layers>
      <wornGraphicPath>Things/Item/Artifact/ShardPsychicShockLance/ShardPsychicShockLance</wornGraphicPath>
      <wornGraphicData>
        <renderUtilityAsPack>true</renderUtilityAsPack>
        <north>
          <scale>(0.7,0.7)</scale>
        </north>
        <south>
          <offset>(0,0.4)</offset>
          <scale>(0.7,0.7)</scale>
        </south>
        <east>
          <offset>(-0.35,0)</offset>
          <scale>(0.85,0.85)</scale>
          <thin>
            <offset>(0,0.1)</offset>
          </thin>
          <hulk>
            <offset>(-0.15,0)</offset>
          </hulk>
          <fat>
            <offset>(-0.15,0)</offset>
          </fat>
          <male>
            <offset>(0,0.1)</offset>
          </male>
        </east>
        <west>
          <offset>(0.35,0)</offset>
          <scale>(0.85,0.85)</scale>
          <thin>
            <offset>(0,0.1)</offset>
          </thin>
          <hulk>
            <offset>(0.15,0)</offset>
          </hulk>
          <fat>
            <offset>(0.15,0)</offset>
          </fat>
          <male>
            <offset>(0,0.1)</offset>
          </male>
        </west>
      </wornGraphicData>
    </apparel>
    <resourceReadoutPriority>Middle</resourceReadoutPriority>
    <tradeNeverStack>true</tradeNeverStack>
    <smeltable>false</smeltable>
    <burnableByRecipe>false</burnableByRecipe>
    <drawGUIOverlay>false</drawGUIOverlay>
    <verbs>
      <li>
        <verbClass>Verb_CastTargetEffectLances</verbClass>
        <label>shard shock lance</label>
        <hasStandardCommand>true</hasStandardCommand>
        <targetable>true</targetable>
        <onlyManualCast>True</onlyManualCast>
        <range>16.9</range>
        <warmupTime>2.4</warmupTime>
        <invalidTargetPawn>psychic shock immune</invalidTargetPawn>
        <targetParams>
          <canTargetBuildings>false</canTargetBuildings>
          <neverTargetIncapacitated>true</neverTargetIncapacitated>
        </targetParams>
        <soundCast>PsychicShockLanceCast</soundCast>
        <soundAiming>PsychicArtifactWarmupSustained</soundAiming>
      </li>
    </verbs>
    <comps>
      <li Class="CompProperties_ApparelReloadable">
        <maxCharges>2</maxCharges>
        <destroyOnEmpty>true</destroyOnEmpty>
        <hotKey>Misc4</hotKey>
        <chargeNoun>charge</chargeNoun>
        <displayGizmoWhileUndrafted>false</displayGizmoWhileUndrafted>
      </li>
      <li>
        <compClass>CompTargetEffect_PsychicShock</compClass>
      </li>
      <li Class="CompProperties_TargetEffect_BrainDamageChance">
        <brainDamageChance>0.3</brainDamageChance>
      </li>
      <li Class="CompProperties_TargetEffect_GoodwillImpact">
        <goodwillImpact>-200</goodwillImpact>
      </li>
      <li Class="CompProperties_TargetEffect_FleckOnTarget">
        <fleckDef>PsycastPsychicEffect</fleckDef>
      </li>
      <li Class="CompProperties_TargetEffect_FleckConnecting">
        <fleckDef>PsycastPsychicLine</fleckDef>
      </li>
      <li Class="CompProperties_Styleable" />
    </comps>
    <allowedArchonexusCount>1</allowedArchonexusCount>
  </ThingDef>

  <ThingDef ParentName="ApparelNoQualityBase">
    <defName>Apparel_ShardPsychicInsanityLance</defName>
    <label>shard insanity lance</label>
    <description>A limited-use psychic effector. It overwhelms the target's mind with terrifying perceptual distortions, driving them into a berserk state. There is a risk of brain damage.\n\nThis insanity lance was crafted from a broken shard of dark archotechnology. It has a shorter range than its non-shard based counterpart and it requires line of sight.</description>
    <graphicData>
      <texPath>Things/Item/Artifact/ShardPsychicInsanityLance/ShardPsychicInsanityLance</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <techLevel>Archotech</techLevel>
    <genericMarketSellable>false</genericMarketSellable>
    <tradeTags>
      <li>UtilitySpecial</li>
    </tradeTags>
    <thingSetMakerTags>
      <li>RewardStandardLowFreq</li>
    </thingSetMakerTags>
    <statBases>
      <WorkToMake>21000</WorkToMake>
      <Mass>0.7</Mass>
      <EquipDelay>1</EquipDelay>
      <MaxHitPoints>60</MaxHitPoints>
    </statBases>
    <recipeMaker>
      <researchPrerequisite>InsanityWeaponry</researchPrerequisite>
      <recipeUsers>
        <li>BioferriteShaper</li>
      </recipeUsers>
      <skillRequirements>
        <Crafting>5</Crafting>
      </skillRequirements>
      <displayPriority>90</displayPriority>
      <unfinishedThingDef>UnfinishedWeapon</unfinishedThingDef>
      <workSpeedStat>GeneralLaborSpeed</workSpeedStat>
      <workSkill>Crafting</workSkill>
      <effectWorking>Smith</effectWorking>
      <soundWorking>Recipe_Machining</soundWorking>
    </recipeMaker>
    <costList>
      <Shard>1</Shard>
      <Bioferrite>75</Bioferrite>
    </costList>
    <tickerType>Normal</tickerType>
    <thingCategories>
      <li>ApparelUtility</li>
    </thingCategories>
    <apparel>
      <countsAsClothingForNudity>false</countsAsClothingForNudity>
      <scoreOffset>4</scoreOffset>
      <careIfWornByCorpse>false</careIfWornByCorpse>
      <careIfDamaged>false</careIfDamaged>
      <ignoredByNonViolent>true</ignoredByNonViolent>
      <wearPerDay>0</wearPerDay>
      <bodyPartGroups>
        <li>Waist</li>
      </bodyPartGroups>
      <layers>
        <li>Belt</li>
      </layers>
      <wornGraphicPath>Things/Item/Artifact/ShardPsychicInsanityLance/ShardPsychicInsanityLance</wornGraphicPath>
      <wornGraphicData>
        <renderUtilityAsPack>true</renderUtilityAsPack>
        <north>
          <scale>(0.85,0.85)</scale>
        </north>
        <south>
          <offset>(0,0.4)</offset>
          <scale>(0.85,0.85)</scale>
        </south>
        <east>
          <offset>(-0.35,0)</offset>
          <scale>(0.85,0.85)</scale>
          <thin>
            <offset>(0,0.1)</offset>
          </thin>
          <hulk>
            <offset>(-0.15,0)</offset>
          </hulk>
          <fat>
            <offset>(-0.15,0)</offset>
          </fat>
          <male>
            <offset>(0,0.1)</offset>
          </male>
        </east>
        <west>
          <offset>(0.35,0)</offset>
          <scale>(0.85,0.85)</scale>
          <thin>
            <offset>(0,0.1)</offset>
          </thin>
          <hulk>
            <offset>(0.15,0)</offset>
          </hulk>
          <fat>
            <offset>(0.15,0)</offset>
          </fat>
          <male>
            <offset>(0,0.1)</offset>
          </male>
        </west>
      </wornGraphicData>
    </apparel>
    <resourceReadoutPriority>Middle</resourceReadoutPriority>
    <tradeNeverStack>true</tradeNeverStack>
    <smeltable>false</smeltable>
    <burnableByRecipe>false</burnableByRecipe>
    <drawGUIOverlay>false</drawGUIOverlay>
    <relicChance>1</relicChance>
    <verbs>
      <li>
        <verbClass>Verb_CastTargetEffectLances</verbClass>
        <label>shard insanity lance</label>
        <hasStandardCommand>true</hasStandardCommand>
        <targetable>true</targetable>
        <onlyManualCast>True</onlyManualCast>
        <range>16.9</range>
        <warmupTime>2.4</warmupTime>
        <invalidTargetPawn>psychic insanity immune</invalidTargetPawn>
        <targetParams>
          <canTargetBuildings>false</canTargetBuildings>
          <neverTargetIncapacitated>true</neverTargetIncapacitated>
        </targetParams>
        <soundCast>PsychicInsanityLanceCast</soundCast>
        <soundAiming>PsychicArtifactWarmupSustained</soundAiming>
      </li>
    </verbs>
    <comps>
      <li Class="CompProperties_ApparelReloadable">
        <maxCharges>2</maxCharges>
        <destroyOnEmpty>true</destroyOnEmpty>
        <hotKey>Misc4</hotKey>
        <chargeNoun>charge</chargeNoun>
        <displayGizmoWhileUndrafted>false</displayGizmoWhileUndrafted>
      </li>
      <li>
        <compClass>CompTargetEffect_Berserk</compClass>
      </li>
      <li Class="CompProperties_TargetEffect_BrainDamageChance">
        <brainDamageChance>0.3</brainDamageChance>
      </li>
      <li Class="CompProperties_TargetEffect_GoodwillImpact">
        <goodwillImpact>-200</goodwillImpact>
      </li>
      <li Class="CompProperties_TargetEffect_FleckOnTarget">
        <fleckDef>PsycastPsychicEffect</fleckDef>
      </li>
      <li Class="CompProperties_TargetEffect_FleckConnecting">
        <fleckDef>PsycastPsychicLine</fleckDef>
      </li>
      <li Class="CompProperties_Styleable"/>
    </comps>
    <allowedArchonexusCount>1</allowedArchonexusCount>
  </ThingDef>

  <ThingDef ParentName="ApparelNoQualityBase">
    <defName>Apparel_BiomutationLance</defName>
    <label>biomutation lance</label>
    <description>A limited-use psychic effector. It twists and reshapes the musculature of the target, causing them to transform into a terrifying creature.\n\nThis biomutation lance was crafted from a broken shard of dark archotechnology.</description>
    <graphicData>
      <texPath>Things/Item/Artifact/BiomutationLance/BiomutationLance</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <techLevel>Archotech</techLevel>
    <relicChance>2</relicChance>
    <genericMarketSellable>false</genericMarketSellable>
    <tradeTags>
      <li>UtilitySpecial</li>
    </tradeTags>
    <thingSetMakerTags>
      <li>RewardStandardHighFreq</li>
    </thingSetMakerTags>
    <statBases>
      <WorkToMake>21000</WorkToMake>
      <Mass>0.5</Mass>
      <EquipDelay>2</EquipDelay>
      <MarketValue>850</MarketValue>
      <MaxHitPoints>80</MaxHitPoints>
    </statBases>
    <recipeMaker>
      <researchPrerequisite>MutationWeaponry</researchPrerequisite>
      <recipeUsers>
        <li>BioferriteShaper</li>
      </recipeUsers>
      <skillRequirements>
        <Crafting>6</Crafting>
      </skillRequirements>
      <displayPriority>100</displayPriority>
      <unfinishedThingDef>UnfinishedWeapon</unfinishedThingDef>
      <workSpeedStat>GeneralLaborSpeed</workSpeedStat>
      <workSkill>Crafting</workSkill>
      <effectWorking>Smith</effectWorking>
      <soundWorking>Recipe_Machining</soundWorking>
    </recipeMaker>
    <costList>
      <Shard>1</Shard>
      <Bioferrite>50</Bioferrite>
      <Meat_Twisted>20</Meat_Twisted>
    </costList>
    <tickerType>Normal</tickerType>
    <thingCategories>
      <li>ApparelUtility</li>
    </thingCategories>
    <apparel>
      <countsAsClothingForNudity>false</countsAsClothingForNudity>
      <scoreOffset>4</scoreOffset>
      <careIfWornByCorpse>false</careIfWornByCorpse>
      <careIfDamaged>false</careIfDamaged>
      <ignoredByNonViolent>true</ignoredByNonViolent>
      <wearPerDay>0</wearPerDay>
      <bodyPartGroups>
        <li>Waist</li>
      </bodyPartGroups>
      <layers>
        <li>Belt</li>
      </layers>
      <wornGraphicPath>Things/Item/Artifact/BiomutationLance/BiomutationLance</wornGraphicPath>
      <wornGraphicData>
        <renderUtilityAsPack>true</renderUtilityAsPack>
        <north>
          <scale>(0.65,0.65)</scale>
        </north>
        <south>
          <offset>(0,0.1)</offset>
          <scale>(0.65,0.65)</scale>
        </south>
        <east>
          <offset>(-0.35,0)</offset>
          <scale>(0.65,0.65)</scale>
          <thin>
            <offset>(0.05,0.1)</offset>
          </thin>
          <hulk>
            <offset>(-0.1,0)</offset>
          </hulk>
          <fat>
            <offset>(-0.15,0.05)</offset>
          </fat>
          <male>
            <offset>(0,0.1)</offset>
          </male>
          <female>
            <offset>(0.05,0.05)</offset>
          </female>
        </east>
        <west>
          <offset>(0.35,0)</offset>
          <scale>(0.65,0.65)</scale>
          <thin>
            <offset>(-0.05,0.1)</offset>
          </thin>
          <hulk>
            <offset>(0.1,0)</offset>
          </hulk>
          <fat>
            <offset>(0.15,0.05)</offset>
          </fat>
          <male>
            <offset>(0,0.1)</offset>
          </male>
          <female>
            <offset>(-0.05,0.05)</offset>
          </female>
        </west>
      </wornGraphicData>
    </apparel>
    <resourceReadoutPriority>Middle</resourceReadoutPriority>
    <tradeNeverStack>true</tradeNeverStack>
    <smeltable>false</smeltable>
    <burnableByRecipe>false</burnableByRecipe>
    <drawGUIOverlay>false</drawGUIOverlay>
    <verbs>
      <li>
        <verbClass>Verb_CastTargetEffectBiomutationLance</verbClass>
        <label>biomutation lance</label>
        <hasStandardCommand>true</hasStandardCommand>
        <targetable>true</targetable>
        <onlyManualCast>True</onlyManualCast>
        <range>25.9</range>
        <warmupTime>2</warmupTime>
        <targetParams>
          <canTargetBuildings>false</canTargetBuildings>
          <neverTargetIncapacitated>true</neverTargetIncapacitated>
        </targetParams>
        <soundAiming>BiomutationLanceWarmup</soundAiming>
        <aimingTargetEffecter>BiomutationLanceWarmup</aimingTargetEffecter>
      </li>
    </verbs>
    <comps>
      <li Class="CompProperties_ApparelReloadable">
        <maxCharges>4</maxCharges>
        <destroyOnEmpty>true</destroyOnEmpty>
        <hotKey>Misc4</hotKey>
        <chargeNoun>charge</chargeNoun>
        <displayGizmoWhileUndrafted>false</displayGizmoWhileUndrafted>
      </li>
      <li>
        <compClass>CompTargetEffect_MutateIntoFleshbeast</compClass>
      </li>
      <li Class="CompProperties_TargetEffect_GoodwillImpact">
        <goodwillImpact>-200</goodwillImpact>
      </li>
      <li Class="CompProperties_TargetEffect_FleckConnecting">
        <fleckDef>PsycastPsychicLine</fleckDef>
      </li>
      <li Class="CompProperties_Styleable"/>
    </comps>
    <allowedArchonexusCount>1</allowedArchonexusCount>
  </ThingDef>
</Defs>