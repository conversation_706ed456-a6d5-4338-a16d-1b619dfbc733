<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <ThingDef ParentName="BuildingBase">
    <defName>BioferriteGenerator</defName>
    <label>bioferrite generator</label>
    <thingClass>Building_BioferriteGenerator</thingClass>
    <description>An electrical generator that works by pressurizing and igniting bioferrite, then harvesting energy from the resulting heat.\n\nBecause of the destruction of psychically active bioferrite, this device generates an uncomfortable psychic drone which disturbs those who pass nearby.</description>
    <tickerType>Normal</tickerType>
    <drawerType>RealtimeOnly</drawerType>
    <graphicData>
      <graphicClass>Graphic_Multi_BuildingWorking</graphicClass>
      <texPath>Things/Building/BioferriteGenerator/BioferriteGenerator</texPath>
      <shaderType>BuildingWorking</shaderType>
      <drawSize>(3,2)</drawSize>
      <shadowData>
        <volume>(2.9, 1, 1.9)</volume>
      </shadowData>
    </graphicData>
    <altitudeLayer>Building</altitudeLayer>
    <passability>PassThroughOnly</passability>
    <fillPercent>0.9</fillPercent>
    <pathCost>50</pathCost>
    <rotatable>true</rotatable>
    <constructionSkillPrerequisite>6</constructionSkillPrerequisite>
    <terrainAffordanceNeeded>Heavy</terrainAffordanceNeeded>
    <designationCategory>Power</designationCategory>
    <uiOrder>2200</uiOrder>
    <canOverlapZones>false</canOverlapZones>
    <statBases>
      <MaxHitPoints>400</MaxHitPoints>
      <WorkToBuild>12000</WorkToBuild>
      <Flammability>0.75</Flammability>
      <Beauty>-40</Beauty>
    </statBases>
    <size>(3,2)</size>
    <costList>
      <Shard>1</Shard>
      <ComponentIndustrial>4</ComponentIndustrial>
      <Bioferrite>100</Bioferrite>
      <Steel>50</Steel>
    </costList>
    <placeWorkers>
      <li>PlaceWorker_NoiseSource</li>
    </placeWorkers>
    <researchPrerequisites>
      <li>BioferriteGenerator</li>
    </researchPrerequisites>
    <comps>
      <li Class="CompProperties_Power">
        <compClass>CompPowerPlant</compClass>
        <basePowerConsumption>-4000</basePowerConsumption>
        <transmitsPower>true</transmitsPower>
        <soundAmbientProducingPower>ChemfuelFiredGenerator_Ambience</soundAmbientProducingPower>
      </li>
      <li Class="CompProperties_Flickable"/>
      <li Class="CompProperties_Refuelable">
        <fuelConsumptionRate>6</fuelConsumptionRate>
        <fuelCapacity>60.0</fuelCapacity>
        <fuelFilter>
          <thingDefs>
            <li>Bioferrite</li>
          </thingDefs>
        </fuelFilter>
        <showAllowAutoRefuelToggle>true</showAllowAutoRefuelToggle>
      </li>
      <li Class="CompProperties_Glower">
        <glowRadius>8</glowRadius>
        <glowColor>(80,141,223,0)</glowColor>
      </li>
      <li Class="CompProperties_HeatPusher">
        <compClass>CompHeatPusherPowered</compClass>
        <heatPerSecond>12</heatPerSecond>
      </li>
      <li Class="CompProperties_Breakdownable"/>
      <li Class="CompProperties_Stunnable">
        <useLargeEMPEffecter>true</useLargeEMPEffecter>
        <affectedDamageDefs>
          <li>EMP</li>
        </affectedDamageDefs>
      </li>
      <li Class="CompProperties_NoiseSource">
        <radius>8.9</radius>
      </li>
    </comps>
  </ThingDef>

  <ThingDef ParentName="BuildingBase">
    <thingClass>Building_Electroharvester</thingClass>
    <defName>Electroharvester</defName>
    <label>electroharvester</label>
    <description>An electrical generator that can be placed near a holding platform, where it will draw power from a contained entity. The generator hooks into the entity's body and harnesses energy generated by unnatural spatial distortions or psychic flows inside it.\n\nLarger entities produce more power. However, electroharvesting agitates entities, making them more likely to escape.\n\nAn electroharvester can connect to multiple platforms. However, each holding platform can only support one electroharvester.</description>
    <tickerType>Normal</tickerType>
    <size>(2,1)</size>
    <graphicData>
      <texPath>Things/Building/Electroharvester/Electroharvester</texPath>
      <graphicClass>Graphic_Multi</graphicClass>
      <drawOffset>0,1.5,0</drawOffset>
      <drawSize>(3,2)</drawSize>
      <shadowData>
        <volume>(2, 1, 1)</volume>
      </shadowData>
    </graphicData>
    <building>
      <ai_chillDestination>false</ai_chillDestination>
      <buildingTags>
        <li>Anomaly</li>
      </buildingTags>
    </building>
    <drawerType>MapMeshAndRealTime</drawerType>
    <altitudeLayer>Building</altitudeLayer>
    <passability>PassThroughOnly</passability>
    <fillPercent>0.9</fillPercent>
    <pathCost>50</pathCost>
    <constructionSkillPrerequisite>4</constructionSkillPrerequisite>
    <designationCategory>Anomaly</designationCategory>
    <uiOrder>110</uiOrder>
    <canOverlapZones>false</canOverlapZones>
    <thingCategories>
      <li>BuildingsPower</li>
    </thingCategories>
    <minifiedDef>MinifiedThing</minifiedDef>
    <statBases>
      <MaxHitPoints>250</MaxHitPoints>
      <WorkToBuild>4000</WorkToBuild>
      <Flammability>0.75</Flammability>
      <Beauty>-10</Beauty>
      <Mass>20</Mass>
    </statBases>
    <costList>
      <ComponentIndustrial>1</ComponentIndustrial>
      <Bioferrite>25</Bioferrite>
      <Steel>50</Steel>
    </costList>
    <researchPrerequisites>
      <li>Electroharvester</li>
    </researchPrerequisites>
    <placeWorkers>
      <li>PlaceWorker_ShowFacilitiesConnections</li>
    </placeWorkers>
    <comps>
      <li Class="CompProperties_Flickable" />
      <li Class="CompProperties_Power">
        <compClass>CompPowerPlantElectroharvester</compClass>
        <basePowerConsumption>-2000</basePowerConsumption> <!-- Max possible power output -->
        <transmitsPower>true</transmitsPower>
        <soundAmbientProducingPower>ChemfuelFiredGenerator_Ambience</soundAmbientProducingPower>
      </li>
      <li Class="CompProperties_Breakdownable"/>
      <li Class="CompProperties_Facility">
        <compClass>CompFacilityInactiveWhenElectricityDisabled</compClass>
        <maxDistance>5.1</maxDistance>
        <statOffsets>
          <ContainmentStrength>-25</ContainmentStrength>
        </statOffsets>
      </li>
      <li Class="CompProperties_Stunnable">
        <useLargeEMPEffecter>true</useLargeEMPEffecter>
        <affectedDamageDefs>
          <li>EMP</li>
        </affectedDamageDefs>
      </li>
      <li Class="CompProperties_AttachPoints">
        <points>
          <li>
            <offset>(-.5, 0, .4)</offset>
            <type>CableConnection0</type>
          </li>
          <li>
            <offset>(-.5, 0, -.3)</offset>
            <type>CableConnection1</type>
          </li>
          <li>
            <offset>(-.5, 0, -.2)</offset>
            <type>CableConnection2</type>
          </li>
          <li>
            <offset>(-.5, 0, -.1)</offset>
            <type>CableConnection3</type>
          </li>
        </points>
      </li>
      <li Class="CompProperties_CableConnection">
        <color>(0.5, 0.5, 0.5, 1)</color>
        <drawMote>true</drawMote>
        <moteDef>Mote_ChargingElectroCables</moteDef>
        <offsets>
          <li>
            <li>(-0.5, 0, 0.72)</li>
            <li>(-0.5, 0, 0.48)</li>
            <li>(-0.5, 0, 0.3)</li>
            <li>(-0.5, 0, 0.1)</li>
          </li>
          <li>
            <li>(-0.4, 0, 0)</li>
            <li>(-0.2, 0, 0)</li>
            <li>(0.2, 0, 0)</li>
            <li>(0.4, 0, 0)</li>
          </li>
          <li>
            <li>(0.4, 0, 0.65)</li>
            <li>(0.4, 0, 0.48)</li>
            <li>(0.4, 0, 0.3)</li>
            <li>(0.4, 0, 0.1)</li>
          </li>
          <li>
            <li>(-0.4, 0, 0.65)</li>
            <li>(-0.2, 0, 0.65)</li>
            <li>(0.2, 0, 0.65)</li>
            <li>(0.4, 0, 0.65)</li>
          </li>
        </offsets>
      </li>
    </comps>
  </ThingDef>
  
</Defs>