﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <EffecterDef Name="Deflect_Metal">
    <defName>Deflect_Metal</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>Deflect_Metal</soundDef>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>SparkFlash</fleckDef>
        <positionLerpFactor>0.6</positionLerpFactor>
        <chancePerTick>0.2</chancePerTick>
        <scale>2.5~4.5</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.2</positionRadius>
        <fleckDef>AirPuff</fleckDef>
        <burstCount>4~5</burstCount>
        <speed>0.4~0.8</speed>
        <scale>0.5~0.8</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.02</positionRadius>
        <moteDef>Mote_SparkThrownFast</moteDef>
        <burstCount>4~5</burstCount>
        <speed>3.3~5</speed>
        <scale>0.1~0.2</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.02</positionRadius>
        <fleckDef>MicroSparksFast</fleckDef>
        <burstCount>1~1</burstCount>
        <speed>0.3~0.4</speed>
        <rotationRate>5~10</rotationRate>
        <scale>0.3~0.5</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.1</positionRadius>
        <fleckDef>SparkFlash</fleckDef>
        <burstCount>1~1</burstCount>
        <scale>0.9~1.3</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
    <offsetTowardsTarget>0.25~0.25</offsetTowardsTarget>
    <positionRadius>0.1</positionRadius>
  </EffecterDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_SparkThrownFast</defName>
    <graphicData>
      <texPath>Things/Mote/SparkThrown</texPath>
    </graphicData>
    <altitudeLayer>Projectile</altitudeLayer>
    <mote>
      <fadeInTime>0.03</fadeInTime>
      <solidTime>0.04</solidTime>
      <fadeOutTime>0.13</fadeOutTime>
      <collide>true</collide>
    </mote>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_SparkThrownFastBlue</defName>
    <graphicData>
      <texPath>Things/Mote/SparkThrownBlue</texPath>
    </graphicData>
    <altitudeLayer>Projectile</altitudeLayer>
    <mote>
      <fadeInTime>0.03</fadeInTime>
      <solidTime>0.04</solidTime>
      <fadeOutTime>0.13</fadeOutTime>
      <collide>true</collide>
    </mote>
  </ThingDef>

  <EffecterDef ParentName="Deflect_Metal">
    <defName>Deflect_Metal_Bullet</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_LongSparkThrown</moteDef>
        <burstCount>1~1</burstCount>
        <scale>0.38~0.6</scale>
        <speed>7~14</speed>
        <angle>135~225</angle>
        <positionRadius>0.01</positionRadius>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef Name="Deflect_General">
    <defName>Deflect_General</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>Deflect_General</soundDef>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>YellowSparkFlash</fleckDef>
        <positionLerpFactor>0.6</positionLerpFactor>
        <chancePerTick>0.2</chancePerTick>
        <scale>2.5~4.5</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.2</positionRadius>
        <fleckDef>AirPuff</fleckDef>
        <burstCount>4~5</burstCount>
        <speed>0.4~0.8</speed>
        <scale>0.5~0.8</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.02</positionRadius>
        <moteDef>Mote_SparkThrownFast</moteDef>
        <burstCount>4~5</burstCount>
        <speed>3.3~5</speed>
        <scale>0.1~0.2</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.1</positionRadius>
        <fleckDef>YellowSparkFlash</fleckDef>
        <burstCount>1~1</burstCount>
        <scale>0.9~1.3</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
    <offsetTowardsTarget>0.25~0.25</offsetTowardsTarget>
    <positionRadius>0.1</positionRadius>
  </EffecterDef>

  <EffecterDef ParentName="Deflect_General">
    <defName>Deflect_General_Bullet</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_LongSparkThrown</moteDef>
        <burstCount>1~1</burstCount>
        <scale>0.38~0.6</scale>
        <speed>7~14</speed>
        <angle>135~225</angle>
        <positionRadius>0.01</positionRadius>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Damage_HitFlesh</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.4</positionRadius>
        <fleckDef>AirPuff</fleckDef>
        <burstCount>4~5</burstCount>
        <speed>0.4~0.8</speed>
        <scale>1.0~1.1</scale>
        <color>(120, 18, 0)</color>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.1</positionRadius>
        <fleckDef>BloodSplash</fleckDef>
        <burstCount>2~3</burstCount>
        <scale>1.1~1.4</scale>
        <color>(210, 50, 0)</color>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.0</positionRadius>
        <fleckDef>BodyImpact</fleckDef>
        <burstCount>1</burstCount>
        <scale>0.4</scale>
        <color>(255, 120, 30, 60)</color>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
    <offsetTowardsTarget>0.25~0.25</offsetTowardsTarget>
    <positionRadius>0.1</positionRadius>
  </EffecterDef>

  <EffecterDef>
    <defName>Damage_HitInsect</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.4</positionRadius>
        <fleckDef>AirPuff</fleckDef>
        <burstCount>4~5</burstCount>
        <speed>0.4~0.8</speed>
        <scale>1.0~1.3</scale>
        <color>(200, 180, 30)</color>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.1</positionRadius>
        <fleckDef>BloodSplash</fleckDef>
        <burstCount>2~3</burstCount>
        <scale>1.6~2.1</scale>
        <color>(236, 228, 20)</color>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.0</positionRadius>
        <fleckDef>BodyImpact</fleckDef>
        <burstCount>1</burstCount>
        <scale>0.55</scale>
        <color>(200, 200, 10, 60)</color>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
    <offsetTowardsTarget>0.25~0.25</offsetTowardsTarget>
    <positionRadius>0.1</positionRadius>
  </EffecterDef>

  <EffecterDef>
    <defName>Damage_HitMechanoid</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.4</positionRadius>
        <fleckDef>AirPuff</fleckDef>
        <burstCount>2~3</burstCount>
        <speed>0.4~0.8</speed>
        <scale>0.8~1.1</scale>
        <color>(0.7, 0.8, 1, 0.7)</color>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.02</positionRadius>
        <moteDef>Mote_SparkThrownFastBlue</moteDef>
        <burstCount>7~10</burstCount>
        <speed>3.3~5</speed>
        <scale>0.2~0.3</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.05</positionRadius>
        <fleckDef>PlainFlash</fleckDef>
        <burstCount>1</burstCount>
        <scale>2.5~3.25</scale>
        <spawnLocType>OnSource</spawnLocType>
        <color>(0.7, 0.8, 1.0, 0.6)</color>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.0</positionRadius>
        <fleckDef>ElectricalSpark</fleckDef>
        <burstCount>1</burstCount>
        <scale>0.83</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
    <offsetTowardsTarget>0.25~0.25</offsetTowardsTarget>
    <positionRadius>0.1</positionRadius>
  </EffecterDef>

  <EffecterDef>
    <defName>DamageDiminished_Metal</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.2</positionRadius>
        <fleckDef>AirPuff</fleckDef>
        <burstCount>3~4</burstCount>
        <speed>0.3~0.7</speed>
        <scale>0.5~0.8</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.02</positionRadius>
        <moteDef>Mote_SparkThrownFast</moteDef>
        <burstCount>4~5</burstCount>
        <speed>3.3~5</speed>
        <scale>0.1~0.2</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.02</positionRadius>
        <fleckDef>MicroSparksFast</fleckDef>
        <burstCount>1~2</burstCount>
        <speed>0.3~0.4</speed>
        <rotationRate>5~10</rotationRate>
        <scale>0.3~0.5</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_SparkThrown</moteDef>
        <burstCount>3~3</burstCount>
        <scale>0.1~0.2</scale>
        <airTime>0.08~0.1</airTime>
        <speed>2~4</speed>
        <positionRadius>0.02</positionRadius>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
    <offsetTowardsTarget>0.31~0.31</offsetTowardsTarget>
    <positionRadius>0.1</positionRadius>
  </EffecterDef>

  <EffecterDef>
    <defName>DamageDiminished_General</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.2</positionRadius>
        <fleckDef>AirPuff</fleckDef>
        <burstCount>3~4</burstCount>
        <speed>0.3~0.7</speed>
        <scale>0.5~0.8</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.02</positionRadius>
        <moteDef>Mote_SparkThrownFast</moteDef>
        <burstCount>4~5</burstCount>
        <speed>3.3~5</speed>
        <scale>0.1~0.2</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_SparkThrown</moteDef>
        <burstCount>3~3</burstCount>
        <scale>0.1~0.2</scale>
        <airTime>0.08~0.1</airTime>
        <speed>2~4</speed>
        <positionRadius>0.02</positionRadius>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
    <offsetTowardsTarget>0.31~0.31</offsetTowardsTarget>
    <positionRadius>0.1</positionRadius>
  </EffecterDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_LongSparkThrown</defName>
    <graphicData>
      <texPath>Things/Mote/LongSparkThrown</texPath>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
    <altitudeLayer>Projectile</altitudeLayer>
    <mote>
      <fadeInTime>0.02</fadeInTime>
      <solidTime>0.05</solidTime>
      <fadeOutTime>0.2</fadeOutTime>
      <collide>true</collide>
      <rotateTowardsMoveDirection>true</rotateTowardsMoveDirection>
    </mote>
  </ThingDef>

  <EffecterDef>
    <defName>Bridge_Collapse</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>Bridge_Collapse</soundDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.2</positionRadius>
        <fleckDef>DustPuff</fleckDef>
        <burstCount>4~7</burstCount>
        <speed>0.8~1.4</speed>
        <scale>2~2.5</scale>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Bridge_CollapseWater</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>Bridge_CollapseWater</soundDef>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <positionRadius>0.2</positionRadius>
        <fleckDef>DustPuff</fleckDef>
        <burstCount>2~2</burstCount>
        <speed>0.8~1.4</speed>
        <scale>2~2.5</scale>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_Splash</moteDef>
        <burstCount>6~8</burstCount>
        <speed>4.3~5.9</speed>
        <scale>1.3~1.6</scale>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Interceptor_BlockedProjectile</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SoundTriggered</subEffecterClass>
        <soundDef>Interceptor_BlockProjectile</soundDef>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>DustPuff</fleckDef>
        <positionRadius>0.1</positionRadius>
        <burstCount>3~5</burstCount>
        <speed>0.6~0.75</speed>
        <scale>1.5~2.3</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <fleckDef>ExplosionFlash</fleckDef>
        <burstCount>1~1</burstCount>
        <scale>3.0~3.0</scale>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_Splash</defName>
    <graphicData>
      <texPath>Things/Mote/Splash</texPath>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.04</fadeInTime>
      <solidTime>0.03</solidTime>
      <fadeOutTime>0.5</fadeOutTime>
      <growthRate>-0.7</growthRate>
      <collide>true</collide>
      <speedPerTime>-22</speedPerTime>
      <rotateTowardsMoveDirection>true</rotateTowardsMoveDirection>
    </mote>
  </ThingDef>
  
  <ThingDef ParentName="MoteBase">
    <defName>Mote_ToxicDamage</defName>
    <graphicData>
      <texPath>Things/Mote/ToxicDamage</texPath>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.04</fadeInTime>
      <solidTime>0.03</solidTime>
      <fadeOutTime>1</fadeOutTime>
    </mote>
  </ThingDef>

  <EffecterDef>
    <defName>Impact_Toxic</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_ToxicDamage</moteDef>
        <positionRadius>0.1</positionRadius>
        <burstCount>3</burstCount>
        <scale>1~1.4</scale>
        <color>(0.1,1,0.1,1)</color>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>

</Defs>