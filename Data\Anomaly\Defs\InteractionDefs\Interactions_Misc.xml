﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <RulePackDef>
    <defName>DarkDialogue</defName>
    <rulePack>
      <rulesStrings>
        <li>rambled->rambled</li>
        <li>rambled->whispered</li>
        <li>rambled->chuckled</li>
        <li>rambled->raved</li>
        <li>rambled->ranted</li>
        <li>rambled->mumbled</li>
        <li>rambled->babbled</li>
        <li>rambled->murmured</li>
        <li>rambled->chattered</li>
        <li>rambled->spluttered</li>

        <li>darkSpeechAdverb->softly</li>
        <li>darkSpeechAdverb->haltingly</li>
        <li>darkSpeechAdverb->erratically</li>
        <li>darkSpeechAdverb->darkly</li>
        <li>darkSpeechAdverb->incoherently</li>
        <li>darkSpeechAdverb->madly</li>

        <li>subjectInsane->humanity's failings</li>
        <li>subjectInsane->the end of existence</li>
        <li>subjectInsane->the tyranny of time</li>
        <li>subjectInsane->the void</li>
        <li>subjectInsane->the illusion of reality</li>
        <li>subjectInsane->the mind's labyrinthine horrors</li>
        <li>subjectInsane->sinister shadows</li>
        <li>subjectInsane->the inevitability of decay</li>
        <li>subjectInsane->the madness of the cosmos</li>
        <li>subjectInsane->dreams' deceptive truth</li>
        <li>subjectInsane->the cryptic language of pain</li>
        <li>subjectInsane->life's futile cycle</li>
        <li>subjectInsane->the futility of hope</li>
        <li>subjectInsane->the fragility of sanity</li>
        <li>subjectInsane->the mockery of happiness</li>
        <li>subjectInsane->the grotesqueness of existence</li>
        <li>subjectInsane->the silence of the universe</li>
        <li>subjectInsane->the cruel dance of fate</li>
        <li>subjectInsane->the irrelevance of morality</li>
        <li>subjectInsane->the mockery of love</li>
        <li>subjectInsane->the bitter taste of truth</li>
        <li>subjectInsane->the echoes of forgotten screams</li>
        <li>subjectInsane->the deceit of memory</li>
        <li>subjectInsane->the relentless march of entropy</li>
        <li>subjectInsane->the cold embrace of solitude</li>
        <li>subjectInsane->the harsh light of despair</li>
        <li>subjectInsane->the emptiness of ambition</li>
        <li>subjectInsane->the bitter irony of joy</li>
        <li>subjectInsane->the hollow promises of faith</li>
        <li>subjectInsane->the perverse beauty of suffering</li>
        <li>subjectInsane->the inevitable fall of civilization</li>

        <li>subjectDarkStudy->Horax</li>
        <li>subjectDarkStudy->humanity</li>
        <li>subjectDarkStudy->shamblers</li>
        <li>subjectDarkStudy->machine horrors</li>
        <li>subjectDarkStudy->fleshbeasts</li>
        <li>subjectDarkStudy->pit gates</li>
        <li>subjectDarkStudy->the unnatural darkness</li>
        <li>subjectDarkStudy->void conduits</li>
        <li>subjectDarkStudy->the monolith</li>
        <li>subjectDarkStudy->the void</li>
        <li>subjectDarkStudy->the physicality of fear</li>
        <li>subjectDarkStudy->the power of rage</li>
        <li>subjectDarkStudy->connections between humans</li>
        <li>subjectDarkStudy->human goals and machine goals</li>
        <li>subjectDarkStudy->where dark entities come from</li>
        <li>subjectDarkStudy->the source of knowledge</li>
        <li>subjectDarkStudy->psychic flows</li>
        <li>subjectDarkStudy->psychic mechanisms</li>
        <li>subjectDarkStudy->psychic manifestation</li>
        <li>subjectDarkStudy->mechanisms of power</li>
    </rulesStrings>
    </rulePack>
  </RulePackDef>

  <InteractionDef>
    <defName>CreepyWords</defName>
    <label>creepy words</label>
    <symbol>Things/Mote/SpeechSymbols/CreepyWords</symbol>
    <ignoreTimeSinceLastInteraction>True</ignoreTimeSinceLastInteraction>
    <logRulesInitiator>
      <include>
        <li>DarkDialogue</li>
      </include>
      <rulesStrings>
        <li>r_logentry->[INITIATOR_nameDef] [rambled] about [subjectInsane].</li>
        <li>r_logentry->[INITIATOR_nameDef] [rambled] [darkSpeechAdverb] about [subjectInsane].</li>
      </rulesStrings>
    </logRulesInitiator>
  </InteractionDef>

  <InteractionDef>
    <defName>InhumanRambling</defName>
    <label>inhuman rambling</label>
    <workerClass>InteractionWorker_InhumanRambling</workerClass>
    <symbol>Things/Mote/SpeechSymbols/InhumanRambling</symbol>
    <ignoreTimeSinceLastInteraction>True</ignoreTimeSinceLastInteraction>
    <logRulesInitiator>
      <include>
        <li>DarkDialogue</li>
      </include>
      <rulesStrings>
        <li>r_logentry->[INITIATOR_nameDef] [rambled] about [subjectInsane].</li>
        <li>r_logentry->[INITIATOR_nameDef] [rambled] [darkSpeechAdverb] about [subjectInsane].</li>
      </rulesStrings>
    </logRulesInitiator>
  </InteractionDef>
  
  <InteractionDef>
    <defName>DisturbingChat</defName>
    <label>strange chat</label>
    <symbol>Things/Mote/SpeechSymbols/InhumanRambling</symbol>
    <recipientThought>SpokeToDisturbing</recipientThought>
    <logRulesInitiator>
      <include>
        <li>DarkDialogue</li>
      </include>
      <rulesStrings>
        <li>r_logentry->[INITIATOR_nameDef] [disturbed] [RECIPIENT_nameDef] by [talkingAbout] [subjectDisturbing].</li>
        <li>r_logentry->[INITIATOR_nameDef] [spoke] [darkSpeechAdverb] to [RECIPIENT_nameDef] about [subjectDisturbing].</li>
        
        <li>spoke->spoke</li>
        <li>spoke->muttered</li>
        <li>spoke->stammered</li>
        <li>spoke->whined</li>
        <li>spoke->grunted</li>
        <li>spoke->mumbled</li>
        <li>spoke(p=5)->[rambled]</li>

        <li>subjectDisturbing->[subjectDarkStudy]</li>
        <li>subjectDisturbing->[subjectInsane]</li>

        <li>disturbed->disturbed</li>
        <li>disturbed->horrified</li>
        <li>disturbed->frightened</li>
        <li>disturbed->upset</li>
        <li>disturbed->perturbed</li>
        <li>disturbed->unsettled</li>
        <li>disturbed->disconcerted</li>
        <li>disturbed->unnerved</li>
        <li>disturbed->alarmed</li>
        <li>disturbed->distressed</li>
        <li>disturbed->dismayed</li>

        <li>maybeDarkSpeechAdverb-></li>
        <li>maybeDarkSpeechAdverb->[darkSpeechAdverb]</li>

        <li>talkingAbout->describing</li>
        <li>talkingAbout->explaining</li>
        <li>talkingAbout->moaning [maybeDarkSpeechAdverb] about</li>
        <li>talkingAbout->talking [maybeDarkSpeechAdverb] about</li>
        <li>talkingAbout->ranting [maybeDarkSpeechAdverb] about</li>
        <li>talkingAbout->going into detail about</li>
      </rulesStrings>
    </logRulesInitiator>
  </InteractionDef>

  <InteractionDef>
    <defName>OccultTeaching</defName>
    <label>occult teaching</label>
    <symbol>Things/Mote/SpeechSymbols/InhumanRambling</symbol>
    <ignoreTimeSinceLastInteraction>True</ignoreTimeSinceLastInteraction>
    <logRulesInitiator>
      <include>
        <li>DarkDialogue</li>
      </include>
      <rulesStrings>
        <li>r_logentry->[INITIATOR_nameDef] explained [INITIATOR_possessive] theories about [subjectDarkStudy] to [RECIPIENT_nameDef].</li>
        <li>r_logentry->[INITIATOR_nameDef] shared [INITIATOR_possessive] ideas about [subjectDarkStudy] with [RECIPIENT_nameDef].</li>
        <li>r_logentry->[INITIATOR_nameDef] spoke about [subjectDarkStudy].</li>
        <li>r_logentry->[INITIATOR_nameDef] went over the subject of [subjectDarkStudy].</li>
        <li>r_logentry->[INITIATOR_nameDef] taught a lesson on [subjectDarkStudy].</li>
      </rulesStrings>
    </logRulesInitiator>
  </InteractionDef>

  <InteractionDef>
    <defName>PrisonerStudyAnomaly</defName>
    <label>prisoner study</label>
    <symbol>Things/Mote/SpeechSymbols/StudyPrisoner</symbol>
    <ignoreTimeSinceLastInteraction>True</ignoreTimeSinceLastInteraction>
    <logRulesInitiator>
      <include>
        <li>DarkDialogue</li>
      </include>
      <rulesStrings>
        <li>r_logentry->[INITIATOR_nameDef] asked [RECIPIENT_nameDef] about [subjectDarkStudy].</li>
        <li>r_logentry->[INITIATOR_nameDef] inquired about [subjectDarkStudy].</li>
        <li>r_logentry->[RECIPIENT_nameDef] mumbled about [subjectInsane].</li>
      </rulesStrings>
    </logRulesInitiator>
  </InteractionDef>

  <InteractionDef>
    <defName>InterrogateIdentity</defName>
    <label>interrogate identity</label>
    <symbol>Things/Mote/SpeechSymbols/Interrogation</symbol>
    <symbolSource>InitiatorFaction</symbolSource>
    <workerClass>InteractionWorker_InterrogateIdentity</workerClass>
    <initiatorXpGainSkill>Social</initiatorXpGainSkill>
    <initiatorXpGainAmount>230</initiatorXpGainAmount>
    <logRulesInitiator>
      <rulesStrings>
        <li>askedAbout->interrogated [RECIPIENT_nameDef] about</li>
        <li>askedAbout->asked [RECIPIENT_nameDef] about</li>
        <li>askedAbout->pressed [RECIPIENT_nameDef] for details about</li>
        <li>askedAbout->inquired [RECIPIENT_nameDef] about</li>
        <li>askedAbout->grilled [RECIPIENT_nameDef] on</li>
        <li>askedAbout->pushed [RECIPIENT_nameDef] for answers about</li>
        <li>askedAbout->asked [RECIPIENT_nameDef] tricky questions about</li>
        <li>askedAbout->tried to catch [RECIPIENT_nameDef] off-guard with questions about</li>
        <li>askedAbout->screamed at [RECIPIENT_nameDef], demanding information about</li>
        <li>askedAbout->bombarded [RECIPIENT_nameDef] with inquiries about</li>
        <li>askedAbout->confronted [RECIPIENT_nameDef] regarding</li>
        <li>askedAbout->pestered [RECIPIENT_nameDef] about</li>
        <li>askedAbout->probed [RECIPIENT_nameDef] on the subject of</li>

        <li>identityTopic->[RECIPIENT_possessive] previous work</li>
        <li>identityTopic->[RECIPIENT_possessive] previous relationships with other factions</li>
        <li>identityTopic->[RECIPIENT_possessive] birth</li>
        <li>identityTopic->[RECIPIENT_possessive] upbringing</li>
        <li>identityTopic->[RECIPIENT_possessive] parents</li>
        <li>identityTopic->[RECIPIENT_possessive] old friends</li>
        <li>identityTopic->[RECIPIENT_possessive] romantic history</li>
        <li>identityTopic->[RECIPIENT_possessive] likes and dislikes</li>
        <li>identityTopic->[RECIPIENT_possessive] emotional responses to disturbing situations</li>
        <li>identityTopic->[RECIPIENT_possessive] food preferences</li>
        <li>identityTopic->[RECIPIENT_possessive] deepest fears</li>
        <li>identityTopic->[RECIPIENT_possessive] cherished memories</li>
        <li>identityTopic->[RECIPIENT_possessive] personal regrets</li>
        <li>identityTopic->[RECIPIENT_possessive] dreams and aspirations</li>
        <li>identityTopic->[RECIPIENT_possessive] reasons for joining [RECIPIENT_possessive] current faction</li>
        <li>identityTopic->[RECIPIENT_possessive] role in a past significant event</li>
        <li>identityTopic->[RECIPIENT_possessive] hobbies and pastimes</li>
        <li>identityTopic->[RECIPIENT_possessive] moral values and principles</li>
        <li>identityTopic->[RECIPIENT_possessive] motivations</li>
        <li>identityTopic->reasons behind [RECIPIENT_possessive] major decisions</li>
        <li>identityTopic->past traumatic experiences</li>
        <li>identityTopic->human thought processes</li>
        <li>identityTopic->childhood experiences</li>
        <li>identityTopic->school experiences</li>
        <li>identityTopic->cultural experiences</li>

        <li>r_logentry->[INITIATOR_nameDef] [askedAbout] [identityTopic].</li>
      </rulesStrings>
    </logRulesInitiator>
  </InteractionDef>

</Defs>