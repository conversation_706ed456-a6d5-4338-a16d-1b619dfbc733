﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <!--=============== Thrown status ==============-->

  <ThingDef ParentName="MoteBase">
    <defName>Mote_Stun</defName>
    <thingClass>MoteDualAttached</thingClass>
    <altitudeLayer>MetaOverlays</altitudeLayer>
    <mote>
      <solidTime>9999</solidTime>
      <needsMaintenance>true</needsMaintenance>
    </mote>
    <graphicData>
      <texPath>Things/Mote/Stun</texPath>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_ColonistFleeing</defName>
    <graphicData>
      <texPath>Things/Mote/ColonistFleeing</texPath>
    </graphicData>
    <thingClass>MoteThrownAttached</thingClass>
    <altitudeLayer>MetaOverlays</altitudeLayer>
    <mote>
      <fadeInTime>0.25</fadeInTime>
      <solidTime>1</solidTime>
      <fadeOutTime>1.4</fadeOutTime>
      <attachedDrawOffset>(0.45, 0, 0.45)</attachedDrawOffset>
    </mote>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_ColonistAttacking</defName>
    <graphicData>
      <texPath>Things/Mote/ColonistAttacking</texPath>
    </graphicData>
    <thingClass>MoteThrownAttached</thingClass>
    <altitudeLayer>MetaOverlays</altitudeLayer>
    <mote>
      <fadeInTime>0.25</fadeInTime>
      <solidTime>1</solidTime>
      <fadeOutTime>1.4</fadeOutTime>
      <attachedDrawOffset>(0.45, 0, 0.45)</attachedDrawOffset>
    </mote>
  </ThingDef>

  <!--================= Pawn states ===================-->

  <ThingDef ParentName="MoteBase">
    <defName>Mote_DrunkBubble</defName>
    <graphicData>
      <texPath>Things/Mote/DrunkBubble</texPath>
    </graphicData>
    <thingClass>MoteThrownAttached</thingClass>
    <altitudeLayer>MetaOverlays</altitudeLayer>
    <mote>
      <fadeInTime>0.50</fadeInTime>
      <solidTime>1.5</solidTime>
      <fadeOutTime>2</fadeOutTime>
      <attachedDrawOffset>(0.2, 0, 0.2)</attachedDrawOffset>
    </mote>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_BerserkBit</defName>
    <graphicData>
      <texPath>Things/Mote/BerserkBit</texPath>
    </graphicData>
    <thingClass>MoteThrownAttached</thingClass>
    <altitudeLayer>MetaOverlays</altitudeLayer>
    <mote>
      <fadeInTime>0.50</fadeInTime>
      <solidTime>1.5</solidTime>
      <fadeOutTime>2</fadeOutTime>
      <attachedDrawOffset>(0.2, 0, 0.2)</attachedDrawOffset>
    </mote>
  </ThingDef>
  
</Defs>
