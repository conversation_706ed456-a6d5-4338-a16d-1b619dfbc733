<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <EffecterDef>
    <defName>Birthday</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerTriggered</subEffecterClass>
        <moteDef>Mote_NewbornBecomeChildGlow</moteDef>
        <rotation>0~0</rotation>
        <absoluteAngle>true</absoluteAngle>
        <attachToSpawnThing>true</attachToSpawnThing>
        <spawnLocType>OnSource</spawnLocType>
      </li>
      <li>
        <subEffecterClass>SubEffecter_SprayerChance</subEffecterClass>
        <positionRadius>0.3</positionRadius>
        <fleckDef>NewbornBecomeChildGlimmer</fleckDef>
        <burstCount>1~3</burstCount>
        <chancePerTick>0.5</chancePerTick>
        <speed>0.2~0.4</speed>
        <rotation>-45</rotation>
        <angle>0</angle>
        <spawnLocType>OnSource</spawnLocType>
      </li>
    </children>
  </EffecterDef>
</Defs>