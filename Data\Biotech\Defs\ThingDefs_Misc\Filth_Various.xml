﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingDef ParentName="BaseFilth">
    <defName>Filth_FlammableBile</defName>
    <label>flammable bile</label>
    <thingClass>LiquidFuel</thingClass>
    <useHitPoints>true</useHitPoints>
    <tickerType>Normal</tickerType>
    <statBases>
      <Beauty>-10</Beauty>
      <Cleanliness>-15</Cleanliness>
      <MaxHitPoints>150</MaxHitPoints>
      <Flammability>2.0</Flammability>
    </statBases>
    <graphicData>
      <texPath>Things/Filth/FlammableBile</texPath>
      <graphicClass>Graphic_Random</graphicClass>
      <drawSize>1.5</drawSize>
    </graphicData>
    <filth>
      <ignoreFilthMultiplierStat>true</ignoreFilthMultiplierStat>
      <disappearsInDays>35~40</disappearsInDays>
      <rainWashes>true</rainWashes>
      <cleaningWorkToReduceThickness>70</cleaningWorkToReduceThickness>
      <canFilthAttach>true</canFilthAttach>
      <maxThickness>1</maxThickness>
      <cleaningSound>Interact_CleanFilth_Fluid</cleaningSound>
    </filth>
  </ThingDef>

  <ThingDef ParentName="BaseFilth">
    <defName>Filth_Floordrawing</defName>
    <label>floordrawing</label>
    <graphicData>
      <graphicClass>Graphic_Random</graphicClass>
      <texPath>Things/Filth/Floordrawing</texPath>
      <shaderType>TransparentBelowSnow</shaderType>
    </graphicData>
    <statBases>
      <Beauty>0</Beauty>
    </statBases>
    <filth>
      <ignoreFilthMultiplierStat>true</ignoreFilthMultiplierStat>
      <disappearsInDays>35~40</disappearsInDays>
      <cleaningWorkToReduceThickness>25</cleaningWorkToReduceThickness>
      <canFilthAttach>false</canFilthAttach>
      <cleaningSound>Interact_CleanFilth_Dirt</cleaningSound>
    </filth>
  </ThingDef>

</Defs>
