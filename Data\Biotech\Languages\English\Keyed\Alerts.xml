<?xml version="1.0" encoding="utf-8" ?>
<LanguageData>

  <AlertMechLacksOverseer>Uncontrolled mechs</AlertMechLacksOverseer>
  <AlertMechLacksOverseerDesc>One or more of your mechanoids is not controlled. Connect your mechanoids to a mechanitor with sufficient bandwidth to control them.\n\nIf left uncontrolled for more than a day, a mechanoid could go feral. When this happens they will join {0} and become hostile to you. If one mechanoid goes feral, it will bring other nearby mechanoids with it.\n\nFeral mechanoids cannot be resurrected at a gestator when killed.</AlertMechLacksOverseerDesc>
  <AlertMechLacksOverseerUncontrolled>The following mechanoids are uncontrolled</AlertMechLacksOverseerUncontrolled>
  <AlertMechLacksOverseerMayGoFeral>The following mechanoids are uncontrolled and could go feral at any moment</AlertMechLacksOverseerMayGoFeral>

  <AlertWarqueenHasLowResources>{0} is low on resources</AlertWarqueenHasLowResources>
  <AlertWarqueenHasLowResourcesDesc>One or more {0} are low on resoures. They will be unable to produce mechs on command if they run out of resources.</AlertWarqueenHasLowResourcesDesc>

  <AlertLowDeathrestPawn>{PAWN} needs deathrest</AlertLowDeathrestPawn>
  <AlertLowDeathrestPawns>{NUMCULPRITS} colonists need deathrest</AlertLowDeathrestPawns>
  <AlertLowDeathrestDesc>A colonist has not deathrested for a long time. If they go too long without deathrest, they will suffer from deathrest exhaustion.\n\nTo deathrest, select the colonist then right-click on their assigned bed or deathrest casket.\n\nThese people need deathrest:\n{CULPRITS}</AlertLowDeathrestDesc>

  <AlertLowHemogen>Low hemogen</AlertLowHemogen>
  <AlertLowHemogenDesc>A colonist has low hemogen. At zero hemogen, they will develop a painful hemogen craving.\n\nFeed them hemogen packs to prevent this. You can obtain hemogen packs using the 'extract hemogen pack' medical operation on a non-hemogenic human.\n\nThe following colonists have low hemogen</AlertLowHemogenDesc>

  <AlertRechargerFull>Recharger full</AlertRechargerFull>
  <AlertRechargerFullDesc>Some mech recharger(s) are storing their maximum number of toxic wastepacks. Mechs can't use these chargers until the toxic wastepacks are hauled away.\n\nSelect a colonist and right-click a recharger to unload it.</AlertRechargerFullDesc>

  <AlertNeedMechChargers>Need mech rechargers</AlertNeedMechChargers>
  <AlertNeedMechChargersDesc>You don't have rechargers for all your mechanoids. If a mech runs out of energy, it will fall into a dormant self-charging state and will recharge very slowly.\n\nBuild more of the following chargers:\n{0}\n\nNote: Only light mechanoids can use regular mech rechargers. Medium or heavier mechanoids require large mech rechargers.</AlertNeedMechChargersDesc>
  <AlertNeedMechChargerBasicMechtech>Research basic mechtech or higher to build rechargers.</AlertNeedMechChargerBasicMechtech>

  <AlertReimplantationAvailable>Reimplantation available</AlertReimplantationAvailable>
  <AlertReimplantationAvailableDesc>{0_nameDef} is waiting to reimplant {0_possessive} xenogerm into one of your colonists.</AlertReimplantationAvailableDesc>

  <AlertLowBabyFood>Low baby food</AlertLowBabyFood>
  <AlertLowBabyFoodDesc>You are dangerously low on baby food.\n\n    Babies needing food: {0}\n    Days worth of baby food in storage: {1}\n\nBuy, find or make some baby-edible food. This includes {2}.</AlertLowBabyFoodDesc>
  <AlertNoBabyFeeder>No baby feeders</AlertNoBabyFeeder>
  <AlertNoBabyFeederDesc>No {FACTION_pawnsPlural} are assigned to feed these babies, or they're all downed.\n\nAssign one of your {FACTION_pawnsPlural} to childcare or assign a lactating {FACTION_pawnSingular} to feed these babies with Urgent priority in the babies' feeding tab, and ensure at least one of them is not downed.</AlertNoBabyFeederDesc>
  <AlertAbandonedBaby>Abandoned baby</AlertAbandonedBaby>
  <AlertAbandonedBabyDesc>There are abandoned babies nearby with no closeby caregivers.  If you don't adopt the babies, they'll probably starve to death.\n\nTo adopt a baby, select it and then select "{0}".</AlertAbandonedBabyDesc>

  <AlertNoBabyFoodCaravan>No baby food in caravan</AlertNoBabyFoodCaravan>
  <AlertNoBabyFoodCaravanDesc>Babies on this caravan have no baby food and no lactating {FACTION_pawnsPlural} are set to feed them.</AlertNoBabyFoodCaravanDesc>

  <AlertToxicBuildup>Toxic buildup</AlertToxicBuildup>
  <AlertToxicBuildupDesc>These colonists have serious toxic buildup:\n\n{0}\n\nMove them away from toxic sources.\n\nToxic buildup can be caused by many things including toxic fallout, tox gas, and polluted terrain.</AlertToxicBuildupDesc>

  <AlertNeedBabyCribs>Need baby cribs</AlertNeedBabyCribs>
  <AlertNeedBabyCribsDesc>You have more babies than cribs.\n\nMake more cribs or, if you have no resources to spare, place some baby sleeping spots for free.</AlertNeedBabyCribsDesc>
  <AlertNeedSlaveCribs>Need slave cribs</AlertNeedSlaveCribs>
  <AlertNeedSlaveCribsDesc>You have more slave babies than you have cribs marked for slave use.\n\nEither make more cribs or change a colonist crib to a slave crib.\n\nIf you have no resources, baby sleeping spots are free and can be placed instantly.</AlertNeedSlaveCribsDesc>

  <AlertPollutedTerrain>Polluted terrain</AlertPollutedTerrain>
  <AlertPollutedTerrainDesc>A person or animal is suffering toxic buildup and is on polluted terrain. Any time someone is on polluted terrain, their toxic buildup will increase over time until they die.\n\nThese people or animals are affected:\n\n{0}\n\nKeep people off polluted terrain, or find a way to protect them from its effects. Certain apparel, implants and gene modifications can protect from toxic buildup. Polluted terrain cannot be covered using a floor, but it can be cleaned by hand and other methods.</AlertPollutedTerrainDesc>

  <AlertMechMissingBodyPart>Mechanoid missing part</AlertMechMissingBodyPart>
  <AlertMechsMissingBodyPart>Mechanoids missing parts</AlertMechsMissingBodyPart>
  <AlertMechMissingBodyPartDesc>One or more of your mechanoids are missing a body part. A mechanitor can repair lost parts.\n\nThe mechanoids with lost body parts are</AlertMechMissingBodyPartDesc>

  <AlertMechNeedsRepair>Mechanoid repair needed</AlertMechNeedsRepair>
  <AlertMechNeedsRepairDescPrefix>These mechanoids are damaged and need repair</AlertMechNeedsRepairDescPrefix>
  <AlertMechNeedsRepairDesc>Order a mechanitor to repair them, or select the mech and turn on auto-repair. Repairing a mechanoid requires energy. If a mechanoid's energy is so low it can't move, someone must carry it to a recharger.</AlertMechNeedsRepairDesc>
  
  <AlertToxifierGeneratorStopped>{0} stopped</AlertToxifierGeneratorStopped>
  <AlertToxifierGeneratorStoppedDesc>Your {0} is not producing power because it has no nearby fertile terrain to convert.\n\nClean polluted terrain with pollution pumps, or by assigning a pollution removal area.</AlertToxifierGeneratorStoppedDesc>

  <AlertPsychicBondedPawnsSeparated>Psychic bond distance</AlertPsychicBondedPawnsSeparated>
  <AlertPsychicBondedPawnsSeparatedDesc>Some colonists with psychic bonds have been separated and are feeling negative effects from the distance. To avoid this, people with a psychic bond must be in the same world tile, caravan, or vehicle. Additionally, they must not be in cryptosleep or similar suspended animation.\n\nThe following people are affected</AlertPsychicBondedPawnsSeparatedDesc>

  <AlertGenebankUnpowered>Gene bank needs power</AlertGenebankUnpowered>
  <AlertGenebankUnpoweredDesc>A gene bank is unpowered, so the genepacks inside it are slowly deteriorating. Restore power to the gene bank to prevent the genepacks from deteriorating.\n\nOnce the gene bank is powered, it will slowly reverse the deterioration of the genepacks inside it.</AlertGenebankUnpoweredDesc>

  <AlertBossgroupIncoming>{0} incoming</AlertBossgroupIncoming>
  <AlertBossgroupIncomingDesc>Signaled by your mechanitor, a hostile {0} is approaching with its combat escort. Be prepared, this won't be an easy fight. The hostile mechanoids will arrive in a few hours or days.</AlertBossgroupIncomingDesc>

  <AlertDeathrestComplete>Deathrest complete</AlertDeathrestComplete>
  <AlertDeathrestCompleteDesc>The following people have completed their deathrest and can wake up safely</AlertDeathrestCompleteDesc>

</LanguageData>