﻿<?xml version="1.0" encoding="utf-8"?>

<Defs>
  <ThingDef ParentName="MoteCocoonBreakingBase">
    <defName>Mote_MeatExplosionMist</defName>
    <mote>
      <solidTime>0.2</solidTime>
      <fadeOutTime>1.3</fadeOutTime>
    </mote>
    <graphicData>
      <color>(171, 27, 17, 120)</color>
      <shaderType>Transparent</shaderType>
      <drawSize>(1.4, 1.4)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_MeatExplosionFlyingSplatter</defName>
    <thingClass>MoteThrown</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.03</fadeInTime>
      <fadeOutTime>0.2</fadeOutTime>
      <solidTime>0.4</solidTime>
      <growthRate>2</growthRate>
      <archHeight>0.5</archHeight>
      <archDuration>0.5</archDuration>
      <speedPerTime>-3</speedPerTime>
    </mote>
    <graphicData>
      <texPath>Things/Filth/Spatter</texPath>
      <graphicClass>Graphic_MoteRandom</graphicClass>
      <color>(171, 27, 17, 170)</color>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_MeatExplosionFlyingPiece</defName>
    <thingClass>MoteThrown</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.03</fadeInTime>
      <fadeOutTime>0.1</fadeOutTime>
      <solidTime>0.87</solidTime>
      <archHeight>0.5</archHeight>
      <archDuration>0.5</archDuration>
      <speedPerTime>-3</speedPerTime>
    </mote>
    <graphicData>
      <texPath>Things/Mote/MeatPieces</texPath>
      <graphicClass>Graphic_MoteRandom</graphicClass>
      <drawSize>0.4</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_MeatExplosionFlyingPieceOverloaded</defName>
    <thingClass>MoteThrown</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.03</fadeInTime>
      <fadeOutTime>0.1</fadeOutTime>
      <solidTime>4</solidTime>
      <archHeight>2</archHeight>
      <archDuration>3</archDuration>
      <speedPerTime>-10</speedPerTime>
    </mote>
    <graphicData>
      <texPath>Things/Mote/MeatPieces</texPath>
      <graphicClass>Graphic_MoteRandom</graphicClass>
      <drawSize>0.4</drawSize>
    </graphicData>
  </ThingDef>


  <ThingDef ParentName="MoteGlowDistorted">
    <defName>TachycardiacArrestExplosion</defName>
    <thingClass>MoteThrown</thingClass>
    <altitudeLayer>MoteLow</altitudeLayer>
    <mote>
      <needsMaintenance>False</needsMaintenance>
      <solidTime>0</solidTime>
      <fadeOutTime>.7</fadeOutTime>
      <growthRate>120</growthRate>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>MoteLargeDistortionWave</shaderType>
      <texPath>Things/Mote/PsychicDistortionRing</texPath>
      <shaderParameters>
        <_DistortionTex>/Things/Mote/PsychicDistortionCurrents</_DistortionTex>
        <_NoiseTex>/Things/Mote/PsycastNoise</_NoiseTex>
        <_distortionIntensity>0.12</_distortionIntensity>
        <_brightnessMultiplier>1.15</_brightnessMultiplier>
        <_noiseIntensity>10</_noiseIntensity>
        <_ageToNoiseRatio>0</_ageToNoiseRatio>
      </shaderParameters>
    </graphicData>
  </ThingDef>


  <ThingDef ParentName="MoteCocoonBreakingBase">
    <defName>Mote_ShamblerRaiseMist</defName>
    <mote>
      <solidTime>0</solidTime>
      <fadeOutTime>1</fadeOutTime>
    </mote>
    <graphicData>
      <color>(23, 27, 17, 120)</color>
      <shaderType>Transparent</shaderType>
      <drawSize>(1.4, 1.4)</drawSize>
    </graphicData>
  </ThingDef>


  <ThingDef ParentName="MoteGlowDistorted">
    <defName>Mote_HoraxHugeSpellWarmup</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.1</fadeInTime>
      <solidTime>1</solidTime>
      <fadeOutTime>0</fadeOutTime>
      <growthRate>-2</growthRate>
      <needsMaintenance>True</needsMaintenance>
    </mote>
    <graphicData>
      <texPath>Things/Mote/HoraxianLineGlow</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>MoteMultiplyAddCircular</shaderType>
      <shaderParameters>
        <_DistortionTex>/Things/Mote/RadiationDistortion_A</_DistortionTex>
        <_texAScale>0.5</_texAScale>
        <_texBScale>2</_texBScale>
        <_texAScrollSpeed>0</_texAScrollSpeed>
        <_texBScrollSpeed>-0.4</_texBScrollSpeed>
        <_Intensity>0.5</_Intensity>
        <_InnerFadeAmount>0</_InnerFadeAmount>
        <_DistortionIntensity>0.5</_DistortionIntensity>
        <_TexAScaleDivisorPerProgress>1.5</_TexAScaleDivisorPerProgress>
      </shaderParameters>
      <drawSize>(6, 6)</drawSize>
    </graphicData>
  </ThingDef>
  
  <ThingDef ParentName="MoteGlowDistorted">
    <defName>Mote_HoraxSmallSpellWarmup</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.05</fadeInTime>
      <fadeOutTime>0.08</fadeOutTime>
      <solidTime>0.15</solidTime>
      <growthRate>2</growthRate>
      <needsMaintenance>True</needsMaintenance>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>MotePsychicWarmupWordOf</shaderType>
      <texPath>Things/Mote/PsychicDistortionRing</texPath>
      <shaderParameters>
        <_distortionIntensity>0.025</_distortionIntensity>
        <_brightnessMultiplier>1.5</_brightnessMultiplier>
        <_pulseSpeed>1</_pulseSpeed>
      </shaderParameters>
      <drawSize>2</drawSize>
    </graphicData>
  </ThingDef>
  
  <ThingDef ParentName="MoteGlowDistorted">
    <defName>Mote_PsychicBanshee</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.05</fadeInTime>
      <fadeOutTime>0.2</fadeOutTime>
      <solidTime>0.4</solidTime>
      <growthRate>4</growthRate>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>MotePsychicWarmupWordOf</shaderType>
      <texPath>Things/Mote/PsychicDistortionRing</texPath>
      <shaderParameters>
        <_distortionIntensity>0.025</_distortionIntensity>
        <_brightnessMultiplier>1.5</_brightnessMultiplier>
        <_pulseSpeed>5</_pulseSpeed>
      </shaderParameters>
      <drawSize>2</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteGlowDistorted">
    <defName>Mote_ActivatedVoidStructure</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <needsMaintenance>True</needsMaintenance>
      <solidTime>30</solidTime>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>MoteActivatedVoidStructure</shaderType>
      <texPath>Things/Mote/PsychicDistortionRing</texPath>
      <shaderParameters>
        <_MaskTex>/Things/Mote/PsychicDistortionRingInv</_MaskTex>
        <_distortionIntensity>0.025</_distortionIntensity>
        <_desaturationIntensity>0.8</_desaturationIntensity>
        <_NoiseTex>/Things/Mote/HarshNoise</_NoiseTex> 
        <_brightnessMultiplier>1.5</_brightnessMultiplier>
        <_pulseSpeed>2</_pulseSpeed>
      </shaderParameters>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteGlowDistorted">
    <defName>Mote_HateChantCall</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>1</fadeInTime>
      <fadeOutTime>1</fadeOutTime>
      <solidTime>5</solidTime>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>MoteHateChant</shaderType>
      <texPath>Things/Mote/HateChantShadow</texPath>
      <shaderParameters>
        <_pulseSpeed>0.3</_pulseSpeed>
        <_distortionIntensity>0.65</_distortionIntensity>
        <_brightnessMultiplier>1.3</_brightnessMultiplier>
        <_snakeParticlesFactor>1.0</_snakeParticlesFactor>
      </shaderParameters>
      <drawSize>3.5</drawSize>
    </graphicData>
  </ThingDef>
  
  <ThingDef ParentName="MoteGlowDistorted">
    <defName>Mote_PsychicRitualInvocation</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.2</fadeInTime>
      <fadeOutTime>0.2</fadeOutTime>
      <solidTime>999</solidTime>
      <needsMaintenance>True</needsMaintenance>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>MoteHateChant</shaderType>
      <texPath>Things/Mote/HateChanterRing</texPath>  
      <shaderParameters>
        <_pulseSpeed>0.3</_pulseSpeed>
        <_distortionIntensity>0</_distortionIntensity>
        <_snakeParticlesFactor>1.1</_snakeParticlesFactor>
        <_MaskTex>/Things/Mote/RitualEffects/HateChantMask</_MaskTex>
      </shaderParameters>
      <drawSize>3</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteGlowDistorted">
    <defName>Mote_HateChantShadow</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteLow</altitudeLayer>
    <mote>
      <fadeInTime>0.2</fadeInTime>
      <fadeOutTime>0.2</fadeOutTime>
      <solidTime>999</solidTime>
      <needsMaintenance>True</needsMaintenance>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>MoteHateChantShadow</shaderType>
      <texPath>Things/Mote/HateChantShadow</texPath>
      <shaderParameters>
        <_shadowMultiplier>0.23</_shadowMultiplier>
      </shaderParameters>
      <drawSize>3.5</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_ChargingElectroCables</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteLow</altitudeLayer>
    <mote>
      <fadeInTime>0.2</fadeInTime>
      <fadeOutTime>0.2</fadeOutTime>
      <solidTime>0.8</solidTime>
    </mote>
    <graphicData>
      <texPath>Things/Mote/ElectricPulse</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>MoteChargingPulse</shaderType>
      <shaderParameters>
        <_DistortionTex>/Things/Mote/Cloudy_C</_DistortionTex>
        <_DistortionIntensity>0.08</_DistortionIntensity>
      </shaderParameters>
      <drawSize>0.3,0.4</drawSize>
    </graphicData>
  </ThingDef>
  
  <ThingDef ParentName="MoteBase">
    <defName>Mote_BioFerriteHarvested</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>Pawn</altitudeLayer>
    <mote>
      <fadeInTime>0.2</fadeInTime>
      <fadeOutTime>0.2</fadeOutTime>
      <solidTime>0.8</solidTime>
    </mote>
    <graphicData>
      <texPath>Things/Mote/BioFerritePulse</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>BioferriteHarvestPulse</shaderType>
      <shaderParameters>
        <_DistortionTex>/Things/Mote/Cloudy_C</_DistortionTex>
        <_DistortionIntensity>0.08</_DistortionIntensity>
      </shaderParameters>
      <drawSize>0.5,0.5</drawSize>
    </graphicData>
  </ThingDef>


  <ThingDef ParentName="MoteBase">
    <defName>Mote_PsychicDistortionRingContractingQuick</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>2</fadeInTime>
      <solidTime>0</solidTime>
      <fadeOutTime>2</fadeOutTime>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <texPath>Things/Mote/Black</texPath>
      <shaderType>MoteLargeDistortionWave</shaderType>
      <shaderParameters>
        <_DistortionTex>/Things/Mote/PsychicDistortionCurrents</_DistortionTex>
        <_NoiseTex>/Things/Mote/PsycastNoise</_NoiseTex>
        <_distortionIntensity>0.01</_distortionIntensity>
        <_brightnessMultiplier>10</_brightnessMultiplier>
        <_noiseIntensity>2</_noiseIntensity>
      </shaderParameters>
      <drawSize>10.0</drawSize>
    </graphicData>
  </ThingDef>


  <ThingDef ParentName="MoteBase">
    <defName>Mote_FleshmelterBolt_Aim</defName>
    <thingClass>MoteDualAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.15</fadeInTime>
      <fadeOutTime>0</fadeOutTime>
      <solidTime>999999</solidTime>
      <needsMaintenance>True</needsMaintenance>
      <rotateTowardsTarget>True</rotateTowardsTarget>
      <scaleToConnectTargets>True</scaleToConnectTargets>
      <fadeOutUnmaintained>True</fadeOutUnmaintained>
    </mote>
    <graphicData>
      <texPath>Things/Mote/FleshmelterBolt_Aim</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>MoteHellfireCannon_Aim</shaderType>
      <shaderParameters>
        <_DistortionTex>/Things/Mote/Cloudy_C</_DistortionTex>
        <_NumFrames>10</_NumFrames>
        <_FramesPerSec>1.35</_FramesPerSec>
        <_DistortionScrollSpeed>.25</_DistortionScrollSpeed>
        <_Intensity>8</_Intensity>
        <_DistortionScale>0.1</_DistortionScale>
      </shaderParameters>
      <drawSize>(3, 0.9)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_FleshmelterBolt_Charge</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.15</fadeInTime>
      <fadeOutTime>0.3</fadeOutTime>
      <solidTime>999999</solidTime>
      <needsMaintenance>True</needsMaintenance>
    </mote>
    <graphicData>
      <texPath>Things/Mote/FleshmelterBolt_ChargeGlow</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>MoteHellfireCannon_Charge</shaderType>
      <shaderParameters>
        <_RandomTex>/Things/Mote/RandomFlicker</_RandomTex>
        <_FlickerFrequency>0.25</_FlickerFrequency>
        <_FlickerAmount>0.6</_FlickerAmount>
        <_InnerCircleIntensity>0.1</_InnerCircleIntensity>
        <_InnerCircleSize>0.4</_InnerCircleSize>
        <_InnerCircleShimmerAmount>0.14</_InnerCircleShimmerAmount>
      </shaderParameters>
    </graphicData>
  </ThingDef>


  <ThingDef ParentName="MoteBase">
    <defName>Mote_FleshmelterBolt_Target</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.5</fadeInTime>
      <fadeOutTime>0.3</fadeOutTime>
      <solidTime>999999</solidTime>
      <needsMaintenance>True</needsMaintenance>
    </mote>
    <graphicData>
      <texPath>Things/Mote/FleshmelterBolt_Target</texPath> <!-- currently empty -->
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>MoteHellfireCannon_Target</shaderType>
      <shaderParameters>
        <_ScanTex>/Things/Mote/FleshmelterBolt_Target_Scan</_ScanTex>
        <_ScanMask>/Things/Mote/FleshmelterBolt_Target_Scan_Mask</_ScanMask>
        <_ScanSpeed>.1</_ScanSpeed>
        <_ScanSpeedAccel>.1</_ScanSpeedAccel>
      </shaderParameters>
      <drawSize>(8, 8)</drawSize>
    </graphicData>
  </ThingDef>


  <ThingDef ParentName="MoteBase">
    <defName>Mote_DistortionPulse</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>1</fadeInTime>
      <solidTime>0</solidTime>
      <fadeOutTime>1</fadeOutTime>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <texPath>Things/Mote/Black</texPath>
      <shaderType>MoteLargeDistortionWave</shaderType>
      <shaderParameters>
        <_DistortionTex>/Things/Mote/PsychicDistortionCurrents</_DistortionTex>
        <_NoiseTex>/Things/Mote/PsycastNoise</_NoiseTex>
        <_distortionIntensity>0.005</_distortionIntensity>
        <_brightnessMultiplier>3.15</_brightnessMultiplier>
        <_noiseIntensity>30</_noiseIntensity>
      </shaderParameters>
      <drawSize>10.0</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_RitualCandleA</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteLow</altitudeLayer>
    <mote>
      <fadeInTime>0.1</fadeInTime>
      <solidTime>999</solidTime>
      <fadeOutTime>2</fadeOutTime>
      <needsMaintenance>True</needsMaintenance>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <texPath>Things/Mote/Candles/CandleClusterA</texPath>
      <shaderType>PsychicRitualCandleMote</shaderType>
      <shaderParameters>
        <_FlameTex>/Things/Mote/Candles/CandleFlame</_FlameTex>
        <_Interval>.05</_Interval>
      </shaderParameters>
      <drawSize>0.8</drawSize>
    </graphicData>
  </ThingDef>
  
  <ThingDef ParentName="MoteBase">
    <defName>Mote_RitualCandleB</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteLow</altitudeLayer>
    <mote>
      <fadeInTime>0.1</fadeInTime>
      <solidTime>999</solidTime>
      <fadeOutTime>2</fadeOutTime>
      <needsMaintenance>True</needsMaintenance>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <texPath>Things/Mote/Candles/CandleClusterB</texPath>
      <shaderType>PsychicRitualCandleMote</shaderType>
      <shaderParameters>
        <_FlameTex>/Things/Mote/Candles/CandleFlame</_FlameTex>
        <_Interval>.05</_Interval>
      </shaderParameters>
      <drawSize>0.8</drawSize>
    </graphicData>
  </ThingDef>
  
  <ThingDef ParentName="MoteBase">
    <defName>Mote_RitualCandleC</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteLow</altitudeLayer>
    <mote>
      <fadeInTime>0.1</fadeInTime>
      <solidTime>999</solidTime>
      <fadeOutTime>2</fadeOutTime>
      <needsMaintenance>True</needsMaintenance>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <texPath>Things/Mote/Candles/CandleClusterC</texPath>
      <shaderType>PsychicRitualCandleMote</shaderType>
      <shaderParameters>
        <_FlameTex>/Things/Mote/Candles/CandleFlame</_FlameTex>
        <_Interval>.05</_Interval>
      </shaderParameters>
      <drawSize>0.8</drawSize>
    </graphicData>
  </ThingDef>


  <ThingDef ParentName="MoteBase">
    <defName>Mote_AbductWarmupDistortionRing</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteLow</altitudeLayer>
    <mote>
      <fadeInTime>.4</fadeInTime>
      <solidTime>2</solidTime>
      <fadeOutTime>0</fadeOutTime>
      <growthRate>-.25</growthRate>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <texPath>Things/Mote/Black</texPath>
      <shaderType>MoteLargeDistortionWave</shaderType>
      <shaderParameters>
        <_DistortionTex>/Things/Mote/PsychicDistortionCurrents</_DistortionTex>
        <_NoiseTex>/Things/Mote/PsycastNoise</_NoiseTex>
        <_distortionIntensity>0.07</_distortionIntensity>
        <_brightnessMultiplier>1</_brightnessMultiplier>
        <_noiseIntensity>0</_noiseIntensity>
      </shaderParameters>
      <drawSize>10.0</drawSize>
    </graphicData>
  </ThingDef>
  
  <ThingDef ParentName="MoteBase">
    <defName>Mote_AbductWarmupDistortionRingBuilding</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteLow</altitudeLayer>
    <mote>
      <fadeInTime>.6</fadeInTime>
      <solidTime>0</solidTime>
      <fadeOutTime>1</fadeOutTime>
      <growthRate>-2</growthRate>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <texPath>Things/Mote/Black</texPath>
      <shaderType>MoteLargeDistortionWave</shaderType>
      <shaderParameters>
        <_DistortionTex>/Things/Mote/PsychicDistortionCurrents</_DistortionTex>
        <_NoiseTex>/Things/Mote/PsycastNoise</_NoiseTex>
        <_distortionIntensity>0.05</_distortionIntensity>
        <_brightnessMultiplier>2</_brightnessMultiplier>
        <_noiseIntensity>4</_noiseIntensity>
      </shaderParameters>
      <drawSize>10.0</drawSize>
    </graphicData>
  </ThingDef>
  
  <ThingDef ParentName="MoteBase">
    <defName>Mote_IncineratorBurst</defName>
    <thingClass>MoteDualAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <isSaveable>false</isSaveable> <!-- because it's pure-vis, and weirdly constructed, it's better to not persist them at all -->
    <mote>
      <fadeInTime>0</fadeInTime>
      <fadeOutTime>1</fadeOutTime>
      <solidTime>999999</solidTime>
      <needsMaintenance>True</needsMaintenance>
      <rotateTowardsTarget>True</rotateTowardsTarget>
      <scaleToConnectTargets>False</scaleToConnectTargets>
      <fadeOutUnmaintained>True</fadeOutUnmaintained>
    </mote>
    <drawOffscreen>true</drawOffscreen>
    <graphicData>
      <texPath>Things/Mote/IncineratorFlameSheet</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>MoteIncineratorBeam</shaderType>
      <shaderParameters>
        <_GradientTex>/Things/Mote/IncineratorBeamGradient</_GradientTex>
        <_AgeScalarNoise>/Things/Mote/IncineratorBeamAgeNoise</_AgeScalarNoise>
        <_ScrollSpeedA>-4 </_ScrollSpeedA>
        <_ScrollSpeedB>3</_ScrollSpeedB>
        <_Intensity>0</_Intensity>
        <_NumFrames>12</_NumFrames>
        <_FramesPerSec>12</_FramesPerSec>
      </shaderParameters>
      <drawSize>10</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_SparkSimple</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.05</fadeInTime>
      <fadeOutTime>0.1</fadeOutTime>
      <solidTime>0.35</solidTime>
    </mote>
    <graphicData>
      <texPath>Things/Mote/SparkSimple</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>GlowAnimated</shaderType>
      <shaderParameters>
        <_NumFrames>4</_NumFrames>
        <_FramesPerSec>8</_FramesPerSec>
      </shaderParameters>
      <drawSize>(1.1, 1.1)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_Clipboard</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>Pawn</altitudeLayer>
    <mote>
      <updateOffsetToMatchTargetRotation>True</updateOffsetToMatchTargetRotation>
      <solidTime>600</solidTime>
      <rotateTowardsTarget>true</rotateTowardsTarget>
      <yFightingOffsetScalar01>0</yFightingOffsetScalar01>
    </mote>
    <graphicData>
      <drawSize>.75</drawSize>
      <graphicClass>Graphic_Multi</graphicClass>
      <texPath>Things/Mote/Clipboard/Clipboard</texPath>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>BiomutationWarmup</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteLow</altitudeLayer>
    <mote>
      <fadeInTime>1</fadeInTime>
      <solidTime>4</solidTime>
      <fadeOutTime>0</fadeOutTime>
      <growthRate>-.45</growthRate>
      <needsMaintenance>true</needsMaintenance>
    </mote>
    <graphicData>
      <texPath>Things/Mote/SimpleLine</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>MoteMultiplyCircular</shaderType>
      <color>(0, 0, 0, .5)</color>
      <shaderParameters>
        <_DistortionTex>/Things/Mote/RadiationDistortion_A</_DistortionTex>
        <_texAScale>0.5</_texAScale>
        <_texBScale>2</_texBScale>
        <_texAScrollSpeed>0</_texAScrollSpeed>
        <_texBScrollSpeed>-.4</_texBScrollSpeed>
        <_Intensity>1</_Intensity>
        <_InnerFadeAmount>1</_InnerFadeAmount>
        <_DistortionSpeed>1</_DistortionSpeed>
        <_DistortionIntensity>3</_DistortionIntensity>
        <_TexAScaleDivisorPerProgress>0</_TexAScaleDivisorPerProgress>
      </shaderParameters>
      <drawSize>(6, 6)</drawSize>
    </graphicData>
  </ThingDef>


  <ThingDef ParentName="MoteBase">
    <defName>AtmosphericHeaterGlowingVents</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0</fadeInTime>
      <solidTime>77777</solidTime>
      <fadeOutTime>1</fadeOutTime>
      <needsMaintenance>true</needsMaintenance>
    </mote>
    <graphicData>
      <texPath>Things/Mote/AtmosphericHeaterVents</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <renderInstanced>true</renderInstanced>
      <shaderType>MoteGlowPulse</shaderType>
      <shaderParameters>
        <_Interval>.5</_Interval>
        <_PulseMin>.8</_PulseMin>
        <_PulseMax>1</_PulseMax>
      </shaderParameters>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>MetalHellFog</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteLow</altitudeLayer>
    <drawOffscreen>true</drawOffscreen>
    <mote>
      <fadeInTime>1</fadeInTime>
      <solidTime>66666</solidTime>
      <fadeOutTime>1</fadeOutTime>
      <needsMaintenance>true</needsMaintenance>
    </mote>
    <graphicData>
      <texPath>Things/Mote/MetalHellFog</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <renderInstanced>true</renderInstanced>
      <shaderType>GroundFog</shaderType>
      <shaderParameters>
        <_ScrollDir>-1</_ScrollDir>
      </shaderParameters>
      <drawSize>(100,100)</drawSize>
    </graphicData>
  </ThingDef>


  <ThingDef ParentName="MoteBase">
    <defName>MetalHellFogHigh</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>Weather</altitudeLayer>
    <drawOffscreen>true</drawOffscreen>
    <mote>
      <fadeInTime>0</fadeInTime>
      <solidTime>66666</solidTime>
      <fadeOutTime>0</fadeOutTime>
      <needsMaintenance>true</needsMaintenance>
    </mote>
    <graphicData>
      <texPath>Things/Mote/MetalHellFog</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <renderInstanced>true</renderInstanced>
      <shaderType>GroundFog</shaderType>
      <drawSize>(80,80)</drawSize>
      <shaderParameters>
        <_ScrollDir>1</_ScrollDir>
      </shaderParameters>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>VoidNodeDistortion</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteLow</altitudeLayer>
    <mote>
      <fadeInTime>0</fadeInTime>
      <solidTime>66666</solidTime>
      <fadeOutTime>0</fadeOutTime>
      <needsMaintenance>true</needsMaintenance>
    </mote>
    <graphicData>
      <texPath>Things/Mote/MetalHellFog</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>VoidNodeDistortion</shaderType>
      <shaderParameters>
        <_DistortionTex>/Things/Mote/VoidNodeDistortion</_DistortionTex>
      </shaderParameters>
      <drawSize>(35,35)</drawSize>
    </graphicData>
  </ThingDef>

  
  <ThingDef ParentName="MoteBase">
    <defName>Mote_HarbingerTreeRoots</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>Pawn</altitudeLayer>
    <mote>
      <fadeInTime>0.1</fadeInTime>
      <fadeOutTime>0.1</fadeOutTime>
      <solidTime>999999</solidTime>
      <needsMaintenance>false</needsMaintenance>
    </mote>
    <graphicData>
      <graphicClass>Graphic_Mote</graphicClass>
      <shaderType>Mote</shaderType>
      <texPath>Things/Mote/HarbingerConsumeRoots</texPath>
      <drawSize>(1.2, 1.2)</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_NoctolEyes</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>Darkness</altitudeLayer>
    <mote>
      <needsMaintenance>True</needsMaintenance>
      <attachedToHead>True</attachedToHead>
      <updateOffsetToMatchTargetRotation>True</updateOffsetToMatchTargetRotation>
      <solidTime>600</solidTime>
      <rotateTowardsTarget>true</rotateTowardsTarget>
      <yFightingOffsetScalar01>0</yFightingOffsetScalar01>
    </mote>
    <graphicData>
      <renderQueue>4000</renderQueue>
      <graphicClass>Graphic_Multi_Mote</graphicClass>
      <shaderType>MoteGlow</shaderType>
      <texPath>Things/Mote/NoctolEyes/NoctolEyes</texPath>
      <drawSize>(2, 2)</drawSize>
    </graphicData>
  </ThingDef>


  <!--Fake void node for use in the gleaming effecter -->
  <ThingDef ParentName="MoteBase">
    <defName>Mote_VoidNode</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <drawOffscreen>true</drawOffscreen>
    <mote>
      <needsMaintenance>True</needsMaintenance>
      <solidTime>999999</solidTime>
      <rotateTowardsTarget>true</rotateTowardsTarget>
      <yFightingOffsetScalar01>0</yFightingOffsetScalar01>
      <scalers>
        <li>
          <curve>
            <points>
              <li>(0,3)</li>
              <li>(0.04,3.097126)</li>
              <li>(0.08,3.095029)</li>
              <li>(0.12,3.006803)</li>
              <li>(0.16,2.845546)</li>
              <li>(0.2,2.624354)</li>
              <li>(0.24,2.356322)</li>
              <li>(0.28,2.054548)</li>
              <li>(0.32,1.732127)</li>
              <li>(0.36,1.402155)</li>
              <li>(0.3999999,1.077729)</li>
              <li>(0.4399999,0.7719452)</li>
              <li>(0.4799999,0.4978993)</li>
              <li>(0.5199999,0.2686872)</li>
              <li>(0.5599999,0.09740615)</li>
              <li>(0.6,-0.002848148)</li>
              <li>(0.64,-0.02069371)</li>
              <li>(0.68,0.01015062)</li>
              <li>(0.72,0.05223926)</li>
              <li>(0.7600001,0.07913275)</li>
              <li>(0.8000001,0.06344408)</li>
              <li>(0.8400001,0.01003069)</li>
              <li>(0.8800001,-0.03120381)</li>
              <li>(0.9200001,-0.03298116)</li>
              <li>(0.9600002,-0.0165919)</li>
              <li>(1,0)</li>
            </points>
          </curve>
          <axisMask>(1, 0, 0)</axisMask>
          <scaleAmt>1</scaleAmt>
          <scaleTime>1</scaleTime>
        </li>

        <li>
          <curve>
            <points>
              <li>(0,-1)</li>
              <li>(0.04,-0.8541513)</li>
              <li>(0.08,-0.7187468)</li>
              <li>(0.12,-0.5936874)</li>
              <li>(0.16,-0.4788743)</li>
              <li>(0.2,-0.3742085)</li>
              <li>(0.24,-0.279591)</li>
              <li>(0.28,-0.1949232)</li>
              <li>(0.32,-0.1201059)</li>
              <li>(0.36,-0.05504)</li>
              <li>(0.3999999,0.0003730059)</li>
              <li>(0.4399999,0.04623234)</li>
              <li>(0.4799999,0.08263683)</li>
              <li>(0.5199999,0.1096854)</li>
              <li>(0.5599999,0.1274769)</li>
              <li>(0.6,0.1361105)</li>
              <li>(0.64,0.135685)</li>
              <li>(0.68,0.1105157)</li>
              <li>(0.72,0.05654465)</li>
              <li>(0.7600001,-0.005044088)</li>
              <li>(0.8000001,-0.05304758)</li>
              <li>(0.8400001,-0.0695904)</li>
              <li>(0.8800001,-0.06293925)</li>
              <li>(0.9200001,-0.04332516)</li>
              <li>(0.9600002,-0.01944608)</li>
              <li>(1,0)</li>
            </points>
          </curve>
          <axisMask>(0, 1, 1)</axisMask>
          <scaleAmt>1</scaleAmt>
          <scaleTime>1.2</scaleTime>
        </li>
      </scalers>
    </mote>
    <graphicData>
      <renderQueue>4000</renderQueue>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>VoidNodeParallax</shaderType>
      <texPath>Things/Building/VoidNode/Core</texPath>
      <shaderParameters>
        <_AmbientShake>.05</_AmbientShake>
        <_SquashNStretch>(.9, .95, .5, -.5)</_SquashNStretch>
        <_MaskTex>/Things/Building/VoidNode/InternalDustMask</_MaskTex>
        <_DustTex>/Things/Building/VoidNode/InternalDust</_DustTex>
        <_DissolveTex>/Things/Building/VoidNode/Core_m</_DissolveTex>
      </shaderParameters>
      <drawSize>(3, 3)</drawSize>
    </graphicData>
  </ThingDef>


  <ThingDef ParentName="MoteBase">
    <defName>VoidNodeCore_DustA</defName>
    <thingClass>MoteThrown</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <drawOffscreen>true</drawOffscreen>
    <mote>
      <needsMaintenance>True</needsMaintenance>
      <solidTime>999999</solidTime>
      <fadeInTime>.1</fadeInTime>
      <fadeOutTime>.1</fadeOutTime>
      <yFightingOffsetScalar01>0</yFightingOffsetScalar01>
    </mote>
    <graphicData>
      <texPath>Things/Building/VoidNode/ExternalDustRing1</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>Mote</shaderType>
      <shaderParameters>
      </shaderParameters>
      <drawSize>(8, 8)</drawSize>
    </graphicData>
  </ThingDef>


  <ThingDef ParentName="MoteBase">
    <defName>VoidNodeCore_DustB</defName>
    <thingClass>MoteThrown</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <drawOffscreen>true</drawOffscreen>
    <mote>
      <needsMaintenance>True</needsMaintenance>
      <solidTime>999999</solidTime>
      <fadeInTime>.1</fadeInTime>
      <fadeOutTime>.1</fadeOutTime>
      <yFightingOffsetScalar01>0</yFightingOffsetScalar01>
    </mote>
    <graphicData>
      <texPath>Things/Building/VoidNode/ExternalDustRing2</texPath>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>Mote</shaderType>
      <shaderParameters>
      </shaderParameters>
      <drawSize>(8, 8)</drawSize>
    </graphicData>
  </ThingDef>


  <ThingDef ParentName="MoteGlowDistorted">
    <defName>Mote_ObeliskExplosionWarmupArea</defName>
    <thingClass>MoteAttached</thingClass>
    <altitudeLayer>MoteLow</altitudeLayer>
    <mote>
      <fadeInTime>0.2</fadeInTime>
      <fadeOutTime>0.3</fadeOutTime>
      <solidTime>99999</solidTime>
      <needsMaintenance>true</needsMaintenance>
      <fadeOutUnmaintained>true</fadeOutUnmaintained>
      <growthRate>-.25</growthRate>
    </mote>
    <graphicData>
      <graphicClass>Graphic_MoteWithAgeSecs</graphicClass>
      <shaderType>MoteObeliskExplosionPulse</shaderType>
      <texPath>Things/Mote/PsychicDistortionApocriton</texPath>
      <shaderParameters>
        <_PulseTex>/Things/Mote/ObeliskPulseWaves</_PulseTex>
        <_distortionIntensity>0</_distortionIntensity>
        <_ScrollSpeed>-.1</_ScrollSpeed>
        <_ScrollScale>0.22</_ScrollScale>
        <_distortionTint>(.5, .5, .5, 0)</_distortionTint>
        <_pulseSpeed>2</_pulseSpeed>
      </shaderParameters>
      <drawSize>10</drawSize>
    </graphicData>
  </ThingDef>

</Defs>