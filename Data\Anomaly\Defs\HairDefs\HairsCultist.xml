﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <StyleItemCategoryDef>
    <defName>Cultist</defName>
    <label>cultist</label>
  </StyleItemCategoryDef>

  <HairDef Name="CultistHairBase" Abstract="True">
    <category>Cultist</category>
    <styleTags>
      <li>Cultist</li>
    </styleTags>
  </HairDef>

  <HairDef ParentName="CultistHairBase">
    <defName>DoubleTuft</defName>
    <label>double tuft</label>
    <styleGender>Any</styleGender>
    <texPath>Things/Pawn/Humanlike/Hairs/DoubleTuft</texPath>
  </HairDef>

  <HairDef ParentName="CultistHairBase">
    <defName>LargeTuft</defName>
    <label>large tuft</label>
    <styleGender>Any</styleGender>
    <texPath>Things/Pawn/Humanlike/Hairs/LargeTuft</texPath>
  </HairDef>

  <HairDef ParentName="CultistHairBase">
    <defName>LongMess</defName>
    <label>long mess</label>
    <styleGender>Any</styleGender>
    <texPath>Things/Pawn/Humanlike/Hairs/LongMess</texPath>
  </HairDef>

  <HairDef ParentName="CultistHairBase">
    <defName>LongOily</defName>
    <label>long oily</label>
    <styleGender>Any</styleGender>
    <texPath>Things/Pawn/Humanlike/Hairs/LongOily</texPath>
  </HairDef>

  <HairDef ParentName="CultistHairBase">
    <defName>Parted</defName>
    <label>parted</label>
    <styleGender>Any</styleGender>
    <texPath>Things/Pawn/Humanlike/Hairs/Parted</texPath>
  </HairDef>

  <HairDef ParentName="CultistHairBase">
    <defName>PatchyShave</defName>
    <label>patchy shave</label>
    <styleGender>Any</styleGender>
    <texPath>Things/Pawn/Humanlike/Hairs/PatchyShave</texPath>
  </HairDef>

  <HairDef ParentName="CultistHairBase">
    <defName>RoughShave</defName>
    <label>rough shave</label>
    <styleGender>Any</styleGender>
    <texPath>Things/Pawn/Humanlike/Hairs/RoughShave</texPath>
  </HairDef>

  <HairDef ParentName="CultistHairBase">
    <defName>ShavedMohawk</defName>
    <label>shaved mohawk</label>
    <styleGender>Any</styleGender>
    <texPath>Things/Pawn/Humanlike/Hairs/ShavedMohawk</texPath>
  </HairDef>

  <HairDef ParentName="CultistHairBase">
    <defName>ShortCut</defName>
    <label>short cut</label>
    <styleGender>Any</styleGender>
    <texPath>Things/Pawn/Humanlike/Hairs/ShortCut</texPath>
  </HairDef>

  <HairDef ParentName="CultistHairBase">
    <defName>SideCut</defName>
    <label>side cut</label>
    <styleGender>Any</styleGender>
    <texPath>Things/Pawn/Humanlike/Hairs/SideCut</texPath>
  </HairDef>

  <HairDef ParentName="CultistHairBase">
    <defName>SideSpikes</defName>
    <label>side spikes</label>
    <styleGender>Any</styleGender>
    <texPath>Things/Pawn/Humanlike/Hairs/SideSpikes</texPath>
  </HairDef>

</Defs>
