<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <PawnKindDef Name="MechanitorBase">
    <defName>Mechanitor</defName>
    <label>mechanitor</label>
    <race>Human</race>
    <combatPower>85</combatPower>
    <backstoryCryptosleepCommonality>1</backstoryCryptosleepCommonality>
    <backstoryFilters>
      <li>
        <categories>
          <li>Scientist</li>
        </categories>
      </li>
    </backstoryFilters>
    <defaultFactionType>PlayerColony</defaultFactionType>
    <chemicalAddictionChance>0.08</chemicalAddictionChance>
    <apparelMoney>0~2500</apparelMoney>
    <apparelTags>
      <li>IndustrialAdvanced</li>
    </apparelTags>
    <apparelRequired>
      <li>Apparel_FlakVest</li>
      <li>Apparel_Duster</li>
      <li>Apparel_CollarShirt</li>
      <li>Apparel_Pants</li>
      <li>Apparel_AirwireHeadset</li>
    </apparelRequired>
    <apparelIgnoreSeasons>true</apparelIgnoreSeasons>
    <apparelIgnorePollution MayRequire="Ludeon.RimWorld.Biotech">true</apparelIgnorePollution>
    <apparelAllowHeadgearChance>0</apparelAllowHeadgearChance>
    <weaponMoney>300~900</weaponMoney>
    <weaponTags>
      <li>Autopistol</li>
    </weaponTags>
    <forceWeaponQuality>Masterwork</forceWeaponQuality>
    <techHediffsMoney>1000~2000</techHediffsMoney>
    <techHediffsTags>
      <li>Simple</li>
      <li>Advanced</li>
      <li>AdvancedWeapon</li>
    </techHediffsTags>
    <techHediffsRequired>
      <li>Mechlink</li>
    </techHediffsRequired>
    <biocodeWeaponChance>1</biocodeWeaponChance>
    <combatEnhancingDrugsChance>0.5</combatEnhancingDrugsChance>
    <combatEnhancingDrugsCount>1~2</combatEnhancingDrugsCount>
    <initialWillRange>0</initialWillRange>
    <initialResistanceRange>0~1</initialResistanceRange>
    <requiredWorkTags>
      <li>Intellectual</li>
    </requiredWorkTags>
    <forcedTraits>
      <Recluse>0</Recluse>
    </forcedTraits>
    <disallowedTraits>
      <li>BodyPurist</li>
    </disallowedTraits>
    <disallowedTraitsWithDegree>
      <PsychicSensitivity>-1</PsychicSensitivity>
      <PsychicSensitivity>-2</PsychicSensitivity>
    </disallowedTraitsWithDegree>
  </PawnKindDef>

  <PawnKindDef ParentName="MechanitorBase">
    <defName>Mechanitor_Basic</defName>
    <apparelMoney>0~1000</apparelMoney>
    <apparelRequired Inherit="False" />
    <techHediffsTags Inherit="False" />
    <forcedTraits Inherit="False" />
    <defaultFactionType Inherit="False" />
  </PawnKindDef>

  <PawnKindDef Name="SanguophageBase" Abstract="True">
    <label>sanguophage</label>
    <race>Human</race>
    <xenotypeSet>
      <xenotypeChances>
        <Sanguophage>999</Sanguophage>
      </xenotypeChances>
    </xenotypeSet>
    <useFactionXenotypes>false</useFactionXenotypes>
    <combatPower>100</combatPower>
    <backstoryCryptosleepCommonality>0.1</backstoryCryptosleepCommonality>
    <chemicalAddictionChance>0.08</chemicalAddictionChance>
    <isFighter>true</isFighter>
    <chronologicalAgeRange>300~2000</chronologicalAgeRange>
    <initialResistanceRange>10~80</initialResistanceRange>
    <initialWillRange>5~15</initialWillRange>
    <apparelRequired>
      <li>Apparel_Duster</li>
      <li>Apparel_CollarShirt</li>
      <li>Apparel_Pants</li>
    </apparelRequired>
    <requiredWorkTags>
      <li>Social</li>
      <li>Violent</li>
    </requiredWorkTags>
    <disallowedTraits>
      <li>Immunity</li>
    </disallowedTraits>
  </PawnKindDef>

  <PawnKindDef ParentName="SanguophageBase">
    <defName>Sanguophage</defName>
    <invNutrition>0.5</invNutrition>
    <invFoodDef>HemogenPack</invFoodDef>
    <combatEnhancingDrugsChance>0.1</combatEnhancingDrugsChance>
    <combatEnhancingDrugsCount>1~2</combatEnhancingDrugsCount>
    <apparelMoney>500~700</apparelMoney>
    <techHediffsChance>0.15</techHediffsChance>
    <techHediffsMoney>0~1800</techHediffsMoney>
    <techHediffsTags>
      <li>Advanced</li>
    </techHediffsTags>
    <weaponMoney>600~800</weaponMoney>
    <weaponTags>
      <li>Gun</li>
      <li>MedievalMeleeDecent</li>
    </weaponTags>
    <apparelAllowHeadgearChance>0.1</apparelAllowHeadgearChance>
    <apparelTags>
      <li>IndustrialBasic</li>
    </apparelTags>
    <apparelRequired Inherit="False">
      <li>Apparel_Pants</li>
    </apparelRequired>
    <apparelDisallowTags>
      <li>Western</li>
    </apparelDisallowTags>
    <specificApparelRequirements>
      <li>
        <alternateTagChoices>
          <li>
            <tag>Cape</tag>
            <chance>0.1</chance>
          </li>
          <li>
            <tag>BestowerHood</tag>
            <chance>0.1</chance>
          </li>
        </alternateTagChoices>
      </li>
      <li>
        <colorGenerator Class="ColorGenerator_Options">
          <options>
            <li>
              <only>(58, 58, 58)</only>
            </li>
            <li>
              <only>(74, 1, 9)</only>
            </li>
          </options>
        </colorGenerator>
      </li>
    </specificApparelRequirements>
  </PawnKindDef>

  <PawnKindDef ParentName="SanguophageBase">
    <defName>Sanguophage_Player</defName>
    <weaponMoney>0</weaponMoney>
    <apparelAllowHeadgearChance>0</apparelAllowHeadgearChance>
    <apparelColor>(58,58,58)</apparelColor>
    <defaultFactionType>PlayerColony</defaultFactionType>
    <apparelMoney>350~600</apparelMoney>
    <apparelTags>
      <li>IndustrialBasic</li>
    </apparelTags>
  </PawnKindDef>

  <PawnKindDef>
    <defName>SanguophageThrall</defName>
    <label>thrall</label>
    <race>Human</race>
    <defaultFactionType>Sanguophages</defaultFactionType>
    <initialResistanceRange>0~1</initialResistanceRange>
    <chemicalAddictionChance>0.05</chemicalAddictionChance>
    <combatEnhancingDrugsChance>0.1</combatEnhancingDrugsChance>
    <combatEnhancingDrugsCount>1~2</combatEnhancingDrugsCount>
    <techHediffsChance>0.15</techHediffsChance>
    <techHediffsMoney>0~1800</techHediffsMoney>
    <maxGenerationAge>55</maxGenerationAge>
    <backstoryCryptosleepCommonality>0.1</backstoryCryptosleepCommonality>
    <invNutrition>1</invNutrition>
    <techHediffsTags>
      <li>Advanced</li>
    </techHediffsTags>
    <itemQuality>Normal</itemQuality>
    <weaponMoney>600~1000</weaponMoney>
    <combatPower>100</combatPower>
    <initialWillRange>2~4</initialWillRange>
    <weaponTags>
      <li>IndustrialGunAdvanced</li>
      <li>MedievalMeleeDecent</li>
    </weaponTags>
    <apparelAllowHeadgearChance>0.5</apparelAllowHeadgearChance>
    <apparelMoney>1250~2500</apparelMoney>
    <apparelTags>
      <li>IndustrialBasic</li>
      <li>IndustrialAdvanced</li>
      <li>IndustrialMilitaryBasic</li>
      <li>IndustrialMilitaryAdvanced</li>
    </apparelTags>
    <requiredWorkTags>
      <li>Violent</li>
    </requiredWorkTags>
    <humanPregnancyChance>0</humanPregnancyChance>
  </PawnKindDef>

  <PawnKindDef ParentName="VillagerBase" Name="Villager_Child">
    <defName>Villager_Child</defName>
    <isFighter>false</isFighter>
    <weaponTags/>
    <weaponMoney>0</weaponMoney>
    <pawnGroupDevelopmentStage>Child</pawnGroupDevelopmentStage>
  </PawnKindDef>

  <PawnKindDef ParentName="TribalBase" Name="TribalChildBase">
    <defName>Tribal_Child</defName>
    <label>villager</label>
    <combatPower>30</combatPower>
    <itemQuality>Poor</itemQuality>
    <gearHealthRange>0.5~1.8</gearHealthRange>
    <apparelMoney>180~350</apparelMoney>
    <weaponMoney>0</weaponMoney>
    <weaponTags/>
    <techHediffsMoney>50~50</techHediffsMoney>
    <techHediffsTags>
      <li>Poor</li>
    </techHediffsTags>
    <techHediffsChance>0.03</techHediffsChance>
    <initialWillRange>1~3</initialWillRange>
    <initialResistanceRange>17~27</initialResistanceRange>
    <pawnGroupDevelopmentStage>Child</pawnGroupDevelopmentStage>
  </PawnKindDef>

</Defs>