﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingDef ParentName="MakeableDrugBase">
    <defName>GoJuice</defName>
    <label>go-juice</label>
    <description>A synthetic performance-enhancing drug developed for space marines during the early days of interplanetary warfare. Go-juice blocks pain, increases movement speed, and improves the user's melee and shooting abilities.\n\nThe military chemists who created it were never able to remove its addictiveness. Some saw this as a downside; others saw it as a benefit.</description>
    <descriptionHyperlinks>
      <HediffDef>GoJuiceHigh</HediffDef>
      <HediffDef>GoJuiceAddiction</HediffDef>
    </descriptionHyperlinks>
    <graphicData>
      <texPath>Things/Item/Drug/GoJuice</texPath>
      <graphicClass>Graphic_StackCount</graphicClass>
    </graphicData>
    <rotatable>false</rotatable>
    <statBases>
      <WorkToMake>600</WorkToMake>
      <MarketValue>53</MarketValue>
      <Mass>0.1</Mass>
    </statBases>
    <techLevel>Industrial</techLevel>
    <ingestible>
      <joyKind>Chemical</joyKind>
      <joy>0.40</joy>
      <drugCategory>Hard</drugCategory>
      <foodType>Processed, Fluid</foodType>
      <baseIngestTicks>80</baseIngestTicks>
      <ingestSound>Ingest_Inject</ingestSound>
      <ingestHoldOffsetStanding>
        <northDefault>
          <offset>(0.18,0,0)</offset>
        </northDefault>
      </ingestHoldOffsetStanding>
      <ingestCommandString>Inject {0}</ingestCommandString>
      <ingestReportString>Injecting {0}.</ingestReportString>
      <useEatingSpeedStat>false</useEatingSpeedStat>
      <chairSearchRadius>0</chairSearchRadius>
      <outcomeDoers>
        <li Class="IngestionOutcomeDoer_GiveHediff">
          <hediffDef>GoJuiceHigh</hediffDef>
          <severity>0.5</severity>
          <toleranceChemical>GoJuice</toleranceChemical>
        </li>
        <li Class="IngestionOutcomeDoer_OffsetNeed">
          <need>Rest</need>
          <offset>0.4</offset>
          <toleranceChemical>GoJuice</toleranceChemical>
        </li>
        <li Class="IngestionOutcomeDoer_OffsetPsyfocus">
          <offset>0.15</offset>
        </li>
      </outcomeDoers>
    </ingestible>
    <recipeMaker>
      <researchPrerequisite>GoJuiceProduction</researchPrerequisite>
      <recipeUsers>
        <li>DrugLab</li>
      </recipeUsers>
      <soundWorking>Recipe_Drug</soundWorking>
      <displayPriority>1500</displayPriority>
    </recipeMaker>
    <costList>
      <Neutroamine>2</Neutroamine>
      <Yayo>1</Yayo>
    </costList>
    <comps>
      <li Class="CompProperties_Drug">
        <chemical>GoJuice</chemical>
        <addictiveness>0.026</addictiveness>
        <existingAddictionSeverityOffset>0.20</existingAddictionSeverityOffset>
        <needLevelOffset>0.9</needLevelOffset>
        <isCombatEnhancingDrug>true</isCombatEnhancingDrug>
        <listOrder>200</listOrder>
        <overdoseSeverityOffset>0.18~0.35</overdoseSeverityOffset>
        <largeOverdoseChance>0.005</largeOverdoseChance>
      </li>
    </comps>
  </ThingDef>
  
  <HediffDef>
    <defName>GoJuiceHigh</defName>
    <hediffClass>Hediff_High</hediffClass>
    <label>high on go-juice</label>
    <labelNoun>a go-juice high</labelNoun>
    <description>Go-juice in the bloodstream. It supercharges combat-related abilities, and instantly increases psyfocus when first injected.</description>
    <defaultLabelColor>(1,0,0.5)</defaultLabelColor>
    <scenarioCanAdd>true</scenarioCanAdd>
    <maxSeverity>1.0</maxSeverity>
    <isBad>false</isBad>
    <comps>
      <li Class="HediffCompProperties_SeverityPerDay">
        <severityPerDay>-0.75</severityPerDay>
        <showHoursToRecover>true</showHoursToRecover>
      </li>
    </comps>
      <stages>
        <li>
          <painFactor>0.1</painFactor>
          <capMods>
            <li>
              <capacity>Consciousness</capacity>
              <offset>0.20</offset>
            </li>
            <li>
              <capacity>Sight</capacity>
              <offset>0.35</offset>
            </li>
            <li>
              <capacity>Moving</capacity>
              <offset>0.50</offset>
            </li>
          </capMods>
        </li>
      </stages>
  </HediffDef>
  
  <ThoughtDef>
    <defName>GoJuiceHigh</defName>
    <workerClass>ThoughtWorker_Hediff</workerClass>
    <hediff>GoJuiceHigh</hediff>
    <validWhileDespawned>true</validWhileDespawned>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
    <stages>
      <li>
        <label>high on go-juice</label>
        <description>I feel pumped but steady. I am the bullet in flight, ready to cut through you.</description>
        <baseMoodEffect>5</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>
  
  <!-- Go-juice addiction -->

  <ChemicalDef>
    <defName>GoJuice</defName>
    <label>go-juice</label>
    <addictionHediff>GoJuiceAddiction</addictionHediff>
    <geneOverdoseChanceFactorResist>0.5</geneOverdoseChanceFactorResist>
    <geneOverdoseChanceFactorImmune>0</geneOverdoseChanceFactorImmune>
  </ChemicalDef>
  
  <NeedDef ParentName="DrugAddictionNeedBase">
    <defName>Chemical_GoJuice</defName>
    <needClass>Need_Chemical</needClass>
    <label>go-juice</label>
    <description>Because of a go-juice addiction, this person needs to regularly consume the drug to avoid withdrawal symptoms.</description>
    <fallPerDay>0.333</fallPerDay>
    <listPriority>45</listPriority>
  </NeedDef>

  <HediffDef ParentName="AddictionBase">
    <defName>GoJuiceAddiction</defName>
    <label>go-juice addiction</label>
    <description>A chemical addiction to go-juice. Long-term presence of go-juice has caused neurological adaptations at the cellular level, so the brain can no longer function properly without the drug.
\nWithout regular doses of go-juice, withdrawal symptoms will begin. However, extended abstinence will force the brain to adapt back to its normal state, resolving the addiction.</description>
    <hediffClass>Hediff_Addiction</hediffClass>
    <causesNeed>Chemical_GoJuice</causesNeed>
    <comps>
      <li Class="HediffCompProperties_SeverityPerDay">
        <severityPerDay>-0.045</severityPerDay>
        <showDaysToRecover>true</showDaysToRecover>
      </li>
    </comps>
    <stages>
      <li>
      </li>
      <li>
        <label>withdrawal</label>
        <painFactor>3</painFactor>
        <hungerRateFactorOffset>0.5</hungerRateFactorOffset>
        <statOffsets>
          <RestFallRateFactor>0.3</RestFallRateFactor>
        </statOffsets>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <offset>-0.20</offset>
          </li>
          <li>
            <capacity>Moving</capacity>
            <offset>-0.50</offset>
          </li>
          <li>
            <capacity>Sight</capacity>
            <offset>-0.20</offset>
          </li>
          <li>
            <capacity>BloodPumping</capacity>
            <offset>-0.20</offset>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <offset>-0.10</offset>
          </li>
        </capMods>
        <mentalStateGivers>
          <li>
            <mentalState>Binging_DrugExtreme</mentalState>
            <mtbDays>40</mtbDays>
          </li>
          <li>
            <mentalState>Wander_Psychotic</mentalState>
            <mtbDays>10</mtbDays>
          </li>
        </mentalStateGivers>
      </li>
    </stages>
  </HediffDef>
  
  <ThoughtDef>
    <defName>GoJuiceWithdrawal</defName>
    <workerClass>ThoughtWorker_Hediff</workerClass>
    <hediff>GoJuiceAddiction</hediff>
    <validWhileDespawned>true</validWhileDespawned>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
    <stages>
      <li>
        <visible>false</visible>
      </li>
      <li>
        <label>go-juice withdrawal</label>
        <description>I'm all fuzzy and can't think straight. My limbs feel heavy, I'm tired and hungry, everything hurts. And why won't my eyes focus properly?</description>
        <baseMoodEffect>-22</baseMoodEffect>
      </li>
    </stages>
  </ThoughtDef>

  <HediffDef ParentName="DrugToleranceBase">
    <defName>GoJuiceTolerance</defName>
    <label>go-juice tolerance</label>
    <description>A built-up tolerance to go-juice. The more severe this tolerance is, the more go-juice it takes to get the same effect.</description>
    <comps>
      <li Class="HediffCompProperties_SeverityPerDay">
        <severityPerDay>-0.015</severityPerDay>
      </li>
      <li Class="HediffCompProperties_DrugEffectFactor">
        <chemical>GoJuice</chemical>
      </li>
    </comps>
    <hediffGivers>
      <li Class="HediffGiver_RandomDrugEffect">
        <hediff>ChemicalDamageModerate</hediff>
        <severityToMtbDaysCurve>
          <points>
            <li>(0.45, 99999)</li>
            <li>(0.5, 120)</li>
            <li>(1, 90)</li>
          </points>
        </severityToMtbDaysCurve>
        <partsToAffect>
          <li>Brain</li>
        </partsToAffect>
      </li>
    </hediffGivers>
  </HediffDef>

</Defs>