﻿<?xml version="1.0" encoding="utf-8"?>

<Defs>
  <SoundDef>
    <defName>ObeliskDamaged</defName>
    <context>MapOnly</context>
    <maxSimultaneous>3</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Building/Obelisks/Damaged</clipFolderPath>
          </li>
        </grains>
        <volumeRange>23</volumeRange>
        <muteWhenPaused>True</muteWhenPaused>
        <tempoAffectedByGameSpeed>true</tempoAffectedByGameSpeed>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>ObeliskAmbientStageOne</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/Obelisks/Ambience/Obelisk_Amb_Stage2_A_01</clipPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <distRange>5~30</distRange>
      </li>
    </subSounds>
  </SoundDef>
  <SoundDef>
    <defName>ObeliskAmbientStageTwo</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/Obelisks/Ambience/Obelisk_Amb_Stage3_A_01</clipPath>
          </li>
        </grains>
        <volumeRange>2</volumeRange>
        <distRange>5~30</distRange>
      </li>
    </subSounds>
  </SoundDef>
  <SoundDef>
    <defName>ObeliskAmbientStageThree</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/Obelisks/Ambience/Obelisk_Amb_Stage4_A_01</clipPath>
          </li>
        </grains>
        <volumeRange>4</volumeRange>
        <distRange>5~30</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>WarpedObelisk_Fall</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <sustain>true</sustain>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/Obelisks/Landing/Obelisk_Landing_B_02</clipPath>
          </li>
        </grains>
        <volumeRange>25</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <sustainLoop>false</sustainLoop>
        <tempoAffectedByGameSpeed>true</tempoAffectedByGameSpeed>
        <repeatMode>NeverTwice</repeatMode>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>WarpedObelisk_Impact</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/Obelisks/Landing/Obelisk_Impact_A_01</clipPath>
          </li>
        </grains>
        <volumeRange>50~55</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <repeatMode>NeverTwice</repeatMode>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>GrayBoxOpening</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipPath>Building/GrayBox/Opening</clipPath>
          </li>
        </grains>
        <distRange>25~50</distRange>
        <volumeRange>14</volumeRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>SecurityDoor_Open</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <gameSpeedRange>1~2</gameSpeedRange>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Building/SecurityDoor/Open/Normal</clipFolderPath>
          </li>
        </grains>
        <volumeRange>25</volumeRange>
        <pitchRange>0.85~1.15</pitchRange>
      </li>
      <li>
        <gameSpeedRange>3~99</gameSpeedRange>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Building/SecurityDoor/Open/Fast</clipFolderPath>
          </li>
        </grains>
        <volumeRange>15</volumeRange>
        <pitchRange>0.85~1.15</pitchRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>SecurityDoor_BeginClosing</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <gameSpeedRange>1~2</gameSpeedRange>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Building/SecurityDoor/BeginClosing/Normal</clipFolderPath>
          </li>
        </grains>
        <volumeRange>25</volumeRange>
        <pitchRange>0.85~1.15</pitchRange>
      </li>
      <li>
        <gameSpeedRange>3~99</gameSpeedRange>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Building/SecurityDoor/BeginClosing/Fast</clipFolderPath>
          </li>
        </grains>
        <volumeRange>15</volumeRange>
        <pitchRange>0.85~1.15</pitchRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>SecurityDoor_EndClosing</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <gameSpeedRange>1~2</gameSpeedRange>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Building/SecurityDoor/EndClosing/Normal</clipFolderPath>
          </li>
        </grains>
        <volumeRange>20</volumeRange>
        <pitchRange>0.85~1.15</pitchRange>
      </li>
      <li>
        <gameSpeedRange>3~99</gameSpeedRange>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Building/SecurityDoor/EndClosing/Fast</clipFolderPath>
          </li>
        </grains>
        <volumeRange>10</volumeRange>
        <pitchRange>0.85~1.15</pitchRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>VoidNode_Ambient</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/VoidNode/VoidNode_Ambience_Loop_A</clipPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <distRange>5~50</distRange>
        <muteWhenPaused>true</muteWhenPaused>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>VoidNode_Explode</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <onCamera>True</onCamera>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/VoidNode/Void_Node_Exploding_01</clipPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>PitBurrow_Collapse</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipPath>Building/PitBurrow/Collapse</clipPath>
          </li>
        </grains>
        <volumeRange>30~40</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>VoidStructure_Emerging</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/VoidStructure/Emerge/VoidStructure_Emerging_Loop_A</clipPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <distRange>5~50</distRange>
        <muteWhenPaused>true</muteWhenPaused>
      </li>
    </subSounds>
  </SoundDef>
  
  <SoundDef>
    <defName>VoidStructure_Emerge</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/VoidStructure/Emerge/VoidStructure_Emerge_A</clipPath>
          </li>
        </grains>
        <volumeRange>60</volumeRange>
        <distRange>5~50</distRange>
        <muteWhenPaused>true</muteWhenPaused>
      </li>
    </subSounds>
  </SoundDef>
  
  <SoundDef>
    <defName>VoidStructure_EmergeSpark</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Building/VoidStructure/Emerge/Spark</clipFolderPath>
          </li>
        </grains>
        <volumeRange>40</volumeRange>
        <distRange>5~50</distRange>
        <muteWhenPaused>true</muteWhenPaused>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>VoidStructure_Activate</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/VoidStructure/VoidStructure_Activation_Oneshot_A</clipPath>
          </li>
        </grains>
        <volumeRange>40</volumeRange>
      </li>
    </subSounds>
  </SoundDef>
  
  <SoundDef>
    <defName>VoidStructure_AmbientPreActivate</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/VoidStructure/VoidStructure_BeforeActivation_Amb_Loop_A</clipPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <distRange>5~30</distRange>
        <muteWhenPaused>true</muteWhenPaused>
      </li>
    </subSounds>
  </SoundDef>
  
  <SoundDef>
    <defName>VoidStructure_AmbientPostActivate</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/VoidStructure/Void_Structure_AfterActivation_Amb_Loop_A</clipPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <distRange>5~30</distRange>
        <muteWhenPaused>true</muteWhenPaused>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>BioferriteHarvester_Ambient</defName>
    <sustain>True</sustain>
    <context>MapOnly</context>
    <priorityMode>PrioritizeNearest</priorityMode>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/BioferriteHarvester/Bioferrite_Harvester_Loop_A</clipPath>
          </li>
        </grains>
        <volumeRange>15</volumeRange>
        <distRange>5~20</distRange>
        <muteWhenPaused>true</muteWhenPaused>
      </li>
    </subSounds>
    <sustainStartSound>BioferriteHarvester_AmbientStart</sustainStartSound>
    <sustainStopSound>BioferriteHarvester_AmbientStop</sustainStopSound>
  </SoundDef>
  
  <SoundDef>
    <defName>BioferriteHarvester_AmbientStart</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/BioferriteHarvester/Bioferrite_Harvester_Start_A</clipPath>
          </li>
        </grains>
        <volumeRange>20</volumeRange>
        <distRange>5~20</distRange>
      </li>
    </subSounds>
  </SoundDef>
  <SoundDef>
    <defName>BioferriteHarvester_AmbientStop</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/BioferriteHarvester/Bioferrite_Harvester_Stop_A</clipPath>
          </li>
        </grains>
        <volumeRange>20</volumeRange>
        <distRange>5~20</distRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>PitGateOpen</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Building/PitGate/Open</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
      </li>
    </subSounds>
  </SoundDef>


  <SoundDef>
    <defName>EntityChainHigh</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipPath>Building/HoldingPlatform/High</clipPath>
          </li>
        </grains>
        <distRange>10~25</distRange>
        <volumeRange>14</volumeRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>EntityChainLow</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipPath>Building/HoldingPlatform/Low</clipPath>
          </li>
        </grains>
        <distRange>10~25</distRange>
        <volumeRange>14</volumeRange>
      </li>
    </subSounds>
  </SoundDef>
  
</Defs>