﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <DutyDef>
    <defName>Marry<PERSON>awn</defName>
    <socialModeMax>Off</socialModeMax>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <!-- Goto my spot -->
        <li Class="JobGiver_GotoTravelDestination">
          <exactCell>true</exactCell>
          <locomotionUrgency>Jog</locomotionUrgency>
        </li>
        
        <!-- Arrived -->
        <li Class="ThinkNode_ConditionalAtDutyLocation">
          <subNodes>
            <!-- Try to marry adjacent fiance -->
            <li Class="JobGiver_MarryAdjacentPawn" />
            
            <!-- Wait for the fiance to arrive -->
            <li Class="JobGiver_Idle">
              <ticks>50</ticks>
            </li>
          </subNodes>
        </li>
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>BestowingCeremony_MoveInPlace</defName>
    <socialModeMax>Off</socialModeMax>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <!-- Goto my spot -->
        <li Class="JobGiver_GotoTravelDestination">
          <exactCell>true</exactCell>
          <locomotionUrgency>Jog</locomotionUrgency>
        </li>
        
        <!-- Arrived -->
        <li Class="ThinkNode_ConditionalAtDutyLocation">
          <subNodes>
            <li Class="JobGiver_Idle">
              <ticks>60</ticks>
            </li>
          </subNodes>
        </li>
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>Spectate</defName>
    <hook>HighPriority</hook>
    <socialModeMax>Off</socialModeMax>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <!-- Spectate -->
        <li Class="JobGiver_SpectateDutySpectateRect" />
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>SpectateCircle</defName>
    <hook>HighPriority</hook>
    <socialModeMax>Off</socialModeMax>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="JobGiver_SpectateInCircleDuty" />
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>SpectateSocial</defName>
    <hook>HighPriority</hook>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <!-- Spectate -->
        <li Class="JobGiver_SpectateInCircleDuty" />
      </subNodes>
    </thinkNode>
  </DutyDef>
  
  <DutyDef>
    <defName>Party</defName>
    <label>party</label>
    <hook>MediumPriority</hook>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <li Class="ThinkNode_ConditionalInGatheringArea">
          <subNodes>
            <!-- Eat -->
            <li Class="ThinkNode_ConditionalRandom">
              <chance>0.05</chance>
              <subNodes>
                <li Class="JobGiver_EatInGatheringArea" />
              </subNodes>
            </li>
            
            <!-- Stand where you are and be socially active -->
            <li Class="ThinkNode_ConditionalRandom">
              <chance>0.35</chance>
              <subNodes>
                <li Class="JobGiver_StandAndBeSociallyActive">
                  <ticksRange>350~750</ticksRange>
                </li>
              </subNodes>
            </li>
            
            <!-- Get joy -->
            <li Class="ThinkNode_ConditionalRandom">
              <chance>0.1</chance>
              <subNodes>
                <li Class="JobGiver_GetJoyInGatheringArea" />
              </subNodes>
            </li>
            
            <!-- Wander -->
            <li Class="JobGiver_WanderInGatheringArea">
              <ticksBetweenWandersRange>50~250</ticksBetweenWandersRange>
            </li>
          </subNodes>
        </li>
        
        <!-- Reach the party area -->
        <li Class="JobGiver_WanderInGatheringArea">
          <ticksBetweenWandersRange>50~250</ticksBetweenWandersRange>
          <locomotionUrgency>Jog</locomotionUrgency>
        </li>
      </subNodes>
    </thinkNode>
  </DutyDef>
  
  <DutyDef>
    <defName>GiveSpeech</defName>
    <hook>HighPriority</hook>
    <socialModeMax>Off</socialModeMax>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <!-- Goto my spot -->
        <li Class="JobGiver_GotoTravelDestination">
          <exactCell>true</exactCell>
          <locomotionUrgency>Jog</locomotionUrgency>
          <ritualTagOnArrival>Arrived</ritualTagOnArrival>
        </li>

        <!-- Arrived -->
        <li Class="ThinkNode_ConditionalAtDutyLocation">
          <subNodes>
            <li Class="JobGiver_GiveSpeech">
              <soundDefMale>Speech_Throne_Male</soundDefMale>
              <soundDefFemale>Speech_Throne_Female</soundDefFemale>
            </li>
          </subNodes>
        </li>
      </subNodes>
    </thinkNode>
  </DutyDef>
</Defs>
