﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingDef ParentName="PawnFlyerBase">
    <defName>PawnFlyer_Stun</defName>
    <pawnFlyer>
      <flightDurationMin>0.75</flightDurationMin>
      <flightSpeed>8</flightSpeed>
      <stunDurationTicksRange>60~180</stunDurationTicksRange>
    </pawnFlyer>
  </ThingDef>
  
  <ThingDef ParentName="PawnFlyerBase">
    <defName>PawnFlyer_ConsumeLeap</defName>
    <pawnFlyer>
      <heightFactor>0.5</heightFactor>
    </pawnFlyer>
  </ThingDef>

  <ThingDef ParentName="SkyfallerBase">
    <defName>WarpedObeliskIncoming</defName>
    <label>obelisk (incoming)</label>
    <graphicData>
      <texPath>Things/Building/ObeliskSkyfaller/ObeliskSkyfaller</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <shaderType>CutoutFlying</shaderType>
      <drawSize>(20, 20)</drawSize>
    </graphicData>
    <skyfaller>
      <movementType>ConstantSpeed</movementType>
      <shadow>Things/Skyfaller/SkyfallerShadowRectangle</shadow>
      <shadowSize>(2, 2)</shadowSize>
      <ticksToImpactRange>780~780</ticksToImpactRange>
      <anticipationSound>WarpedObelisk_Fall</anticipationSound>
      <anticipationSoundTicks>780</anticipationSoundTicks>
      <impactSound>WarpedObelisk_Impact</impactSound>
      <rotateGraphicTowardsDirection>true</rotateGraphicTowardsDirection>
      <motesPerCell>0</motesPerCell>
      <explosionRadius>4</explosionRadius>
      <explosionDamage>Bomb</explosionDamage>
    </skyfaller>
  </ThingDef>

  <ThingDef ParentName="PawnGroundSpawner">
    <defName>FleshbeastGroundSpawner</defName>
    <thingClass>FleshbeastGroundSpawner</thingClass>
  </ThingDef>

  <ThingDef ParentName="SkyfallerBase">
    <defName>NoctolithIncoming</defName>
    <label>noctolith (incoming)</label>
    <size>(2, 2)</size>
    <skyfaller>
      <movementType>ConstantSpeed</movementType>
      <shadow>Things/Skyfaller/SkyfallerShadowRectangle</shadow>
      <anticipationSound>DropPod_Fall</anticipationSound>
      <anticipationSoundTicks>100</anticipationSoundTicks>
      <impactSound>DropPod_Impact</impactSound>
      <rotateGraphicTowardsDirection>true</rotateGraphicTowardsDirection>
      <motesPerCell>0</motesPerCell>
      <shadowSize>(2, 2)</shadowSize>
      <explosionRadius>4</explosionRadius>
      <explosionDamage>Bomb</explosionDamage>
    </skyfaller>
  </ThingDef>

  <ThingDef ParentName="EtherealThingBase">
    <defName>IncineratorSpray</defName>
    <selectable>false</selectable>
    <tickerType>Normal</tickerType>
    <thingClass>IncineratorSpray</thingClass>
    <drawOffscreen>true</drawOffscreen>
  </ThingDef>

  <ThingDef ParentName="EtherealThingBase">
    <defName>DyingRevenant</defName>
    <selectable>false</selectable>
    <tickerType>Normal</tickerType>
    <thingClass>DyingRevenant</thingClass>
    <drawOffscreen>true</drawOffscreen>
  </ThingDef>

  <ThingDef ParentName="EtherealThingBase">
    <defName>MetalHellFloorCracks</defName>
    <thingClass>Thing</thingClass>
    <drawerType>MapMeshOnly</drawerType>
    <altitudeLayer>FloorEmplacement</altitudeLayer>
    <graphicData>
      <graphicClass>Graphic_Single</graphicClass>
      <texPath>Things/Misc/AnomalyEndGameGroundTextures/AnomalyEndGameFloorCracks</texPath>
      <drawSize>(40, 40)</drawSize>
      <shaderType>Transparent</shaderType>
      <color>(255, 255, 255, 70)</color>
    </graphicData>
  </ThingDef>
  
  <ThingDef ParentName="EtherealThingBase">
    <defName>MetalHellFloorMarkings</defName>
    <thingClass>Thing</thingClass>
    <drawerType>MapMeshOnly</drawerType>
    <altitudeLayer>FloorEmplacement</altitudeLayer>
    <graphicData>
      <graphicClass>Graphic_Single</graphicClass>
      <texPath>Things/Misc/AnomalyEndGameGroundTextures/AnomalyEndGameFloorMarkings</texPath>
      <drawSize>(40, 40)</drawSize>
      <shaderType>Transparent</shaderType>
      <color>(72, 58, 60, 120)</color>
    </graphicData>
  </ThingDef>


  <ThingDef ParentName="EtherealThingBase">
    <defName>VoidStructureIncoming</defName>
    <selectable>false</selectable>
    <tickerType>Normal</tickerType>
    <thingClass>ThingWithComps</thingClass>
    <drawOffscreen>true</drawOffscreen>
    <comps>
        <li Class="CompProperties_DestroyAfterEffect">
          <effecterDef>VoidStructureIncoming</effecterDef>
        </li>
    </comps>
  </ThingDef>

  <ThingDef ParentName="EtherealThingBase">
    <defName>MetalHellAmbience</defName>
    <selectable>false</selectable>
    <tickerType>Normal</tickerType>
    <thingClass>ThingWithComps</thingClass>
    <drawOffscreen>true</drawOffscreen>
    <comps>
      <li Class="CompProperties_Effecter">
        <effecterDef>MetalHellAmbience</effecterDef>
      </li>
    </comps>
  </ThingDef>
  
  <ThingDef ParentName="EtherealThingBase">
    <defName>VoidMonolithPyramind</defName>
    <thingClass>VoidMonolithPyramid</thingClass>
    <selectable>false</selectable>
    <tickerType>Normal</tickerType>
    <drawerType>RealtimeOnly</drawerType>
    <graphicData>
      <texPath>Things/Building/VoidMonolith/VoidMonolith_E_Pyramid</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>(5, 5)</drawSize>
    </graphicData>
    <comps>
      <li Class="CompProperties_Effecter">
        <effecterDef>MonolithGleamingPyramid_Sustained</effecterDef>
      </li>
    </comps>
  </ThingDef>
  
</Defs>
