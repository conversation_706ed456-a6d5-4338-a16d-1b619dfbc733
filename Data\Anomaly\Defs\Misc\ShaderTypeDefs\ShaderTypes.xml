<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <ShaderTypeDef>
    <defName>MoteHateChant</defName>
    <shaderPath>Map/MoteHateChant</shaderPath>
  </ShaderTypeDef>
  <ShaderTypeDef>
    <defName>MoteHateChantShadow</defName>
    <shaderPath>Map/MoteHateChantShadow</shaderPath>
  </ShaderTypeDef>
  <ShaderTypeDef>
    <defName>VoidNodeParallax</defName>
    <shaderPath>Map/VoidNodeParallax</shaderPath>
  </ShaderTypeDef>
  <ShaderTypeDef>
    <defName>Ember</defName>
    <shaderPath>Map/Ember</shaderPath>
  </ShaderTypeDef>
  <ShaderTypeDef>
    <defName>EmberGlow</defName>
    <shaderPath>Map/EmberGlow</shaderPath>
  </ShaderTypeDef>
  <ShaderTypeDef>
    <defName>TransparentColorLerp</defName>
    <shaderPath>Map/TransparentColorLerp</shaderPath>
  </ShaderTypeDef>
  <ShaderTypeDef>
    <defName>TransparentAnimatedColorLerp</defName>
    <shaderPath>Map/TransparentAnimatedColorLerp</shaderPath>
  </ShaderTypeDef>
  <ShaderTypeDef>
    <defName>BuildingSquashNStretch</defName>
    <shaderPath>Map/BuildingSquashNStretch</shaderPath>
  </ShaderTypeDef>
  <ShaderTypeDef>
    <defName>RevenantDeath</defName>
    <shaderPath>Map/RevenantDeath</shaderPath>
  </ShaderTypeDef>
  <ShaderTypeDef>
    <defName>Metalblood</defName>
    <shaderPath>Misc/Metalblood</shaderPath>
  </ShaderTypeDef>
  <ShaderTypeDef>
    <defName>Lightshaft</defName>
    <shaderPath>Map/Lightshaft</shaderPath>
  </ShaderTypeDef>
  <ShaderTypeDef>
    <defName>MoteMultiplyCircular</defName>
    <shaderPath>Map/MoteMultiplyCircular</shaderPath>
  </ShaderTypeDef>
  <ShaderTypeDef>
    <defName>HoraxianSnake</defName>
    <shaderPath>Map/HoraxianSnake</shaderPath>
  </ShaderTypeDef>
  <ShaderTypeDef>
    <defName>BioferriteHarvester</defName>
    <shaderPath>Map/BioferriteHarvester</shaderPath>
  </ShaderTypeDef>
  <ShaderTypeDef>
    <defName>MoteHeatDistortion</defName>
    <shaderPath>Map/MoteHeatDistortion</shaderPath>
  </ShaderTypeDef>
  <ShaderTypeDef>
    <defName>FerroBlob</defName>
    <shaderPath>Map/FerroBlob</shaderPath>
  </ShaderTypeDef>
  <ShaderTypeDef>
    <defName>FerroWave</defName>
    <shaderPath>Map/FerroWave</shaderPath>
  </ShaderTypeDef>
  <ShaderTypeDef>
    <defName>BuildingWorking</defName>
    <shaderPath>Map/BuildingWorking</shaderPath>
  </ShaderTypeDef>
  <ShaderTypeDef>
    <defName>GroundFog</defName>
    <shaderPath>Map/GroundFog</shaderPath>
  </ShaderTypeDef>
  <ShaderTypeDef>
    <defName>VoidNodeDistortion</defName>
    <shaderPath>Map/VoidNodeDistortion</shaderPath>
  </ShaderTypeDef>
  <ShaderTypeDef>
    <defName>MoteObeliskExplosionPulse</defName>
    <shaderPath>Map/MoteApocritonPulse</shaderPath>
  </ShaderTypeDef>
  <ShaderTypeDef>
    <defName>EndlessPit</defName>
    <shaderPath>Map/EndlessPit</shaderPath>
  </ShaderTypeDef>
</Defs>