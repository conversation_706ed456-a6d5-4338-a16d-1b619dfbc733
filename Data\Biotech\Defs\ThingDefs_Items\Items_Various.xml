<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingDef Name="GeneSetHolderBase" Abstract="True">
    <statBases>
      <MaxHitPoints>100</MaxHitPoints>
      <Mass>0.5</Mass>
      <Flammability>0</Flammability>
    </statBases>
    <thingCategories>
      <li>ItemsMisc</li>
    </thingCategories>
    <stackLimit>1</stackLimit>
    <tradeNeverGenerateStacked>true</tradeNeverGenerateStacked>
    <category>Item</category>
    <drawerType>MapMeshOnly</drawerType>
    <resourceReadoutPriority>Middle</resourceReadoutPriority>
    <selectable>true</selectable>
    <altitudeLayer>Item</altitudeLayer>
    <alwaysHaulable>true</alwaysHaulable>
    <rotatable>false</rotatable>
    <healthAffectsPrice>false</healthAffectsPrice>
    <pathCost>14</pathCost>
    <comps>
      <li Class="CompProperties_Forbiddable"/>
    </comps>
  </ThingDef>

  <ThingDef ParentName="GeneSetHolderBase">
    <defName>Genepack</defName>
    <label>genepack</label>
    <description>A small capsule containing a package of genetic information. To use a genepack, it must be placed in a gene bank near a gene assembler and combined into an implantable xenogerm.\n\nIf not kept in a powered gene bank, this genepack will slowly deteriorate.</description>
    <thingClass>Genepack</thingClass>
    <tickerType>Rare</tickerType>
    <useHitPoints>true</useHitPoints>
    <canDeteriorateUnspawned>true</canDeteriorateUnspawned>
    <deteriorateFromEnvironmentalEffects>false</deteriorateFromEnvironmentalEffects>
    <inspectorTabs>
      <li>ITab_Genes</li>
    </inspectorTabs>
    <graphicData>
      <texPath>Things/Item/Special/Genepack</texPath>
      <graphicClass>Graphic_Genepack</graphicClass>
      <drawSize>(0.75, 0.75)</drawSize>
    </graphicData>
    <statBases>
      <MarketValue>100</MarketValue>
      <DeteriorationRate>5</DeteriorationRate>
    </statBases>
    <descriptionHyperlinks>
      <ThingDef>Xenogerm</ThingDef>
      <ThingDef>GeneAssembler</ThingDef>
    </descriptionHyperlinks>
    <thingSetMakerTags>
      <li>RewardStandardMidFreq</li>
    </thingSetMakerTags>
  </ThingDef>

  <ThingDef ParentName="GeneSetHolderBase">
    <defName>Xenogerm</defName>
    <label>xenogerm</label>
    <description>A self-contained biological organ containing one or more implantable xenogenes.\n\nOnce implanted inside a host's body, the xenogerm modifies the host's genes, gene expression, and phenotypic development using viruses, hormones, bio-synthesized drugs, and possibly mechanites. Depending on the xenogerm, the host will develop any of a wide variety of exotic traits and abilities, transforming them into a different human xenotype.\n\nXenogerm implantation is a traumatic process. Once implanted with a xenogerm, a person will be bedridden for days as the transformation sets in.\n\nDuring storage and transport, xenogerms are kept safe in sealed containers.</description>
    <thingClass>Xenogerm</thingClass>
    <drawerType>MapMeshAndRealTime</drawerType>
    <inspectorTabs>
      <li>ITab_Genes</li>
    </inspectorTabs>
    <graphicData>
      <texPath>Things/Item/Special/Xenogerm</texPath>
      <graphicClass>Graphic_Single</graphicClass>
      <drawSize>(0.85, 0.85)</drawSize>
    </graphicData>
    <statBases>
      <MarketValue>100</MarketValue>
    </statBases>
    <descriptionHyperlinks>
      <ThingDef>Genepack</ThingDef>
      <ThingDef>GeneAssembler</ThingDef>
    </descriptionHyperlinks>
  </ThingDef>

  <ThingDef>
    <defName>ArchiteCapsule</defName>
    <label>archite capsule</label>
    <description>A small capsule containing archites - microscopic machines produced by a superintelligent archotech. These devices are capable of physical and psychic feats which seem impossible to human scientists. As with all archotechnology, no human can create one. We can only try to scavenge the ones that archotechs create for their own unfathomable reasons.\n\nThis capsule is useless by itself, but it can be spent at a gene assembler to create a new archite-powered xenogerm. This requires researching archogenetics and a genepack that contains an archite gene. Archite genepacks can be obtained from traders.</description>
    <thingClass>ThingWithComps</thingClass>
    <graphicData>
      <texPath>Things/Item/Resource/ArchiteCapsule</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <statBases>
      <MaxHitPoints>200</MaxHitPoints>
      <MarketValue>700</MarketValue>
      <Mass>0.5</Mass>
      <Flammability>0</Flammability>
    </statBases>
    <descriptionHyperlinks>
      <ThingDef>Xenogerm</ThingDef>
      <ThingDef>GeneAssembler</ThingDef>
    </descriptionHyperlinks>
    <thingCategories>
      <li>ItemsMisc</li>
    </thingCategories>
    <stackLimit>25</stackLimit>
    <category>Item</category>
    <drawerType>MapMeshOnly</drawerType>
    <resourceReadoutPriority>Middle</resourceReadoutPriority>
    <selectable>true</selectable>
    <altitudeLayer>Item</altitudeLayer>
    <alwaysHaulable>true</alwaysHaulable>
    <drawGUIOverlay>true</drawGUIOverlay>
    <rotatable>false</rotatable>
    <pathCost>14</pathCost>
    <thingSetMakerTags>
      <li>RewardStandardHighFreq</li>
    </thingSetMakerTags>
    <comps>
      <li Class="CompProperties_Forbiddable"/>
    </comps>
  </ThingDef>

  <ThingDef ParentName="ResourceBase">
    <defName>MechanoidTransponder</defName>
    <label>mechanoid transponder</label>
    <description>A transponder micro-organ that connects a mechanitor to a mechanoid, even over a great distance. Mechanitors are capable of controlling many mechanoids using a mix of psychic and electromagnetic signals.\n\nDecrypting the transponder will allow you to pinpoint the location of the last mechanitor who controlled the mechanoid.</description>
    <thingClass>ThingWithComps</thingClass>
    <graphicData>
      <texPath>Things/Item/Special/MechanitorComplexMap</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <category>Item</category>
    <thingCategories>
      <li>ItemsMisc</li>
    </thingCategories>
    <tradeability>None</tradeability>
    <selectable>true</selectable>
    <statBases>
      <Mass>0.1</Mass>
      <Flammability>0</Flammability>
      <MarketValue>200</MarketValue>
    </statBases>
    <comps>
      <li Class="CompProperties_Usable">
        <compClass>CompUseableDatacore</compClass>
        <useJob>ReadDatacore</useJob>
        <useLabel>Decrypt transponder</useLabel>
      </li>
      <li Class="CompProperties_UseEffectPlaySound">
        <soundOnUsed>DatacoreRead</soundOnUsed>
      </li>
      <li Class="CompProperties_UseEffectDestroySelf" />
      <li Class="CompProperties_UseEffectGiveQuest">
        <quest>MechanitorShip</quest>
      </li>
      <li Class="CompProperties_InspectString">
        <inspectString>Can be decrypted at a research bench.\nSelect a colonist and right-click this to decrypt it.</inspectString>
      </li>
    </comps>
  </ThingDef>

  <ThingDef>
    <defName>HumanOvum</defName>
    <label>ovum</label>
    <description>An extracted, unfertilized human ovum cell stored in a protective capsule. Once fertilized by a male, it becomes an embryo which can then be implanted into a human mother or growth vat.</description>
    <thingClass>HumanOvum</thingClass>
    <graphicData>
      <texPath>Things/Item/Special/Ovum</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <statBases>
      <MaxHitPoints>50</MaxHitPoints>
      <MarketValue>50</MarketValue>
      <Mass>0.5</Mass>
    </statBases>
    <thingCategories>
      <li>ItemsMisc</li>
    </thingCategories>
    <stackLimit>10</stackLimit>
    <category>Item</category>
    <alwaysHaulable>true</alwaysHaulable>
    <drawGUIOverlay>true</drawGUIOverlay>
    <useHitPoints>true</useHitPoints>
    <selectable>true</selectable>
    <rotatable>false</rotatable>
    <pathCost>14</pathCost>
    <comps>
      <li Class="CompProperties_HasPawnSources"/>
    </comps>
  </ThingDef>

  <ThingDef>
    <defName>HumanEmbryo</defName>
    <label>embryo</label>
    <description>A human embryo which can be implanted in a mother to create a pregnancy.</description>
    <thingClass>HumanEmbryo</thingClass>
    <graphicData>
      <texPath>Things/Item/Special/Embryo</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <statBases>
      <MaxHitPoints>50</MaxHitPoints>
      <MarketValue>50</MarketValue>
      <Mass>0.5</Mass>
    </statBases>
    <thingCategories>
      <li>ItemsMisc</li>
    </thingCategories>
    <category>Item</category>
    <alwaysHaulable>true</alwaysHaulable>
    <drawGUIOverlay>true</drawGUIOverlay>
    <useHitPoints>true</useHitPoints>
    <selectable>true</selectable>
    <rotatable>false</rotatable>
    <pathCost>14</pathCost>
    <comps>
      <li Class="CompProperties_HasPawnSources"/>
    </comps>
    <inspectorTabs>
      <li>ITab_Genes</li>
    </inspectorTabs>
  </ThingDef>

</Defs>