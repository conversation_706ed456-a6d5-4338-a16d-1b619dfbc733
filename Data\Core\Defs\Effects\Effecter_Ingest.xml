﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <EffecterDef>
    <defName>EatVegetarian</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <scale>0.5~0.7</scale>
        <ticksBetweenMotes>20</ticksBetweenMotes>
        <positionLerpFactor>0.35</positionLerpFactor>
        <moteDef>Mote_FoodBitVegetarian</moteDef>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>EatMeat</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <scale>0.25~0.4</scale>
        <ticksBetweenMotes>20</ticksBetweenMotes>
        <positionLerpFactor>0.35</positionLerpFactor>
        <moteDef>Mote_FoodBitMeat</moteDef>
      </li>
    </children>
  </EffecterDef>

  <EffecterDef>
    <defName>Smoke_Joint</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <moteDef>Mote_SmokeJoint</moteDef>
        <ticksBetweenMotes>30</ticksBetweenMotes>
        <scale>0.35~0.45</scale>
        <absoluteAngle>true</absoluteAngle>
        <angle>30~70</angle>
        <speed>0.18~0.3</speed>
      </li>
    </children>
  </EffecterDef>
  
  <ThingDef ParentName="MoteBase">
    <defName>Mote_SmokeJoint</defName>
    <thingClass>MoteThrown</thingClass>
    <graphicData>
      <texPath>Things/Mote/Smoke</texPath>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.25</fadeInTime>
      <solidTime>2.75</solidTime>
      <fadeOutTime>4.7</fadeOutTime>
      <growthRate>0.47</growthRate>
      <acceleration>(0,0,0.0014)</acceleration>
    </mote>
  </ThingDef>

  <EffecterDef>
    <defName>Smoke_Flake</defName>
    <children>
      <li>
        <subEffecterClass>SubEffecter_SprayerContinuous</subEffecterClass>
        <moteDef>Mote_SmokeFlake</moteDef>
        <ticksBetweenMotes>30</ticksBetweenMotes>
        <scale>0.40~0.50</scale>
        <absoluteAngle>true</absoluteAngle>
        <angle>30~70</angle>
        <speed>0.18~0.3</speed>
      </li>
    </children>
  </EffecterDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_SmokeFlake</defName>
    <thingClass>MoteThrown</thingClass>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <graphicData>
      <texPath>Things/Mote/Smoke</texPath>
      <color>(200, 225, 255)</color>
    </graphicData>
    <mote>
      <fadeInTime>0.25</fadeInTime>
      <solidTime>2.75</solidTime>
      <fadeOutTime>4.7</fadeOutTime>
      <growthRate>0.54</growthRate>
      <acceleration>(0,0,0.0014)</acceleration>
    </mote>
  </ThingDef>
  
</Defs>
