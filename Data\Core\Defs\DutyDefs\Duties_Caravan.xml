﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <DutyDef>
    <defName>PrepareCaravan_GatherItems</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <!-- Basic needs -->
        <li Class="ThinkNode_PrioritySorter">
          <subNodes>
            <li Class="JobGiver_GetFood">
              <minCategory>Hungry</minCategory>
            </li>
            <li Class="JobGiver_GetRest">
              <minCategory>VeryTired</minCategory>
            </li>
            <li Class="JobGiver_SatisfyChemicalNeed" />
            <li Class="JobGiver_SatifyChemicalDependency" MayRequire="Ludeon.RimWorld.Biotech" />
          </subNodes>
        </li>

        <!-- Drop items if you're assigned to unload everything -->
        <!-- (before loading new items!) -->
        <li Class="JobGiver_UnloadYourInventory" />

        <!-- Unload carriers if they're assigned to unload everything -->
        <!-- (before loading new items!) -->
        <li Class="JobGiver_UnloadMyLordCarriers" />

        <!-- Gather items -->
        <li Class="JobGiver_PrepareCaravan_GatherItems"/>
        
        <!-- Wander -->
        <li Class="ThinkNode_Tagger">
          <tagToGive>WaitingForOthersToFinishGatheringItems</tagToGive>
          <subNodes>
            <li Class="JobGiver_WanderAnywhere"/>
          </subNodes>
        </li>
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>PrepareCaravan_Wait</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <!-- Basic needs -->
        <li Class="ThinkNode_Subtree">
          <treeDef>SatisfyVeryUrgentNeeds</treeDef>
        </li>
                
        <!-- Follow colonists if you're a pack animal -->
        <li Class="ThinkNode_ConditionalPackAnimalHasColonistToFollowWhilePacking">
          <subNodes>
            <li Class="JobGiver_PackAnimalFollowColonists" />

            <!-- Wander anywhere if you're close enough -->
            <li Class="JobGiver_WanderAnywhere"/>
          </subNodes>
        </li>

        <!-- Wander near duty location -->
        <li Class="ThinkNode_ConditionalHasDutyTarget">
          <subNodes>
            <li Class="JobGiver_WanderNearDutyLocation">
              <wanderRadius>5</wanderRadius>
              <expiryInterval>120</expiryInterval>
            </li>
          </subNodes>
        </li>
        
        <!-- Wander anywhere -->
        <li Class="JobGiver_WanderAnywhere"/>
      </subNodes>
    </thinkNode>
  </DutyDef>

  <!-- Gather animals to meeting spot. Used by both gatherers and animals that need to be gathered -->
  <DutyDef>
    <defName>PrepareCaravan_GatherAnimals</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <!-- Basic needs -->
        <li Class="ThinkNode_Subtree">
          <treeDef>SatisfyVeryUrgentNeeds</treeDef>
        </li>

        <!-- Colonist -->
        <li Class="ThinkNode_ConditionalColonist">
          <subNodes>
            <!-- Gather pawns -->
            <li Class="JobGiver_PrepareCaravan_GatherPawns"/>
            <li Class="JobGiver_GotoTravelDestination">
              <locomotionUrgency>Jog</locomotionUrgency>
            </li>
          </subNodes>
        </li>

        <!-- Wander anywhere -->
        <li Class="JobGiver_WanderAnywhere"/>
      </subNodes>
    </thinkNode>
  </DutyDef>

  <!-- Collect animals before leaving. Used by both collectors and animals that need to be collected -->
  <DutyDef>
    <defName>PrepareCaravan_CollectAnimals</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <!-- Basic needs -->
        <li Class="ThinkNode_Subtree">
          <treeDef>SatisfyVeryUrgentNeeds</treeDef>
        </li>

        <!-- Colonist -->
        <li Class="ThinkNode_ConditionalColonist">
          <subNodes>
            <!-- Gather pawns -->
            <li Class="JobGiver_PrepareCaravan_CollectPawns"/>
            <li Class="JobGiver_GotoTravelDestination">
              <locomotionUrgency>Jog</locomotionUrgency>
            </li>
          </subNodes>
        </li>

        <!-- Wander anywhere -->
        <li Class="JobGiver_WanderAnywhere"/>
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>PrepareCaravan_GatherDownedPawns</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <!-- Basic needs -->
        <li Class="ThinkNode_Subtree">
          <treeDef>SatisfyVeryUrgentNeeds</treeDef>
        </li>
      
        <!-- Gather downed pawns -->
        <li Class="JobGiver_PrepareCaravan_GatherDownedPawns"/>
        
        <!-- Wander around destination -->
        <li Class="JobGiver_GotoTravelDestination">
          <destinationFocusIndex>2</destinationFocusIndex>
        </li>
      </subNodes>
    </thinkNode>
  </DutyDef>

  <DutyDef>
    <defName>PrepareCaravan_Pause</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <!-- Colonist -->
        <li Class="ThinkNode_ConditionalColonist">
          <subNodes>
            <!-- Just do the normal work -->
            <li Class="ThinkNode_Subtree">
              <treeDef>MainColonistBehaviorCore</treeDef>
            </li>
          </subNodes>
        </li>
        
        <!-- Non-colonist -->
        <li Class="ThinkNode_ConditionalColonist">
          <invert>true</invert>
          <subNodes>
            <!-- Basic needs -->
            <li Class="ThinkNode_Subtree">
              <treeDef>SatisfyVeryUrgentNeeds</treeDef>
            </li>
          </subNodes>
        </li>
        
        <!-- Wander in prison cell if prisoner -->
        <li Class="ThinkNode_ConditionalPrisonerInPrisonCell">
          <subNodes>
            <li Class="JobGiver_WanderCurrentRoom"/>
          </subNodes>
        </li>
        
        <!-- Wander anywhere -->
        <li Class="JobGiver_WanderAnywhere"/>
      </subNodes>
    </thinkNode>
  </DutyDef>

  <!-- Return animals to a pen. Used by both ropers and ropees. -->
  <DutyDef>
    <defName>ReturnedCaravan_PenAnimals</defName>
    <thinkNode Class="ThinkNode_Priority">
      <subNodes>
        <!-- Basic needs -->
        <li Class="ThinkNode_Subtree">
          <treeDef>SatisfyVeryUrgentNeeds</treeDef>
        </li>

        <!-- Colonist -->
        <li Class="ThinkNode_ConditionalColonist">
          <subNodes>
            <!-- return animals -->
            <li Class="JobGiver_ReturnedCaravan_PenAnimals"/>
          </subNodes>
        </li>

        <!-- Wander anywhere -->
        <li Class="JobGiver_WanderAnywhere"/>
      </subNodes>
    </thinkNode>
  </DutyDef>

</Defs>