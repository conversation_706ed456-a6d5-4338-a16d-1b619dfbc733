﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <InteractionDef>
    <defName>SanguophageChat</defName>
    <label>sanguophage chat</label>
    <workerClass>InteractionWorker_SanguophageTalk</workerClass>
    <symbol>Things/Mote/SpeechSymbols/Chitchat</symbol>
    <logRulesInitiator>
      <rulesStrings>
        <li>r_logentry->[INITIATOR_nameDef] and [RECIPIENT_nameDef] [talkedabout] [subject].</li>
        <li>r_logentry->[INITIATOR_nameDef] [talkedabout] [subject] with [RECIPIENT_nameDef].</li>

        <li>talkedabout->spoke about</li>
        <li>talkedabout->talked about</li>
        <li>talkedabout->whispered about</li>
        <li>talkedabout(p=0.5)->spoke [adverb] about</li> 
        <li>talkedabout(p=0.5)->talked [adverb] about</li>
        <li>talkedabout(p=0.5)->whispered [adverb] about</li>
        <li>talkedabout->shared a word about</li>
        <li>talkedabout->exchanged thoughts about</li>
        <li>talkedabout->exchanged ideas about</li>
        <li>talkedabout->expressed shared interest in</li>
        <li>talkedabout->debated about</li>
        <li>talkedabout->argued about</li>
        <li>talkedabout->shared stories of</li>

        <li>adverb->amicably</li>
        <li>adverb->quietly</li>
        <li>adverb->softly</li>
        <li>adverb->disgustedly</li>
        <li>adverb->bitterly</li>
        <li>adverb->wearily</li>
        <li>adverb->thoughtfully</li>
        <li>adverb->soberly</li>
        <li>adverb->grimly</li>
        <li>adverb->solemnly</li>

        <li>subject(p=20)->plans for [planType]</li>
        <li>subject(p=0.1)->[TalkTopicHeavy]</li>
        <li>subject->hemogen</li>
        <li>subject->blood quality</li>
        <li>subject->cannibalism</li>
        <li>subject->deathlessness</li>
        <li>subject->entropy</li>
        <li>subject->eternity</li>
        <li>subject->the passage of time</li>
        <li>subject->life and death</li>
        <li>subject->deathrest</li>
        <li>subject->cheating death</li>
        <li>subject->thralls</li>
        <li>subject->humanity</li>
        <li>subject->fire</li>
        <li>subject->burning</li>
        <li>subject->pain</li>
        <li>subject->agony</li>
        <li>subject->beheading</li>
        <li>subject->flaying enemies</li>
        <li>subject->torture</li>
        <li>subject->archites</li>
        <li>subject->genetics</li>
        <li>subject->genes</li>
        <li>subject->xenotypes</li>
        <li>subject->sanguophages</li>
        <li>subject->hussars</li>
        <li>subject->impids</li>
        <li>subject->dirtmoles</li>
        <li>subject->genies</li>
        <li>subject->neanderthals</li>
        <li>subject->pigskins</li>
        <li>subject->wasters</li>
        <li>subject->deep history</li>
        <li>subject->the history of the future</li>

        <li>planType->domination</li>
        <li>planType->escape</li>
        <li>planType->blending in</li>
        <li>planType->wielding influence</li>
        <li>planType->collecting hemogen</li>
        <li>planType->mastering technology</li>
        <li>planType->spreading to other worlds</li>
        <li>planType->influencing humans</li>
        <li>planType->destroying sanguophage hunters</li>
        <li>planType->enjoying endless life</li>


      </rulesStrings>
    </logRulesInitiator>
  </InteractionDef>

  <InteractionDef>
    <defName>BabyPlay</defName>
    <label>play</label>
    <workerClass>InteractionWorker</workerClass>
    <ignoreTimeSinceLastInteraction>True</ignoreTimeSinceLastInteraction>
    <symbol>Things/Mote/Childcare/BabyPlay</symbol>
    <logRulesInitiator>
      <rulesStrings>
        <li>r_logentry->[INITIATOR_nameDef] made [RECIPIENT_nameDef] [reaction].</li>
        <li>r_logentry->[INITIATOR_nameDef] [interaction] [RECIPIENT_nameDef].</li>
        <li>r_logentry->[INITIATOR_nameDef] played [babyGame] with [RECIPIENT_nameDef].</li>

        <li>reaction->giggle</li>
        <li>reaction->smile</li>
        <li>reaction->squirm with joy</li>
        <li>reaction->laugh</li>
        <li>reaction->drool playfully</li>
        <li>reaction->gurgle</li>
        <li>reaction->squeal</li>
        <li>reaction->burp</li>
        <li>reaction->stare in fascination</li>

        <li>interaction->delighted</li>
        <li>interaction->played with</li>
        <li>interaction->entertained</li>
        <li>interaction->amused</li>
        <li>interaction->tickled</li>
        <li>interaction->nuzzled</li>
        <li>interaction->snuffled</li>
        <li>interaction->hugged</li>
        <li>interaction->tossed</li>
        <li>interaction->sang to</li>
        <li>interaction->told a story to</li>
        <li>interaction->played a magic trick on</li>

        <li>babyGame->hide-and-go-seek</li>
        <li>babyGame->peekaboo</li>
        <li>babyGame->find-the-finger</li>
        <li>babyGame->monkey-see-monkey-do</li>
        <li>babyGame->show-and-tell</li>
        <li>babyGame->fingerpuppets</li>

      </rulesStrings>
    </logRulesInitiator>
  </InteractionDef>

  <!--Child lesson taking/teaching-->

  <RulePackDef>
    <defName>LessonInteractionTeachingAdverbs</defName>
    <rulePack>
      <rulesStrings>
        <li>teach_adverb->calmly</li>
        <li>teach_adverb->animatedly</li>
        <li>teach_adverb->intently</li>
        <li>teach_adverb->quickly</li>
        <li>teach_adverb->lightheartedly</li>
        <li>teach_adverb->carefully</li>
      </rulesStrings>
    </rulePack>
  </RulePackDef>

  <RulePackDef>
    <defName>LessonInteractionTeachingVerbs</defName>
    <rulePack>
      <rulesStrings>
        <li>teach_verb->demonstrated</li>
        <li>teach_verb->explained</li>
        <li>teach_verb->showed</li>
        <li>teach_verb->taught</li>
      </rulesStrings>
    </rulePack>
  </RulePackDef>

  <RulePackDef>
    <defName>LessonInteractionCommonSituations</defName>
    <rulePack>
      <rulesStrings>
        <li>situation(p=2)->safely</li>
        <li>situation->at night</li>
        <li>situation->in the cold</li>
        <li>situation->while injured</li>
        <li>situation(p=0.2)->while drunk</li>
      </rulesStrings>
    </rulePack>
  </RulePackDef>

  <RulePackDef>
    <defName>LessonInteractionHowTo</defName>
    <rulePack>
      <include>
        <li>LessonInteractionTeachingAdverbs</li>
        <li>LessonInteractionTeachingVerbs</li>
      </include>
      <rulesStrings>
        <li>r_logentry(p=0.5)->[INITIATOR_nameDef] [teach_adverb] [teach_verb] how to [action] [article_tool] to [RECIPIENT_nameDef].</li>
        <li>r_logentry->[INITIATOR_nameDef] [teach_verb] how to [action] [article_tool] to [RECIPIENT_nameDef].</li>
      </rulesStrings>
    </rulePack>
  </RulePackDef>

  <RulePackDef>
    <defName>LessonInteractionTeachSubjectTo</defName>
    <rulePack>
      <include>
        <li>LessonInteractionTeachingAdverbs</li>
      </include>
      <rulesStrings>
        <li>r_logentry(p=0.25)->[INITIATOR_nameDef] [teach_adverb] [talk_verb] [subject] to [RECIPIENT_nameDef].</li>
        <li>r_logentry(p=0.5)->[INITIATOR_nameDef] [talk_verb] [subject] to [RECIPIENT_nameDef].</li>

        <li>talk_verb->talked about</li>
        <li>talk_verb->gave an overview of</li>
        <li>talk_verb->gave a summary of</li>
        <li>talk_verb->lectured about</li>
      </rulesStrings>
    </rulePack>
  </RulePackDef>

  <RulePackDef>
    <defName>LessonInteractionStudySubjectWith</defName>
    <rulePack>
      <include>
        <li>LessonInteractionTeachingAdverbs</li>
      </include>
      <rulesStrings>
        <li>r_logentry(p=0.25)->[INITIATOR_nameDef] [teach_adverb] [study_verb] [subject] with [RECIPIENT_nameDef].</li>
        <li>r_logentry(p=0.5)->[INITIATOR_nameDef] [study_verb] [subject] with [RECIPIENT_nameDef].</li>

        <li>study_verb->covered</li>
        <li>study_verb->went over</li>
        <li>study_verb->researched</li>
        <li>study_verb->studied</li>
      </rulesStrings>
    </rulePack>
  </RulePackDef>

  <RulePackDef>
    <defName>LessonInteractionQuiz</defName>
    <rulePack>
      <rulesStrings>
        <li>r_logentry(p=0.5)->[INITIATOR_nameDef] [teach_test] [RECIPIENT_nameDef] on [action_gerund] [article_tool] [situation].</li>

        <li>teach_test->quizzed</li>
        <li>teach_test->tested</li>
        <li>teach_test->grilled</li>
        <li>teach_test->cross-examined</li>
      </rulesStrings>
    </rulePack>
  </RulePackDef>

  <InteractionDef>
    <defName>LessonShooting</defName>
    <label>shooting lesson</label>
    <ignoreTimeSinceLastInteraction>True</ignoreTimeSinceLastInteraction>
    <symbol>Things/Mote/Childcare/Teaching</symbol>
    <logRulesInitiator>
      <include>
        <li>LessonInteractionHowTo</li>
        <li>LessonInteractionQuiz</li>
        <li>LessonInteractionCommonSituations</li>
      </include>
      <rulesStrings>
        <li>article_tool->a rifle</li>
        <li>article_tool->a pistol</li>
        <li>article_tool->a shotgun</li>
        <li>article_tool->a rocket launcher</li>
        <li>article_tool->an SMG</li>
        <li>article_tool->a minigun</li>
        <li>article_tool->a short bow</li>
        <li>article_tool->a recurve bow</li>
        <li>article_tool->a greatbow</li>

        <li>action->aim</li>
        <li>action->shoot</li>
        <li>action->care for</li>
        <li>action->reload</li>

        <li>action_gerund->aiming</li>
        <li>action_gerund->shooting</li>
        <li>action_gerund->caring for</li>
        <li>action_gerund->reloading</li>
      </rulesStrings>
    </logRulesInitiator>
  </InteractionDef>

  <InteractionDef>
    <defName>LessonMelee</defName>
    <label>melee lesson</label>
    <ignoreTimeSinceLastInteraction>True</ignoreTimeSinceLastInteraction>
    <symbol>Things/Mote/Childcare/Teaching</symbol>
    <logRulesInitiator>
      <include>
        <li>LessonInteractionHowTo</li>
        <li>LessonInteractionQuiz</li>
        <li>LessonInteractionCommonSituations</li>
      </include>
      <rulesStrings>
        <li>article_tool->an axe</li>
        <li>article_tool->a club</li>
        <li>article_tool->a sword</li>
        <li>article_tool->an elbow blade</li>
        <li>article_tool->a hand talon</li>
        <li>article_tool->a longsword</li>
        <li>article_tool->a monosword</li>
        <li>article_tool->a plasmasword</li>
        <li>article_tool->a spear</li>
        <li>article_tool->a mace</li>
        <li>article_tool->a warhammer</li>
        <li>article_tool->a fist</li>

        <li>action->parry</li>
        <li>action->fight with</li>
        <li>action->counter</li>
        <li>action->care for</li>
        <li>action->use</li>
        <li>action->swing</li>

        <li>action_gerund->parrying</li>
        <li>action_gerund->fighting with</li>
        <li>action_gerund->countering</li>
        <li>action_gerund->caring for</li>
        <li>action_gerund->using</li>
        <li>action_gerund->swinging</li>
      </rulesStrings>
    </logRulesInitiator>
  </InteractionDef>

  <InteractionDef>
    <defName>LessonConstruction</defName>
    <label>construction lesson</label>
    <ignoreTimeSinceLastInteraction>True</ignoreTimeSinceLastInteraction>
    <symbol>Things/Mote/Childcare/Teaching</symbol>
    <logRulesInitiator>
      <include>
        <li>LessonInteractionHowTo</li>
        <li>LessonInteractionQuiz</li>
        <li>LessonInteractionCommonSituations</li>
        <li>LessonInteractionTeachSubjectTo</li>
        <li>LessonInteractionStudySubjectWith</li>
      </include>
      <rulesStrings>
        <li>article_tool->a hammer</li>
        <li>article_tool->a table saw</li>
        <li>article_tool->a ruler</li>
        <li>article_tool->a ladder</li>
        <li>article_tool->a level</li>

        <li>action->use</li>
        <li>action->work with</li>
        <li>action->inspect</li>

        <li>action_gerund->using</li>
        <li>action_gerund->working with</li>
        <li>action_gerund->inspecting</li>

        <li>subject->architecture</li>
        <li>subject->construction techniques</li>
        <li>subject->deconstruction techniques</li>
        <li>subject->building materials</li>
        <li>subject->modeling software</li>
        <li>subject->blueprints</li>
      </rulesStrings>
    </logRulesInitiator>
  </InteractionDef>

  <InteractionDef>
    <defName>LessonMining</defName>
    <label>mining lesson</label>
    <ignoreTimeSinceLastInteraction>True</ignoreTimeSinceLastInteraction>
    <symbol>Things/Mote/Childcare/Teaching</symbol>
    <logRulesInitiator>
      <include>
        <li>LessonInteractionHowTo</li>
        <li>LessonInteractionQuiz</li>
        <li>LessonInteractionCommonSituations</li>
        <li>LessonInteractionTeachSubjectTo</li>
        <li>LessonInteractionStudySubjectWith</li>
      </include>
      <rulesStrings>
        <li>article_tool->a pickaxe</li>
        <li>article_tool->a deep drill</li>
        <li>article_tool->a shovel</li>
        <li>article_tool->a long-range mineral scanner</li>
        <li>article_tool->a ground-penetrating scanner</li>

        <li>action->use</li>
        <li>action->mine with</li>
        <li>action->work with</li>
        <li>action->inspect</li>

        <li>action_gerund->using</li>
        <li>action_gerund->mining with</li>
        <li>action_gerund->working with</li>
        <li>action_gerund->inspecting</li>

        <li>subject->mineral veins</li>
        <li>subject->precious gemstones</li>
        <li>subject->geology</li>
        <li>subject->tunneling</li>
        <li>subject->sediment analysis</li>
        <li>subject->cave-dwelling insects</li>

        <li>situation(p=2)->underground</li>
      </rulesStrings>
    </logRulesInitiator>
  </InteractionDef>

  <InteractionDef>
    <defName>LessonCooking</defName>
    <label>cooking lesson</label>
    <ignoreTimeSinceLastInteraction>True</ignoreTimeSinceLastInteraction>
    <symbol>Things/Mote/Childcare/Teaching</symbol>
    <logRulesInitiator>
      <include>
        <li>LessonInteractionHowTo</li>
        <li>LessonInteractionQuiz</li>
        <li>LessonInteractionTeachSubjectTo</li>
        <li>LessonInteractionStudySubjectWith</li>
      </include>
      <rulesStrings>
        <li>article_tool->a stove</li>
        <li>article_tool->a spatula</li>
        <li>article_tool->an oven</li>
        <li>article_tool->a saucepan</li>
        <li>article_tool->a nutrient paste dispenser</li>
        <li>article_tool->a butcher table</li>

        <li>action->clean</li>
        <li>action->cook with</li>

        <li>action_gerund->cleaning</li>
        <li>action_gerund->cooking with</li>

        <li>subject->spices</li>
        <li>subject->packaged survival meals</li>
        <li>subject->egg varieties</li>
        <li>subject->varieties of meats</li>
        <li>subject->flavors</li>
        <li>subject->recipes</li>

        <li>situation->while caravaning</li>
        <li>situation->blindfolded</li>
        <li>situation->in a hurry</li>
        <li>situation->safely</li>
      </rulesStrings>
    </logRulesInitiator>
  </InteractionDef>

  <InteractionDef>
    <defName>LessonPlants</defName>
    <label>gardening lesson</label>
    <ignoreTimeSinceLastInteraction>True</ignoreTimeSinceLastInteraction>
    <symbol>Things/Mote/Childcare/Teaching</symbol>
    <logRulesInitiator>
      <include>
        <li>LessonInteractionHowTo</li>
        <li>LessonInteractionQuiz</li>
        <li>LessonInteractionCommonSituations</li>
        <li>LessonInteractionTeachSubjectTo</li>
        <li>LessonInteractionStudySubjectWith</li>
      </include>
      <rulesStrings>
        <li>article_tool->a sun lamp</li>
        <li>article_tool->a hydroponics basin</li>
        <li>article_tool->a sprinkler</li>
        <li>article_tool->a shovel</li>
        <li>article_tool->pruning shears</li>

        <li>action->garden with</li>
        <li>action->use</li>
        <li>action->inspect</li>

        <li>action_gerund->using</li>
        <li>action_gerund->gardening with</li>
        <li>action_gerund->inspecting</li>

        <li>subject->types of soil</li>
        <li>subject->pesticides</li>
        <li>subject->irrigation techniques</li>
        <li>subject->varieties of cacti</li>
        <li>subject->anima trees</li>
        <li>subject->gauranlen trees</li>
      </rulesStrings>
    </logRulesInitiator>
  </InteractionDef>

  <InteractionDef>
    <defName>LessonAnimals</defName>
    <label>animal lesson</label>
    <ignoreTimeSinceLastInteraction>True</ignoreTimeSinceLastInteraction>
    <symbol>Things/Mote/Childcare/Teaching</symbol>
    <logRulesInitiator>
      <include>
        <li>LessonInteractionTeachSubjectTo</li>
        <li>LessonInteractionStudySubjectWith</li>
        <li>LessonInteractionTeachingVerbs</li>
      </include>
      <rulesStrings>
        <li>r_logentry(p=4)->[INITIATOR_nameDef] [teach_verb] how to [animal_verb] [article_animal] to [RECIPIENT_nameDef].</li>

        <li>article_animal->a megasloth</li>
        <li>article_animal->a megaspider</li>
        <li>article_animal->a thrumbo</li>
        <li>article_animal->a boomrat</li>
        <li>article_animal->a chinchilla</li>
        <li>article_animal->a guinea pig</li>
        <li>article_animal->a cobra</li>
        <li>article_animal->an emu</li>
        <li>article_animal->an iguana</li>
        <li>article_animal->a raccoon</li>
        <li>article_animal->a warg</li>
        <li>article_animal->a yak</li>
        <li>article_animal->a squirrel</li>
        <li>article_animal->a muffalo</li>

        <li>animal_verb->tame</li>
        <li>animal_verb->feed</li>
        <li>animal_verb->calm</li>
        <li>animal_verb->spook</li>
        <li>animal_verb->gain the trust of</li>

        <li>subject->animal psychology</li>
        <li>subject->taming techniques</li>
        <li>subject->dangerous wildlife</li>
        <li>subject->reptiles</li>
        <li>subject->scaria</li>
        <li>subject->kibble</li>
      </rulesStrings>
    </logRulesInitiator>
  </InteractionDef>

  <InteractionDef>
    <defName>LessonCrafting</defName>
    <label>crafting lesson</label>
    <ignoreTimeSinceLastInteraction>True</ignoreTimeSinceLastInteraction>
    <symbol>Things/Mote/Childcare/Teaching</symbol>
    <logRulesInitiator>
      <include>
        <li>LessonInteractionHowTo</li>
        <li>LessonInteractionQuiz</li>
        <li>LessonInteractionTeachSubjectTo</li>
        <li>LessonInteractionStudySubjectWith</li>
      </include>
      <rulesStrings>
        <li>article_tool->a screwdriver</li>
        <li>article_tool->a pair of pliers</li>
        <li>article_tool->a circuit board</li>
        <li>article_tool->a tailor bench</li>
        <li>article_tool->a machining table</li>
        <li>article_tool->a fabricator</li>

        <li>action->use</li>
        <li>action->work with</li>
        <li>action->inspect</li>

        <li>action_gerund->using</li>
        <li>action_gerund->working with</li>
        <li>action_gerund->inspecting</li>

        <li>subject->organizing tools</li>
        <li>subject->tinkering accomplishments</li>
        <li>subject->gadgets</li>
        <li>subject->fabric varieties</li>
        <li>subject->cloth weaving techniques</li>
        <li>subject->plate armor</li>
        <li>subject->utility belts</li>
        <li>subject->bionics</li>

        <li>situation->safely</li>
        <li>situation->without instructions</li>
        <li>situation->by candlelight</li>
        <li>situation(p=0.1)->while drunk</li>
      </rulesStrings>
    </logRulesInitiator>
  </InteractionDef>

  <InteractionDef>
    <defName>LessonArtistic</defName>
    <label>art lesson</label>
    <ignoreTimeSinceLastInteraction>True</ignoreTimeSinceLastInteraction>
    <symbol>Things/Mote/Childcare/Teaching</symbol>
    <logRulesInitiator>
      <include>
        <li>LessonInteractionHowTo</li>
        <li>LessonInteractionTeachSubjectTo</li>
        <li>LessonInteractionStudySubjectWith</li>
      </include>
      <rulesStrings>
        <li>article_tool->a paint brush</li>
        <li>article_tool->a charcoal pencil</li>
        <li>article_tool->a chisel</li>
        <li>article_tool->a canvas</li>
        <li>article_tool->a lump of clay</li>

        <li>action->make art with</li>
        <li>action->use</li>
        <li>action->express one's self with</li>

        <li>action_gerund->making art with</li>
        <li>action_gerund->using</li>
        <li>action_gerund->expressing one's self with</li>

        <li>subject->sculpture materials</li>
        <li>subject->famous artists</li>
        <li>subject->famous paintings</li>
        <li>subject->glitterworld galleries</li>
        <li>subject->portraiture</li>
        <li>subject->abstract art</li>
        <li>subject->mixing paints</li>
      </rulesStrings>
    </logRulesInitiator>
  </InteractionDef>

  <InteractionDef>
    <defName>LessonMedicine</defName>
    <label>medical lesson</label>
    <ignoreTimeSinceLastInteraction>True</ignoreTimeSinceLastInteraction>
    <symbol>Things/Mote/Childcare/Teaching</symbol>
    <logRulesInitiator>
      <include>
        <li>LessonInteractionHowTo</li>
        <li>LessonInteractionQuiz</li>
        <li>LessonInteractionCommonSituations</li>
        <li>LessonInteractionTeachSubjectTo</li>
        <li>LessonInteractionStudySubjectWith</li>
      </include>
      <rulesStrings>
        <li>r_logentry(p=5)->[INITIATOR_nameDef] [teach_verb] how to [medical_verb] [hediff] to [RECIPIENT_nameDef].</li>

        <li>article_tool->a medicine pack</li>
        <li>article_tool->a bandage</li>
        <li>article_tool->a syringe</li>
        <li>article_tool->a healroot plant</li>
        <li>article_tool->a vitals monitor</li>
        <li>article_tool->a scalpel</li>
        <li>article_tool->a healer mech serum dose</li>
        <li>article_tool->a painstopper</li>

        <li>action->use</li>
        <li>action->inspect</li>

        <li>action_gerund->using</li>
        <li>action_gerund->inspecting</li>

        <li>hediff->blood loss</li>
        <li>hediff->a heart attack</li>
        <li>hediff->heatstroke</li>
        <li>hediff->food poisoning</li>
        <li>hediff->carcinoma</li>
        <li>hediff->gut worms</li>
        <li>hediff->muscle parasites</li>
        <li>hediff->paralytic abasia</li>
        <li>hediff->malaria</li>
        <li>hediff->the flu</li>
        <li>hediff->cirrhosis</li>

        <li>medical_verb->diagnose</li>
        <li>medical_verb->triage</li>
        <li>medical_verb->treat</li>

        <li>subject->anesthesia dosing</li>
        <li>subject->types of cancer</li>
        <li>subject->surgical procedures</li>
        <li>subject->bedside manner</li>
        <li>subject->sterilization methods</li>
      </rulesStrings>
    </logRulesInitiator>
  </InteractionDef>

  <InteractionDef>
    <defName>LessonSocial</defName>
    <label>social lesson</label>
    <ignoreTimeSinceLastInteraction>True</ignoreTimeSinceLastInteraction>
    <symbol>Things/Mote/Childcare/Teaching</symbol>
    <logRulesInitiator>
      <include>
        <li>LessonInteractionTeachSubjectTo</li>
        <li>LessonInteractionStudySubjectWith</li>
      </include>
      <rulesStrings>
        <li>subject->public speaking</li>
        <li>subject->faction relations</li>
        <li>subject->recruiting prisoners</li>
        <li>subject->haggling techniques</li>
        <li>subject->maintaining relationships</li>
        <li>subject->comms console etiquette</li>
        <li>subject->boosting morale</li>
        <li>subject->naughty words</li>
        <li>subject->sign language</li>
      </rulesStrings>
    </logRulesInitiator>
  </InteractionDef>

  <InteractionDef>
    <defName>LessonIntellectual</defName>
    <label>intellectual lesson</label>
    <ignoreTimeSinceLastInteraction>True</ignoreTimeSinceLastInteraction>
    <symbol>Things/Mote/Childcare/Teaching</symbol>
    <logRulesInitiator>
      <include>
        <li>LessonInteractionTeachSubjectTo</li>
        <li>LessonInteractionStudySubjectWith</li>
      </include>
      <rulesStrings>
        <li>subject->genetics</li>
        <li>subject->techprints</li>
        <li>subject->multi-analyzer functions</li>
        <li>subject->philosophy</li>
        <li>subject->mathematics</li>
        <li>subject->biology</li>
        <li>subject->chemistry</li>
        <li>subject->archotech mysteries</li>
        <li>subject->starship reactors</li>
        <li>subject->the scientific method</li>
        <li>subject->seminal whitepapers</li>
        <li>subject->Johnson-Tanaka propulsion</li>
      </rulesStrings>
    </logRulesInitiator>
  </InteractionDef>

  <!--fallback for mods-->
  <InteractionDef>
    <defName>LessonGeneric</defName>
    <label>lesson</label>
    <ignoreTimeSinceLastInteraction>True</ignoreTimeSinceLastInteraction>
    <symbol>Things/Mote/Childcare/Teaching</symbol>
    <logRulesInitiator>
      <rulesStrings>
        <li>r_logentry->[INITIATOR_nameDef] taught a lesson to [RECIPIENT_nameDef]</li>
      </rulesStrings>
    </logRulesInitiator>
  </InteractionDef>
  
</Defs>