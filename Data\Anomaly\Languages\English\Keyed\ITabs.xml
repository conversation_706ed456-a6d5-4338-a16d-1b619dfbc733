﻿<?xml version="1.0" encoding="utf-8" ?>

<LanguageData>

  <!-- ITab_Entity -->
  <HoldingPlatformEscapeMTBDays>Escape interval</HoldingPlatformEscapeMTBDays>
  <HoldingPlatformEscapeMTBDaysDesc>The average time between escapes. This is affected by containment strength, the type of entity, the entity’s movement capability, and the duration of the entity's imprisonment. Some entities are also suppressed by cold.</HoldingPlatformEscapeMTBDaysDesc>
  <TabEntity>Entity</TabEntity>
  <StudyInterval>Study interval</StudyInterval>
  <StudyIntervalDesc>How often this entity can be studied.</StudyIntervalDesc>
  <EntityStudyMode_MaintainOnly>Maintain only</EntityStudyMode_MaintainOnly>
  <EntityStudyMode_MaintainOnlyDesc>Wardens will not perform special actions on this entity, but other interactions are still available.</EntityStudyMode_MaintainOnlyDesc>
  <EntityStudyMode_Study>Study</EntityStudyMode_Study>
  <EntityStudyMode_StudyDesc>Researchers will study the entity.</EntityStudyMode_StudyDesc>
  <EntityStudyMode_Release>Release</EntityStudyMode_Release>
  <EntityStudyMode_ReleaseDesc>A warden will release the entity from the holding platform. Entities may be hostile when released.</EntityStudyMode_ReleaseDesc>
  <EntityStudyMode_Execute>Execute</EntityStudyMode_Execute>
  <EntityStudyMode_ExecuteDesc>A warden will execute the restrained entity.</EntityStudyMode_ExecuteDesc>
  <EntityStudyMode_Extract>Extract bioferrite</EntityStudyMode_Extract>
  <EntityStudyMode_ExtractDesc>Doctors will extract bioferrite from the captured entity whenever possible. This extraction method is simple, but inefficient. Can’t be done if the entity is attached to a bioferrite harvester.</EntityStudyMode_ExtractDesc>
  <RequiresBioferriteExtraction>Requires bioferrite extraction.</RequiresBioferriteExtraction>
  <BioferriteHarvesterAttached>Blocked by bioferrite harvester.</BioferriteHarvesterAttached>
  <CantBeExecuted>Can't be executed.</CantBeExecuted>
  <StudyKnowledgeGain>Knowledge gain</StudyKnowledgeGain>
  <StudyKnowledgeGainDesc>Amount of knowledge gained per study interval. Better containment helps the researcher focus on study instead of safety, yielding more knowledge.</StudyKnowledgeGainDesc>
  <MedicineQualityDescriptionEntity>What type of medicine can be given to this entity.</MedicineQualityDescriptionEntity>

  <FactorForElectroharvester>Electroharvester</FactorForElectroharvester>
  <FactorForDaysInCaptivity>Long captivity</FactorForDaysInCaptivity>
  <FactorForColdCaptivity>Cold containment bonus</FactorForColdCaptivity>
  <IncapableOfEscaping>Incapable of escaping</IncapableOfEscaping>
  <EntityDowned>Entity downed</EntityDowned>
  <FactorContainmentStrength>Containment strength</FactorContainmentStrength>
  <FactorElectroharvester>Electroharvester</FactorElectroharvester>
  <FactorActivity>Activity</FactorActivity>

</LanguageData>
