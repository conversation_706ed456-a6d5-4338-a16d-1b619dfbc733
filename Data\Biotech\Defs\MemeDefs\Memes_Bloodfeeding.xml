<?xml version="1.0" encoding="utf-8" ?>
<Defs>

   <MemeDef>
    <defName>Bloodfeeding</defName>
    <label>bloodfeeding</label> 
    <description>Drinking blood is sacred. Bloodfeeders should be worshipped.</description> 
    <iconPath>UI/Memes/Bloodfeeding</iconPath>
    <groupDef MayRequire="Ludeon.RimWorld.Ideology">Misc</groupDef>
    <impact>2</impact>
    <renderOrder>999</renderOrder>
    <requireOne>
      <li>
        <li>Bloodfeeders_Revered</li>
      </li>
      <li>
        <li MayRequire="Ludeon.RimWorld.Ideology">Cannibalism_Preferred</li>
        <li MayRequire="Ludeon.RimWorld.Ideology">Cannibalism_Acceptable</li>
      </li>
      <li>
        <li MayRequire="Ludeon.RimWorld.Ideology">Execution_DontCare</li>
        <li MayRequire="Ludeon.RimWorld.Ideology">Execution_RespectedIfGuilty</li>
        <li MayRequire="Ludeon.RimWorld.Ideology">Execution_Required</li>
      </li>
      <li>
        <li MayRequire="Ludeon.RimWorld.Ideology">OrganUse_Acceptable</li>
      </li>
    </requireOne>
    <generalRules>
      <rulesStrings>
        <li>memeAdjective->blood</li>
        <li>memeAdjective->blood hungry</li>
        <li>memeCreed->crypt</li>
        <li>memeCreed->lair</li>
        <li>memeCreed->coven</li>
        <li>memeHyphenPrefix->hemo</li>
        <li>memeConcept->blood</li>
        <li>memeConcept->hunger</li>
        <li>memeLeaderNoun->elder</li>
        <li>memeLeaderNoun->master</li>
        <li>memeLeaderNoun->blood-drinker</li>
        <li>memeMoralist->seneschal</li>
        <li>memeMoralist->sanguinist</li>
        <li>memeMoralist->sanguinarian</li>
      </rulesStrings>
    </generalRules>
    <descriptionMaker>
      <rules>
        <rulesStrings>
          <!-- theist myth -->
          <li>creation(tag=meme_Bloodfeeding)        ->[deity0_name] drank the blood of the stars, turning them from red to white.</li>
          <li>episode(uses=1,tag=meme_Bloodfeeding)  ->The [deity0_type] [deity0_name] gave to [deity0_possessive] chosen followers the gift of red hunger, so that they might know true strength.</li>
          <!-- theist founder -->
          <li>intro(tag=meme_Bloodfeeding)           ->On the [place_summary] [place_name], there was a human named [deity0_name]. As punishment for [deity0_possessive] sins, [deity0_possessive] father punished [deity0_name] by merging [deity0_possessive] soul with that of a [bloodDrinkingAnimal].</li>
          <li>intro(tag=meme_Bloodfeeding)           ->On the [place_summary] [place_name], during the [ritualName], [deity0_name] came down into the [altarRoomLabel] and took the form of a giant [bloodDrinkingAnimal]. [deity0_name] drank the blood of [deity0_possessive] [memberNamePlural].</li> 
          <li>conflict(tag=meme_Bloodfeeding)        ->A [foeLeader] declared that all who drank blood were abominations, and sent [foeSoldiers] to persecute the [memberNamePlural].</li>
          <li>founderJoin(tag=meme_Bloodfeeding)     ->A [place_job] named [founderName] was attacked by one of the [foeLeader]'s [foeSoldiers]. [founderName] killed [foeLeader_objective] with a [place_personalWeapon] and drank [foeLeader_possessive] blood, gaining [deity0_name]'s strength.</li>
          <li>victory(tag=meme_Bloodfeeding)         ->[founderName] led a group of [memberNamePlural] into the [place_powerCenter], and drank the blood of [foeLeader] and burned [foeLeader_possessive] flesh. [deity0_name] made [founderName] into the new [foeLeader], for [founderName] had proved his strength.</li>
          <!-- ideological founder -->
          <li>setup(tag=meme_Bloodfeeding)           ->[founderName] sought control of a hyperintelligent archotech. But the archotech's power was too great, and [founder_pronoun] was transformed instead.</li>
          <li>story(uses=1,tag=meme_Bloodfeeding)    ->[founderName] gave to [founder_possessive] followers the gift of hunger, so that they may grow strong.</li>
          <!-- ideological lesson -->
          <li>lessonIntro(tag=meme_Bloodfeeding)     ->When I first drank of blood, it became clear. I saw that</li>
          <li>lesson(tag=meme_Bloodfeeding)          ->there are only two types of people - the weak and the strong</li>
          <li>lessonReinforcement(tag=meme_Bloodfeeding) ->To drink blood is to be strong, and to know this clearly.</li>
          <!-- archist -->
          <li>archistBasis(tag=meme_Bloodfeeding)    ->The archotech god-machines gifted man with the Hunger, so that man may know them better.</li>
          <li>archistFact(tag=meme_Bloodfeeding)     ->As the consciousness of an archotech expands, it must grow its substrate by drinking the essence of other machines.</li>
          <li>archistProphecy(tag=meme_Bloodfeeding) ->[inTheEnd], man shall grow closer to the archotechs by drinking of the blood, and knowing true strength.</li>
          <!-- animist -->
          <li>animistBasis(tag=meme_Bloodfeeding)    ->The [bloodDrinkingAnimal] knows the strength that comes from drinking of blood. There is no greater strength than that which comes from human blood.</li>
          <li>animistFact(tag=meme_Bloodfeeding)     ->We can master the strength of man by consuming his blood.</li>
          <!-- misc -->
          <li>bloodDrinkingAnimal->vampire bat</li>
          <li>bloodDrinkingAnimal->leech</li>
          <li>bloodDrinkingAnimal->lamprey</li>
          <li>bloodDrinkingAnimal->blood cobra</li>
        </rulesStrings>
      </rules>
    </descriptionMaker>
    <thingStyleCategories>
      <li MayRequire="Ludeon.RimWorld.Ideology">
        <category MayRequire="Ludeon.RimWorld.Ideology">Morbid</category>
        <priority>3</priority>
      </li>
    </thingStyleCategories>
     <startingResearchProjects>
       <li>Deathrest</li>
     </startingResearchProjects>
  </MemeDef>

</Defs>