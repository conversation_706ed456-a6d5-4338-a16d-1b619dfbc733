﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <SoundDef>
    <defName>Pawn_Noctol_Call</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Noctol/Call</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <pitchRange>0.95~1.05</pitchRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Noctol_Wounded</defName>
    <context>MapOnly</context>
    <maxVoices>1</maxVoices>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Noctol/Pain</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <pitchRange>0.95~1.05</pitchRange>
        <repeatMode>NeverTwice</repeatMode>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Noctol_Death</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Noctol/Death</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <pitchRange>0.95~1.05</pitchRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

</Defs>