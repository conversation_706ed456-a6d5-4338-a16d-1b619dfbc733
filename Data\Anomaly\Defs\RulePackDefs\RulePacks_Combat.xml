﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <RulePackDef>
    <defName>Combat_RangedFlamethrower</defName>
    <include>
      <li>Combat_RangedBase</li>
      <li>Combat_SkillIncludes</li>
    </include>
    <rulePack>
      <rulesStrings>
        <li>r_logentry->[INITIATOR_definite] [firedat] [RECIPIENT_definite] with [INITIATOR_possessive] [WEAPON_label].</li>
        <li>r_logentry->[INITIATOR_definite] [fired] [INITIATOR_possessive] [WEAPON_label] at [RECIPIENT_definite].</li>
        <li>r_logentry->[INITIATOR_definite] [firedat] [RECIPIENT_definite].</li>
        <li>r_logentry(RECIPIENT_missing==True)->[INITIATOR_definite] [fired] [INITIATOR_possessive] [WEAPON_label].</li>

        <li>fired(p=2)->[verb_fired]</li>
        <li>fired->[skillAdv] [verb_fired]</li>

        <li>firedat->fired at</li>
        <li>firedat->fired [skillAdv] at</li>
        <li>firedat(p=0.5)->aimed and fired at</li>
        <li>firedat(p=0.5)->[skillAdv] aimed and fired at</li>
        <li>firedat(p=0.3)->opened fire at</li>

        <li>verb_fired->fired</li>
      </rulesStrings>
    </rulePack>
  </RulePackDef>
</Defs>