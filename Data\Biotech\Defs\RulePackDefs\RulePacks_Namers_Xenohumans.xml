﻿<?xml version="1.0" encoding="utf-8" ?>

<Defs>

  <RulePackDef>
    <defName>Namer<PERSON>ersonPigskin</defName>
    <rulePack>
      <rulesStrings>
        <li>r_name->[firstName] [lastName]</li>
        <li>r_name->[firstName] '[nickName]' [lastName]</li>

        <li>maybeHyphen(p=4)-></li>
        <li>maybeHyphen->-</li>

        <li>firstName(p=1)->[SylP][nameEnd]</li>
        <li>firstName(p=3)->[SylP][nameEnd]</li>

        <li>nickName->[nicknamePig]</li>

        <li>lastName(p=3)->[lastGenerated]</li>
        <li>lastGenerated->[SylP][nameEnd]</li>
        <li>lastGenerated->[SylP][maybeHyphen][SylP][nameEnd]</li>

        <li>nameEnd->ug</li>
        <li>nameEnd->ag</li>
        <li>nameEnd->up</li>
        <li>nameEnd->ork</li>
        <li>nameEnd->org</li>
        <li>nameEnd->en</li>
        <li>nameEnd->er</li>
        <li>nameEnd->ub</li>
        <li>nameEnd->opp</li>
        <li>nameEnd->is</li>
        <li>nameEnd->ow</li>
        <li>nameEnd->ap</li>
        <li>nameEnd->ag</li>
        <li>nameEnd->og</li>
        <li>nameEnd->ogg</li>
        <li>nameEnd->obb</li>
        <li>nameEnd->oik</li>
        <li>nameEnd->oin</li>

      </rulesStrings>
      <rulesFiles>
        <li>SylP->WordParts/Syllables_Pig</li>
        <li>nicknamePig->Names/Nick_Unisex_Pig</li>
      </rulesFiles>
    </rulePack>
  </RulePackDef>

  <RulePackDef>
    <defName>NamerPersonImpid</defName>
    <rulePack>
      <rulesStrings>
        <li>r_name->[firstName] [lastName]</li>

        <li>firstName(p=0.5)->[SylI]</li>
        <li>firstName(p=2)->[SylI][nameEnd]</li>
        <li>firstName->[SylI][SylI][nameEnd]</li>

        <li>lastName(p=2)->[SylI][nameEnd]</li>
        <li>lastName->[SylI][nameEnd]</li>
        <li>lastName->[SylI][SylI][nameEnd]</li>

        <li>nameEnd->iv</li>
        <li>nameEnd->ich</li>
        <li>nameEnd->izz</li>
        <li>nameEnd->in</li>
        <li>nameEnd->ox</li>
        <li>nameEnd->ov</li>
        <li>nameEnd->rach</li>
        <li>nameEnd->rath</li>
        <li>nameEnd->rat</li>
        <li>nameEnd->rov</li>
        <li>nameEnd->rozz</li>
        <li>nameEnd->ron</li>
        <li>nameEnd->vin</li>
        <li>nameEnd->von</li>
        <li>nameEnd->vozz</li>
        <li>nameEnd->ith</li>
        <li>nameEnd->nove</li>
        <li>nameEnd->novo</li>
        <li>nameEnd->nav</li>
        <li>nameEnd->naz</li>
        <li>nameEnd->nath</li>

      </rulesStrings>
      <rulesFiles>
        <li>SylI->WordParts/Syllables_Impid</li>
      </rulesFiles>
    </rulePack>
  </RulePackDef>

  <RulePackDef>
    <defName>NamerPersonYttakin</defName>
    <rulePack>
      <rulesStrings>
        <li>r_name->[firstName] [lastName]</li>

        <li>firstName(p=0.5)->[SylY]</li>
        <li>firstName(p=2)->[SylY][nameEnd]</li>
        <li>firstName->[SylY][SylY][nameEnd]</li>

        <li>lastName(p=2)->[SylY][nameEnd]</li>
        <li>lastName->[SylY][SylY][nameEnd]</li>
        <li>lastName->Kin'[SylY][nameEnd]</li> <!-- Common names beginnings among Yttakin; compare Scottish "Mc" -->
        <li>lastName->Wyn'[SylY][nameEnd]</li>

        <li>nameEnd(p=3)->ytt</li>
        <li>nameEnd->esh</li>
        <li>nameEnd->ott</li>
        <li>nameEnd->osh</li>
        <li>nameEnd->ark</li>
        <li>nameEnd->erk</li>
        <li>nameEnd->ersh</li>
        <li>nameEnd->uk</li>
        <li>nameEnd->yon</li>
        <li>nameEnd->en</li>
        <li>nameEnd->ul</li>
        <li>nameEnd->ya</li>
        <li>nameEnd->ysh</li>
        <li>nameEnd->ys</li>
        <li>nameEnd->yn</li>
      </rulesStrings>
      <rulesFiles>
        <li>SylY->WordParts/Syllables_Yttakin</li>
      </rulesFiles>
    </rulePack>
  </RulePackDef>

  <RulePackDef>
    <defName>NamerPersonWaster</defName>
    <rulePack>
      <rulesStrings>
        <li>r_name->[firstName] [lastName]</li>
        <li>r_name->[firstName] '[nicknameWaster]' [lastName]</li>

        <li>firstName->[Syl][Syl]</li>
        <li>firstName->[Syl]</li>

        <li>lastName->[Syl][End]</li>

        <li>End->ad</li>
        <li>End->ak</li>
        <li>End->ank</li>
        <li>End->ash</li>
        <li>End->eek</li>
        <li>End->ek</li>
        <li>End->enk</li>
        <li>End->er</li>
        <li>End->esk</li>
        <li>End->ez</li>
        <li>End->gun</li>
        <li>End->ik</li>
        <li>End->ip</li>
        <li>End->isk</li>
        <li>End->ix</li>
        <li>End->iz</li>
        <li>End->kill</li>
        <li>End->kin</li>
        <li>End->ling</li>
        <li>End->man</li>
        <li>End->men</li>
        <li>End->nik</li>
        <li>End->ox</li>
        <li>End->skow</li>
        <li>End->smoke</li>
        <li>End->waste</li>
        <li>End->y</li>
      </rulesStrings>
      <rulesFiles>
        <li>Syl->WordParts/Syllables_Waster</li>
        <li>nicknameWaster->Names/Nick_Unisex_Waster</li>
      </rulesFiles>
    </rulePack>
  </RulePackDef>

  <RulePackDef Name="NamerDirtmoleBase" Abstract="True">
    <rulePack>
      <rulesStrings>
        <li>r_name->[firstName] [Syl][End]</li>

        <li>End->cate</li>
        <li>End->chite</li>
        <li>End->cite</li>
        <li>End->fide</li>
        <li>End->gite</li>
        <li>End->line</li>
        <li>End->mine</li>
        <li>End->mite</li>
        <li>End->na</li>
        <li>End->nate</li>
        <li>End->nel</li>
        <li>End->net</li>
        <li>End->phite</li>
        <li>End->quart</li>
        <li>End->rift</li>
        <li>End->rit</li>
        <li>End->rock</li>
        <li>End->ryl</li>
        <li>End->salt</li>
        <li>End->site</li>
        <li>End->spar</li>
        <li>End->spore</li>
        <li>End->sum</li>
        <li>End->tite</li>
        <li>End->turf</li>
        <li>End->vein</li>
        <li>End->vel</li>
        <li>End->vine</li>
        <li>End->vite</li>
        <li>End->zite</li>
        <li>End->drite</li>
      </rulesStrings>
      <rulesFiles>
        <li>Syl->WordParts/Syllables_Dirtmole</li>
      </rulesFiles>
    </rulePack>
  </RulePackDef>

  <RulePackDef ParentName="NamerDirtmoleBase">
    <defName>NamerPersonDirtmole_Male</defName>
    <rulePack>
      <rulesRaw>
        <li Class="Rule_NamePerson">
          <keyword>firstName</keyword>
          <gender>Male</gender>
        </li>
      </rulesRaw>
    </rulePack>
  </RulePackDef>

  <RulePackDef ParentName="NamerDirtmoleBase">
    <defName>NamerPersonDirtmole_Female</defName>
    <rulePack>
      <rulesRaw>
        <li Class="Rule_NamePerson">
          <keyword>firstName</keyword>
          <gender>Female</gender>
        </li>
      </rulesRaw>
    </rulePack>
  </RulePackDef>

</Defs>
