<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <RecipeDef Name="BaseMechanoidRecipe" Abstract="True">
    <jobString>Gestating mech.</jobString>
    <workAmount>1800</workAmount>
    <gestationCycles>1</gestationCycles>
    <formingTicks>120000</formingTicks><!-- 2 days -->
    <mechanitorOnlyRecipe>true</mechanitorOnlyRecipe>
    <workSpeedStat>MechFormingSpeed</workSpeedStat>
    <soundWorking>MechGestatorCycle_Initiating</soundWorking>
  </RecipeDef>

  <RecipeDef Name="LightMechanoidRecipe" ParentName="BaseMechanoidRecipe" Abstract="True">
    <researchPrerequisite>BasicMechtech</researchPrerequisite>
    <ingredients>
      <li>
        <filter>
          <thingDefs>
            <li>Steel</li>
          </thingDefs>
        </filter>
        <count>50</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>SubcoreBasic</li>
          </thingDefs>
        </filter>
        <count>1</count>
      </li>
    </ingredients>
  </RecipeDef>

  <RecipeDef ParentName="LightMechanoidRecipe">
    <defName>Militor</defName>
    <label>gestate militor</label>
    <description>Gestate a militor mechanoid.</description>
    <products>
      <Mech_Militor>1</Mech_Militor>
    </products>
    <descriptionHyperlinks>
      <ThingDef>Mech_Militor</ThingDef>
    </descriptionHyperlinks>
  </RecipeDef>

  <RecipeDef ParentName="LightMechanoidRecipe">
    <defName>Lifter</defName>
    <label>gestate lifter</label>
    <description>Gestate a lifter mechanoid.</description>
    <products>
      <Mech_Lifter>1</Mech_Lifter>
    </products>
    <descriptionHyperlinks>
      <ThingDef>Mech_Lifter</ThingDef>
    </descriptionHyperlinks>
  </RecipeDef>

  <RecipeDef ParentName="LightMechanoidRecipe">
    <defName>Constructoid</defName>
    <label>gestate constructoid</label>
    <description>Gestate a constructoid mechanoid.</description>
    <products>
      <Mech_Constructoid>1</Mech_Constructoid>
    </products>
    <descriptionHyperlinks>
      <ThingDef>Mech_Constructoid</ThingDef>
    </descriptionHyperlinks>
  </RecipeDef>

  <RecipeDef ParentName="LightMechanoidRecipe">
    <defName>Fabricor</defName>
    <label>gestate fabricor</label>
    <description>Gestate a fabricor mechanoid.</description>
    <researchPrerequisite>HighMechtech</researchPrerequisite>
    <ingredients Inherit="False">
      <li>
        <filter>
          <thingDefs>
            <li>Steel</li>
          </thingDefs>
        </filter>
        <count>100</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>SubcoreHigh</li>
          </thingDefs>
        </filter>
        <count>1</count>
      </li>
    </ingredients>
    <products>
      <Mech_Fabricor>1</Mech_Fabricor>
    </products>
    <descriptionHyperlinks>
      <ThingDef>Mech_Fabricor</ThingDef>
    </descriptionHyperlinks>
  </RecipeDef>

  <RecipeDef ParentName="LightMechanoidRecipe">
    <defName>Agrihand</defName>
    <label>gestate agrihand</label>
    <description>Gestate an agrihand mechanoid.</description>
    <researchPrerequisite>BasicMechtech</researchPrerequisite>
    <products>
      <Mech_Agrihand>1</Mech_Agrihand>
    </products>
    <descriptionHyperlinks>
      <ThingDef>Mech_Agrihand</ThingDef>
    </descriptionHyperlinks>
  </RecipeDef>

  <RecipeDef ParentName="LightMechanoidRecipe">
    <defName>Cleansweeper</defName>
    <label>gestate cleansweeper</label>
    <description>Gestate a cleansweeper mechanoid.</description>
    <researchPrerequisite>BasicMechtech</researchPrerequisite>
    <products>
      <Mech_Cleansweeper>1</Mech_Cleansweeper>
    </products>
    <descriptionHyperlinks>
      <ThingDef>Mech_Cleansweeper</ThingDef>
    </descriptionHyperlinks>
  </RecipeDef>

  <RecipeDef ParentName="LightMechanoidRecipe">
    <defName>Paramedic</defName>
    <label>gestate paramedic</label>
    <description>Gestate a paramedic mechanoid.</description>
    <researchPrerequisite>HighMechtech</researchPrerequisite>
    <ingredients Inherit="False">
      <li>
        <filter>
          <thingDefs>
            <li>Steel</li>
          </thingDefs>
        </filter>
        <count>100</count>
      </li>
      <li>
        <filter>
          <thingDefs>
            <li>SubcoreHigh</li>
          </thingDefs>
        </filter>
        <count>1</count>
      </li>
    </ingredients>
    <products>
      <Mech_Paramedic>1</Mech_Paramedic>
    </products>
    <descriptionHyperlinks>
      <ThingDef>Mech_Paramedic</ThingDef>
    </descriptionHyperlinks>
  </RecipeDef>

</Defs>