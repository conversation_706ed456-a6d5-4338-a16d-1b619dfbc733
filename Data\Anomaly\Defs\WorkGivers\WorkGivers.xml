<?xml version="1.0" encoding="utf-8" ?>

<Defs>
  
  <!-- Basic -->

  <WorkGiverDef>
    <defName>DoBillsBioferriteShaper</defName>
    <label>make things at bioferrite shaper</label>
    <giverClass>WorkGiver_DoBill</giverClass>
    <workType>Smithing</workType>
    <priorityInType>75</priorityInType>
    <fixedBillGiverDefs>
      <li>BioferriteShaper</li>
    </fixedBillGiverDefs>
    <verb>work at</verb>
    <gerund>working at</gerund>
    <requiredCapacities>
      <li>Manipulation</li>
    </requiredCapacities>
    <prioritizeSustains>true</prioritizeSustains>
  </WorkGiverDef>

  <WorkGiverDef>
    <defName>DoBillsSerumCentrifuge</defName>
    <label>make things at serum lab</label>
    <giverClass>WorkGiver_DoBill</giverClass>
    <workType>Crafting</workType>
    <priorityInType>75</priorityInType>
    <fixedBillGiverDefs>
      <li>SerumCentrifuge</li>
    </fixedBillGiverDefs>
    <verb>work at</verb>
    <gerund>working at</gerund>
    <requiredCapacities>
      <li>Manipulation</li>
    </requiredCapacities>
    <prioritizeSustains>true</prioritizeSustains>
  </WorkGiverDef>
  
  <!-- Research -->

  <WorkGiverDef>
    <defName>TakeEntityToHoldingPlatform</defName>
    <label>capture entity</label>
    <giverClass>WorkGiver_TakeEntityToHoldingPlatform</giverClass>
    <workType>Hauling</workType>
    <verb>capture</verb>
    <gerund>capturing</gerund>
    <priorityInType>300</priorityInType>
  </WorkGiverDef>
  
  <!-- Warden -->

  <WorkGiverDef>
    <defName>ReleaseEntity</defName>
    <label>release entities</label>
    <giverClass>WorkGiver_ReleaseEntity</giverClass>
    <workType>Warden</workType>
    <verb>release</verb>
    <gerund>releasing</gerund>
    <priorityInType>65</priorityInType>
    <requiredCapacities>
      <li>Manipulation</li>
    </requiredCapacities>
  </WorkGiverDef>

  <WorkGiverDef>
    <defName>TransferEntity</defName>
    <label>transfer entities</label>
    <giverClass>WorkGiver_TransferEntity</giverClass>
    <workType>Hauling</workType>
    <verb>transfer</verb>
    <gerund>transferring</gerund>
    <priorityInType>64</priorityInType>
    <requiredCapacities>
      <li>Manipulation</li>
    </requiredCapacities>
  </WorkGiverDef>

  <WorkGiverDef>
    <defName>ActivitySuppression</defName>
    <label>suppress entity activity</label>
    <giverClass>WorkGiver_Warden_SuppressActivity</giverClass>
    <workType>Warden</workType>
    <verb>suppress</verb>
    <gerund>suppressing</gerund>
    <priorityInType>95</priorityInType>
    <requiredCapacities>
      <li>Manipulation</li>
    </requiredCapacities>
  </WorkGiverDef>

  <WorkGiverDef>
    <defName>ExecuteEntity</defName>
    <label>execute entities</label>
    <giverClass>WorkGiver_ExecuteEntity</giverClass>
    <workType>Warden</workType>
    <verb>execute</verb>
    <gerund>executing</gerund>
    <priorityInType>115</priorityInType>
    <requiredCapacities>
      <li>Manipulation</li>
    </requiredCapacities>
  </WorkGiverDef>
  
  <WorkGiverDef>
    <defName>InterrogatePrisoner</defName>
    <label>interrogate identity</label>
    <giverClass>WorkGiver_Warden_InterrogateIdentity</giverClass>
    <workType>Warden</workType>
    <verb>interrogate</verb>
    <gerund>interrogating</gerund>
    <priorityInType>120</priorityInType>
    <requiredCapacities>
      <li>Manipulation</li>
    </requiredCapacities>
  </WorkGiverDef>
  
  <WorkGiverDef>
    <defName>TakeBioferriteOutOfHarvester</defName>
    <label>take bioferrite out of bioferrite harvester</label>
    <giverClass>WorkGiver_TakeBioferriteOutOfHarvester</giverClass>
    <workType>Hauling</workType>
    <verb>take bioferrite</verb>
    <gerund>taking bioferrite from</gerund>
    <priorityInType>20</priorityInType>
    <requiredCapacities>
      <li>Manipulation</li>
    </requiredCapacities>
    <canBeDoneByMechs>true</canBeDoneByMechs>
    <canBeDoneByMutants>false</canBeDoneByMutants>
  </WorkGiverDef>

  <WorkGiverDef>
    <defName>DoctorTendToEntities</defName>
    <label>tend to entities</label>
    <giverClass>WorkGiver_TendEntity</giverClass>
    <workType>Doctor</workType>
    <verb>tend to</verb>
    <gerund>tending to</gerund>
    <priorityInType>95</priorityInType>
    <requiredCapacities>
      <li>Manipulation</li>
    </requiredCapacities>
    <prioritizeSustains>true</prioritizeSustains>
  </WorkGiverDef>

  <WorkGiverDef>
    <defName>ExtractBioferrite</defName>
    <label>extract bioferrite from entities</label>
    <giverClass>WorkGiver_ExtractBioferrite</giverClass>
    <workType>Doctor</workType>
    <verb>extract from</verb>
    <gerund>extracting from</gerund>
    <priorityInType>10</priorityInType>
    <requiredCapacities>
      <li>Manipulation</li>
    </requiredCapacities>
    <prioritizeSustains>true</prioritizeSustains>
  </WorkGiverDef>

  <WorkGiverDef>
    <defName>FillIn</defName>
    <label>fill in</label>
    <giverClass>WorkGiver_FillIn</giverClass>
    <workType>Construction</workType>
    <priorityInType>51</priorityInType>
    <verb>fill</verb>
    <gerund>filling in</gerund>
    <requiredCapacities>
      <li>Manipulation</li>
    </requiredCapacities>
  </WorkGiverDef>
  
  <!-- Dark study -->

  <WorkGiverDef>
    <defName>StudyInteract</defName>
    <label>study</label>
    <giverClass>WorkGiver_DarkStudyInteract</giverClass>
    <workType>DarkStudy</workType>
    <verb>study</verb>
    <gerund>studying</gerund>
    <priorityInType>110</priorityInType>
    <canBeDoneByMechs>false</canBeDoneByMechs>
  </WorkGiverDef>

</Defs>