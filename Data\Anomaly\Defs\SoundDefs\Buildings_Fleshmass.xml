﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>
  
  <SoundDef>
    <defName>Fleshmass_Damaged</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Building/Fleshmass/Damaged</clipFolderPath>
          </li>
        </grains>
        <volumeRange>20~25</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <repeatMode>NeverTwice</repeatMode>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Fleshmass_Destroyed</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Building/Fleshmass/Destroyed</clipFolderPath>
          </li>
        </grains>
        <volumeRange>10~15</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <repeatMode>NeverTwice</repeatMode>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>FleshmassHeart_Throb</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Building/FleshmassHeart/Throb</clipFolderPath>
          </li>
        </grains>
        <volumeRange>20~25</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <repeatMode>NeverTwice</repeatMode>
        <gameSpeedRange>1</gameSpeedRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>FleshmassHeart_Destroyed</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Building/FleshmassHeart/Destroyed</clipFolderPath>
          </li>
        </grains>
        <volumeRange>20~25</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>FleshmassHeart_Ambience</defName>
    <context>MapOnly</context>
    <sustain>True</sustain>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/FleshmassHeart/Fleshmass_Heart_Ambience_A_01</clipPath>
          </li>
        </grains>
        <volumeRange>10</volumeRange>
        <muteWhenPaused>True</muteWhenPaused>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>FleshmassHeart_Emerge</defName>
    <context>MapOnly</context>
    <sustain>True</sustain>
    <priorityMode>PrioritizeNearest</priorityMode>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Misc/Emergence/Emergence_Surface</clipPath>
          </li>
        </grains>
        <volumeRange>25</volumeRange>
        <distRange>15~30</distRange>
        <muteWhenPaused>True</muteWhenPaused>
      </li>
      <li>
        <muteWhenPaused>True</muteWhenPaused>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/FleshmassHeart/Emergence_Point_Fleshmass_Heart_Loop_A</clipPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
        <distRange>15~60</distRange>
      </li>
    </subSounds>
    <sustainStopSound>FleshmassHeart_Emerge_End</sustainStopSound>
  </SoundDef>

  <SoundDef>
    <defName>FleshmassHeart_Emerge_End</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Clip">
            <clipPath>Building/FleshmassHeart/Fleshmass_Heart_Emerging_A_End</clipPath>
          </li>
        </grains>
        <volumeRange>30~30</volumeRange>
        <pitchRange>0.9~1.1</pitchRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>SpitterSpawn</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Building/FleshmassSpitter/Spawn</clipFolderPath>
          </li>
        </grains>
        <distRange>25~50</distRange>
        <volumeRange>14</volumeRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>SpitterSpit</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Building/FleshmassSpitter/Spit</clipFolderPath>
          </li>
        </grains>
        <distRange>25~50</distRange>
        <volumeRange>14</volumeRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>SpitterSpitLands</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Building/FleshmassSpitter/SpitLands</clipFolderPath>
          </li>
        </grains>
        <distRange>25~50</distRange>
        <volumeRange>14</volumeRange>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>FleshmassBirth</defName>
    <context>MapOnly</context>
    <maxSimultaneous>1</maxSimultaneous>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Misc/FleshmassBirth</clipFolderPath>
          </li>
        </grains>
        <volumeRange>30</volumeRange>
      </li>
    </subSounds>
  </SoundDef>

</Defs>