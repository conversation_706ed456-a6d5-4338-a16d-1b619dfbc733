<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <StatDef>
    <defName>Fertility</defName>
    <label>fertility</label>
    <description>Fertility affects the chance of a pregnancy occurring. Pregnancy chance is dependent on the fertility of both partners. This also affects the chance of successfully implanting an embryo in a surrogate.</description>
    <category>BasicsPawn</category>
    <defaultBaseValue>1</defaultBaseValue>
    <minValue>0</minValue>
    <toStringStyle>PercentZero</toStringStyle>
    <showOnHumanlikes>true</showOnHumanlikes>
    <showOnAnimals>false</showOnAnimals>
    <showOnMechanoids>false</showOnMechanoids>
    <showOnEntities>false</showOnEntities>
    <showDevelopmentalStageFilter>Adult</showDevelopmentalStageFilter>
    <parts>
      <li Class="StatPart_FertilityByGenderAge">
        <maleFertilityAgeFactor>
          <points>
            <li>(14, 0)</li>
            <li>(18, 1)</li>
            <li>(50, 1)</li>
            <li>(90, 0)</li>
          </points>
        </maleFertilityAgeFactor>
        <femaleFertilityAgeFactor>
          <points>
            <li>(14, 0)</li>
            <li>(20, 1)</li>
            <li>(28, 1)</li>
            <li>(35, 0.5)</li>
            <li>(40, 0.1)</li>
            <li>(45, 0.02)</li>
            <li>(50, 0)</li>
          </points>
        </femaleFertilityAgeFactor>
      </li>
      <li Class="StatPart_FertilityByHediffs"/>
    </parts>
  </StatDef>

  <!-- Mechanitor -->
  
  <StatDef Abstract="True" Name="MechanitorStatBase">
    <workerClass>StatWorker_Mechanitor</workerClass>
    <category>Mechanitor</category>
    <defaultBaseValue>0</defaultBaseValue>
    <minValue>0</minValue>
    <showOnAnimals>false</showOnAnimals>
    <showOnHumanlikes>true</showOnHumanlikes>
    <showOnMechanoids>false</showOnMechanoids>
    <displayPriorityInCategory>2000</displayPriorityInCategory>
    <showIfUndefined>true</showIfUndefined>
  </StatDef>

  <StatDef ParentName="MechanitorStatBase">
    <defName>MechBandwidth</defName>
    <label>mech bandwidth</label>
    <description>How much bandwidth a mechanitor has. More bandwidth allows a mechanitor to produce and control more mechs at the same time.\n\nLosing bandwidth means losing control of mechs, leaving them without an overseer. Such mechs can eventually become feral if not re-dominated.</description>
  </StatDef>

  <StatDef ParentName="MechanitorStatBase">
    <defName>MechControlGroups</defName>
    <label>mech control groups</label>
    <description>How many control groups this mechanitor can direct independently. Each control group can be given its own separate orders.</description>
  </StatDef>

  <StatDef ParentName="MechanitorStatBase"> 
    <defName>MechRemoteRepairDistance</defName>
    <label>mech remote repair distance</label>
    <description>How far away a mech can be during remote repair.</description>
    <showIfUndefined>false</showIfUndefined>
  </StatDef>

  <StatDef ParentName="MechanitorStatBase">
    <defName>MechRemoteShieldDistance</defName>
    <label>mech remote shield distance</label>
    <description>The maximum range at which a mechanitor can place a shield on a mechanoid.</description>
    <showIfUndefined>false</showIfUndefined>
  </StatDef>

  <StatDef ParentName="MechanitorStatBase">
    <defName>MechRemoteShieldEnergy</defName>
    <label>mech remote shield energy</label>
    <description>The energy a remote shield will have when created by a mechanitor.</description>
    <showIfUndefined>false</showIfUndefined>
  </StatDef>

  <StatDef ParentName="MechanitorStatBase">
    <defName>MechFormingSpeed</defName>
    <label>mech gestation speed</label>
    <description>A multiplier on the speed at which this mechanitor can form new mechanoids at a gestator.</description>
    <defaultBaseValue>1</defaultBaseValue>
    <minValue>0.1</minValue>
    <toStringStyle>PercentZero</toStringStyle>
    <skillNeedFactors>
      <li Class="SkillNeed_BaseBonus">
        <skill>Crafting</skill>
        <baseValue>0.75</baseValue>
        <bonusPerLevel>0.025</bonusPerLevel>
      </li>
    </skillNeedFactors>
  </StatDef>

  <StatDef ParentName="MechanitorStatBase">
    <defName>MechRepairSpeed</defName>
    <label>mech repair speed</label>
    <description>A multiplier on how fast a mechanitor can repair mechanoids.</description>
    <defaultBaseValue>1</defaultBaseValue>
    <toStringStyle>PercentZero</toStringStyle>
    <skillNeedFactors>
      <li Class="SkillNeed_BaseBonus">
        <skill>Crafting</skill>
        <baseValue>0.8</baseValue>
        <bonusPerLevel>0.1</bonusPerLevel>
      </li>
    </skillNeedFactors>
  </StatDef>

  <StatDef ParentName="MechanitorStatBase">
    <defName>SubcoreEncodingSpeed</defName>
    <label>subcore encoding speed</label>
    <description>A multiplier on how fast a mechanitor can create subcores.</description>
    <defaultBaseValue>1</defaultBaseValue>
    <toStringStyle>PercentZero</toStringStyle>
    <skillNeedFactors>
      <li Class="SkillNeed_BaseBonus">
        <skill>Crafting</skill>
        <baseValue>0.75</baseValue>
        <bonusPerLevel>0.1</bonusPerLevel>
      </li>
    </skillNeedFactors>
  </StatDef>

  <!-- Mech -->

  <StatDef ParentName="MechStatBase">
    <defName>BandwidthCost</defName>
    <label>bandwidth cost</label>
    <description>How much bandwidth this mech consumes when under mechanitor control.</description>
    <defaultBaseValue>0</defaultBaseValue>
    <minValue>0</minValue>
    <displayPriorityInCategory>2000</displayPriorityInCategory>
    <showIfUndefined>false</showIfUndefined>
  </StatDef>

  <StatDef ParentName="MechStatBase">
    <defName>ControlTakingTime</defName>
    <label>control taking time</label>
    <description>How many seconds it will take for a mechanitor to take control of this mech.</description>
    <defaultBaseValue>12</defaultBaseValue>
    <minValue>0</minValue>
    <displayPriorityInCategory>2010</displayPriorityInCategory>
    <toStringStyle>Integer</toStringStyle>
    <formatString>{0}s</formatString>
    <showIfUndefined>false</showIfUndefined>
  </StatDef>

  <StatDef ParentName="MechStatBase">
    <defName>MechEnergyUsageFactor</defName>
    <label>energy usage multiplier</label>
    <description>A multiplier on how fast a mechanoid consumes its energy reserves while operating.</description>
    <defaultBaseValue>1</defaultBaseValue>
    <minValue>0</minValue>
    <toStringStyle>PercentZero</toStringStyle>
    <displayPriorityInCategory>2000</displayPriorityInCategory>
    <scenarioRandomizable>true</scenarioRandomizable>
    <showIfUndefined>false</showIfUndefined>
  </StatDef>

  <StatDef ParentName="MechStatBase">
    <defName>WastepacksPerRecharge</defName>
    <label>wastepacks per recharge</label>
    <description>How many toxic wastepacks this mechanoid will generate at a mech recharger when going from 0 to 100% energy. Larger mechanoids will generally create more toxic wastepacks.</description>
    <postProcessStatFactors>
      <li>BandwidthCost</li>
    </postProcessStatFactors>
    <defaultBaseValue>5</defaultBaseValue>
    <minValue>0</minValue>
    <displayPriorityInCategory>2000</displayPriorityInCategory>
    <showIfUndefined>false</showIfUndefined>
    <parts>
      <li Class="StatPart_Hyperlinks">
        <thingDefs>
          <li>Wastepack</li>
        </thingDefs>
      </li>
    </parts>
  </StatDef>

  <StatDef ParentName="MechStatBase">
    <defName>MechEnergyLossPerHP</defName>
    <label>repair energy cost</label>
    <description>The amount of energy that this mechanoid loses for every 100 damage repaired.</description>
    <workerClass>StatWorker_MechEnergyLossPerHP</workerClass>
    <defaultBaseValue>0.333</defaultBaseValue>
    <minValue>0</minValue>
    <toStringStyle>Integer</toStringStyle>
    <displayPriorityInCategory>2020</displayPriorityInCategory>
    <showIfUndefined>false</showIfUndefined>
  </StatDef>

  <!-- Genes -->

  <StatDef>
    <defName>HemogenGainFactor</defName>
    <label>hemogen gain multiplier</label>
    <description>A multiplier on the amount of hemogen gained.</description>
    <category>BasicsPawn</category>
    <toStringStyle>PercentZero</toStringStyle>
    <defaultBaseValue>1</defaultBaseValue>
    <hideAtValue>1</hideAtValue>
  </StatDef>

  <StatDef>
    <defName>RawNutritionFactor</defName>
    <label>raw nutrition multiplier</label>
    <description>A multiplier on how nutritious raw food is for this person. Note that since meals usually have more nutrition than their raw ingredients, a boost to this stat may only mean the person gets the same nutrition from raw food as if it were cooked.</description>
    <category>BasicsPawn</category>
    <toStringStyle>PercentZero</toStringStyle>
    <defaultBaseValue>1</defaultBaseValue>
    <hideAtValue>1</hideAtValue>
    <minValue>0.0001</minValue>
    <displayPriorityInCategory>1001</displayPriorityInCategory>
  </StatDef>

  <StatDef>
    <defName>CancerRate</defName>
    <label>cancer rate factor</label>
    <description>A multiplier on how likely this person is to develop cancer in any given time frame.</description>
    <category>BasicsPawn</category>
    <toStringStyle>PercentZero</toStringStyle>
    <defaultBaseValue>1</defaultBaseValue>
    <hideAtValue>1</hideAtValue>
    <minValue>0.01</minValue>
    <showOnAnimals>false</showOnAnimals>
    <showOnMechanoids>false</showOnMechanoids>
    <displayPriorityInCategory>2100</displayPriorityInCategory>
  </StatDef>

  <StatDef>
    <defName>LearningRateFactor</defName>
    <label>learning rate factor</label>
    <description>A multiplier on how quickly a child's learning need is fulfilled by learning activities.</description>
    <category>BasicsPawn</category>
    <toStringStyle>PercentZero</toStringStyle>
    <defaultBaseValue>1</defaultBaseValue>
    <hideAtValue>1</hideAtValue>
    <minValue>0.01</minValue>
    <showOnAnimals>false</showOnAnimals>
    <showOnMechanoids>false</showOnMechanoids>
    <showDevelopmentalStageFilter>Child</showDevelopmentalStageFilter>
    <displayPriorityInCategory>1350</displayPriorityInCategory>
  </StatDef>

  <StatDef>
    <defName>GrowthVatOccupantSpeed</defName>
    <label>growth vat occupant speed</label>
    <description>A multiplier on how quickly this person will grow when in a growth vat.</description>
    <category>PawnMisc</category>
    <defaultBaseValue>1</defaultBaseValue>
    <hideAtValue>1</hideAtValue>
    <toStringStyle>PercentZero</toStringStyle>
    <showOnAnimals>false</showOnAnimals>
    <showOnMechanoids>false</showOnMechanoids>
    <scenarioRandomizable>true</scenarioRandomizable>
    <displayPriorityInCategory>1850</displayPriorityInCategory>
    <cacheable>true</cacheable>
    <parts>
      <li Class="StatPart_GrowthVatSpeedFactor" />
    </parts>
  </StatDef>

</Defs>