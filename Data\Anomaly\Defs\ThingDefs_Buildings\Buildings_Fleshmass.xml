<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <ThingDef ParentName="BuildingBase" Name="Fleshmass">
    <defName>Fleshmass</defName>
    <label>fleshmass</label>
    <description>A solid mass of twisted flesh. It throbs and shivers with horrible life.</description>
    <filthLeaving>Filth_Fleshmass</filthLeaving>
    <graphicData>
      <texPath>Things/Building/Linked/Fleshmass_Atlas</texPath> <!-- Placeholder -->
      <graphicClass>Graphic_Single</graphicClass>
      <linkType>CornerOverlay</linkType>
      <linkFlags>
        <li>Fleshmass</li>
      </linkFlags>
      <cornerOverlayPath>Things/Building/Linked/FleshmassPiece</cornerOverlayPath>
      <shaderType>CutoutComplex</shaderType>
      <damageData>
        <rect>(0.15, 0.15, 0.75, 0.75)</rect>
        <scratches>
          <li>Damage/FleshScratch1</li>
          <li>Damage/FleshScratch2</li>
          <li>Damage/FleshScratch3</li>
        </scratches>
      </damageData>
    </graphicData>
    <tickerType>Rare</tickerType>
    <altitudeLayer>Building</altitudeLayer>
    <passability>Impassable</passability>
    <blockWind>true</blockWind>
    <castEdgeShadows>true</castEdgeShadows>
    <fillPercent>1</fillPercent>
    <coversFloor>false </coversFloor>
    <neverMultiSelect>true</neverMultiSelect>
    <rotatable>false</rotatable>
    <terrainAffordanceNeeded>Light</terrainAffordanceNeeded>
    <saveCompressible>false</saveCompressible>
    <holdsRoof>true</holdsRoof>
    <staticSunShadowHeight>1.0</staticSunShadowHeight>
    <blockLight>true</blockLight>
    <mineable>false</mineable>
    <fertility>0</fertility>
    <soundImpactDefault>Fleshmass_Damaged</soundImpactDefault>
    <canOverlapZones>false</canOverlapZones>
    <autoTargetNearbyIdenticalThings>true</autoTargetNearbyIdenticalThings>
    <statBases>
      <MaxHitPoints>40</MaxHitPoints>
      <Flammability>0.7</Flammability>
      <Beauty>-6</Beauty>
    </statBases>
    <building>
      <isInert>true</isInert>
      <claimable>false</claimable>
      <canBuildNonEdificesUnder>false</canBuildNonEdificesUnder>
      <deconstructible>false</deconstructible>
      <quickTargetable>true</quickTargetable>
      <supportsWallAttachments>true</supportsWallAttachments>
      <isTargetable>true</isTargetable>
      <destroyEffecter>FleshmassDestroyed</destroyEffecter>
      <destroyShakeAmount>0</destroyShakeAmount>
      <artificialForMeditationPurposes>false</artificialForMeditationPurposes>
    </building>
    <comps>
      <li Class="CompProperties_Fleshmass" />
      <li Class="CompProperties_DestroyNearbyPlantsOnSpawn">
        <radius>2</radius>
      </li>
    </comps>
  </ThingDef>

  <ThingDef ParentName="Fleshmass">
    <defName>Fleshmass_Active</defName>
  </ThingDef>

  <ThingDef ParentName="BuildingNaturalBase">
    <defName>FleshSack</defName>
    <label>flesh sack</label>
    <description>A swollen lump of flesh with something inside. These stationary fleshbeasts act as digestive organs, supplying the surrounding fleshmass with nutrients. However, they're unable to break down non-organic matter, meaning that valuable items can occasionally be found within.</description>
    <thingClass>Building_FleshSack</thingClass>
    <category>Building</category>
    <size>(2,2)</size>
    <graphicData>
      <graphicClass>Graphic_Random</graphicClass>
      <texPath>Things/Building/Fleshmass/Fleshsack</texPath>
      <drawSize>2</drawSize>
      <drawOffset>(0, 0.02, 0)</drawOffset>
      <damageData>
        <rect>(0.3, 0.3, 1.4, 1.4)</rect>
        <scratches>
          <li>Damage/FleshScratch1</li>
          <li>Damage/FleshScratch2</li>
          <li>Damage/FleshScratch3</li>
        </scratches>
      </damageData>
    </graphicData>
    <drawerType>MapMeshAndRealTime</drawerType>
    <altitudeLayer>Building</altitudeLayer>
    <passability>PassThroughOnly</passability>
    <blockWind>true</blockWind>
    <fillPercent>0.75</fillPercent>
    <terrainAffordanceNeeded>Light</terrainAffordanceNeeded>
    <soundImpactDefault>Fleshmass_Damaged</soundImpactDefault>
    <rotatable>false</rotatable>
    <statBases>
      <MaxHitPoints>150</MaxHitPoints>
      <Flammability>0.7</Flammability>
      <Beauty>-12</Beauty>
    </statBases>
    <building>
      <isInert>true</isInert>
      <claimable>false</claimable>
      <canBuildNonEdificesUnder>false</canBuildNonEdificesUnder>
      <deconstructible>false</deconstructible>
      <quickTargetable>true</quickTargetable>
      <isTargetable>true</isTargetable>
      <destroyEffecter>FleshmassDestroyed</destroyEffecter>
    </building>
    <comps>
      <li Class="CompProperties_FleshmassBase">
        <size>2</size>
      </li>
    </comps>
  </ThingDef>

  <ThingDef ParentName="BuildingNaturalBase">
    <defName>FleshmassHeart</defName>
    <label>fleshmass heart</label>
    <description>The primary organ of a fleshmass infestation. The heart gathers material through roots extended into the ground. Once it has collected enough, it spreads fleshmass around itself in a spurt of cancer-like growth. It will keep growing until it consumes everything.\n\nThe heart grows fleshmass spitters. These defensive organs can spit acid long distances. The heart will also generate fleshbeast defenders to protect itself.\n\nUltra-rapid regeneration makes the heart nearly invulnerable. It can only be killed by analyzing samples taken from the nerve bundles that form within its growing mass.</description>
    <thingClass>Building_FleshmassHeart</thingClass>
    <uiIconPath>Things/Building/FleshmassHeart</uiIconPath>
    <category>Building</category>
    <size>(3,3)</size>
    <drawerType>RealtimeOnly</drawerType>
    <tickerType>Normal</tickerType>
    <destroyable>false</destroyable>
    <preventSkyfallersLandingOn>true</preventSkyfallersLandingOn>
    <useHitPoints>false</useHitPoints>
    <altitudeLayer>Building</altitudeLayer>
    <passability>Impassable</passability>
    <terrainAffordanceNeeded>Heavy</terrainAffordanceNeeded>
    <blockWind>true</blockWind>
    <fillPercent>0.75</fillPercent>
    <rotatable>false</rotatable>
    <soundImpactDefault>Fleshmass_Damaged</soundImpactDefault>
    <holdsRoof>true</holdsRoof>
    <statBases>
      <Beauty>-25</Beauty>
    </statBases>
    <building>
      <isEdifice>true</isEdifice>
      <isInert>true</isInert>
      <isTargetable>false</isTargetable>
      <claimable>false</claimable>
      <canBuildNonEdificesUnder>false</canBuildNonEdificesUnder>
      <deconstructible>false</deconstructible>
      <destroyEffecter>FleshmassHeartDestroyed</destroyEffecter>
      <soundAmbient>FleshmassHeart_Ambience</soundAmbient>
    </building>
    <killedLeavingsRanges>
      <Meat_Twisted>200~300</Meat_Twisted>
    </killedLeavingsRanges>
    <forceLeavingsAllowed>true</forceLeavingsAllowed>
    <comps>
      <li Class="CompProperties_GrowsFleshmassTendrils">
        <compClass>CompFleshmassHeart</compClass>
        <mtbGrowthCycleHours>20</mtbGrowthCycleHours>
        <minGrowthCycleHours>12</minGrowthCycleHours>
        <maxGrowthCycleHours>36</maxGrowthCycleHours>
        <startingGrowthPointsByThreat>
          <points>
            <li>(0, 500)</li>
            <li>(800, 750)</li>
            <li>(1600, 1000)</li>
            <li>(5000, 1250)</li>
          </points>
        </startingGrowthPointsByThreat>
        <growthCyclePointsByThreat>
          <points>
            <li>(0, 75)</li>
            <li>(800, 125)</li>
            <li>(1600, 200)</li>
            <li>(5000, 250)</li>
          </points>
        </growthCyclePointsByThreat>
        <fleshbeastPointsByThreat>
          <points>
            <li>(0, 100)</li>
            <li>(800, 200)</li>
            <li>(1600, 400)</li>
            <li>(5000, 600)</li>
          </points>
        </fleshbeastPointsByThreat>
        <fleshbeastBirthThresholdRange>125~200</fleshbeastBirthThresholdRange>
      </li>
      <li Class="CompProperties_BiosignatureOwner"/>
      <li Class="CompProperties_Interactable">
        <compClass>CompDestroyHeart</compClass>
        <ticksToActivate>600</ticksToActivate> <!-- 10 seconds -->
        <activateTexPath>UI/Commands/KillHeart</activateTexPath>

        <activateLabelString>Induce tachycardiac death...</activateLabelString>
        <activateDescString>Use electrical pulses to induce a tachycardiac overload and kill the fleshmass heart.</activateDescString>
        <guiLabelString>Choose who should do this</guiLabelString>
        <jobString>Induce tachycardiac death</jobString>
        <activatingStringPending>Inducing tachycardiac death</activatingStringPending>
        <activatingString>Inducing tachycardiac death: {1}s</activatingString>
        <showMustBeActivatedByColonist>false</showMustBeActivatedByColonist>

        <targetingParameters>
          <canTargetBuildings>false</canTargetBuildings>
          <canTargetAnimals>false</canTargetAnimals>
          <canTargetMechs>false</canTargetMechs>
          <onlyTargetControlledPawns>true</onlyTargetControlledPawns>
        </targetingParameters>
      </li>
      <li Class="CompProperties_DestroyNearbyPlantsOnSpawn">
        <radius>4</radius>
      </li>
      <li Class="CompProperties_LeaveFilthOnDestroyed">
        <filthDef>Filth_LooseGround</filthDef>
        <thickness>2</thickness>
      </li>
      <li Class="CompProperties_FleshmassBase">
        <size>3</size>
      </li>
    </comps>
  </ThingDef>

  <ThingDef>
    <defName>FleshmassHeartSpawner</defName>
    <label>underground emergence</label>
    <description>Something is emerging from the ground here.</description>
    <thingClass>BuildingGroundSpawner</thingClass>
    <destroyable>false</destroyable>
    <holdsRoof>true</holdsRoof>
    <selectable>true</selectable>
    <tickerType>Normal</tickerType>
    <useHitPoints>false</useHitPoints>
    <drawerType>RealtimeOnly</drawerType>
    <size>(3,3)</size>
    <uiIconPath>UI/Icons/UndergroundEmergence</uiIconPath>
    <building>
      <groundSpawnerSustainedEffecter>EmergencePointSustained3X3</groundSpawnerSustainedEffecter>
      <groundSpawnerCompleteEffecter>EmergencePointComplete3X3</groundSpawnerCompleteEffecter>
      <groundSpawnerThingToSpawn>FleshmassHeart</groundSpawnerThingToSpawn>
      <groundSpawnerSpawnDelay>2500~5000</groundSpawnerSpawnDelay>
      <groundSpawnerDestroyAdjacent>true</groundSpawnerDestroyAdjacent>
      <groundSpawnerSustainerSound>FleshmassHeart_Emerge</groundSpawnerSustainerSound>
      <groundSpawnerLetterLabel>Fleshmass heart</groundSpawnerLetterLabel>
      <groundSpawnerLetterText>A fleshmass heart has emerged! This cancer-like creature will expand its living mass across the surface.\n\nThe heart itself is invulnerable to normal weapons. Fleshmass nerve bundles will soon appear - attack them to gather samples and find a way to kill the heart.</groundSpawnerLetterText>
    </building>
  </ThingDef>

  <ThingDef ParentName="BuildingNaturalBase">
    <defName>Fleshbulb</defName>
    <label>fleshbulb</label>
    <description>A bulbous mass of flesh. These fleshbeast organs store and refine nutrients for surrounding fleshmass. The chemical process generates a soft bioluminescent glow.</description>
    <thingClass>Building</thingClass>
    <statBases>
      <MaxHitPoints>35</MaxHitPoints>
      <Flammability>0.7</Flammability>
      <Beauty>-12</Beauty>
    </statBases>
    <graphicData>
      <graphicClass>Graphic_Random</graphicClass>
      <texPath>Things/Building/Fleshmass/Fleshbulb</texPath>
      <drawSize>(1.0, 1.0)</drawSize>
      <drawOffset>(0, 0.02, 0)</drawOffset>
      <damageData>
        <rect>(0.15, 0.15, 0.7, 0.7)</rect>
        <scratches>
          <li>Damage/FleshScratch1</li>
          <li>Damage/FleshScratch2</li>
          <li>Damage/FleshScratch3</li>
        </scratches>
      </damageData>
    </graphicData>
    <drawerType>RealtimeOnly</drawerType>
    <altitudeLayer>Building</altitudeLayer>
    <category>Building</category>
    <passability>PassThroughOnly</passability>
    <fillPercent>0.3</fillPercent>
    <rotatable>false</rotatable>
    <terrainAffordanceNeeded>Light</terrainAffordanceNeeded>
    <killedLeavingsChance>0.5</killedLeavingsChance>
    <killedLeavingsRanges>
      <Meat_Twisted>4~8</Meat_Twisted>
    </killedLeavingsRanges>
    <forceLeavingsAllowed>true</forceLeavingsAllowed>
    <soundImpactDefault>Fleshmass_Damaged</soundImpactDefault>
    <building>
      <isInert>true</isInert>
      <claimable>false</claimable>
      <deconstructible>false</deconstructible>
      <quickTargetable>true</quickTargetable>
      <isTargetable>true</isTargetable>
      <destroyEffecter>FleshmassDestroyed</destroyEffecter>
      <destroyShakeAmount>0</destroyShakeAmount>
    </building>
    <comps>
      <li Class="CompProperties_Glower">
        <glowRadius>11</glowRadius>
        <glowColor>(74, 37, 2, 0)</glowColor>
      </li>
      <li Class="CompProperties_FleshmassBase">
        <size>1</size>
      </li>
    </comps>
  </ThingDef>

  <ThingDef ParentName="BuildingNaturalBase">
    <defName>NerveBundle</defName>
    <label>nerve bundle</label>
    <description>A junction point in the fleshmass nervous system. When destroyed, it leaves behind a lump of neural tissue that you can study. Study enough different neural lumps and you can unlock a way to kill the fleshmass heart itself.</description>
    <thingClass>Building</thingClass>
    <size>(2, 2)</size>
    <statBases>
      <MaxHitPoints>100</MaxHitPoints>
      <Flammability>0.7</Flammability>
      <Beauty>-12</Beauty>
    </statBases>
    <graphicData>
      <graphicClass>Graphic_Single_SquashNStretch</graphicClass>
      <texPath>Things/Building/Fleshmass/FleshmassNerveBundle/FleshmassNerveBundle</texPath>
      <drawSize>(2.5, 2.5)</drawSize>
      <drawOffset>(0, 0.02, 0)</drawOffset>
      <shaderType>BuildingSquashNStretch</shaderType>
      <maxSnS>(.9, .95)</maxSnS>
      <offsetSnS>(.5, -.45)</offsetSnS>
      <damageData>
        <rect>(0.3, 0.3, 1.4, 1.4)</rect>
        <scratches>
          <li>Damage/FleshScratch1</li>
          <li>Damage/FleshScratch2</li>
          <li>Damage/FleshScratch3</li>
        </scratches>
      </damageData>
    </graphicData>
    <drawerType>RealtimeOnly</drawerType>
    <tickerType>Normal</tickerType>
    <altitudeLayer>Building</altitudeLayer>
    <passability>Impassable</passability>
    <blockWind>true</blockWind>
    <fillPercent>0.75</fillPercent>
    <terrainAffordanceNeeded>Light</terrainAffordanceNeeded>
    <soundSpawned>SpitterSpawn</soundSpawned>
    <soundImpactDefault>Fleshmass_Damaged</soundImpactDefault>
    <rotatable>false</rotatable>
    <building>
      <isInert>true</isInert>
      <claimable>false</claimable>
      <canBuildNonEdificesUnder>false</canBuildNonEdificesUnder>
      <deconstructible>false</deconstructible>
      <quickTargetable>true</quickTargetable>
      <isTargetable>true</isTargetable>
      <destroyEffecter>FleshmassDestroyed</destroyEffecter>
    </building>
    <comps>
      <li>
        <compClass>CompNerveBundle</compClass>
      </li>
      <li Class="CompProperties_FleshmassBase">
        <size>2</size>
      </li>
      <li Class="CompProperties_InspectString">
        <inspectString>Kill to collect: Neural lump</inspectString>
      </li>
    </comps>
  </ThingDef>

  <ThingDef ParentName="BuildingNaturalBase">
    <defName>FleshmassSpitter</defName>
    <label>fleshmass spitter</label>
    <description>A defensive fleshmass organ that can spit acid long distances. The acid is a natural byproduct of toxins filtered from the surrounding biomass.</description>
    <thingClass>Building</thingClass>
    <size>(2, 2)</size>
    <statBases>
      <MaxHitPoints>300</MaxHitPoints>
      <Flammability>0.7</Flammability>
      <Beauty>-12</Beauty>
    </statBases>
    <graphicData>
      <graphicClass>Graphic_Indexed_SquashNStretch</graphicClass>
      <texPath>Things/Building/Fleshmass/FleshmassSpitter</texPath>
      <drawSize>(2.5, 2.5)</drawSize>
      <drawOffset>(0, 0.02, 0)</drawOffset>
      <shaderType>BuildingSquashNStretch</shaderType>
      <maxSnS>(.9, .95)</maxSnS>
      <offsetSnS>(.5, -.45)</offsetSnS>
      <damageData>
        <rect>(0.3, 0.3, 1.4, 1.4)</rect>
        <scratches>
          <li>Damage/FleshScratch1</li>
          <li>Damage/FleshScratch2</li>
          <li>Damage/FleshScratch3</li>
        </scratches>
      </damageData>
    </graphicData>
    <drawerType>RealtimeOnly</drawerType>
    <tickerType>Normal</tickerType>
    <altitudeLayer>Building</altitudeLayer>
    <passability>Impassable</passability>
    <blockWind>true</blockWind>
    <fillPercent>0.75</fillPercent>
    <terrainAffordanceNeeded>Light</terrainAffordanceNeeded>
    <soundSpawned>SpitterSpawn</soundSpawned>
    <soundImpactDefault>Fleshmass_Damaged</soundImpactDefault>
    <rotatable>false</rotatable>
    <building>
      <isInert>true</isInert>
      <claimable>false</claimable>
      <canBuildNonEdificesUnder>false</canBuildNonEdificesUnder>
      <deconstructible>false</deconstructible>
      <quickTargetable>true</quickTargetable>
      <isTargetable>true</isTargetable>
      <destroyEffecter>FleshmassDestroyed</destroyEffecter>
    </building>
    <verbs>
      <li>
        <verbClass>Verb_Shoot</verbClass>
        <forceNormalTimeSpeed>false</forceNormalTimeSpeed>
        <warmupTime>4.0</warmupTime>
        <forcedMissRadius>3</forcedMissRadius>
        <defaultProjectile>Bullet_Shell_AcidSpit</defaultProjectile>
        <isMortar>true</isMortar>
        <requireLineOfSight>false</requireLineOfSight>
        <minRange>6.9</minRange>
        <range>41.9</range>
        <burstShotCount>1</burstShotCount>
        <soundCast>SpitterSpit</soundCast>
        <targetParams>
          <canTargetLocations>true</canTargetLocations>
        </targetParams>
      </li>
    </verbs>
    <comps>
      <li>
        <compClass>CompFleshmassSpitter</compClass>
      </li>
      <li Class="CompProperties_FleshmassBase">
        <size>2</size>
      </li>
    </comps>
  </ThingDef>

  <ThingDef ParentName="BaseBullet">
    <defName>Bullet_Shell_AcidSpit</defName>
    <label>acid spit</label>
    <graphicData>
      <graphicClass>Graphic_Single_AgeSecs</graphicClass>
      <texPath>Things/Projectile/FleshmassSpitterProjectileSheet</texPath>
      <drawSize>(.75, .75)</drawSize>
      <shaderType>MoteGlow</shaderType>
    </graphicData>
    <thingClass>Projectile_Explosive</thingClass>
    <projectile>
      <useGraphicClass>True</useGraphicClass>
      <shadowSize>1</shadowSize>
      <damageDef>AcidBurn</damageDef>
      <spinRate>20</spinRate>
      <damageAmountBase>10</damageAmountBase>
      <speed>41</speed>
      <arcHeightFactor>1</arcHeightFactor>
      <explosionRadius>3.9</explosionRadius>
      <flyOverhead>true</flyOverhead>
      <soundExplode>SpitterSpitLands</soundExplode>
      <filth>Filth_SpentAcid</filth>
      <filthCount>1</filthCount>
      <explosionEffect>Shell_AcidSpitImpact</explosionEffect>
      <explosionEffectLifetimeTicks>60</explosionEffectLifetimeTicks>
      <doExplosionVFX>false</doExplosionVFX>
    </projectile>
    <comps>
        <li Class="CompProperties_ProjectileEffecter">
          <effecterDef>Shell_AcidSpitStream</effecterDef>
        </li>
        <li Class="CompProperties_ProjectileEffecter">
          <effecterDef>Shell_AcidSpitLaunched</effecterDef>
        </li>
    </comps>
  </ThingDef>
</Defs>