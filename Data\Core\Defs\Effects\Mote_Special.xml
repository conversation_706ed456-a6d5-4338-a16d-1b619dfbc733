﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <!-- Interaction bubbles -->

  <ThingDef Name="InteractionMoteBase" ParentName="MoteBase" Abstract="True">
    <thingClass>MoteBubble</thingClass>
    <altitudeLayer>MetaOverlays</altitudeLayer>
    <mote>
      <solidTime>2.0</solidTime>
      <attachedDrawOffset>(0.75, 0, 0.75)</attachedDrawOffset>
    </mote>
    <graphicData>
      <shaderType>Cutout</shaderType>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="InteractionMoteBase">
    <defName>Mote_Speech</defName>
    <graphicData>
      <texPath>Things/Mote/Speech</texPath>
      <drawSize>1.25</drawSize>
    </graphicData>
  </ThingDef>

  <ThingDef ParentName="InteractionMoteBase">
    <defName>Mote_Thought</defName>
    <graphicData>
      <texPath>Things/Mote/Thought</texPath>
      <drawSize>1.25</drawSize>
  </graphicData>
  </ThingDef>

  <!-- Mood thought bubbles -->
  
  <ThingDef Name="MoodMoteBase" ParentName="MoteBase" Abstract="True">
    <thingClass>MoteBubble</thingClass>
    <altitudeLayer>MetaOverlays</altitudeLayer>
    <graphicData>
      <texPath>Things/Mote/Thought</texPath>
      <shaderType>TransparentPostLight</shaderType>
      <drawSize>1.0</drawSize>
    </graphicData>
    <mote>
      <attachedDrawOffset>(0.75, 0, 0.75)</attachedDrawOffset>
      <solidTime>0.65</solidTime>
      <fadeOutTime>1.2</fadeOutTime>
    </mote>
  </ThingDef>

  <ThingDef ParentName="MoodMoteBase">
    <defName>Mote_ThoughtGood</defName>
    <graphicData>
      <color>(100,200,100)</color>
    </graphicData>
  </ThingDef>
  
  <ThingDef ParentName="MoodMoteBase">
    <defName>Mote_ThoughtBad</defName>
    <graphicData>
      <color>(155,70,70)</color>
    </graphicData>
  </ThingDef>

  <!-- Misc -->

  <ThingDef ParentName="MoteBase">
    <defName>Mote_Text</defName>
    <graphicData>
      <texPath>Things/Mote/Transparent</texPath>
    </graphicData>
    <thingClass>MoteText</thingClass>
    <drawGUIOverlay>true</drawGUIOverlay>
    <altitudeLayer>MetaOverlays</altitudeLayer>
    <mote>
          <realTime>true</realTime>
      <solidTime>2.2</solidTime>
    </mote>
  </ThingDef>

  <ThingDef ParentName="MoteBase" Name="Mote_ProgressBar">
    <defName>Mote_ProgressBar</defName>
    <graphicData>
      <texPath>Things/Mote/Transparent</texPath>
    </graphicData>
    <thingClass>MoteProgressBar</thingClass>
    <altitudeLayer>MetaOverlays</altitudeLayer>
    <mote>
      <solidTime>999</solidTime>
    </mote>
  </ThingDef>

  <ThingDef ParentName="Mote_ProgressBar">
    <defName>Mote_ProgressBarAlwaysVisible</defName>
    <thingClass>MoteProgressBarAlwaysVisible</thingClass>
  </ThingDef>

  <ThingDef ParentName="MoteBase">
    <defName>Mote_TempRoof</defName>
    <graphicData>
      <texPath>Things/Mote/TempRoof</texPath>
    </graphicData>
    <altitudeLayer>MoteOverhead</altitudeLayer>
    <mote>
      <fadeInTime>0.04</fadeInTime>
      <solidTime>1.2</solidTime>
      <fadeOutTime>1.5</fadeOutTime>
    </mote>
  </ThingDef>
  
  <ThingDef ParentName="MoteBase">
    <defName>Mote_Danger</defName>
    <graphicData>
      <texPath>Things/Mote/ColonistFleeing</texPath>
    </graphicData>
    <thingClass>MoteThrownAttached</thingClass>
    <altitudeLayer>MetaOverlays</altitudeLayer>
    <mote>
      <fadeInTime>0.25</fadeInTime>
      <solidTime>1</solidTime>
      <fadeOutTime>1.4</fadeOutTime>
    </mote>
  </ThingDef>

</Defs>
