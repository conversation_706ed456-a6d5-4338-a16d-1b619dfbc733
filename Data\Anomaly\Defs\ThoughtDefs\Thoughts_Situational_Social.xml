﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThoughtDef>
    <defName>SpokeToDisturbing</defName>
    <thoughtClass>Thought_MemorySocial</thoughtClass>
    <durationDays>1</durationDays>
    <stackLimit>100</stackLimit>
    <stackLimitForSameOtherPawn>10</stackLimitForSameOtherPawn>
    <developmentalStageFilter>Baby, Child, Adult</developmentalStageFilter>
    <socialTargetDevelopmentalStageFilter>Baby, Child, Adult</socialTargetDevelopmentalStageFilter>
    <nullifyingTraits>
      <li>Disturbing</li>
    </nullifyingTraits>
    <nullifyingHediffs>
      <li>Inhumanized</li>
    </nullifyingHediffs>
    <stages>
      <li>
        <label>unsettling conversation</label>
        <baseOpinionOffset>-3</baseOpinionOffset>
      </li>
    </stages>
    <thoughtToMake>SpokeToDisturbingMood</thoughtToMake>
  </ThoughtDef>
  
  <ThoughtDef>
    <defName>Joyous</defName>
    <thoughtClass>Thought_SituationalSocial</thoughtClass>
    <workerClass>ThoughtWorker_Joyous</workerClass>
    <validWhileDespawned>true</validWhileDespawned>
    <stages>
      <li>
        <label>joyous</label>
        <baseOpinionOffset>20</baseOpinionOffset>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>GhoulNegative</defName>
    <thoughtClass>Thought_SituationalSocial</thoughtClass>
    <workerClass>ThoughtWorker_Ghoul</workerClass>
    <validWhileDespawned>true</validWhileDespawned>
    <ignoreMutants>false</ignoreMutants>
    <nullifyingHediffs>
      <li>Inhumanized</li>
    </nullifyingHediffs>
    <stages>
      <li>
        <label>ghoul</label>
        <baseOpinionOffset>-20</baseOpinionOffset>
      </li>
    </stages>
  </ThoughtDef>

  <ThoughtDef>
    <defName>GhoulPositive</defName>
    <thoughtClass>Thought_SituationalSocial</thoughtClass>
    <workerClass>ThoughtWorker_Ghoul</workerClass>
    <validWhileDespawned>true</validWhileDespawned>
    <ignoreMutants>false</ignoreMutants>
    <requiredHediffs>
      <li>Inhumanized</li>
    </requiredHediffs>
    <stages>
      <li>
        <label>ghoul</label>
        <baseOpinionOffset>20</baseOpinionOffset>
      </li>
    </stages>
  </ThoughtDef>

</Defs>
