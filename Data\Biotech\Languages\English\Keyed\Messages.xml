<?xml version="1.0" encoding="utf-8" ?>
<LanguageData>
  
  <MessageColonistReaching2ndTrimesterPregnancy>{PAWN_labelShort}'s pregnancy progressed from 1st to 2nd trimester.</MessageColonistReaching2ndTrimesterPregnancy>
  <MessageColonistReaching3rdTrimesterPregnancy>{PAWN_labelShort}'s pregnancy progressed from 2nd to 3rd trimester.</MessageColonistReaching3rdTrimesterPregnancy>
  <MessageColonistInFinalStagesOfLabor>{PAWN_labelShort}'s labor has begun the final stages.</MessageColonistInFinalStagesOfLabor>

  <MessageMechChargerDestroyedMechGoesBerserk>A mech charger was destroyed while {PAWN_nameDef} was charging! The power disruption has scrambled its brain, and it is going berserk.</MessageMechChargerDestroyedMechGoesBerserk>

  <MessageCantUseOnResistingPerson>Cannot use {ABILITY_label} on anyone who would actively resist.</MessageCantUseOnResistingPerson>
  <MessageCannotUseOnSameXenotype>{0_labelShort} already has the {0_xenotype} xenotype.</MessageCannotUseOnSameXenotype>
  <MessagePawnHasNoXenogenes>{0_labelShort} has no available xenogenes to implant.</MessagePawnHasNoXenogenes>
  <MessageCanOnlyTargetColonistsPrisonersAndSlaves>Xenogerms can only be implanted into colonists, prisoners, and slaves.</MessageCanOnlyTargetColonistsPrisonersAndSlaves>
  <MessageCannotImplantInTempFactionMembers>Cannot implant in temporary faction members.</MessageCannotImplantInTempFactionMembers>
  <MessageXenogermCompleted>A xenogerm has been completed.</MessageXenogermCompleted>
  <MessageXenogermCancelledMissingPack>Work on the xenogerm at {0_labelShort} was cancelled: Missing required genepack.</MessageXenogermCancelledMissingPack>
  <MessageAbsorbingXenogermWillAngerFaction>Absorbing {PAWN_nameDef}'s xenogerm will anger {0}.</MessageAbsorbingXenogermWillAngerFaction>
  <MessageCannotUseOnOtherFactions>{ABILITY_label} can only be used on colonists, prisoners, and slaves.</MessageCannotUseOnOtherFactions>
  <MessageCannotBecomeNonPreferredXenotype>{0_labelShort} refuses to become a disliked xenotype.</MessageCannotBecomeNonPreferredXenotype>

  <MessageCaravanAddingEscortingMech>Auto-adding {MECH_nameDef} to the caravan, since it is escorting {OVERSEER_nameDef}</MessageCaravanAddingEscortingMech> 
  <MessageCaravanRemovingEscortingMech>Auto-removing {MECH_nameDef} from the caravan, since it is escorting {OVERSEER_nameDef}</MessageCaravanRemovingEscortingMech>

  <MessageMechanitorCasketOpened>{0} was a mechanitor. You can remove {0_possessive} {1_label} and use it on a colonist.</MessageMechanitorCasketOpened>
  
  <MessageNoBloodfeedersPrisonerInteractionReset>Some prisoners' interaction mode required a bloodfeeder. Since the last bloodfeeder is gone, their interaction modes have reset to "No interaction".</MessageNoBloodfeedersPrisonerInteractionReset>

  <MessagePregnancyTerminated>{PAWN_nameDef}'s pregnancy was successfully terminated.</MessagePregnancyTerminated>
  
  <!-- Pollution -->
  <MessageAngeredPollutedCell>{0} has been angered ({1})</MessageAngeredPollutedCell>
  <MessageWarningPollutedCell>Need unpolluted terrain.</MessageWarningPollutedCell>
  <MessageWarningNotPollutedCell>Need polluted terrain.</MessageWarningNotPollutedCell>
  <MessagePlantDiedOfPollution>{0} has died because of pollution.</MessagePlantDiedOfPollution>
  <MessagePlantDiedOfNoPollution>{0} has died because of a lack of pollution.</MessagePlantDiedOfNoPollution>
  <MessageWorldTilePollutionChanged>Pollution has increased by {0} at {1}.</MessageWorldTilePollutionChanged>
  <MessageCocoonDisturbed>A cocoon has been disturbed and is opening!</MessageCocoonDisturbed>
  <MessageCocoonDisturbedPlural>Cocoons have been disturbed and are opening!</MessageCocoonDisturbedPlural>
  <MessagePlantDiedOfRot_PollutedTerrain>{0} has rotted due to polluted terrain.</MessagePlantDiedOfRot_PollutedTerrain>

  <MessageTargetMustBeDownedToForceReimplant>{PAWN_nameDef} must be downed before you can force {PAWN_objective} to reimplant {PAWN_possessive} xenogerm.</MessageTargetMustBeDownedToForceReimplant>

  <MessageWorkTypeDisabledAge>{0_nameDef} is only {1} years old. {2} work can only be done from age {3}.</MessageWorkTypeDisabledAge>

  <MessageDeathrestingPawnCanWakeSafely>{PAWN_nameDef} can now wake from deathrest safely.</MessageDeathrestingPawnCanWakeSafely>

  <MessageMechanitorLostControlOfMech>{0_nameDef} has lost control of a {1_kindBase} mech</MessageMechanitorLostControlOfMech>
  <MessageMechanitorDisconnectedFromMech>{0_nameDef} has disconnected from {1_nameDef} mech.</MessageMechanitorDisconnectedFromMech>

  <!-- Xenogerm -->
  <MetabolismTooLowToCreateXenogerm>The total metabolism of the selected genes must be {MIN} or higher to continue, but currently it is only {AMOUNT}.</MetabolismTooLowToCreateXenogerm>
  <ComplexityTooHighToCreateXenogerm>The total complexity of the selected genes must be {MAX} or less to create a xenogerm, but currently it is {AMOUNT}.</ComplexityTooHighToCreateXenogerm>
  <MessageNoSelectedGenepacks>Cannot create a xenogerm with no genes.</MessageNoSelectedGenepacks>
  <MessageNoSelectedGenes>Cannot create a xenotype with no genes.</MessageNoSelectedGenes>
  <CanOnlyStoreNumGenepacks>{0_label} can only store {1} genepacks.</CanOnlyStoreNumGenepacks>
  <XenotypeNameCannotBeEmpty>Xenotype name cannot be empty.</XenotypeNameCannotBeEmpty>
  <MessageGeneMissingPrerequisite>{0} lacks a prerequisite gene</MessageGeneMissingPrerequisite>

  <MessageDeathrestCapacityChanged>{PAWN_nameDef}'s deathrest capacity has increased from {OLD} to {NEW}.</MessageDeathrestCapacityChanged>

  <SanguophagesArrivingSoon>The sanguophages will arrive in a few hours for their meeting.</SanguophagesArrivingSoon>
  <SanguophagesBegunMeeting>The sanguophages have begun their meeting.</SanguophagesBegunMeeting>
  <SanguophagesLeavingTemperature>The sanguophages are leaving due to life-threatening temperature.</SanguophagesLeavingTemperature>

  <MessagePawnWokenFromSunlight>{PAWN_nameDef} was woken from {PAWN_possessive} deathrest due to sunlight exposure.</MessagePawnWokenFromSunlight>
  <MessageBedExposedToSunlight>The {BED_labelShort} {PAWN_nameDef} is assigned to use for deathrest has been exposed to sunlight. {PAWN_nameDef} cannot deathrest there.</MessageBedExposedToSunlight>

  <GeneExtractorNoPowerEjectedMessage>{PAWN_labelShort} was ejected from a gene extractor due to lack of power.</GeneExtractorNoPowerEjectedMessage>

  <!-- Mechanitor -->
  <MessagePawnKilledRipscanner>{PAWN_nameDef}'s brain has been destroyed by the subcore ripscanner.</MessagePawnKilledRipscanner>
  <MessageSubcoreSoftscannerCompleted>Subcore softscanner completed for {PAWN_nameDef}.</MessageSubcoreSoftscannerCompleted>

  <MessageCannotResurrectDessicatedCorpse>Cannot resurrect dessicated corpses.</MessageCannotResurrectDessicatedCorpse>

  <MessageAboutToExplode>{0} is about to explode!</MessageAboutToExplode>

  <!-- Childcare -->
  <MessageChildcareDisabled>{FEEDER_labelShort} cannot perform childcare tasks.</MessageChildcareDisabled>
  <MessageChildcareNotAssigned>{FEEDER_labelShort} is not assigned to do childcare tasks.</MessageChildcareNotAssigned>

  <MessageTooManyCustomXenotypes>You already have the maximum number of custom xenotypes. You must delete some if you wish to create more.</MessageTooManyCustomXenotypes>
  <MessageConflictingGenesPresent>Cannot save xenotype with multiple conflicting genes.</MessageConflictingGenesPresent>

  <MessageDeathrestBuildingBound>{BUILDING_labelShort} has been bound to {PAWN_nameDef} ({CUR}/{MAX} deathrest capacity).</MessageDeathrestBuildingBound>

  <MessageDevelopmentalStageSelectionDisabledByScenario>Developmental stage selection disabled by scenario.</MessageDevelopmentalStageSelectionDisabledByScenario>

  <MessagePawnHadNotEnoughBloodToProduceHemogenPack>{PAWN_nameDef} did not have enough blood to produce a hemogen pack. </MessagePawnHadNotEnoughBloodToProduceHemogenPack>
  <MessageCannotStartHemogenExtraction>{PAWN_nameDef} doesn't have enough blood to produce a hemogen pack.</MessageCannotStartHemogenExtraction>

  <MessageCannotPostponeGrowthMoment>You must choose how {PAWN_nameDef} will grow now.</MessageCannotPostponeGrowthMoment>

  <MessageDraftedPawnCarryingBaby>Warning: {0_labelShort} is carrying a baby while drafted.</MessageDraftedPawnCarryingBaby>
  <MessageTakingBabyToSafeTemperature>{ADULT_nameDef} is moving {BABY_nameDef} somewhere with a safer temperature.</MessageTakingBabyToSafeTemperature>

  <MessagePregnancyFailed>Pregnancy failed between {MOTHER_nameDef} and {FATHER_nameDef}</MessagePregnancyFailed>
  <MessageFerilizeFailed>{PAWN_nameDef} failed to fertilize ovum</MessageFerilizeFailed>
  <CombinedGenesExceedMetabolismLimits>Combined genes exceeded metabolism limits.</CombinedGenesExceedMetabolismLimits>

</LanguageData>