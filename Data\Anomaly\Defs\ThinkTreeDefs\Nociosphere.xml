﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>
  
  <ThinkTreeDef>
    <defName>Nociosphere</defName>
    <thinkRoot Class="ThinkNode_Priority">
      <subNodes>
        <!-- Despawned -->
        <li Class="ThinkNode_Subtree">
          <treeDef>Despawned</treeDef>
        </li>
        
        <!-- Downed -->
        <li Class="ThinkNode_Subtree">
          <treeDef>Downed</treeDef>
        </li>
        
        <!-- Activity-Dormant -->
        <li Class="ThinkNode_ActivityModePassive">
          <subNodes>
            <li Class="JobGiver_ActivityDormant" />
          </subNodes>
        </li>

        <!-- Depart if required -->
        <li Class="JobGiver_NociosphereDepart" />
        
        <!-- Teleport if possible -->
        <li Class="JobGiver_NociosphereSkip" />
        
        <!-- Attacks -->
        <li Class="JobGiver_NociosphereFight" />
        
        <!-- Idle fallback -->
        <li Class="JobGiver_Idle" />
      </subNodes>
    </thinkRoot>
  </ThinkTreeDef>

  <ThinkTreeDef>
    <defName>NociosphereConstant</defName>
    <thinkRoot Class="ThinkNode_Priority">
      <subNodes>
        <!-- Despawned -->
        <li Class="ThinkNode_Subtree">
          <treeDef>Despawned</treeDef>
        </li>
      </subNodes>
    </thinkRoot>
  </ThinkTreeDef>
  
</Defs>
