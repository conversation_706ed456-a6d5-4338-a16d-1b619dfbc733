﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <!--===================== Misc =====================-->

  <BodyPartDef>
    <defName>Body</defName>
    <label>body</label>
    <hitPoints>40</hitPoints>
    <skinCovered>true</skinCovered>
    <executionPartPriority>500</executionPartPriority>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Beak</defName>
    <label>beak</label>
    <hitPoints>20</hitPoints>
    <frostbiteVulnerability>1.5</frostbiteVulnerability>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <skinCovered>false</skinCovered>
    <solid>true</solid>
    <alive>false</alive>
    <bleedRate>0</bleedRate>
    <tags>
      <li>EatingSource</li>
      <li>TalkingSource</li>
      <li>ManipulationLimbCore</li>
    </tags>
  </BodyPartDef>
  
  <BodyPartDef>
    <defName>InsectHead</defName>
    <label>head</label>
    <hitPoints>30</hitPoints>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <skinCovered>false</skinCovered>
    <executionPartPriority>500</executionPartPriority>
  </BodyPartDef>

  <BodyPartDef>
    <defName>SnakeBody</defName>
    <label>body</label>
    <hitPoints>30</hitPoints>
    <skinCovered>true</skinCovered>
    <tags>
      <li>MovingLimbCore</li>
    </tags>
    <executionPartPriority>500</executionPartPriority>
  </BodyPartDef>

  <BodyPartDef>
    <defName>SnakeHead</defName>
    <label>head</label>
    <hitPoints>30</hitPoints>
    <skinCovered>true</skinCovered>
    <tags>
      <li>HearingSource</li>
    </tags>
  </BodyPartDef>

  <BodyPartDef>
    <defName>SnakeMouth</defName>
    <label>mouth</label>
    <hitPoints>20</hitPoints>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <skinCovered>true</skinCovered>
    <tags>
      <li>EatingSource</li>
      <li>TalkingSource</li>
      <li>ManipulationLimbCore</li>
    </tags>
  </BodyPartDef>

  <BodyPartDef>
    <defName>InsectHeart</defName>
    <label>heart</label>
    <hitPoints>20</hitPoints>
    <bleedRate>5</bleedRate>
    <skinCovered>false</skinCovered>
    <tags>
      <li>BloodPumpingSource</li>
      <li>BloodFiltrationSource</li>
      <li>MetabolismSource</li>
      <li>BreathingSource</li>
    </tags>
  </BodyPartDef>

  <BodyPartDef>
    <defName>InsectNostril</defName>
    <label>nostril</label>
    <hitPoints>10</hitPoints>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <skinCovered>false</skinCovered>
  </BodyPartDef>

  <BodyPartDef>
    <defName>InsectMouth</defName>
    <label>mouth</label>
    <hitPoints>10</hitPoints>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <skinCovered>false</skinCovered>
    <tags>
      <li>EatingSource</li>
      <li>ManipulationLimbCore</li>
    </tags>
  </BodyPartDef>

  <BodyPartDef>
    <defName>AnimalJaw</defName>
    <label>jaw</label>
    <hitPoints>10</hitPoints>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <skinCovered>false</skinCovered>
    <solid>true</solid>
    <bleedRate>0</bleedRate>
    <tags>
      <li>ManipulationLimbCore</li>
    </tags>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Jowl</defName>
    <label>jowl</label>
    <hitPoints>20</hitPoints>
    <skinCovered>true</skinCovered>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Tail</defName>
    <label>tail</label>
    <hitPoints>10</hitPoints>
    <frostbiteVulnerability>8</frostbiteVulnerability>
    <skinCovered>true</skinCovered>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Antenna</defName>
    <label>antenna</label>
    <hitPoints>10</hitPoints>
    <frostbiteVulnerability>10</frostbiteVulnerability>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <skinCovered>false</skinCovered>
    <alive>false</alive>
    <bleedRate>0.3</bleedRate>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Pronotum</defName>
    <label>pronotum</label>
    <hitPoints>20</hitPoints>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <skinCovered>false</skinCovered>
    <solid>true</solid>
    <bleedRate>0</bleedRate>
    <tags>
      <li>BreathingPathway</li>
      <li>EatingPathway</li>
      <li>TalkingPathway</li>
    </tags>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Shell</defName>
    <label>shell</label>
    <hitPoints>30</hitPoints>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <skinCovered>false</skinCovered>
    <alive>false</alive>
    <bleedRate>0</bleedRate>
    <solid>true</solid>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Elytra</defName>
    <label>elytra</label>
    <hitPoints>30</hitPoints>
    <frostbiteVulnerability>0.5</frostbiteVulnerability>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <skinCovered>false</skinCovered>
    <alive>false</alive>
    <bleedRate>0</bleedRate>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Tusk</defName>
    <label>tusk</label>
    <hitPoints>20</hitPoints>
    <skinCovered>false</skinCovered>
    <solid>true</solid>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <alive>false</alive>
    <bleedRate>0</bleedRate>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Trunk</defName>
    <label>trunk</label>
    <hitPoints>15</hitPoints>
    <frostbiteVulnerability>5</frostbiteVulnerability>
    <skinCovered>true</skinCovered>
    <tags>
      <li>ManipulationLimbCore</li>
    </tags>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Horn</defName>
    <label>horn</label>
    <hitPoints>20</hitPoints>
    <skinCovered>false</skinCovered>
    <solid>true</solid>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <alive>false</alive>
    <bleedRate>0</bleedRate>
  </BodyPartDef>
  
  <BodyPartDef>
    <defName>HeadClaw</defName>
    <label>head claw</label>
    <hitPoints>15</hitPoints>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <skinCovered>false</skinCovered>
    <solid>true</solid>
    <alive>false</alive>
    <bleedRate>0</bleedRate>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Hump</defName>
    <label>hump</label>
    <hitPoints>20</hitPoints>
    <skinCovered>true</skinCovered>
    <solid>false</solid>
  </BodyPartDef>

  <BodyPartDef>
    <defName>TurtleShell</defName>
    <label>shell</label>
    <hitPoints>50</hitPoints>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <skinCovered>false</skinCovered>
    <solid>true</solid>
    <alive>false</alive>
    <bleedRate>0</bleedRate>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Plastron</defName>
    <label>plastron</label>
    <hitPoints>20</hitPoints>
    <skinCovered>false</skinCovered>
    <solid>true</solid>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <alive>false</alive>
    <bleedRate>0</bleedRate>
  </BodyPartDef>

  <BodyPartDef>
    <defName>TurtleBeak</defName>
    <label>beak</label>
    <hitPoints>10</hitPoints>
    <skinCovered>true</skinCovered>
    <solid>true</solid>
    <alive>false</alive>
    <bleedRate>0</bleedRate>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <tags>
      <li>ManipulationLimbCore</li>
    </tags>
  </BodyPartDef>

  <!--===================== Insect legs =====================-->

  <BodyPartDef>
    <defName>InsectLeg</defName>
    <label>leg</label>
    <hitPoints>20</hitPoints>
    <frostbiteVulnerability>0.1</frostbiteVulnerability>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <skinCovered>false</skinCovered>
    <tags>
      <li>MovingLimbCore</li>
      <li>ManipulationLimbCore</li>
    </tags>
  </BodyPartDef>

  <!--===================== Paws =====================-->

  <BodyPartDef>
    <defName>Paw</defName>
    <label>paw</label>
    <hitPoints>10</hitPoints>
    <frostbiteVulnerability>6</frostbiteVulnerability>
    <skinCovered>true</skinCovered>
    <tags>
      <li>MovingLimbSegment</li>
    </tags>
  </BodyPartDef>

  <!--===================== Hoofs =====================-->

  <BodyPartDef>
    <defName>Hoof</defName>
    <label>hoof</label>
    <hitPoints>10</hitPoints>
    <frostbiteVulnerability>6</frostbiteVulnerability>
    <skinCovered>true</skinCovered>
    <alive>false</alive>
    <bleedRate>0</bleedRate>
    <solid>true</solid>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <tags>
      <li>MovingLimbSegment</li>
    </tags>
  </BodyPartDef>

  <!--===================== Claws =====================-->
  
  <BodyPartDef Name="ClawBase" Abstract="True">
    <hitPoints>7</hitPoints>
    <solid>true</solid>
    <alive>false</alive>
    <bleedRate>0</bleedRate>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <skinCovered>false</skinCovered>
    <frostbiteVulnerability>10</frostbiteVulnerability>
    <pawnGeneratorCanAmputate>true</pawnGeneratorCanAmputate>
  </BodyPartDef>

  <BodyPartDef ParentName="ClawBase">
    <defName>FrontClaw</defName>
    <label>front claw</label>
    <labelShort>claw</labelShort>
    <tags>
      <li>MovingLimbDigit</li>
      <li>ManipulationLimbDigit</li>
    </tags>
  </BodyPartDef>

  <BodyPartDef ParentName="ClawBase">
    <defName>RearClaw</defName>
    <label>rear claw</label>
    <labelShort>claw</labelShort>
    <tags>
      <li>MovingLimbDigit</li>
    </tags>
  </BodyPartDef>

</Defs>
