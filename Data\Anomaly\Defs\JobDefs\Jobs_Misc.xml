﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <!-- Revenant -->
  <JobDef>
    <defName>RevenantAttack</defName>
    <driverClass>JobDriver_RevenantAttack</driverClass>
    <reportString>stalking TargetA.</reportString>
    <checkOverrideOnDamage>Never</checkOverrideOnDamage>
  </JobDef>

  <JobDef>
    <defName>RevenantWander</defName>
    <driverClass>JobDriver_RevenantWander</driverClass>
    <reportString>wandering</reportString>
  </JobDef>

  <JobDef>
    <defName>RevenantEscape</defName>
    <driverClass>JobDriver_RevenantEscape</driverClass>
    <reportString>escaping</reportString>
  </JobDef>

  <JobDef>
    <defName>RevenantSleep</defName>
    <driverClass>JobDriver_RevenantSleep</driverClass>
    <reportString>hibernating</reportString>
  </JobDef>

  <JobDef>
    <defName>SmashThing</defName>
    <driverClass>JobDriver_UseItem</driverClass>
    <reportString>smashing TargetA.</reportString>
    <allowOpportunisticPrefix>true</allowOpportunisticPrefix>
  </JobDef>

  <JobDef>
    <defName>CarryToEntityHolder</defName>
    <driverClass>JobDriver_CarryToEntityHolder</driverClass>
    <reportString>carrying TargetB to TargetA.</reportString>
    <allowOpportunisticPrefix>true</allowOpportunisticPrefix>
  </JobDef>

  <JobDef>
    <defName>TransferBetweenEntityHolders</defName>
    <driverClass>JobDriver_TransferBetweenEntityHolders</driverClass>
    <reportString>transferring TargetC to another holding location.</reportString>
  </JobDef>

  <JobDef>
    <defName>CarryToEntityHolderAlreadyHolding</defName>
    <driverClass>JobDriver_CarryToEntityHolderAlreadyHolding</driverClass>
    <reportString>carrying TargetB to TargetA.</reportString>
    <allowOpportunisticPrefix>true</allowOpportunisticPrefix>
  </JobDef>

  <JobDef>
    <defName>ReleaseEntity</defName>
    <driverClass>JobDriver_ReleaseEntity</driverClass>
    <reportString>releasing TargetB</reportString>
    <allowOpportunisticPrefix>true</allowOpportunisticPrefix>
  </JobDef>

  <JobDef>
    <defName>TendEntity</defName>
    <driverClass>JobDriver_TendEntity</driverClass>
    <reportString>tending to TargetA.</reportString>
    <casualInterruptible>false</casualInterruptible>
  </JobDef>
  
  <JobDef>
    <defName>ExecuteEntity</defName>
    <driverClass>JobDriver_ExecuteEntity</driverClass>
    <reportString>executing TargetA.</reportString>
    <casualInterruptible>false</casualInterruptible>
  </JobDef>

  <JobDef>
    <defName>ExtractBioferrite</defName>
    <driverClass>JobDriver_ExtractBioferrite</driverClass>
    <reportString>extracting bioferrite from TargetA.</reportString>
    <casualInterruptible>false</casualInterruptible>
  </JobDef>

  <JobDef>
    <defName>ActivityDormant</defName>
    <driverClass>JobDriver_ActivityDormant</driverClass>
    <reportString>low activity dormancy</reportString>
    <casualInterruptible>false</casualInterruptible>
    <allowOpportunisticPrefix>true</allowOpportunisticPrefix>
    <neverShowWeapon>true</neverShowWeapon>
  </JobDef>
  
  <JobDef>
    <defName>EntityGoPassive</defName>
    <driverClass>JobDriver_ActivityGoPassive</driverClass>
    <reportString>going dormant</reportString>
    <casualInterruptible>false</casualInterruptible>
    <allowOpportunisticPrefix>true</allowOpportunisticPrefix>
  </JobDef>
  
  <JobDef>
    <defName>NociosphereDepart</defName>
    <driverClass>JobDriver_NociosphereDepart</driverClass>
    <reportString>departing</reportString>
    <casualInterruptible>false</casualInterruptible>
    <allowOpportunisticPrefix>true</allowOpportunisticPrefix>
    <checkOverrideOnDamage>Never</checkOverrideOnDamage>
  </JobDef>
  
  <JobDef>
    <defName>HateChanting</defName>
    <driverClass>JobDriver_HateChant</driverClass>
    <reportString>hate chanting.</reportString>
    <casualInterruptible>false</casualInterruptible>
  </JobDef>

  <JobDef>
    <defName>TalkCreepJoiner</defName>
    <driverClass>JobDriver_TalkCreepJoiner</driverClass>
    <reportString>talking to TargetA.</reportString>
    <allowOpportunisticPrefix>true</allowOpportunisticPrefix>
  </JobDef>
  
  <JobDef>
    <defName>GoldenCubePlay</defName>
    <driverClass>JobDriver_PlayGoldenCube</driverClass>
    <reportString>playing with golden cube.</reportString>
    <joyDuration>2000</joyDuration>
    <allowOpportunisticPrefix>true</allowOpportunisticPrefix>
  </JobDef>

  <JobDef>
    <defName>BuildCubeSculpture</defName>
    <driverClass>JobDriver_BuildCubeSculpture</driverClass>
    <reportString>building a {0} sculpture.</reportString>
    <allowOpportunisticPrefix>true</allowOpportunisticPrefix>
    <alwaysShowReport>true</alwaysShowReport>
  </JobDef>

  <JobDef>
    <defName>InvestigateMonolith</defName>
    <driverClass>JobDriver_InvestigateMonolith</driverClass>
    <reportString>investigating TargetA.</reportString>
  </JobDef>

  <JobDef>
    <defName>ActivateMonolith</defName>
    <driverClass>JobDriver_ActivateMonolith</driverClass>
    <reportString>activating TargetA.</reportString>
  </JobDef>

  <JobDef>
    <defName>TakeBioferriteOutOfHarvester</defName>
    <driverClass>JobDriver_TakeBioferriteOutOfHarvester</driverClass>
    <reportString>taking bioferrite out of TargetA.</reportString>
    <allowOpportunisticPrefix>true</allowOpportunisticPrefix>
  </JobDef>

  <JobDef>
    <defName>ChimeraSwitchToAttackMode</defName>
    <driverClass>JobDriver_ChimeraSwitchToAttackMode</driverClass>
    <reportString>attacking.</reportString>
  </JobDef>
  
  <JobDef>
    <defName>DevourerDigest</defName>
    <driverClass>JobDriver_DevourerDigest</driverClass>
    <checkOverrideOnDamage>Never</checkOverrideOnDamage>
    <casualInterruptible>false</casualInterruptible>
    <playerInterruptible>false</playerInterruptible>
  </JobDef>
  
  <JobDef>
    <defName>UnnaturalCorpseAttack</defName>
    <driverClass>JobDriver_UnnaturalCorpseAttack</driverClass>
    <reportString>chasing TargetA.</reportString>
    <checkOverrideOnDamage>Never</checkOverrideOnDamage>
  </JobDef>

  <JobDef>
    <defName>UnnaturalCorpseSkip</defName>
    <driverClass>JobDriver_UnnaturalCorpseSkip</driverClass>
    <reportString>chasing TargetA.</reportString> <!-- not visible to player -->
    <checkOverrideOnDamage>Never</checkOverrideOnDamage>
  </JobDef>

  <JobDef>
    <defName>FleeAndCowerShort</defName>
    <driverClass>JobDriver_FleeAndCowerShort</driverClass>
    <reportString>fleeing.</reportString>
    <checkOverrideOnDamage>OnlyIfInstigatorNotJobTarget</checkOverrideOnDamage>
    <isIdle>true</isIdle>
  </JobDef>
  
</Defs>