﻿<?xml version="1.0" encoding="utf-8"?>

<Defs>

  <!-- Shamblers -->

  <HediffDef>
    <defName>Shambler</defName>
    <label>shambler</label>
    <description>This dead creature has been re-animated by corrupted archites. Shamblers are slow and mindless, and will attack relentlessly. After a few days on their feet, shamblers die from metabolic exhaustion. Captured shamblers can't move and thus won't expire.</description>
    <hediffClass>Hediff_Shambler</hediffClass>
    <everCurableByItem>false</everCurableByItem>
    <duplicationAllowed>false</duplicationAllowed>
    <keepOnBodyPartRestoration>True</keepOnBodyPartRestoration>
    <stages>
      <li>
        <naturalHealingFactor>0</naturalHealingFactor>
        <painFactor>0</painFactor>
        <statFactors>
          <MeleeCooldownFactor>1.5</MeleeCooldownFactor> <!-- 66% as fast -->
          <PsychicSensitivity>0</PsychicSensitivity>
        </statFactors>
        <statOffsets>
          <ComfyTemperatureMin>-60</ComfyTemperatureMin>
          <ComfyTemperatureMax>30</ComfyTemperatureMax>
          <MinimumContainmentStrength>25</MinimumContainmentStrength>
          <ToxicResistance>1</ToxicResistance>
        </statOffsets>
        <capMods>
          <li>
            <capacity>Talking</capacity>
            <postFactor>0</postFactor>
          </li>
          <li>
            <capacity>Consciousness</capacity>
            <postFactor>0.7</postFactor>
          </li>
        </capMods>
      </li>
    </stages>
    <comps>
      <li Class="HediffCompProperties_DisappearsAndKills">
        <compClass>HediffComp_DisappearsAndKills_Shambler</compClass>
        <disappearsAfterTicks>252000~288000</disappearsAfterTicks> <!-- 4.2 ~ 4.8 days -->
        <showRemainingTime>true</showRemainingTime>
      </li>
      <li Class="HediffCompProperties_DisappearsOnDeath" />
      <li Class="HediffCompProperties_AttachPoints" />
    </comps>
  </HediffDef>

  <HediffDef>
    <defName>Rising</defName>
    <label>rising</label>
    <description>Mysterious archotechnological influences are reanimating this creature's corpse.</description>
    <everCurableByItem>false</everCurableByItem>
    <recordDownedTale>false</recordDownedTale>
    <stages>
      <li>
        <painFactor>0</painFactor>
        <capMods>
          <li>
            <capacity>Moving</capacity>
            <postFactor>0</postFactor>
          </li>
          <li>
            <capacity>Manipulation</capacity>
            <postFactor>0</postFactor>
          </li>
          <li>
            <capacity>Talking</capacity>
            <postFactor>0</postFactor>
          </li>
          <li>
            <capacity>Consciousness</capacity>
            <setMax>0.1</setMax>
          </li>
        </capMods>
      </li>
    </stages>
  </HediffDef>

  <!-- We need this hediff so that we can track which corpses came from shamblers since the shambler hediff is removed on death
       Currently this is used to increase deterioration rates and render scars on the corpses -->
  <HediffDef>
    <defName>ShamblerCorpse</defName>
    <label>shambler corpse</label>
    <description>This creature had previously been re-animated by corrupted archites.</description>
    <forceRemoveOnResurrection>true</forceRemoveOnResurrection>

    <!-- Since mutant status is reverted on death, we can maintain the scars here instead -->
    <renderNodeProperties>
      <li Class="PawnRenderNodeProperties_Overlay">
        <debugLabel>Shambler wounds</debugLabel>
        <workerClass>PawnRenderNodeWorker_OverlayShambler</workerClass>
        <overlayLayer>Body</overlayLayer>
        <baseLayer>20</baseLayer>
        <pawnType>HumanlikeOnly</pawnType>
      </li>
      <li>
        <debugLabel>Shambler wounds</debugLabel>
        <nodeClass>PawnRenderNode_AnimalPart</nodeClass>
        <workerClass>PawnRenderNodeWorker_OverlayShambler</workerClass>
        <overlayLayer>Body</overlayLayer>
        <baseLayer>20</baseLayer>
        <pawnType>NonHumanlikeOnly</pawnType>
      </li>
    </renderNodeProperties>
  </HediffDef>

  <!-- Ghouls -->

  <HediffDef>
    <defName>Ghoul</defName>
    <label>ghoul</label>
    <description>This person has been implanted with an archotech shard, twisting them into a jittering murder machine. Its body is an amalgam of flesh and metal, while its mind cycles between half-conscious stupor and murderous intent.\n\nThey cannot work; they only fight. Ghouls must eat raw meat. If they go hungry, they can turn hostile.\n\nMany find ghouls' constant twitching to be disturbing, even when they aren't killing someone.</description>
    <hediffClass>HediffWithComps</hediffClass>
    <everCurableByItem>false</everCurableByItem>
    <keepOnBodyPartRestoration>True</keepOnBodyPartRestoration>
    <stages>
      <li>
        <painFactor>0</painFactor>
        <statFactors>
          <MaxNutrition>2</MaxNutrition>
          <PsychicSensitivity>0</PsychicSensitivity>
        </statFactors>
        <statOffsets>
          <ComfyTemperatureMin>-40</ComfyTemperatureMin>
          <ComfyTemperatureMax>40</ComfyTemperatureMax>
          <MinimumContainmentStrength>35</MinimumContainmentStrength>
        </statOffsets>
        <capMods>
          <li>
            <capacity>Talking</capacity>
            <postFactor>0</postFactor>
          </li>
        </capMods>
      </li>
    </stages>
    <comps>
      <li Class="HediffCompProperties_AttachPoints" />
    </comps>
  </HediffDef>

  <HediffDef>
    <defName>MeatHunger</defName>
    <hediffClass>Hediff_MeatHunger</hediffClass>
    <label>meat hunger</label>
    <description>This creature is ravenously hungry for raw meat. It may become hostile if it gets too hungry.</description>
    <everCurableByItem>false</everCurableByItem>
    <maxSeverity>1</maxSeverity>
    <alwaysShowSeverity>true</alwaysShowSeverity>
    <stages>
      <li>
        <becomeVisible>false</becomeVisible>
      </li>
      <li>
        <minSeverity>0.1</minSeverity>
        <label>initial</label>
      </li>
      <li>
        <minSeverity>0.5</minSeverity>
        <label>moderate</label>
      </li>
      <li>
        <minSeverity>0.8</minSeverity>
        <label>extreme</label>
      </li>
    </stages>
  </HediffDef>

  <HediffDef>
    <defName>GhoulFrenzy</defName>
    <hediffClass>HediffWithComps</hediffClass>
    <label>ghoul frenzy</label>
    <description>This creature's blood is saturated with stress hormones and liquid energy, permitting it to move and attack at incredible speeds for a short time.</description>
    <everCurableByItem>false</everCurableByItem>
    <stages>
      <li>
        <statFactors>
          <MeleeCooldownFactor>0.7</MeleeCooldownFactor>
        </statFactors>
        <statOffsets>
          <MoveSpeed>4</MoveSpeed>
        </statOffsets>
      </li>
    </stages>
    <comps>
      <li Class="HediffCompProperties_Disappears">
        <showRemainingTime>true</showRemainingTime>
      </li>
      <li Class="HediffCompProperties_Effecter">
        <stateEffecter>GhoulFrenzy</stateEffecter>
      </li>
    </comps>
  </HediffDef>

  <HediffDef>
    <defName>ResurrectionComa</defName>
    <hediffClass>HediffWithComps</hediffClass>
    <label>resurrection coma</label>
    <description>A mixture of deadlife dust and advanced biochemicals course through this creature’s veins.</description>
    <stages>
      <li>
        <capMods>
          <li>
            <capacity>Consciousness</capacity>
            <setMax>0.1</setMax>
          </li>
        </capMods>
      </li>
  </stages>
    <comps>
      <li Class="HediffCompProperties_Disappears">
        <disappearsAfterTicks>10000~15000</disappearsAfterTicks> <!-- 4 ~ 6 hours -->
        <showRemainingTime>true</showRemainingTime>
      </li>
    </comps>
  </HediffDef>

  <!-- Awoken Corpse -->

  <HediffDef>
    <hediffClass>HediffWithComps</hediffClass>
    <defName>AwokenCorpse</defName>
    <label>awoken unnatural corpse</label>
    <description>The energy building within this corpse is erupting. It has risen and is attacking with increasing power and speed. It will not stop until it reaches its victim.</description>
    <initialSeverity>0.001</initialSeverity>
    <maxSeverity>1</maxSeverity>
    <everCurableByItem>false</everCurableByItem>
    <keepOnBodyPartRestoration>True</keepOnBodyPartRestoration>
    <comps>
      <li Class="HediffCompProperties_SeverityPerSecond">
        <severityPerSecondRange>0.0111</severityPerSecondRange> <!-- 15 seconds per stage, for 6 stages -->
      </li>
      <li Class="HediffCompProperties_MessageStageIncreased">
        <message>The unnatural corpse is moving faster!</message>
      </li>
    </comps>
    <stages>
      <li>
        <minSeverity>0</minSeverity>
        <statFactors>
          <MoveSpeed>0.5</MoveSpeed>
          <IncomingDamageFactor>0.2</IncomingDamageFactor>
        </statFactors>
      </li>
      <li>
        <minSeverity>0.166</minSeverity>
        <statFactors>
          <MoveSpeed>1</MoveSpeed>
          <IncomingDamageFactor>0.2</IncomingDamageFactor>
        </statFactors>
      </li>
      <li>
        <minSeverity>0.333</minSeverity>
        <statFactors>
          <MoveSpeed>1.5</MoveSpeed>
          <IncomingDamageFactor>0.2</IncomingDamageFactor>
        </statFactors>
      </li>
      <li>
        <minSeverity>0.5</minSeverity>
        <statFactors>
          <MoveSpeed>2</MoveSpeed>
          <IncomingDamageFactor>0.2</IncomingDamageFactor>
        </statFactors>
      </li>
      <li>
        <minSeverity>0.666</minSeverity>
        <statFactors>
          <MoveSpeed>2.5</MoveSpeed>
          <IncomingDamageFactor>0.2</IncomingDamageFactor>
        </statFactors>
      </li>
      <li>
        <minSeverity>0.833</minSeverity>
        <statFactors>
          <MoveSpeed>3</MoveSpeed>
          <IncomingDamageFactor>0.2</IncomingDamageFactor>
        </statFactors>
      </li>
    </stages>
  </HediffDef>

  <HediffDef>
    <defName>RapidRegeneration</defName>
    <label>rapid regeneration</label>
    <hediffClass>Hediff_RapidRegeneration</hediffClass>
    <description>This creature has stored an incredible amount of healing energy. While not unlimited, it will rapidly regenerate until its energy has been exhausted.</description>
    <isBad>false</isBad>
    <preventsDeath>true</preventsDeath>
    <keepOnBodyPartRestoration>True</keepOnBodyPartRestoration>
    <stages>
      <li>
        <minSeverity>0</minSeverity>
        <regeneration>30000</regeneration>
        <showRegenerationStat>false</showRegenerationStat>
        <statOffsets>
          <PainShockThreshold>10</PainShockThreshold>
        </statOffsets>
      </li>
    </stages>
  </HediffDef>
  
</Defs>