<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <RitualOutcomeEffectDef>
    <defName>ChildBirth</defName>
    <description>All quality factors determine how healthy the mom and baby are going to be.</description>
    <allowAttachableOutcome>false</allowAttachableOutcome>
    <workerClass>RitualOutcomeEffectWorker_ChildBirth</workerClass>
    <givesDevelopmentPoints>false</givesDevelopmentPoints>
    <allowOutcomeWithNoAttendance>true</allowOutcomeWithNoAttendance>
    <extraOutcomeDescriptions>
      <li>
        <description>Additionally, the mother will have a {0_percentage} chance to die during birth.</description>
        <qualityToChance>PregnancyUtility.ChanceMomDiesDuringBirth</qualityToChance>
      </li>
    </extraOutcomeDescriptions>
    <comps>
      <li Class="RitualOutcomeComp_PawnAge">
        <label>{PAWN_labelShort}'s age</label>
        <labelAbstract>mother's age</labelAbstract>
        <roleId>mother</roleId>
        <curve>
          <points>
            <li>(14, 0.0)</li>
            <li>(15, 0.3)</li>
            <li>(20, 0.5)</li>
            <li>(30, 0.5)</li>
            <li>(40, 0.3)</li>
            <li>(65, 0.0)</li>
          </points>
        </curve>
      </li>
      <li Class="RitualOutcomeComp_PawnSkill">
        <label>{PAWN_labelShort}'s medicine skill</label>
        <labelAbstract>doctor's medicine skill</labelAbstract>
        <roleId>doctor</roleId>
        <skillDef>Medicine</skillDef>
        <curve>
          <points>
            <li>(0, 0.0)</li>
            <li>(20, 0.25)</li>
          </points>
        </curve>
      </li>
      <li Class="RitualOutcomeComp_LovedOnePresent">
        <label>loved one present</label>
        <labelNotMet>loved one absent</labelNotMet>
        <lovedOneOfRole>mother</lovedOneOfRole>
        <qualityOffset>0.05</qualityOffset>
      </li>
      <li Class="RitualOutcomeComp_Indoors">
        <label>indoors</label>
        <qualityOffset>0.05</qualityOffset>
      </li>
      <li Class="RitualOutcomeComp_RoomStat">
        <label>room cleanliness</label>
        <statDef>Cleanliness</statDef>
        <curve>
          <points>
            <li>0,  0</li>
            <li>0.6, 0.1</li>
          </points>
        </curve>
      </li>
      <li Class="RitualOutcomeComp_TargetThingStat">
        <label>bed health effects</label>
        <statDef>BirthRitualQualityOffset</statDef>
        <mustBeBed>true</mustBeBed>
        <curve>
          <points>
            <li>0,  0</li>
            <li>5, 0.05</li>
          </points>
        </curve>
      </li>
    </comps>
    <outcomeChances>
      <li>
        <label>Still</label>
        <description>{MOTHER_labelShort}'s baby was stillborn.</description>
        <chance>0.025</chance>
        <positivityIndex>-1</positivityIndex>
      </li>
      <li>
        <label>Sick</label>
        <description>{MOTHER_labelShort} gave birth to a baby. Unfortunately, the baby is sick.</description>
        <chance>0.075</chance>
        <positivityIndex>0</positivityIndex>
      </li>
      <li>
        <label>Healthy</label>
        <description>{MOTHER_labelShort} gave birth to a healthy baby!</description>
        <chance>0.9</chance>
        <positivityIndex>1</positivityIndex>
      </li>
    </outcomeChances>
  </RitualOutcomeEffectDef>

</Defs>