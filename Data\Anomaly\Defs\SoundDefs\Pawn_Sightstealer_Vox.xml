﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>
  <SoundDef>
    <defName>Pawn_Sightstealer_Call</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Sightstealer/Call</clipFolderPath>
          </li>
        </grains>
        <volumeRange>20~28</volumeRange>
        <pitchRange>0.9770588~1.137647</pitchRange>
        <distRange>0~50.40025</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Sightstealer_Wounded</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Sightstealer/Pain</clipFolderPath>
          </li>
        </grains>
        <volumeRange>25~35</volumeRange>
        <pitchRange>0.9311764~1.068824</pitchRange>
        <distRange>0~70</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Sightstealer_Death</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Sightstealer/Death</clipFolderPath>
          </li>
        </grains>
        <volumeRange>25~35</volumeRange>
        <pitchRange>0.9311764~1.068824</pitchRange>
        <distRange>0~70</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Sightstealer_Howl</defName>
    <context>MapOnly</context>
    <subSounds>
      <li>
        <onCamera>True</onCamera>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Sightstealer/Howl</clipFolderPath>
          </li>
        </grains>
        <volumeRange>25~35</volumeRange>
        <pitchRange>0.9311764~1.068824</pitchRange>
        <distRange>0~70</distRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>
</Defs>