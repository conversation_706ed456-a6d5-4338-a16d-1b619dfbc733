﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <BodyPartDef>
    <defName>Torso</defName>
    <label>torso</label>
    <hitPoints>40</hitPoints>
    <skinCovered>true</skinCovered>
    <canScarify>true</canScarify>
    <canSuggestAmputation>false</canSuggestAmputation>
    <executionPartPriority>400</executionPartPriority>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Shoulder</defName>
    <label>shoulder</label>
    <hitPoints>30</hitPoints>
    <skinCovered>true</skinCovered>
    <tags>
      <li>ManipulationLimbSegment</li>
      <li>Mirrored</li>
    </tags>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Arm</defName>
    <label>arm</label>
    <hitPoints>30</hitPoints>
    <skinCovered>true</skinCovered>
    <canScarify>true</canScarify>
    <tags>
      <li>ManipulationLimbCore</li>
      <li>Mirrored</li>
    </tags>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Hand</defName>
    <label>hand</label>
    <hitPoints>20</hitPoints>
    <frostbiteVulnerability>0.5</frostbiteVulnerability>
    <skinCovered>true</skinCovered>
    <tags>
      <li>ManipulationLimbSegment</li>
    </tags>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Finger</defName>
    <label>finger</label>
    <hitPoints>8</hitPoints>
    <frostbiteVulnerability>8</frostbiteVulnerability>
    <pawnGeneratorCanAmputate>true</pawnGeneratorCanAmputate>
    <skinCovered>true</skinCovered>
    <tags>
      <li>ManipulationLimbDigit</li>
      <li>Mirrored</li>
    </tags>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Waist</defName>
    <label>utility slot</label>
    <conceptual>true</conceptual>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Toe</defName>
    <label>toe</label>
    <hitPoints>8</hitPoints>
    <frostbiteVulnerability>10</frostbiteVulnerability>
    <pawnGeneratorCanAmputate>true</pawnGeneratorCanAmputate>
    <skinCovered>true</skinCovered>
    <tags>
      <li>MovingLimbDigit</li>
      <li>Mirrored</li>
    </tags>
  </BodyPartDef>
  
  <!-- Bones -->

  <BodyPartDef>
    <defName>Clavicle</defName>
    <label>clavicle</label>
    <hitPoints>25</hitPoints>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <skinCovered>false</skinCovered>
    <solid>true</solid>
    <bleedRate>0</bleedRate>
    <tags>
      <li>ManipulationLimbSegment</li>
      <li>Mirrored</li>
    </tags>
    <destroyableByDamage>false</destroyableByDamage>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Sternum</defName>
    <label>sternum</label>
    <hitPoints>20</hitPoints>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <skinCovered>false</skinCovered>
    <solid>true</solid>
    <bleedRate>0</bleedRate>
    <tags>
      <li>BreathingSourceCage</li>
    </tags>
    <destroyableByDamage>false</destroyableByDamage>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Humerus</defName>
    <label>humerus</label>
    <hitPoints>25</hitPoints>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <skinCovered>false</skinCovered>
    <solid>true</solid>
    <bleedRate>0</bleedRate>
    <tags>
      <li>ManipulationLimbSegment</li>
      <li>Mirrored</li>
    </tags>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Radius</defName>
    <label>radius</label>
    <hitPoints>20</hitPoints>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <skinCovered>false</skinCovered>
    <solid>true</solid>
    <bleedRate>0</bleedRate>
    <tags>
      <li>ManipulationLimbSegment</li>
      <li>Mirrored</li>
    </tags>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Pelvis</defName>
    <label>pelvis</label>
    <hitPoints>25</hitPoints>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <skinCovered>false</skinCovered>
    <solid>true</solid>
    <bleedRate>0</bleedRate>
    <tags>
      <li>Pelvis</li>
    </tags>
    <destroyableByDamage>false</destroyableByDamage>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Femur</defName>
    <label>femur</label>
    <hitPoints>25</hitPoints>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <skinCovered>false</skinCovered>
    <solid>true</solid>
    <bleedRate>0</bleedRate>
    <tags>
      <li>MovingLimbSegment</li>
      <li>Mirrored</li>
    </tags>
  </BodyPartDef>

  <BodyPartDef>
    <defName>Tibia</defName>
    <label>tibia</label>
    <hitPoints>25</hitPoints>
    <permanentInjuryChanceFactor>0</permanentInjuryChanceFactor>
    <skinCovered>false</skinCovered>
    <solid>true</solid>
    <bleedRate>0</bleedRate>
    <tags>
      <li>MovingLimbSegment</li>
      <li>Mirrored</li>
    </tags>
  </BodyPartDef>

</Defs>
