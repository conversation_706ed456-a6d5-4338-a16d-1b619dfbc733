<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThingDef Abstract="True" Name="SuperHeavyMechanoid" ParentName="HeavyMechanoid">
    <statBases>
      <MarketValue>1600</MarketValue>
      <ControlTakingTime>30</ControlTakingTime>
      <BandwidthCost>5</BandwidthCost>
      <ArmorRating_Blunt>0.25</ArmorRating_Blunt>
      <ArmorRating_Sharp>0.75</ArmorRating_Sharp>
      <PsychicSensitivity>0.75</PsychicSensitivity>
      <MechEnergyLossPerHP>0.15</MechEnergyLossPerHP>
    </statBases>
    <race>
      <baseHealthScale>4.5</baseHealthScale>
      <mechWeightClass>UltraHeavy</mechWeightClass>
    </race>
    <butcherProducts Inherit="False">
      <Steel>50</Steel>
      <Plasteel>20</Plasteel>
    </butcherProducts>
  </ThingDef>

  <!-- Apocriton -->
  <ThingDef ParentName="BaseMechanoidWalker">
    <defName>Mech_Apocriton</defName>
    <label>apocriton</label>
    <description>A mechanoid commander designed to coordinate and motivate other mechs during long extermination campaigns. Its most obvious power is its ability to resurrect recently-killed mechs by supercharging their self-repair processes. Less obviously, it is intelligent and psychically present, radiating hatred into the minds of anyone in a wide radius. It can also move quickly with its built-in jump launcher.\n\nWhile all mechanoids have a dim psychically-present intelligence, only the apocriton and a few others truly feel hatred for their victims and understand the suffering they inflict.</description>
    <statBases>
      <MarketValue>1600</MarketValue>
      <MoveSpeed>3.2</MoveSpeed>
      <EnergyShieldRechargeRate>0.25</EnergyShieldRechargeRate>
      <EnergyShieldEnergyMax>1.5</EnergyShieldEnergyMax>
      <BandwidthCost>2</BandwidthCost>
      <ControlTakingTime>18</ControlTakingTime>
      <EMPResistance>0.8</EMPResistance>
      <ArmorRating_Blunt>0.40</ArmorRating_Blunt>
      <ArmorRating_Sharp>0.75</ArmorRating_Sharp>
    </statBases>
    <race>
      <body>Apocriton</body>
      <mechWeightClass>UltraHeavy</mechWeightClass>
      <lifeStageAges Inherit="False">
        <li>
          <def>MechanoidFullyFormed</def>
          <minAge>0</minAge>
          <soundWounded>Pawn_Mech_Apocriton_Wounded</soundWounded>
          <soundAngry>Pawn_Mech_Apocriton_Call</soundAngry>
          <soundDeath>Pawn_Mech_Apocriton_Death</soundDeath>
          <soundCall>Pawn_Mech_Apocriton_Call</soundCall>
        </li>
        <li>
          <def>MechanoidFullyFormed</def>
          <minAge>100</minAge>
          <soundWounded>Pawn_Mech_Apocriton_Wounded</soundWounded>
          <soundAngry>Pawn_Mech_Apocriton_Call</soundAngry>
          <soundDeath>Pawn_Mech_Apocriton_Death</soundDeath>
          <soundCall>Pawn_Mech_Apocriton_Call</soundCall>
        </li>
      </lifeStageAges>
      <baseHealthScale>5.2</baseHealthScale>
      <soundAmbience>Pawn_Mech_Apocriton_Ambience</soundAmbience>
      <detritusLeavings>
        <li>
          <def>ChunkMechanoidSlag</def>
          <texOverrideIndex>15</texOverrideIndex>
          <spawnChance>0.1</spawnChance>
        </li>
        <li>
          <def>ChunkMechanoidSlag</def>
          <texOverrideIndex>8</texOverrideIndex>
          <spawnChance>0.1</spawnChance>
        </li>
        <li>
          <def>ChunkMechanoidSlag</def>
          <texOverrideIndex>13</texOverrideIndex>
          <spawnChance>0.1</spawnChance>
        </li>
      </detritusLeavings>
    </race>
    <tools>
      <li>
        <label>head</label>
        <capacities>
          <li>Blunt</li>
        </capacities>
        <power>9</power>
        <cooldownTime>2.6</cooldownTime>
        <linkedBodyPartsGroup>HeadAttackTool</linkedBodyPartsGroup>
        <ensureLinkedBodyPartsGroupAlwaysUsable>true</ensureLinkedBodyPartsGroupAlwaysUsable>
      </li>
    </tools>
    <killedLeavingsPlayerHostile>
      <NanostructuringChip>1</NanostructuringChip>
    </killedLeavingsPlayerHostile>
    <comps>
      <li Class="CompProperties_Explosive">
        <wickTicks>0</wickTicks>
        <explosiveRadius>30.9</explosiveRadius>
        <explodeOnKilled>true</explodeOnKilled>
        <explosiveDamageType>MechBandShockwave</explosiveDamageType>
        <propagationSpeed>0.5</propagationSpeed>
        <chanceNeverExplodeFromDamage>1</chanceNeverExplodeFromDamage>
        <extraInspectStringKey>MechStunPulseWarning</extraInspectStringKey>
      </li>
      <li Class="CompProperties_Effecter">
        <effecterDef>ApocrionAttached</effecterDef>
      </li>
    </comps>
  </ThingDef>

  <PawnKindDef ParentName="BaseMechanoidKind">
    <defName>Mech_Apocriton</defName>
    <label>apocriton</label>
    <race>Mech_Apocriton</race>
    <combatPower>600</combatPower>
    <maxPerGroup>3</maxPerGroup>
    <isBoss>true</isBoss>
    <allowInMechClusters>false</allowInMechClusters>
    <lifeStages>
      <li>
        <bodyGraphicData>
          <texPath>Things/Pawn/Mechanoid/Apocriton</texPath>
          <maskPath>Things/Pawn/Mechanoid/AllegianceOverlays/Mech_Apocriton</maskPath>
          <shaderType>CutoutWithOverlay</shaderType>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>2</drawSize>
        </bodyGraphicData>
      </li>
      <li>
        <bodyGraphicData>
          <texPath>Things/Pawn/Mechanoid/ApocritonAncient</texPath>
          <maskPath>Things/Pawn/Mechanoid/AllegianceOverlays/Mech_Apocriton</maskPath>
          <shaderType>CutoutWithOverlay</shaderType>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>2</drawSize>
        </bodyGraphicData>
      </li>
    </lifeStages>
    <weaponMoney>9999~9999</weaponMoney>
    <weaponTags>
      <li>MechanoidGunToxicNeedle</li>
    </weaponTags>
    <techHediffsChance>1</techHediffsChance>
    <techHediffsMoney>9999~9999</techHediffsMoney>
    <abilities>
      <li>LongjumpMech</li>
      <li>ResurrectionMech</li>
    </abilities>
    <skipResistant>true</skipResistant>
  </PawnKindDef>
  
  <!-- Centurion -->
  <ThingDef Name="MechCenturion" ParentName="SuperHeavyMechanoid">
    <defName>Mech_Centurion</defName>
    <label>centurion</label>
    <description>An ultraheavy mech with a built-in shield bubble generator. The centurion carries a point-defense bulb turret capable of firing while the mechanoid is moving.</description>
    <statBases>
      <MoveSpeed>1.6</MoveSpeed>
      <EnergyShieldRechargeRate>0.5</EnergyShieldRechargeRate>
      <EnergyShieldEnergyMax>3</EnergyShieldEnergyMax>
      <MeleeDoorDamageFactor>1.5</MeleeDoorDamageFactor>
    </statBases>
    <race>
      <body>Mech_Centurion</body>
      <baseBodySize>3.6</baseBodySize>
      <lifeStageAges>
        <li>
          <def>MechanoidFullyFormed</def>
          <minAge>0</minAge>
          <soundWounded>Pawn_Mech_Centurion_Wounded</soundWounded>
          <soundDeath>Pawn_Mech_Centurion_Death</soundDeath>
          <soundCall>Pawn_Mech_Centurion_Call</soundCall>
        </li>
        <li>
          <def>MechanoidFullyFormed</def>
          <minAge>100</minAge>
          <soundWounded>Pawn_Mech_Centurion_Wounded</soundWounded>
          <soundDeath>Pawn_Mech_Centurion_Death</soundDeath>
          <soundCall>Pawn_Mech_Centurion_Call</soundCall>
        </li>
      </lifeStageAges>
      <baseHealthScale>3</baseHealthScale>
      <detritusLeavings>
        <li>
          <def>ChunkMechanoidSlag</def>
          <texOverrideIndex>0</texOverrideIndex>
          <spawnChance>0.75</spawnChance>
        </li>
        <li>
          <def>ChunkMechanoidSlag</def>
          <texOverrideIndex>8</texOverrideIndex>
          <spawnChance>0.75</spawnChance>
        </li>
        <li>
          <def>ChunkMechanoidSlag</def>
          <texOverrideIndex>3</texOverrideIndex>
          <spawnChance>0.75</spawnChance>
        </li>
      </detritusLeavings>
    </race>
    <comps>
      <li Class="CompProperties_ProjectileInterceptor">
        <radius>6</radius>
        <interceptGroundProjectiles>true</interceptGroundProjectiles>
        <color>(0.4, 0.4, 0.4)</color>
        <reactivateEffect>BulletShieldGenerator_Reactivate</reactivateEffect>
        <activeSound>BulletShield_Ambience</activeSound>
        <hitPoints>300</hitPoints>
        <hitPointsRestoreInstantlyAfterCharge>true</hitPointsRestoreInstantlyAfterCharge>
        <chargeDurationTicks>1800</chargeDurationTicks><!-- 30s -->
        <rechargeHitPointsIntervalTicks>120</rechargeHitPointsIntervalTicks>
        <minIdleAlpha>0.2</minIdleAlpha>
        <drawWithNoSelection>True</drawWithNoSelection>
        <disarmedByEmpForTicks>1500</disarmedByEmpForTicks>
        <gizmoTipKey>ProjectileInterceptorTip</gizmoTipKey>
      </li>
      <li Class="CompProperties_TurretGun">
        <turretDef>Gun_ChargeBlasterTurret</turretDef>
        <angleOffset>-90</angleOffset>
        <renderNodeProperties>
          <li>
            <nodeClass>PawnRenderNode_TurretGun</nodeClass>
            <workerClass>PawnRenderNodeWorker_TurretGun</workerClass>
            <parentTagDef>Body</parentTagDef>
            <overrideMeshSize>(1, 1)</overrideMeshSize>
            <baseLayer>20</baseLayer>
            <pawnType>Any</pawnType>
            <drawData>
              <dataWest>
                <rotationOffset>180</rotationOffset>
              </dataWest>
            </drawData>
          </li>
        </renderNodeProperties>
      </li>
    </comps>
    <tools>
      <li>
        <label>head</label>
        <capacities>
          <li>Demolish</li>
        </capacities>
        <power>12</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>HeadAttackTool</linkedBodyPartsGroup>
        <ensureLinkedBodyPartsGroupAlwaysUsable>true</ensureLinkedBodyPartsGroupAlwaysUsable>
        <chanceFactor>0.2</chanceFactor>
      </li>
    </tools>
  </ThingDef>

  <ThingDef ParentName="BaseWeaponTurret">
    <defName>Gun_ChargeBlasterTurret</defName>
    <label>charge blaster turret</label>
    <description>A small charge blaster designed for use on a defense turret.</description>
    <tradeability>None</tradeability>
    <destroyOnDrop>true</destroyOnDrop>
    <graphicData>
      <texPath>Things/Item/Equipment/WeaponRanged/ChargeBlasterTurret</texPath>
      <graphicClass>Graphic_Single</graphicClass>
    </graphicData>
    <statBases>
      <Mass>2.6</Mass>
      <AccuracyTouch>0.60</AccuracyTouch>
      <AccuracyShort>0.80</AccuracyShort>
      <AccuracyMedium>0.90</AccuracyMedium>
      <AccuracyLong>0.85</AccuracyLong>
    </statBases>
    <verbs>
      <li>
        <verbClass>Verb_Shoot</verbClass>
        <defaultProjectile>Bullet_ChargeBlasterHeavy</defaultProjectile>
        <range>44.9</range>
        <soundCast>Shot_ChargeBlaster</soundCast>
        <soundCastTail>GunTail_Heavy</soundCastTail>
        <muzzleFlashScale>9</muzzleFlashScale>
        <defaultCooldownTime>2.5</defaultCooldownTime>
        <linkedBodyPartsGroup>BulbTurret</linkedBodyPartsGroup>
        <ensureLinkedBodyPartsGroupAlwaysUsable>true</ensureLinkedBodyPartsGroupAlwaysUsable>
        <ticksBetweenBurstShots>150</ticksBetweenBurstShots>
      </li>
    </verbs>
  </ThingDef>
  
  <PawnKindDef Name="MechCenturionKind" ParentName="HeavyMechanoidKind">
    <defName>Mech_Centurion</defName>
    <label>centurion</label>
    <race>Mech_Centurion</race>
    <combatPower>250</combatPower>
    <maxPerGroup>3</maxPerGroup>
    <allowInMechClusters>false</allowInMechClusters>
    <lifeStages>
      <li>
        <bodyGraphicData>
          <texPath>Things/Pawn/Mechanoid/Centurion</texPath>
          <maskPath>Things/Pawn/Mechanoid/AllegianceOverlays/Mech_Centurion</maskPath>
          <shaderType>CutoutWithOverlay</shaderType>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>2.5</drawSize>
          <shadowData>
            <volume>(0.6, 0.8, 0.6)</volume>
          </shadowData>
        </bodyGraphicData>
      </li>
      <li>
        <bodyGraphicData>
          <texPath>Things/Pawn/Mechanoid/CenturionAncient</texPath>
          <maskPath>Things/Pawn/Mechanoid/AllegianceOverlays/Mech_Centurion</maskPath>
          <shaderType>CutoutWithOverlay</shaderType>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>2.5</drawSize>
          <shadowData>
            <volume>(0.6, 0.8, 0.6)</volume>
          </shadowData>
        </bodyGraphicData>
      </li>
    </lifeStages>      
  </PawnKindDef>

  <!-- Warqueen -->
  <ThingDef Name="MechWarqueen" ParentName="SuperHeavyMechanoid">
    <defName>Mech_Warqueen</defName>
    <label>war queen</label>
    <description>An ultra-heavy mech with a built-in mech gestator. Fed with appropriate resources, the war queen can form small war urchin combat mechs within its massive carapace and deploy them into combat.\n\nEven more than other mechanoids, the war queen resembles a giant, living insect. All war mechs can be terrifying, but humans tend to find the war queen disturbing on a deeper level.</description>
    <statBases>
      <MoveSpeed>1.6</MoveSpeed>
      <EnergyShieldRechargeRate>0.5</EnergyShieldRechargeRate>
      <EnergyShieldEnergyMax>3</EnergyShieldEnergyMax>
      <MeleeDoorDamageFactor>1.5</MeleeDoorDamageFactor>
      <EMPResistance>0.7</EMPResistance>
    </statBases>
    <race>
      <body>Mech_Warqueen</body>
      <baseBodySize>4</baseBodySize>
      <lifeStageAges>
        <li>
          <def>MechanoidFullyFormed</def>
          <minAge>0</minAge>
          <soundWounded>Pawn_Mech_Warqueen_Wounded</soundWounded>
          <soundDeath>Pawn_Mech_Warqueen_Death</soundDeath>
          <soundCall>Pawn_Mech_Warqueen_Call</soundCall>
        </li>
        <li>
          <def>MechanoidFullyFormed</def>
          <minAge>100</minAge>
          <soundWounded>Pawn_Mech_Warqueen_Wounded</soundWounded>
          <soundDeath>Pawn_Mech_Warqueen_Death</soundDeath>
          <soundCall>Pawn_Mech_Warqueen_Call</soundCall>
        </li>
      </lifeStageAges>
      <baseHealthScale>5.2</baseHealthScale>
      <dutyBoss>Warqueen</dutyBoss>
      <detritusLeavings>
        <li>
          <def>ChunkMechanoidSlag</def>
          <texOverrideIndex>13</texOverrideIndex>
          <spawnChance>0.75</spawnChance>
        </li>
        <li>
          <def>ChunkMechanoidSlag</def>
          <texOverrideIndex>3</texOverrideIndex>
          <spawnChance>0.75</spawnChance>
        </li>
        <li>
          <def>ChunkMechanoidSlag</def>
          <texOverrideIndex>0</texOverrideIndex>
          <spawnChance>0.75</spawnChance>
        </li>
      </detritusLeavings>
    </race>
    <tools>
      <li>
        <label>head</label>
        <capacities>
          <li>Blunt</li>
        </capacities>
        <power>4</power>
        <cooldownTime>2</cooldownTime>
        <linkedBodyPartsGroup>HeadAttackTool</linkedBodyPartsGroup>
        <ensureLinkedBodyPartsGroupAlwaysUsable>true</ensureLinkedBodyPartsGroupAlwaysUsable>
        <chanceFactor>0.2</chanceFactor>
      </li>
    </tools>
    <comps>
      <li Class="CompProperties_MechCarrier">
        <spawnPawnKind>Mech_WarUrchin</spawnPawnKind>
        <fixedIngredient>Steel</fixedIngredient>
        <costPerPawn>25</costPerPawn>
        <startingIngredientCount>600</startingIngredientCount>
        <maxIngredientCount>600</maxIngredientCount>
        <spawnEffecter>WarqueenWarUrchinsSpawned</spawnEffecter>
        <spawnedMechEffecter>WarUrchinSpawned</spawnedMechEffecter>
        <attachSpawnedEffecter>true</attachSpawnedEffecter>
        <attachSpawnedMechEffecter>true</attachSpawnedMechEffecter>
      </li>
      <li Class="CompProperties_TurretGun">
        <turretDef>Gun_ChargeBlasterTurret</turretDef>
        <angleOffset>-90</angleOffset>
        <autoAttack>false</autoAttack>
        <renderNodeProperties>
          <li>
            <nodeClass>PawnRenderNode_TurretGun</nodeClass>
            <workerClass>PawnRenderNodeWorker_TurretGun</workerClass>
            <overrideMeshSize>(1, 1)</overrideMeshSize>
            <parentTagDef>Body</parentTagDef>
            <baseLayer>20</baseLayer>
            <pawnType>Any</pawnType>
            <drawData>
              <dataWest>
                <rotationOffset>180</rotationOffset>
              </dataWest>
            </drawData>
          </li>
        </renderNodeProperties>
      </li>
    </comps>
    <killedLeavingsPlayerHostile>
      <PowerfocusChip>1</PowerfocusChip>
    </killedLeavingsPlayerHostile>
  </ThingDef>

  <PawnKindDef Name="MechWarqueenKind" ParentName="HeavyMechanoidKind">
    <defName>Mech_Warqueen</defName>
    <label>war queen</label>
    <labelPlural>war queens</labelPlural>
    <race>Mech_Warqueen</race>
    <combatPower>600</combatPower>
    <maxPerGroup>3</maxPerGroup>
    <isBoss>true</isBoss>
    <allowInMechClusters>false</allowInMechClusters>
    <lifeStages>
      <li>
        <bodyGraphicData>
          <texPath>Things/Pawn/Mechanoid/Warqueen</texPath>
          <maskPath>Things/Pawn/Mechanoid/AllegianceOverlays/MechWarqueen</maskPath>
          <shaderType>CutoutWithOverlay</shaderType>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>3</drawSize>
          <shadowData>
            <volume>(0.7, 0.8, 0.7)</volume>
          </shadowData>
        </bodyGraphicData>
      </li>
      <li>
        <bodyGraphicData>
          <texPath>Things/Pawn/Mechanoid/WarqueenAncient</texPath>
          <maskPath>Things/Pawn/Mechanoid/AllegianceOverlays/MechWarqueen</maskPath>
          <shaderType>CutoutWithOverlay</shaderType>
          <graphicClass>Graphic_Multi</graphicClass>
          <drawSize>3</drawSize>
          <shadowData>
            <volume>(0.7, 0.8, 0.7)</volume>
          </shadowData>
        </bodyGraphicData>
      </li>
    </lifeStages>    
    <controlGroupPortraitZoom>0.7</controlGroupPortraitZoom>  
  </PawnKindDef>

    <!-- Diabolus -->
    <ThingDef Name="MechDiabolus" ParentName="SuperHeavyMechanoid">
      <defName>Mech_Diabolus</defName>
      <label>diabolus</label>
      <description>An ultra-heavy mechanoid with an ultra-powerful hellsphere cannon. Made for siegebreaking, its hellsphere cannon takes time to charge up a shot, but can melt concrete and vaporize bone. The diabolus dissipates the hellsphere cannon's waste power in a heat column mounted on its back, which can pulse to ignite flammable objects nearby.\n\nThis mech's name comes from a thousand-year-old poem written by a Haspian monk who survived the erasure of his monastery. His religiously-tinged work describes a razor-bodied fiend pulsing with flesh-searing heat as it crushed scorched bodies under massive claws. The few who have faced a diabolus and survived tend to agree with this depiction.</description>
      <race>
        <body>Mech_Diabolus</body>
        <baseBodySize>4</baseBodySize>
        <lifeStageAges>
          <li>
            <def>MechanoidFullyFormed</def>
            <minAge>0</minAge>
            <soundWounded>Pawn_Mech_Diabolus_Wounded</soundWounded>
            <soundDeath>Pawn_Mech_Diabolus_Death</soundDeath>
            <soundCall>Pawn_Mech_Diabolus_Call</soundCall>
          </li>
          <li>
            <def>MechanoidFullyFormed</def>
            <minAge>100</minAge>
            <soundWounded>Pawn_Mech_Diabolus_Wounded</soundWounded>
            <soundDeath>Pawn_Mech_Diabolus_Death</soundDeath>
            <soundCall>Pawn_Mech_Diabolus_Call</soundCall>
          </li>
        </lifeStageAges>
        <baseHealthScale>4.5</baseHealthScale>
        <detritusLeavings>
          <li>
            <def>ChunkMechanoidSlag</def>
            <texOverrideIndex>3</texOverrideIndex>
            <spawnChance>0.75</spawnChance>
          </li>
          <li>
            <def>ChunkMechanoidSlag</def>
            <texOverrideIndex>17</texOverrideIndex>
            <spawnChance>0.75</spawnChance>
          </li>
          <li>
            <def>ChunkMechanoidSlag</def>
            <texOverrideIndex>1</texOverrideIndex>
            <spawnChance>0.75</spawnChance>
          </li>
        </detritusLeavings>
      </race>
      <tools>
        <li>
          <label>head</label>
          <capacities>
            <li>Blunt</li>
          </capacities>
          <power>18</power>
          <cooldownTime>2.6</cooldownTime>
          <linkedBodyPartsGroup>HeadAttackTool</linkedBodyPartsGroup>
          <ensureLinkedBodyPartsGroupAlwaysUsable>true</ensureLinkedBodyPartsGroupAlwaysUsable>
        </li>
      </tools>
      <comps>
        <li Class="CompProperties_TargetingBeam" />
        <li Class="CompProperties_TurretGun">
          <turretDef>Gun_ChargeBlasterTurret</turretDef>
          <angleOffset>-90</angleOffset>
          <renderNodeProperties>
            <li>
              <nodeClass>PawnRenderNode_TurretGun</nodeClass>
              <workerClass>PawnRenderNodeWorker_TurretGun</workerClass>
              <parentTagDef>Body</parentTagDef>
              <overrideMeshSize>(1, 1)</overrideMeshSize>
              <baseLayer>20</baseLayer>
              <pawnType>Any</pawnType>
              <drawData>
                <dataWest>
                  <rotationOffset>180</rotationOffset>
                </dataWest>
              </drawData>
            </li>
          </renderNodeProperties>
        </li>
      </comps>
      <statBases>
        <BandwidthCost>5</BandwidthCost>
        <EMPResistance>0.6</EMPResistance>
      <MoveSpeed>2.4</MoveSpeed>
      </statBases>
      <killedLeavingsPlayerHostile>
        <SignalChip>1</SignalChip>
      </killedLeavingsPlayerHostile>
    </ThingDef>
    
    <PawnKindDef Name="MechDiabolusKind" ParentName="HeavyMechanoidKind">
      <defName>Mech_Diabolus</defName>
      <label>diabolus</label>
      <race>Mech_Diabolus</race>
      <combatPower>500</combatPower>
      <maxPerGroup>3</maxPerGroup>
      <isBoss>true</isBoss>
      <allowInMechClusters>false</allowInMechClusters>
      <lifeStages>
        <li>
          <bodyGraphicData>
            <texPath>Things/Pawn/Mechanoid/Diabolus</texPath>
            <maskPath>Things/Pawn/Mechanoid/AllegianceOverlays/Mech_Diablo</maskPath>
            <shaderType>CutoutWithOverlay</shaderType>
              <graphicClass>Graphic_Multi</graphicClass>
              <drawSize>3</drawSize>
          </bodyGraphicData>
        </li>
        <li>
          <bodyGraphicData>
            <texPath>Things/Pawn/Mechanoid/DiabolusAncient</texPath>
            <maskPath>Things/Pawn/Mechanoid/AllegianceOverlays/Mech_Diablo</maskPath>
            <shaderType>CutoutWithOverlay</shaderType>
              <graphicClass>Graphic_Multi</graphicClass>
              <drawSize>3</drawSize>
          </bodyGraphicData>
        </li>
      </lifeStages>      
      <weaponTags>
        <li>HellsphereCannonGun</li>
      </weaponTags>
      <abilities>
        <li>FireBurst</li>
      </abilities>
    </PawnKindDef>

</Defs>