<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <ThinkTreeDef>
    <defName>HumanlikeBaby</defName>
    <thinkRoot Class="ThinkNode_Priority">
      <subNodes>
        <li Class="ThinkNode_Subtree">
          <treeDef>Despawned</treeDef>
        </li>
        <li Class="ThinkNode_QueuedJob" />
        <li Class="JobGiver_KeepLyingDown" />
        <li Class="JobGiver_IdleError" />
      </subNodes>
    </thinkRoot>
  </ThinkTreeDef>

  <ThinkTreeDef>
    <defName>HumanlikeBabyConstant</defName>
    <thinkRoot Class="ThinkNode_Priority">
      <subNodes>
        <li Class="ThinkNode_ConditionalCanDoLordJobNow">
          <subNodes>
            <li Class="ThinkNode_Subtree">
              <treeDef>LordDutyConstant</treeDef>
            </li>
          </subNodes>
        </li>
      </subNodes>
    </thinkRoot>
  </ThinkTreeDef>

</Defs>
