﻿<?xml version="1.0" encoding="utf-8" ?>
<Defs>

  <BiomeDef>
    <defName>BorealForest</defName>
    <label>boreal forest</label>
    <description>Forests of coniferous trees. Despite the harsh winters, boreal forests sustain a diverse population of small and large animals, and have warm summers.</description>
    <workerClass>BiomeWorker_BorealForest</workerClass>
    <allowFarmingCamps>false</allowFarmingCamps>
    <animalDensity>2.8</animalDensity>
    <plantDensity>0.40</plantDensity>
    <settlementSelectionWeight>0.9</settlementSelectionWeight>
    <movementDifficulty>1</movementDifficulty>
    <forageability>0.75</forageability>
    <foragedFood>RawBerries</foragedFood>
    <wildPlantRegrowDays>25</wildPlantRegrowDays>
    <texture>World/Biomes/BorealForest</texture>
    <soundsAmbient>
      <li>Ambient_NightInsects_Standard</li>
    </soundsAmbient>
    <diseaseMtbDays>60</diseaseMtbDays>
    <diseases>
      <li>
        <diseaseInc>Disease_Flu</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_Plague</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_GutWorms</diseaseInc>
        <commonality>50</commonality>
      </li>
      <li>
        <diseaseInc>Disease_FibrousMechanites</diseaseInc>
        <commonality>30</commonality>
      </li>
      <li>
        <diseaseInc>Disease_SensoryMechanites</diseaseInc>
        <commonality>30</commonality>
      </li>
      <li>
        <diseaseInc>Disease_MuscleParasites</diseaseInc>
        <commonality>50</commonality>
      </li>

      <li>
        <diseaseInc>Disease_AnimalFlu</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_AnimalPlague</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_OrganDecay</diseaseInc>
        <commonality>10</commonality>
      </li>
    </diseases>
    <terrainsByFertility>
      <li>
        <terrain>Soil</terrain>
        <min>-999</min>
        <max>0.87</max>
      </li>
      <li>
        <terrain>SoilRich</terrain>
        <min>0.87</min>
        <max>999</max>
      </li>
    </terrainsByFertility>
    <terrainPatchMakers>
      <li>
        <perlinFrequency>0.015</perlinFrequency>
        <thresholds>
          <li>
            <terrain>MossyTerrain</terrain>
            <min>0.0</min>
            <max>0.32</max>
          </li>
          <li>
            <terrain>Marsh</terrain>
            <min>0.32</min>
            <max>0.76</max>
          </li>
          <li>
            <terrain>WaterShallow</terrain>
            <min>0.76</min>
            <max>0.98</max>
          </li>
          <li>
            <terrain>WaterDeep</terrain>
            <min>0.98</min>
            <max>999</max>
          </li>
        </thresholds>
      </li>
    </terrainPatchMakers>
    <baseWeatherCommonalities>
      <Clear>18</Clear>
      <Fog>1</Fog>
      <Rain>2</Rain>
      <DryThunderstorm>1</DryThunderstorm>
      <RainyThunderstorm>1</RainyThunderstorm>
      <FoggyRain>1</FoggyRain>
      <SnowGentle>4</SnowGentle>
      <SnowHard>4</SnowHard>
      <GrayPall MayRequire="Ludeon.RimWorld.Anomaly">1</GrayPall>
    </baseWeatherCommonalities>
    <wildPlants>
      <Plant_Grass>9.0</Plant_Grass>
      <Plant_TreePine>5.0</Plant_TreePine>
      <Plant_Moss>4.0</Plant_Moss>
      <Plant_Bush>3.0</Plant_Bush>
      <Plant_Brambles>2.0</Plant_Brambles>
      <Plant_Ripthorn MayRequire="Ludeon.RimWorld.Biotech">4</Plant_Ripthorn>
      <Plant_TreeBirch>1.5</Plant_TreeBirch>
      <Plant_TreePoplar>1.2</Plant_TreePoplar>
      <Plant_Berry>0.16</Plant_Berry>
      <Plant_HealrootWild>0.16</Plant_HealrootWild>
      <Plant_TreeGrayPine MayRequire="Ludeon.RimWorld.Biotech">4</Plant_TreeGrayPine>
      <Plant_GrayGrass MayRequire="Ludeon.RimWorld.Biotech">9.0</Plant_GrayGrass>
    </wildPlants>
    <wildAnimals>
      <Yak>0.12</Yak>
      <Horse>0.15</Horse>
      <Squirrel>1</Squirrel>
      <Hare>1</Hare>
      <Rat>1</Rat>
      <Elk>0.5</Elk>
      <Caribou>0.5</Caribou>
      <Muffalo>0.5</Muffalo>
      <WildBoar>0.5</WildBoar>
      <Ibex>0.5</Ibex>
      <Raccoon>0.5</Raccoon>
      <Turkey>0.5</Turkey>
      <Deer>0.5</Deer>
      <Megasloth>0.1</Megasloth>
      <Fox_Red>0.07</Fox_Red>
      <Fox_Arctic>0.07</Fox_Arctic>
      <Bear_Grizzly>0.07</Bear_Grizzly>
      <Wolf_Timber>0.07</Wolf_Timber>
      <Wolf_Arctic>0.07</Wolf_Arctic>
      <Cougar>0.07</Cougar>
      <Lynx>0.07</Lynx>
      <Warg>0.07</Warg>
    </wildAnimals>
    <pollutionWildAnimals MayRequire="Ludeon.RimWorld.Biotech">
      <WasteRat MayRequire="Ludeon.RimWorld.Biotech">1</WasteRat>
      <Rat>0.5</Rat>
      <Raccoon>0.25</Raccoon>
      <Warg>0.05</Warg>
    </pollutionWildAnimals>
    <allowedPackAnimals>
      <li>Muffalo</li>
    </allowedPackAnimals>
  </BiomeDef>

  <BiomeDef>
    <defName>Tundra</defName>
    <label>tundra</label>
    <description>These mostly-frozen plains bear almost no trees and little vegetation. There are a few small animals interspersed with large herds of migratory grazers and their predators.</description>
    <workerClass>BiomeWorker_Tundra</workerClass>
    <allowFarmingCamps>false</allowFarmingCamps>
    <animalDensity>1.1</animalDensity>
    <plantDensity>0.19</plantDensity>
    <settlementSelectionWeight>0.32</settlementSelectionWeight>
    <campSelectionWeight>0.1</campSelectionWeight>
    <movementDifficulty>1</movementDifficulty>
    <texture>World/Biomes/Tundra</texture>
    <forageability>0.5</forageability>
    <foragedFood>RawBerries</foragedFood>
    <wildPlantRegrowDays>28</wildPlantRegrowDays>
    <soundsAmbient>
      <li>Ambient_NightInsects_Standard</li>
    </soundsAmbient>
    <diseaseMtbDays>80</diseaseMtbDays>
    <diseases>
      <li>
        <diseaseInc>Disease_Flu</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_Plague</diseaseInc>
        <commonality>80</commonality>
      </li>
      <li>
        <diseaseInc>Disease_FibrousMechanites</diseaseInc>
        <commonality>40</commonality>
      </li>
      <li>
        <diseaseInc>Disease_SensoryMechanites</diseaseInc>
        <commonality>40</commonality>
      </li>
      <li>
        <diseaseInc>Disease_GutWorms</diseaseInc>
        <commonality>30</commonality>
      </li>
      <li>
        <diseaseInc>Disease_MuscleParasites</diseaseInc>
        <commonality>30</commonality>
      </li>

      <li>
        <diseaseInc>Disease_AnimalFlu</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_AnimalPlague</diseaseInc>
        <commonality>80</commonality>
      </li>
      <li>
        <diseaseInc>Disease_OrganDecay</diseaseInc>
        <commonality>10</commonality>
      </li>
    </diseases>
    <terrainsByFertility>
      <li>
        <terrain>Soil</terrain>
        <min>-999</min>
        <max>0.87</max>
      </li>
      <li>
        <terrain>SoilRich</terrain>
        <min>0.87</min>
        <max>999</max>
      </li>
    </terrainsByFertility>
    <terrainPatchMakers>
      <li>
        <perlinFrequency>0.015</perlinFrequency>
        <thresholds>
          <li>
            <terrain>MossyTerrain</terrain>
            <min>0.64</min>
            <max>0.83</max>
          </li>
          <li>
            <terrain>Marsh</terrain>
            <min>0.83</min>
            <max>999</max>
          </li>
        </thresholds>
      </li>
    </terrainPatchMakers>
    <baseWeatherCommonalities>
      <Clear>18</Clear>
      <Fog>1</Fog>
      <Rain>2</Rain>
      <DryThunderstorm>1</DryThunderstorm>
      <RainyThunderstorm>1</RainyThunderstorm>
      <FoggyRain>1</FoggyRain>
      <SnowGentle>4</SnowGentle>
      <SnowHard>4</SnowHard>
      <GrayPall MayRequire="Ludeon.RimWorld.Anomaly">1</GrayPall>
    </baseWeatherCommonalities>
    <wildPlants>
      <Plant_Grass>3.6</Plant_Grass>
      <Plant_GrayGrass MayRequire="Ludeon.RimWorld.Biotech">3</Plant_GrayGrass>
      <Plant_Moss>0.5</Plant_Moss>
      <Plant_Bush>0.3</Plant_Bush>
      <Plant_Astragalus>0.1</Plant_Astragalus>
      <Plant_Dandelion>0.1</Plant_Dandelion>
      <Plant_TreePine>0.08</Plant_TreePine>
      <Plant_TreeBirch>0.08</Plant_TreeBirch>
      <Plant_TreeGrayPine MayRequire="Ludeon.RimWorld.Biotech">0.08</Plant_TreeGrayPine>
      <Plant_Berry>0.07</Plant_Berry>
      <Plant_HealrootWild>0.05</Plant_HealrootWild>
    </wildPlants>
    <wildAnimals>
      <Hare>2</Hare>
      <Snowhare>2</Snowhare>
      <Caribou>2</Caribou>
      <Elk>2</Elk>
      <Muffalo>2</Muffalo>
      <Ibex>2</Ibex>
      <Megasloth>0.2</Megasloth>
      <Fox_Arctic>0.07</Fox_Arctic>
      <Wolf_Arctic>0.07</Wolf_Arctic>
      <Wolf_Timber>0.07</Wolf_Timber>
      <Lynx>0.07</Lynx>
      <Warg>0.07</Warg>
      <Bear_Polar>0.07</Bear_Polar>
      <Bear_Grizzly>0.07</Bear_Grizzly>
    </wildAnimals>
    <pollutionWildAnimals MayRequire="Ludeon.RimWorld.Biotech">
      <WasteRat MayRequire="Ludeon.RimWorld.Biotech">1</WasteRat>
      <Warg>0.05</Warg>
    </pollutionWildAnimals>
    <allowedPackAnimals>
      <li>Muffalo</li>
    </allowedPackAnimals>
  </BiomeDef>

  <BiomeDef>
    <defName>ColdBog</defName>
    <label>cold bog</label>
    <description>A wetland packed with trees and vines. Much of the marshy land here can't support heavy structures, moving around is slow due to choking vegetation. Disease is endemic in this dense, wet ecosystem.</description>
    <workerClass>BiomeWorker_ColdBog</workerClass>
    <allowFarmingCamps>false</allowFarmingCamps>
    <animalDensity>3.3</animalDensity>
    <plantDensity>0.60</plantDensity>
    <settlementSelectionWeight>0.17</settlementSelectionWeight>
    <campSelectionWeight>0.1</campSelectionWeight>
    <movementDifficulty>4</movementDifficulty>
    <texture>World/Biomes/ColdBog</texture>
    <forageability>0.5</forageability>
    <foragedFood>RawBerries</foragedFood>
    <wildPlantRegrowDays>20</wildPlantRegrowDays>
    <soundsAmbient>
      <li>Ambient_NightInsects_Standard</li>
    </soundsAmbient>
    <diseaseMtbDays>45</diseaseMtbDays>
    <diseases>
      <li>
        <diseaseInc>Disease_Flu</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_Plague</diseaseInc>
        <commonality>80</commonality>
      </li>
      <li>
        <diseaseInc>Disease_FibrousMechanites</diseaseInc>
        <commonality>40</commonality>
      </li>
      <li>
        <diseaseInc>Disease_SensoryMechanites</diseaseInc>
        <commonality>40</commonality>
      </li>
      <li>
        <diseaseInc>Disease_GutWorms</diseaseInc>
        <commonality>30</commonality>
      </li>
      <li>
        <diseaseInc>Disease_MuscleParasites</diseaseInc>
        <commonality>30</commonality>
      </li>

      <li>
        <diseaseInc>Disease_AnimalFlu</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_AnimalPlague</diseaseInc>
        <commonality>80</commonality>
      </li>
      <li>
        <diseaseInc>Disease_OrganDecay</diseaseInc>
        <commonality>10</commonality>
      </li>
    </diseases>
    <terrainsByFertility>
      <li>
        <terrain>Soil</terrain>
        <min>-999</min>
        <max>0.87</max>
      </li>
      <li>
        <terrain>SoilRich</terrain>
        <min>0.87</min>
        <max>999</max>
      </li>
    </terrainsByFertility>
    <terrainPatchMakers>
      <li>
        <perlinFrequency>0.028</perlinFrequency>
        <thresholds>
          <li>
            <terrain>MarshyTerrain</terrain>
            <min>0.4</min>
            <max>999</max>
          </li>
        </thresholds>
      </li>
      <li>
        <perlinFrequency>0.027</perlinFrequency>
        <thresholds>
          <li>
            <terrain>SoilRich</terrain>
            <min>-0.03</min>
            <max>0.22</max>
          </li>
          <li>
            <terrain>Mud</terrain>
            <min>0.22</min>
            <max>0.6</max>
          </li>
          <li>
            <terrain>WaterShallow</terrain>
            <min>0.6</min>
            <max>999</max>
          </li>
        </thresholds>
      </li>
      <li>
        <perlinFrequency>0.015</perlinFrequency>
        <thresholds>
          <li>
            <terrain>MossyTerrain</terrain>
            <min>0.64</min>
            <max>0.83</max>
          </li>
          <li>
            <terrain>Marsh</terrain>
            <min>0.83</min>
            <max>999</max>
          </li>
        </thresholds>
      </li>
    </terrainPatchMakers>
    <baseWeatherCommonalities>
      <Clear>18</Clear>
      <Fog>1</Fog>
      <Rain>2</Rain>
      <DryThunderstorm>1</DryThunderstorm>
      <RainyThunderstorm>1</RainyThunderstorm>
      <FoggyRain>1</FoggyRain>
      <SnowGentle>4</SnowGentle>
      <SnowHard>4</SnowHard>
      <GrayPall MayRequire="Ludeon.RimWorld.Anomaly">1</GrayPall>
    </baseWeatherCommonalities>
    <wildPlants>
      <Plant_Chokevine>3.0</Plant_Chokevine>
      <Plant_TallGrass>2.4</Plant_TallGrass>
      <Plant_Moss>0.5</Plant_Moss>
      <Plant_Bush>0.3</Plant_Bush>
      <Plant_Astragalus>0.1</Plant_Astragalus>
      <Plant_TreeWillow>0.6</Plant_TreeWillow>
      <Plant_TreeCypress>0.6</Plant_TreeCypress>
      <Plant_TreeMaple>0.6</Plant_TreeMaple>
      <Plant_Berry>0.07</Plant_Berry>
      <Plant_HealrootWild>0.05</Plant_HealrootWild>
    </wildPlants>
    <wildAnimals>
      <GuineaPig>0.07</GuineaPig>
      <Yak>0.2</Yak>
      <Squirrel>1</Squirrel>
      <Hare>1</Hare>
      <Rat>1</Rat>
      <Elk>0.5</Elk>
      <Caribou>0.5</Caribou>
      <Muffalo>0.5</Muffalo>
      <WildBoar>0.5</WildBoar>
      <Ibex>0.5</Ibex>
      <Raccoon>0.5</Raccoon>
      <Turkey>0.5</Turkey>
      <Deer>0.5</Deer>
      <Megasloth>0.1</Megasloth>
      <Fox_Red>0.07</Fox_Red>
      <Fox_Arctic>0.07</Fox_Arctic>
      <Wolf_Timber>0.07</Wolf_Timber>
      <Wolf_Arctic>0.07</Wolf_Arctic>
      <Cougar>0.07</Cougar>
      <Lynx>0.07</Lynx>
      <Warg>0.07</Warg>
      <Bear_Polar>0.07</Bear_Polar>
      <Bear_Grizzly>0.07</Bear_Grizzly>
    </wildAnimals>
    <pollutionWildAnimals MayRequire="Ludeon.RimWorld.Biotech">
      <WasteRat MayRequire="Ludeon.RimWorld.Biotech">1</WasteRat>
      <Rat>0.5</Rat>
      <Raccoon>0.25</Raccoon>
      <Warg>0.05</Warg>
    </pollutionWildAnimals>
    <allowedPackAnimals>
      <li>Muffalo</li>
    </allowedPackAnimals>
  </BiomeDef>

  <BiomeDef>
    <defName>IceSheet</defName>
    <label>ice sheet</label>
    <description>Sheets of ice which can be kilometers thick. There is no soil for plants to grow in. The only animals here are migrating to somewhere else - or badly lost.</description>
    <workerClass>BiomeWorker_IceSheet</workerClass>
    <allowRoads>false</allowRoads>
    <allowRivers>false</allowRivers>
    <allowFarmingCamps>false</allowFarmingCamps>
    <animalDensity>0.2</animalDensity>
    <plantDensity>0</plantDensity>
    <hasVirtualPlants>false</hasVirtualPlants>
    <settlementSelectionWeight>0.15</settlementSelectionWeight>
    <campSelectionWeight>0.1</campSelectionWeight>
    <movementDifficulty>1.5</movementDifficulty>
    <texture>World/Biomes/IceSheet</texture>
    <forageability>0</forageability>
    <isExtremeBiome>true</isExtremeBiome>
    <soundsAmbient>
      <li>Ambient_Wind_Desolate</li>
    </soundsAmbient>
    <diseaseMtbDays>90</diseaseMtbDays>
    <diseases>
      <li>
        <diseaseInc>Disease_Flu</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_Plague</diseaseInc>
        <commonality>80</commonality>
      </li>
      <li>
        <diseaseInc>Disease_FibrousMechanites</diseaseInc>
        <commonality>30</commonality>
      </li>
      <li>
        <diseaseInc>Disease_SensoryMechanites</diseaseInc>
        <commonality>30</commonality>
      </li>

      <li>
        <diseaseInc>Disease_AnimalFlu</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_AnimalPlague</diseaseInc>
        <commonality>80</commonality>
      </li>
      <li>
        <diseaseInc>Disease_OrganDecay</diseaseInc>
        <commonality>10</commonality>
      </li>
    </diseases>
    <terrainsByFertility>
      <li>
        <terrain>Ice</terrain>
        <min>-999</min>
        <max>999</max>
      </li>
    </terrainsByFertility>
    <terrainPatchMakers>
      <li>
        <perlinFrequency>0.015</perlinFrequency>
        <thresholds>
          <li>
            <terrain>WaterShallow</terrain>
            <min>0.76</min>
            <max>0.98</max>
          </li>
          <li>
            <terrain>WaterDeep</terrain>
            <min>0.98</min>
            <max>999</max>
          </li>
        </thresholds>
      </li>
    </terrainPatchMakers>
    <baseWeatherCommonalities>
      <Clear>12</Clear>
      <Fog>0</Fog>
      <Rain>0</Rain>
      <DryThunderstorm>2</DryThunderstorm>
      <RainyThunderstorm>0</RainyThunderstorm>
      <FoggyRain>0</FoggyRain>
      <SnowGentle>20</SnowGentle>
      <SnowHard>40</SnowHard>
      <GrayPall MayRequire="Ludeon.RimWorld.Anomaly">1</GrayPall>
    </baseWeatherCommonalities>
    <wildAnimals>
      <Snowhare>2</Snowhare>
      <Muffalo>0.1</Muffalo>
      <Bear_Polar>0.1</Bear_Polar>
      <Wolf_Arctic>0.1</Wolf_Arctic>
      <Fox_Arctic>0.1</Fox_Arctic>
      <Lynx>0.1</Lynx>
      <Megasloth>0.1</Megasloth>
    </wildAnimals>
    <allowedPackAnimals>
      <li>Muffalo</li>
    </allowedPackAnimals>
  </BiomeDef>

  <BiomeDef>
    <defName>SeaIce</defName>
    <label>sea ice</label>
    <description>Permanent ice sheets floating on water. There is no soil for plants to grow, no minerals to mine, and almost no animal life.</description>
    <workerClass>BiomeWorker_SeaIce</workerClass>
    <canAutoChoose>false</canAutoChoose>
    <allowRoads>false</allowRoads>
    <allowRivers>false</allowRivers>
    <allowFarmingCamps>false</allowFarmingCamps>
    <animalDensity>0.1</animalDensity>
    <plantDensity>0</plantDensity>
    <hasVirtualPlants>false</hasVirtualPlants>
    <hasBedrock>false</hasBedrock>
    <settlementSelectionWeight>0.01</settlementSelectionWeight>
    <campSelectionWeight>0.1</campSelectionWeight>
    <movementDifficulty>1.5</movementDifficulty>
    <texture>World/Biomes/IceSheetOcean</texture>
    <forageability>0</forageability>
    <isExtremeBiome>true</isExtremeBiome>
    <soundsAmbient>
      <li>Ambient_Wind_Desolate</li>
    </soundsAmbient>
    <diseaseMtbDays>90</diseaseMtbDays>
    <diseases>
      <li>
        <diseaseInc>Disease_Flu</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_Plague</diseaseInc>
        <commonality>80</commonality>
      </li>
      <li>
        <diseaseInc>Disease_FibrousMechanites</diseaseInc>
        <commonality>30</commonality>
      </li>
      <li>
        <diseaseInc>Disease_SensoryMechanites</diseaseInc>
        <commonality>30</commonality>
      </li>

      <li>
        <diseaseInc>Disease_AnimalFlu</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_AnimalPlague</diseaseInc>
        <commonality>80</commonality>
      </li>
      <li>
        <diseaseInc>Disease_OrganDecay</diseaseInc>
        <commonality>10</commonality>
      </li>
    </diseases>
    <terrainsByFertility>
      <li>
        <terrain>Ice</terrain>
        <min>-999</min>
        <max>999</max>
      </li>
    </terrainsByFertility>
    <terrainPatchMakers>
      <li>
        <perlinFrequency>0.015</perlinFrequency>
        <thresholds>
          <li>
            <terrain>WaterShallow</terrain>
            <min>0.76</min>
            <max>0.98</max>
          </li>
          <li>
            <terrain>WaterDeep</terrain>
            <min>0.98</min>
            <max>999</max>
          </li>
        </thresholds>
      </li>
    </terrainPatchMakers>
    <baseWeatherCommonalities>
      <Clear>12</Clear>
      <Fog>0</Fog>
      <Rain>0</Rain>
      <DryThunderstorm>2</DryThunderstorm>
      <RainyThunderstorm>0</RainyThunderstorm>
      <FoggyRain>0</FoggyRain>
      <SnowGentle>8</SnowGentle>
      <SnowHard>8</SnowHard>
      <GrayPall MayRequire="Ludeon.RimWorld.Anomaly">1</GrayPall>
    </baseWeatherCommonalities>
    <wildAnimals>
      <Snowhare>2</Snowhare>
      <Muffalo>0.1</Muffalo>
      <Bear_Polar>0.1</Bear_Polar>
      <Wolf_Arctic>0.1</Wolf_Arctic>
      <Fox_Arctic>0.1</Fox_Arctic>
    </wildAnimals>
    <allowedPackAnimals>
      <li>Muffalo</li>
    </allowedPackAnimals>
  </BiomeDef>

</Defs>