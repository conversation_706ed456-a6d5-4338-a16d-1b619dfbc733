﻿<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <MapGeneratorDef Name="Undercave">
    <defName>Undercave</defName>
    <label>undercave</label>
    <isUnderground>true</isUnderground>
    <forceCaves>true</forceCaves>
    <ignoreAreaRevealedLetter>true</ignoreAreaRevealedLetter>
    <disableCallAid>true</disableCallAid>
    <customMapComponents>
      <li>UndercaveMapComponent</li>
    </customMapComponents>
    <pocketMapProperties>
      <biome>Undercave</biome>
      <temperature>15</temperature>
    </pocketMapProperties>
    <genSteps>
      <li>ElevationFertility</li>
      <li>Undercave_RocksFromGrid</li>
      <li>Undercave_Caves</li>
      <li>Terrain</li>
      <li>CavesTerrain</li>
      <li>Undercave_ScatterRuinsSimple</li>
      <li>Fleshbulbs</li>
      <li>RockChunks</li>
      <li>FindPitGateExit</li>
      <li>Dreadmeld</li>
      <li>Fleshmass</li>
      <li>FleshSacks</li>
      <li>UndercaveInterest</li>
      <li>Plants</li>
      <li>Fog</li>
    </genSteps>
  </MapGeneratorDef>

  <GenStepDef>
    <defName>Undercave_Caves</defName>
    <order>11</order>
    <genStep Class="GenStep_Undercaves">
      <baseWidth>4</baseWidth>
      <branchChance>0.4</branchChance>
      <minTunnelWidth>1.6</minTunnelWidth>
      <widthOffsetPerCell>0.008</widthOffsetPerCell>
    </genStep>
  </GenStepDef>

  <GenStepDef>
    <defName>Undercave_RocksFromGrid</defName>
    <order>200</order>
    <genStep Class="GenStep_RocksFromGrid">
      <overrideBlotchesPer10kCells>10</overrideBlotchesPer10kCells>
    </genStep>
  </GenStepDef>

  <!-- Undercave ruins -->
  <GenStepDef>
    <defName>Undercave_ScatterRuinsSimple</defName>
    <order>249</order>
    <genStep Class="GenStep_ScatterRuinsSimple">
      <allowInWaterBiome>false</allowInWaterBiome>
      <countPer10kCellsRange>2~3</countPer10kCellsRange>
      <mustBeStandable>true</mustBeStandable>
      <canBeOnEdge>true</canBeOnEdge>
      <ruinSizeChanceCurve>
        <points>
          <li>(6, 0)</li>
          <li>(6.001, 4)</li>
          <li>(10, 0)</li>
        </points>
      </ruinSizeChanceCurve>
      <destroyChanceExp>3</destroyChanceExp>
      <clearSurroundingArea>true</clearSurroundingArea>
    </genStep>
  </GenStepDef>

  <GenStepDef>
    <defName>Fleshmass</defName>
    <order>250</order>
    <genStep Class="GenStep_Fleshmass">
      <fleshFrequency>0.15</fleshFrequency>
      <fleshThreshold>0.2</fleshThreshold>
    </genStep>
  </GenStepDef>

  <GenStepDef>
    <defName>FleshSacks</defName>
    <order>251</order>
    <genStep Class="GenStep_FleshSacks">
      <numFleshSacksFromPoints>
        <points>
          <li>(0, 3)</li>
          <li>(500, 5)</li>
          <li>(1000, 7)</li>
          <li>(5000, 9)</li>
        </points>
      </numFleshSacksFromPoints>
    </genStep>
  </GenStepDef>

  <GenStepDef>
    <defName>FindPitGateExit</defName>
    <order>300</order>
    <genStep Class="GenStep_FindPitGateExit"/>
  </GenStepDef>

  <GenStepDef>
    <defName>Fleshbulbs</defName>
    <order>550</order>
    <genStep Class="GenStep_Fleshbulbs">
      <numBulbsRange>45~60</numBulbsRange>
    </genStep>
  </GenStepDef>

  <GenStepDef>
    <defName>Dreadmeld</defName>
    <order>600</order>
    <genStep Class="GenStep_Dreadmeld" />
  </GenStepDef>

  <GenStepDef>
    <defName>UndercaveInterest</defName>
    <order>650</order>
    <genStep Class="GenStep_UndercaveInterest" />
  </GenStepDef>

  <BiomeDef>
    <defName>Undercave</defName>
    <label>undercave</label>
    <description>This cave is filled with the putrid stench of rotting flesh.</description>
    <generatesNaturally>false</generatesNaturally>
    <animalDensity>0</animalDensity>
    <plantDensity>0.20</plantDensity>
    <wildAnimalsCanWanderInto>false</wildAnimalsCanWanderInto>
    <baseWeatherCommonalities>
      <Undercave>1</Undercave>
    </baseWeatherCommonalities>
    <diseaseMtbDays>80</diseaseMtbDays>
    <diseases>
      <li>
        <diseaseInc>Disease_Flu</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_Plague</diseaseInc>
        <commonality>80</commonality>
      </li>
      <li>
        <diseaseInc>Disease_FibrousMechanites</diseaseInc>
        <commonality>40</commonality>
      </li>
      <li>
        <diseaseInc>Disease_SensoryMechanites</diseaseInc>
        <commonality>40</commonality>
      </li>
      <li>
        <diseaseInc>Disease_GutWorms</diseaseInc>
        <commonality>30</commonality>
      </li>
      <li>
        <diseaseInc>Disease_MuscleParasites</diseaseInc>
        <commonality>30</commonality>
      </li>
      <li>
        <diseaseInc>Disease_AnimalFlu</diseaseInc>
        <commonality>100</commonality>
      </li>
      <li>
        <diseaseInc>Disease_AnimalPlague</diseaseInc>
        <commonality>80</commonality>
      </li>
    </diseases>
  </BiomeDef>

</Defs>