<?xml version="1.0" encoding="utf-8"?>
<Defs>

  <!-- Tunneler -->
  <SoundDef>
    <defName>Pawn_Mech_Tunneler_Wounded</defName>  
    <context>MapOnly</context>  
    <maxVoices>1</maxVoices>  
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Mechanoid/Tunneler/Wounded</clipFolderPath>
          </li>
        </grains>      
        <volumeRange>20</volumeRange>      
        <pitchRange>0.95~1.05</pitchRange>
        <repeatMode>NeverTwice</repeatMode>      
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Mech_Tunneler_Death</defName>  
    <context>MapOnly</context>  
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Mechanoid/Tunneler/Death</clipFolderPath>
          </li>
        </grains>      
        <volumeRange>20</volumeRange>      
        <pitchRange>0.95~1.05</pitchRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

  <SoundDef>
    <defName>Pawn_Mech_Tunneler_Call</defName>  
    <context>MapOnly</context>  
    <subSounds>
      <li>
        <grains>
          <li Class="AudioGrain_Folder">
            <clipFolderPath>Pawn/Mechanoid/Tunneler/Call</clipFolderPath>
          </li>
        </grains>      
        <volumeRange>20</volumeRange>      
        <pitchRange>0.95~1.05</pitchRange>
        <sustainLoop>False</sustainLoop>
      </li>
    </subSounds>
  </SoundDef>

</Defs>